import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useModels } from '../context/ModelContext';
import realDataService from '../services/realDataService';
import ImageWithFallback from './ui/ImageWithFallback';

const FloatingModelPreviews = ({
  count = 8,
  animationDuration = 20,
  opacity = 0.1,
  size = 'medium' // small, medium, large
}) => {
  const { models, featuredModels } = useModels();
  const [floatingModels, setFloatingModels] = useState([]);

  // Size configurations
  const sizeConfig = {
    small: { min: 60, max: 100 },
    medium: { min: 80, max: 140 },
    large: { min: 120, max: 200 }
  };

  const currentSize = sizeConfig[size] || sizeConfig.medium;

  // Prepare floating models using realDataService
  useEffect(() => {
    const fetchFloatingModels = async () => {
      try {
        // Get all models from realDataService
        const allModels = await realDataService.getAllModels();

        if (allModels.length > 0) {
          const validModels = allModels.filter(model =>
            model && (model._id || model.id) && model.imageUrl
          );

          if (validModels.length > 0) {
            // Create floating elements with random properties
            const floating = Array.from({ length: count }, (_, index) => {
              const model = validModels[Math.floor(Math.random() * validModels.length)];

              return {
                id: `floating-${index}`,
                model,
                // Random positioning
                initialX: Math.random() * 100,
                initialY: Math.random() * 100,
                // Random size within range
                size: Math.random() * (currentSize.max - currentSize.min) + currentSize.min,
                // Random animation properties
                duration: animationDuration + Math.random() * 10 - 5, // ±5 seconds variation
                delay: Math.random() * animationDuration,
                // Random rotation
                rotation: Math.random() * 360,
                // Random direction
                direction: Math.random() > 0.5 ? 1 : -1,
                // Random blur
                blur: Math.random() * 2 + 1
              };
            });

            setFloatingModels(floating);
          }
        } else {
          setFloatingModels([]);
        }
      } catch (error) {
        setFloatingModels([]);
      }
    };

    fetchFloatingModels();
  }, [count, currentSize, animationDuration]);

  // Animation variants for floating
  const floatingVariants = {
    animate: (custom) => ({
      x: [0, custom.direction * 50, 0],
      y: [0, -30, 0],
      rotate: [custom.rotation, custom.rotation + 360],
      scale: [1, 1.1, 1],
      transition: {
        duration: custom.duration,
        repeat: Infinity,
        ease: "easeInOut",
        delay: custom.delay
      }
    })
  };

  if (floatingModels.length === 0) {
    return null;
  }

  return (
    <div className="absolute inset-0 overflow-hidden pointer-events-none">
      {floatingModels.map((item) => (
        <motion.div
          key={item.id}
          className="absolute"
          style={{
            left: `${item.initialX}%`,
            top: `${item.initialY}%`,
            width: `${item.size}px`,
            height: `${item.size}px`,
            opacity: opacity,
            filter: `blur(${item.blur}px)`
          }}
          variants={floatingVariants}
          animate="animate"
          custom={item}
        >
          <div className="relative w-full h-full">
            {/* Glow effect */}
            <div
              className="absolute inset-0 rounded-2xl bg-gradient-to-br from-blue-400/30 to-purple-400/30 blur-lg"
              style={{
                transform: 'scale(1.2)'
              }}
            />

            {/* Image container */}
            <div className="relative w-full h-full rounded-2xl overflow-hidden shadow-2xl border border-white/20">
              <ImageWithFallback
                src={item.model.imageUrl}
                alt={item.model.title}
                className="w-full h-full object-cover"
                fallbackSrc="/images/placeholder.jpg"
              />

              {/* Overlay gradient */}
              <div className="absolute inset-0 bg-gradient-to-t from-black/50 via-transparent to-white/20" />

              {/* Shimmer effect */}
              <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -skew-x-12 animate-shimmer" />
            </div>
          </div>
        </motion.div>
      ))}
    </div>
  );
};

// Enhanced version with more complex animations
export const FloatingModelPreviewsAdvanced = ({
  count = 12,
  layers = 3,
  opacity = 0.08
}) => {
  const { models, featuredModels } = useModels();
  const [layeredModels, setLayeredModels] = useState([]);

  useEffect(() => {
    const fetchLayeredModels = async () => {
      try {
        // Get all models from realDataService
        const allModels = await realDataService.getAllModels();

        if (allModels.length > 0) {
          const validModels = allModels.filter(model =>
            model && (model._id || model.id) && model.imageUrl
          );

          if (validModels.length > 0) {
            const layered = Array.from({ length: layers }, (_, layerIndex) => {
              const layerCount = Math.ceil(count / layers);
              const layerModels = Array.from({ length: layerCount }, (_, index) => {
                const model = validModels[Math.floor(Math.random() * validModels.length)];

                return {
                  id: `layer-${layerIndex}-${index}`,
                  model,
                  layer: layerIndex,
                  // Layer-specific properties
                  size: 60 + layerIndex * 30 + Math.random() * 40,
                  opacity: opacity * (1 + layerIndex * 0.3),
                  speed: 15 + layerIndex * 5,
                  initialX: Math.random() * 100,
                  initialY: Math.random() * 100,
                  direction: Math.random() > 0.5 ? 1 : -1,
                  rotation: Math.random() * 360
                };
              });

              return layerModels;
            }).flat();

            setLayeredModels(layered);
          }
        } else {
          setLayeredModels([]);
        }
      } catch (error) {
        setLayeredModels([]);
      }
    };

    fetchLayeredModels();
  }, [count, layers, opacity]);

  const layerVariants = {
    animate: (custom) => ({
      x: [0, custom.direction * (30 + custom.layer * 20), 0],
      y: [0, -20 - custom.layer * 10, 0],
      rotate: [custom.rotation, custom.rotation + 180 * custom.direction],
      scale: [1, 1.05 + custom.layer * 0.05, 1],
      transition: {
        duration: custom.speed,
        repeat: Infinity,
        ease: "easeInOut",
        delay: custom.layer * 2
      }
    })
  };

  if (layeredModels.length === 0) {
    return null;
  }

  return (
    <div className="absolute inset-0 overflow-hidden pointer-events-none">
      {layeredModels.map((item) => (
        <motion.div
          key={item.id}
          className="absolute"
          style={{
            left: `${item.initialX}%`,
            top: `${item.initialY}%`,
            width: `${item.size}px`,
            height: `${item.size}px`,
            opacity: item.opacity,
            zIndex: item.layer,
            filter: `blur(${1 + item.layer}px)`
          }}
          variants={layerVariants}
          animate="animate"
          custom={item}
        >
          <div className="relative w-full h-full">
            {/* Multi-layer glow */}
            <div
              className={`absolute inset-0 rounded-2xl blur-lg ${
                item.layer === 0 ? 'bg-gradient-to-br from-blue-400/20 to-cyan-400/20' :
                item.layer === 1 ? 'bg-gradient-to-br from-purple-400/20 to-pink-400/20' :
                'bg-gradient-to-br from-orange-400/20 to-red-400/20'
              }`}
              style={{ transform: `scale(${1.2 + item.layer * 0.1})` }}
            />

            {/* Image with enhanced effects */}
            <div className="relative w-full h-full rounded-2xl overflow-hidden shadow-2xl border border-white/10">
              <ImageWithFallback
                src={item.model.imageUrl}
                alt={item.model.title}
                className="w-full h-full object-cover"
                fallbackSrc="/images/placeholder.jpg"
              />

              {/* Layer-specific overlays */}
              <div className={`absolute inset-0 ${
                item.layer === 0 ? 'bg-gradient-to-t from-blue-900/30 to-transparent' :
                item.layer === 1 ? 'bg-gradient-to-t from-purple-900/30 to-transparent' :
                'bg-gradient-to-t from-orange-900/30 to-transparent'
              }`} />

              {/* Animated shimmer */}
              <div
                className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent -skew-x-12"
                style={{
                  animation: `shimmer ${3 + item.layer}s infinite linear`
                }}
              />
            </div>
          </div>
        </motion.div>
      ))}
    </div>
  );
};

export default FloatingModelPreviews;
