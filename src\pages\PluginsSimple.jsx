import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { toast } from 'react-hot-toast';
import { /* content */ };
  FiDownload, FiStar, FiPackage, FiTool, FiSearch,
  FiFilter, FiGrid, FiList
} from 'react-icons/fi';
import Header from '../components/Header';
import Footer from '../components/Footer';
import axios from 'axios';

const PluginsSimple = () => {
  const [plugins, setPlugins] = useState([]);
  const [filteredPlugins, setFilteredPlugins] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('''; // Fixed broken string
  const [selectedCategory, setSelectedCategory] = useState('all''; // Fixed broken string
  const [viewMode, setViewMode] = useState('grid''; // Fixed broken string

  useEffect(() => {
    // Fixed content
  };
  loadPlugins();
  }, []);

  useEffect(() => {
    // Fixed content
  };
  filterPlugins();
  }, [plugins, searchQuery, selectedCategory]);

  const loadPlugins = async () => {
    // Fixed content
  };
  setIsLoading(true);
    try { /* content */ };
      const response = await axios.get('/api/extensions', {
    params: {
    limit: 50,
          sortBy: 'downloads'
        }
      });

      if (condition) {
    // Fixed content
  }
  setPlugins(response.data.data.extensions);
        toast.success(`Loaded ${response.data.data.extensions.length} extensions`);
      }
    } catch (error) { /* content */ };
      toast.error('Failed to load extensions''; // Fixed broken string
    } finally { /* content */ };
      setIsLoading(false);
    }
  };

  const filterPlugins = () => {
    let filtered = plugins;

    // Filter by search query
    if (condition) {
    // Fixed content
  }
  filtered = filtered.filter(plugin =>
        plugin.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        plugin.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
        plugin.developer.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    // Filter by category
    if (condition) {
    // Fixed content
  }
  filtered = filtered.filter(plugin =>
        plugin.category.toLowerCase() === selectedCategory.toLowerCase()
      );
    }

    setFilteredPlugins(filtered);
  };

  const categories = [
    'all',
    ...new Set(plugins.map(plugin => plugin.category))
  ];

  const getPriceColor = (price) => {
    // Fixed content
  };
  if (price === 'Free') return 'text-green-600 dark:text-green-400';
    return 'text-blue-600 dark:text-blue-400';
  };

  const getRatingStars = (rating) => {
    // Fixed content
  };
  const stars = [];
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 !== 0;

    for (let i = 0; i < fullStars; i++) { /* content */ };
      stars.push(<FiStar key={i} className="h-4 w-4 fill-current text-yellow-400" />);
    }

    if (condition) {
    // Fixed content
  }
  stars.push(<FiStar key="half" className="h-4 w-4 fill-current text-yellow-400 opacity-50" />);
    }

    const emptyStars = 5 - Math.ceil(rating);
    for (let i = 0; i < emptyStars; i++) { /* content */ };
      stars.push(<FiStar key={`empty-${i}`} className="h-4 w-4 text-gray-300 dark:text-gray-600" />);
    }

    return stars;
  };

  const PluginCard = ({ plugin }) => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="bg-white dark:bg-gray-800 rounded-lg shadow-md hover:shadow-lg transition-all duration-300 overflow-hidden"
    >
      <div className="p-6">
        {/* Header */}
        <div className="flex justify-between items-start mb-4">
          <div className="flex-1">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-1">
              {plugin.name}
            </h3>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              by {plugin.developer}
            </p>
          </div>
          <div className="flex items-center space-x-2">
            <span className={`text-lg font-bold ${getPriceColor(plugin.price)}`}>
              {plugin.price}
            </span>
          </div>
        </div>

        {/* Description */}
        <p className="text-gray-700 dark:text-gray-300 text-sm mb-4 line-clamp-2">
          {plugin.description}
        </p>

        {/* Category */}
        <div className="flex items-center mb-3">
          <FiPackage className="h-4 w-4 text-gray-500 mr-2" />
          <span className="text-sm text-gray-600 dark:text-gray-400">
            {plugin.category}
          </span>
        </div>

        {/* Rating and Downloads */}
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-1">
            {getRatingStars(plugin.rating)}
            <span className="text-sm text-gray-600 dark:text-gray-400 ml-2">
              {plugin.rating}
            </span>
          </div>
          <div className="flex items-center text-sm text-gray-600 dark:text-gray-400">
            <FiDownload className="h-4 w-4 mr-1" />
            <span>{plugin.downloads.toLocaleString()}</span>
          </div>
        </div>

        {/* Actions */}
        <div className="flex space-x-2">
          <button className="flex-1 flex items-center justify-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors text-sm">
            <FiDownload className="h-4 w-4 mr-2" />
            Download
          </button>
        </div>
      </div>
    </motion.div>
  );

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <Header />

      <main className="pt-24 pb-16">
        {/* Hero Section */}
        <section className="bg-gradient-to-br from-purple-600 to-indigo-700 text-white py-16">
          <div className="container mx-auto px-4 text-center">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
            >
              <FiTool className="h-16 w-16 mx-auto mb-6 text-purple-200" />
              <h1 className="text-4xl md:text-5xl font-bold mb-4">
                SketchUp Extensions
              </h1>
              <p className="text-xl text-purple-100 max-w-2xl mx-auto">
                Discover powerful extensions to enhance your SketchUp workflow
              </p>
            </motion.div>
          </div>
        </section>

        {/* Filters Section */}
        <section className="py-8 bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
          <div className="container mx-auto px-4">
            <div className="flex flex-col lg:flex-row gap-4 items-center justify-between">
              {/* Search */}
              <div className="flex-1 max-w-md">
                <div className="relative">
                  <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
                  <input
                    type="text"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    placeholder="Search extensions..."
                    className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
              </div>

              {/* Category Filter */}
              <div className="flex items-center space-x-4">
                <FiFilter className="h-5 w-5 text-gray-500" />
                <select
                  value={selectedCategory}
                  onChange={(e) => setSelectedCategory(e.target.value)}
                  className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  {categories.map(category => (
                    <option key={category} value={category}>
                      {category === 'all' ? 'All Categories' : category}
                    </option>
                  ))}
                </select>
              </div>

              {/* View Mode */}
              <div className="flex items-center space-x-2">
                <button
                  onClick={() => setViewMode('grid')}
                  className={`p-2 rounded-lg transition-colors ${
    // Fixed content
  }
  viewMode === 'grid'
                      ? 'bg-blue-600 text-white'
                      : 'bg-gray-200 dark:bg-gray-700 text-gray-600 dark:text-gray-400'
                  }`}
                >
                  <FiGrid className="h-5 w-5" />
                </button>
                <button
                  onClick={() => setViewMode('list')}
                  className={`p-2 rounded-lg transition-colors ${
    // Fixed content
  }
  viewMode === 'list'
                      ? 'bg-blue-600 text-white'
                      : 'bg-gray-200 dark:bg-gray-700 text-gray-600 dark:text-gray-400'
                  }`}
                >
                  <FiList className="h-5 w-5" />
                </button>
              </div>
            </div>
          </div>
        </section>

        {/* Extensions Grid */}
        <section className="py-12">
          <div className="container mx-auto px-4">
            {isLoading ? (
              <div className="text-center py-12">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
                <p className="text-gray-600 dark:text-gray-400">Loading extensions...</p>
              </div>
            ) : filteredPlugins.length > 0 ? (
              <>
                <div className="flex justify-between items-center mb-8">
                  <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
                    {filteredPlugins.length} Extension{filteredPlugins.length !== 1 ? 's' : ''} Found
                  </h2>
                </div>

                <div className={`grid gap-6 ${
    // Fixed content
  }
  viewMode === 'grid'
                    ? 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3'
                    : 'grid-cols-1'
                }`}>
                  {filteredPlugins.map((plugin, index) => (
                    <PluginCard key={plugin._id || index} plugin={plugin} />
                  ))}
                </div>
              </>
            ) : (
              <div className="text-center py-12">
                <FiPackage className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                  No extensions found
                </h3>
                <p className="text-gray-600 dark:text-gray-400">
                  Try adjusting your search or filter criteria
                </p>
              </div>
            )}
          </div>
        </section>
      </main>

      <Footer />
    </div>
  );
};

export default PluginsSimple;
