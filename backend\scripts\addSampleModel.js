import mongoose from 'mongoose';
import Model from '../src/models/Model.js';
import User from '../src/models/User.js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/3dsketchup';

// Sample model data
const sampleModel = {
  title: "Luxury Bedroom Interior",
  description: "A beautifully designed luxury bedroom with modern furniture, elegant lighting, and premium materials. Perfect for architectural visualization and interior design projects.",
  category: "Residential",
  subcategory: "Bedroom",
  tags: ["bedroom", "luxury", "modern", "interior", "furniture", "lighting"],
  format: "Sketchup 2023",
  year: "2023",
  fileSize: 15925248, // 15.2 MB in bytes
  fileFormat: "skp",
  fileUrl: "https://3dsketchup.net/models/luxury-bedroom.skp",
  imageUrl: "https://3dsketchup.net/images/luxury-bedroom-preview.jpg",
  modelUrl: "https://3dsketchup.net/models/luxury-bedroom.glb",
  polygonCount: 125000,
  textured: true,
  rigged: false,
  animated: false,
  dimensions: {
    width: 4.5,
    height: 3.2,
    depth: 5.8,
    unit: "m"
  },
  isPremium: true,
  downloads: 1247,
  views: 3521,
  rating: 4.8,
  reviews: []
};

async function addSampleModel() {
  try {
    // Connect to MongoDB
    await mongoose.connect(MONGODB_URI);
    console.log('Connected to MongoDB');

    // Find admin user to set as creator
    const adminUser = await User.findOne({ role: 'admin' });
    if (!adminUser) {
      console.error('Admin user not found. Please create an admin user first.');
      process.exit(1);
    }

    // Add creator information
    sampleModel.createdBy = adminUser._id;
    sampleModel.createdAt = new Date();
    sampleModel.updatedAt = new Date();

    // Check if model already exists
    const existingModel = await Model.findOne({ title: sampleModel.title });
    if (existingModel) {
      console.log('Sample model already exists:', existingModel.title);
      process.exit(0);
    }

    // Create new model
    const model = new Model(sampleModel);
    await model.save();

    console.log('Sample model created successfully:');
    console.log('- Title:', model.title);
    console.log('- ID:', model._id);
    console.log('- Category:', model.category);
    console.log('- Price:', model.price);
    console.log('- Status:', model.status);

    process.exit(0);
  } catch (error) {
    console.error('Error adding sample model:', error);
    process.exit(1);
  }
}

// Run the script
addSampleModel();
