import jwt from 'jsonwebtoken';
import User from '../models/User.js';

// Protect routes
export const protect = async (req, res, next) => {
  let token;

  if (
    req.headers.authorization &&
    req.headers.authorization.startsWith('Bearer')
  ) {
    // Set token from Bear<PERSON> token in header
    token = req.headers.authorization.split(' ')[1];
  }

  // Make sure token exists
  if (!token) {
    return res.status(401).json({
      success: false,
      error: 'Not authorized to access this route'
    });
  }

  try {
    // Verify token
    const decoded = jwt.verify(token, process.env.JWT_SECRET);

    // Find user by ID
    const user = await User.findById(decoded.id);

    // Check if user exists
    if (!user) {
      return res.status(401).json({
        success: false,
        error: 'User not found or deleted'
      });
    }

    // Set user on request object
    req.user = user;

    next();
  } catch (err) {
    console.error('Auth error:', err);
    return res.status(401).json({
      success: false,
      error: 'Not authorized to access this route'
    });
  }
};

// Grant access to specific roles
export const authorize = (...roles) => {
  return (req, res, next) => {
    // Admin role has access to everything
    if (req.user.role === 'admin') {
      return next();
    }

    if (!roles.includes(req.user.role)) {
      return res.status(403).json({
        success: false,
        error: `User role ${req.user.role} is not authorized to access this route`
      });
    }
    next();
  };
};

// Grant access based on specific permissions
export const authorizePermission = (...permissions) => {
  return (req, res, next) => {
    // Admin role has all permissions
    if (req.user.role === 'admin') {
      return next();
    }

    // Check if user has explicit permissions
    if (req.user.permissions && Array.isArray(req.user.permissions)) {
      const hasPermission = permissions.some(permission =>
        req.user.permissions.includes(permission)
      );

      if (hasPermission) {
        return next();
      }
    }

    // Map roles to default permissions
    const rolePermissions = {
      'admin': ['all'],
      'moderator': ['view', 'edit', 'delete', 'approve', 'upload'],
      'contributor': ['view', 'create', 'edit_own'],
      'user': ['view', 'download']
    };

    // Check if user's role has the required permission
    const userRolePermissions = rolePermissions[req.user.role] || [];

    if (userRolePermissions.includes('all') ||
        permissions.some(permission => userRolePermissions.includes(permission))) {
      return next();
    }

    return res.status(403).json({
      success: false,
      error: `You don't have permission to perform this action`
    });
  };
};
