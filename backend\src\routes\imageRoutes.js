import express from 'express';
import {
  uploadModelImages,
  deleteModelImage,
  setMainImage,
  getModelImages,
  reorderImages
} from '../controllers/imageController.js';

const router = express.Router();

// Public routes
router.get('/:modelId', getModelImages);

// Admin routes (add authentication middleware here)
router.post('/upload/:modelId', uploadModelImages);
router.delete('/:modelId/:imageId', deleteModelImage);
router.put('/:modelId/:imageId/set-main', setMainImage);
router.put('/:modelId/reorder', reorderImages);

export default router;
