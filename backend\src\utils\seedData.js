import mongoose from 'mongoose';
import dotenv from 'dotenv';
import bcrypt from 'bcryptjs';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// Load models
import User from '../models/User.js';
import Model from '../models/Model.js';
import Category from '../models/Category.js';
import Subscription from '../models/Subscription.js';
import Payment from '../models/Payment.js';
import Review from '../models/Review.js';
import Activity from '../models/Activity.js';

// Load environment variables
dotenv.config();

// Connect to MongoDB
mongoose.connect(process.env.MONGODB_URI, {
  useNewUrlParser: true,
  useUnifiedTopology: true,
});

// Get current directory
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Create upload directories if they don't exist
const uploadDirs = ['../../uploads', '../../uploads/models', '../../uploads/images', '../../uploads/previews'];
uploadDirs.forEach(dir => {
  const dirPath = path.join(__dirname, dir);
  if (!fs.existsSync(dirPath)) {
    fs.mkdirSync(dirPath, { recursive: true });
  }
});

// Sample data
const sampleUsers = [
  {
    name: 'Admin User',
    email: '<EMAIL>',
    password: 'password123',
    role: 'admin',
    bio: 'Administrator of 3DSKETCHUP.NET',
    location: 'New York, USA',
    subscription: {
      type: 'professional',
      status: 'active',
      startDate: new Date(),
      endDate: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000) // 1 year from now
    },
    downloadCredits: 100
  },
  {
    name: 'Premium User',
    email: '<EMAIL>',
    password: 'password123',
    role: 'user',
    bio: 'Professional 3D artist',
    location: 'Los Angeles, USA',
    subscription: {
      type: 'premium',
      status: 'active',
      startDate: new Date(),
      endDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000) // 30 days from now
    },
    downloadCredits: 50
  },
  {
    name: 'Basic User',
    email: '<EMAIL>',
    password: 'password123',
    role: 'user',
    bio: 'Hobbyist 3D designer',
    location: 'Chicago, USA',
    subscription: {
      type: 'basic',
      status: 'active',
      startDate: new Date(),
      endDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000) // 30 days from now
    },
    downloadCredits: 20
  },
  {
    name: 'Free User',
    email: '<EMAIL>',
    password: 'password123',
    role: 'user',
    bio: 'Just getting started with 3D modeling',
    location: 'Miami, USA',
    subscription: {
      type: 'free',
      status: 'active'
    },
    downloadCredits: 5
  }
];

const sampleCategories = [
  {
    name: 'Residential',
    description: 'Residential buildings and interiors',
    featured: true,
    order: 1,
    icon: 'default-category.svg'
  },
  {
    name: 'Commercial',
    description: 'Commercial buildings and interiors',
    featured: true,
    order: 2,
    icon: 'default-category.svg'
  },
  {
    name: 'Exterior',
    description: 'Exterior scenes and buildings',
    featured: true,
    order: 3,
    icon: 'default-category.svg'
  },
  {
    name: 'Landscape/Garden',
    description: 'Landscape and garden designs',
    featured: true,
    order: 4,
    icon: 'default-category.svg'
  },
  {
    name: 'Furniture',
    description: 'Furniture and decor items',
    featured: false,
    order: 5,
    icon: 'default-category.svg'
  },
  {
    name: 'Flower/Shrub/Bush',
    description: 'Plants and vegetation',
    featured: false,
    order: 6,
    icon: 'default-category.svg'
  },
  {
    name: 'Other',
    description: 'Miscellaneous 3D models',
    featured: false,
    order: 7,
    icon: 'default-category.svg'
  }
];

// Function to seed the database
const seedDatabase = async () => {
  try {
    // Check if database already has data
    const userCount = await User.countDocuments();

    if (userCount > 0 && process.argv[2] !== '-f') {
      console.log('Database already has data. Use -f flag to force reseed.');
      process.exit(0);
      return;
    }

    console.log('Clearing existing data and reseeding database...');

    // Clear existing data
    await User.deleteMany();
    await Model.deleteMany();
    await Category.deleteMany();
    await Subscription.deleteMany();
    await Payment.deleteMany();
    await Review.deleteMany();
    await Activity.deleteMany();

    console.log('Database cleared');

    // Create users
    const createdUsers = [];
    for (const user of sampleUsers) {
      // Hash password
      const salt = await bcrypt.genSalt(10);
      const hashedPassword = await bcrypt.hash(user.password, salt);

      const newUser = await User.create({
        ...user,
        password: hashedPassword
      });

      createdUsers.push(newUser);
    }

    console.log(`${createdUsers.length} users created`);

    // Create categories
    const createdCategories = [];
    for (const category of sampleCategories) {
      const newCategory = await Category.create(category);
      createdCategories.push(newCategory);
    }

    console.log(`${createdCategories.length} categories created`);

    // Create sample models
    const sampleModels = [];

    // Generate 20 sample models
    for (let i = 1; i <= 20; i++) {
      const isPremium = i % 3 === 0; // Every third model is premium
      const categoryIndex = i % createdCategories.length;
      const userIndex = i % createdUsers.length;

      sampleModels.push({
        title: `Sample 3D Model ${i}`,
        description: `This is a detailed description for sample 3D model ${i}. It includes information about the model's features, design, and potential uses.`,
        category: createdCategories[categoryIndex].name,
        subcategory: i % 2 === 0 ? 'Interior' : 'Exterior',
        format: 'Sketchup 2023',
        year: '2023',
        imageUrl: '/uploads/images/sample_image.jpg',
        fileUrl: '/uploads/models/sample_model.skp',
        modelUrl: i % 2 === 0 ? '/uploads/previews/sample_preview.glb' : null,
        fileSize: 1024 * 1024 * (i % 10 + 1), // Random file size between 1-10 MB
        fileFormat: 'skp',
        polygonCount: 10000 * i,
        textured: i % 2 === 0,
        rigged: i % 5 === 0,
        animated: i % 10 === 0,
        dimensions: {
          width: 10,
          height: 5,
          depth: 3,
          unit: 'm'
        },
        tags: ['sample', `model${i}`, i % 2 === 0 ? 'interior' : 'exterior', 'sketchup'],
        downloads: Math.floor(Math.random() * 100),
        views: Math.floor(Math.random() * 500),
        rating: (Math.random() * 4 + 1).toFixed(1), // Random rating between 1-5
        isPremium,
        createdBy: createdUsers[userIndex]._id
      });
    }

    // Add special luxury bedroom model (Model 18 replacement)
    sampleModels.push({
      title: 'Luxury Bedroom Interior - Enscape Model',
      description: 'Stunning luxury bedroom interior design with premium furniture, elegant lighting, and sophisticated decor. Perfect for high-end residential projects and architectural visualization. Features detailed textures, realistic materials, and optimized for Enscape rendering. Includes king-size bed, premium bedding, designer furniture, ambient lighting, and luxury accessories.',
      category: 'Residential',
      subcategory: 'Interior',
      format: 'Sketchup 2023',
      year: '2024',
      imageUrl: 'https://3dsketchup.net/wp-content/uploads/2024/01/luxury-bedroom-interior-skp_model-enscape-0401080323-1.jpg',
      fileUrl: 'https://3dsketchup.net/free-download/luxury-bedroom-interior-skp_model-enscape-0401080323/',
      modelUrl: '/uploads/previews/luxury_bedroom_preview.glb',
      fileSize: 45 * 1024 * 1024, // 45 MB
      fileFormat: 'skp',
      polygonCount: 180000,
      textured: true,
      rigged: false,
      animated: false,
      dimensions: {
        width: 6.5,
        height: 3.2,
        depth: 5.8,
        unit: 'm'
      },
      tags: ['luxury', 'bedroom', 'interior', 'residential', 'enscape', 'premium', 'furniture', 'lighting', 'modern', 'elegant'],
      downloads: 1247,
      views: 3892,
      rating: 4.8,
      isPremium: true,
      createdBy: createdUsers[0]._id // Admin user
    });

    // Create sample image and model files if they don't exist
    const sampleImagePath = path.join(__dirname, '../../uploads/images/sample_image.jpg');
    const sampleModelPath = path.join(__dirname, '../../uploads/models/sample_model.skp');
    const samplePreviewPath = path.join(__dirname, '../../uploads/previews/sample_preview.glb');

    if (!fs.existsSync(sampleImagePath)) {
      // Create a simple placeholder image file
      fs.writeFileSync(sampleImagePath, 'Sample image placeholder');
    }

    if (!fs.existsSync(sampleModelPath)) {
      // Create a simple placeholder model file
      fs.writeFileSync(sampleModelPath, 'Sample model placeholder');
    }

    if (!fs.existsSync(samplePreviewPath)) {
      // Create a simple placeholder preview file
      fs.writeFileSync(samplePreviewPath, 'Sample preview placeholder');
    }

    // Create models
    const createdModels = await Model.insertMany(sampleModels);
    console.log(`${createdModels.length} models created`);

    // Create subscriptions
    const subscriptions = [];
    for (let i = 0; i < 3; i++) {
      const user = createdUsers[i];
      if (user.subscription.type !== 'free') {
        subscriptions.push({
          user: user._id,
          plan: user.subscription.type,
          status: user.subscription.status,
          startDate: user.subscription.startDate,
          endDate: user.subscription.endDate,
          price: user.subscription.type === 'basic' ? 9.99 : user.subscription.type === 'premium' ? 19.99 : 29.99,
          interval: 'monthly',
          features: {
            downloadLimit: user.subscription.type === 'basic' ? 20 : user.subscription.type === 'premium' ? 50 : 100,
            accessToPremiumModels: user.subscription.type !== 'basic',
            prioritySupport: user.subscription.type === 'professional',
            commercialUse: user.subscription.type === 'professional'
          }
        });
      }
    }

    const createdSubscriptions = await Subscription.insertMany(subscriptions);
    console.log(`${createdSubscriptions.length} subscriptions created`);

    // Create payments
    const payments = [];
    for (let i = 0; i < 3; i++) {
      const user = createdUsers[i];
      if (user.subscription.type !== 'free') {
        payments.push({
          user: user._id,
          amount: user.subscription.type === 'basic' ? 9.99 : user.subscription.type === 'premium' ? 19.99 : 29.99,
          currency: 'USD',
          paymentMethod: 'credit_card',
          status: 'completed',
          type: 'subscription',
          description: `Subscription payment for ${user.subscription.type} plan`,
          stripePaymentId: `pi_${Math.random().toString(36).substring(2, 15)}`,
          stripeCustomerId: `cus_${Math.random().toString(36).substring(2, 15)}`,
          subscriptionPlan: user.subscription.type
        });
      }
    }

    const createdPayments = await Payment.insertMany(payments);
    console.log(`${createdPayments.length} payments created`);

    // Create reviews
    const reviews = [];
    const reviewMap = new Map(); // To track unique user-model combinations

    for (let i = 0; i < 30; i++) {
      const modelIndex = i % createdModels.length;
      const userIndex = i % createdUsers.length;

      const userId = createdUsers[userIndex]._id.toString();
      const modelId = createdModels[modelIndex]._id.toString();
      const key = `${userId}-${modelId}`;

      // Skip if this user-model combination already exists
      if (reviewMap.has(key)) {
        continue;
      }

      reviewMap.set(key, true);

      reviews.push({
        user: createdUsers[userIndex]._id,
        model: createdModels[modelIndex]._id,
        rating: Math.floor(Math.random() * 5) + 1,
        title: `Review ${i + 1}`,
        comment: `This is a sample review ${i + 1} for the model. It includes feedback about the quality, usability, and overall satisfaction with the 3D model.`,
        verified: i % 2 === 0
      });
    }

    const createdReviews = await Review.insertMany(reviews);
    console.log(`${createdReviews.length} reviews created`);

    // Create activities
    const activities = [];
    const activityTypes = [
      'download', 'upload', 'save', 'comment', 'view', 'login', 'register',
      'profile_update', 'password_update', 'subscription_update', 'subscription_create',
      'subscription_cancel', 'subscription_reactivate', 'security_update'
    ];

    for (const user of createdUsers) {
      // Number of activities per user (between 5 and 15)
      const numActivities = Math.floor(Math.random() * 10) + 5;

      for (let i = 0; i < numActivities; i++) {
        // Random activity type
        const type = activityTypes[Math.floor(Math.random() * activityTypes.length)];

        // Random model (for model-related activities)
        const model = createdModels[Math.floor(Math.random() * createdModels.length)];

        // Random date in the last 30 days
        const date = new Date();
        date.setDate(date.getDate() - Math.floor(Math.random() * 30));

        const activity = {
          user: user._id,
          type,
          createdAt: date
        };

        // Add model reference for model-related activities
        if (['download', 'upload', 'save', 'comment', 'view'].includes(type)) {
          activity.model = model._id;
        }

        // Add details for specific activity types
        if (type === 'comment') {
          activity.details = {
            comment: 'Great model, I love the details!'
          };
        } else if (type === 'login') {
          activity.ipAddress = '192.168.1.' + Math.floor(Math.random() * 255);
          activity.userAgent = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36';
        }

        activities.push(activity);
      }

      // Add login history to users
      const loginActivities = activities.filter(a => a.user.toString() === user._id.toString() && a.type === 'login');

      if (loginActivities.length > 0) {
        const loginHistory = loginActivities.map(a => ({
          date: a.createdAt,
          ipAddress: a.ipAddress || '127.0.0.1',
          userAgent: a.userAgent || 'Unknown',
          successful: true
        }));

        await User.findByIdAndUpdate(user._id, { loginHistory });
      }
    }

    const createdActivities = await Activity.insertMany(activities);
    console.log(`${createdActivities.length} activities created`);

    // Update users with saved models and download history
    for (const user of createdUsers) {
      // Add random saved models
      const savedModels = [];
      for (let i = 0; i < Math.floor(Math.random() * 5) + 1; i++) {
        const randomModel = createdModels[Math.floor(Math.random() * createdModels.length)];
        if (!savedModels.includes(randomModel._id)) {
          savedModels.push(randomModel._id);
        }
      }

      // Add random download history
      const downloadHistory = [];
      for (let i = 0; i < Math.floor(Math.random() * 10) + 1; i++) {
        const randomModel = createdModels[Math.floor(Math.random() * createdModels.length)];
        const downloadDate = new Date();
        downloadDate.setDate(downloadDate.getDate() - Math.floor(Math.random() * 30));

        downloadHistory.push({
          model: randomModel._id,
          downloadedAt: downloadDate
        });
      }

      await User.findByIdAndUpdate(user._id, {
        savedModels,
        downloadHistory
      });
    }

    console.log('Users updated with saved models and download history');
    console.log('Database seeded successfully');
    process.exit(0);
  } catch (error) {
    console.error('Error seeding database:', error);
    process.exit(1);
  }
};

// Run the seed function
seedDatabase();
