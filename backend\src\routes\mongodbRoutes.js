import express from 'express';
import mongoose from 'mongoose';
import Model from '../models/Model.js';
import User from '../models/User.js';
import Category from '../models/Category.js';
import Review from '../models/Review.js';
import Collection from '../models/Collection.js';
import Subscription from '../models/Subscription.js';
import Payment from '../models/Payment.js';

const router = express.Router();

// @desc    Get statistics
// @route   GET /api/mongodb/statistics
// @access  Public
router.get('/statistics', async (req, res) => {
  try {
    // Get real statistics from MongoDB
    const modelsCount = await Model.countDocuments();
    const usersCount = await User.countDocuments();
    const categoriesCount = await Category.countDocuments();

    // Calculate total downloads
    const totalDownloads = await Model.aggregate([
      { $group: { _id: null, total: { $sum: '$downloads' } } }
    ]);

    const downloadsCount = totalDownloads.length > 0 ? totalDownloads[0].total : 0;

    res.json({
      success: true,
      data: {
        models: modelsCount,
        users: usersCount,
        categories: categoriesCount,
        downloads: downloadsCount
      }
    });
  } catch (error) {
    console.error('Error fetching statistics:', error);
    res.status(500).json({
      success: false,
      error: 'Server error'
    });
  }
});

// @desc    Get all models
// @route   GET /api/mongodb/models
// @access  Public
router.get('/models', async (req, res) => {
  try {
    // Copy req.query
    const reqQuery = { ...req.query };

    // Fields to exclude
    const removeFields = ['select', 'sort', 'page', 'limit', 'search'];

    // Loop over removeFields and delete them from reqQuery
    removeFields.forEach(param => delete reqQuery[param]);

    // Create query string
    let queryStr = JSON.stringify(reqQuery);

    // Create operators ($gt, $gte, etc)
    queryStr = queryStr.replace(/\b(gt|gte|lt|lte|in)\b/g, match => `$${match}`);

    // Finding resource
    let query = Model.find(JSON.parse(queryStr));

    // Search functionality
    if (req.query.search) {
      query = query.find({ $text: { $search: req.query.search } });
    }

    // Select Fields
    if (req.query.select) {
      const fields = req.query.select.split(',').join(' ');
      query = query.select(fields);
    }

    // Sort
    if (req.query.sort) {
      const sortBy = req.query.sort.split(',').join(' ');
      query = query.sort(sortBy);
    } else {
      query = query.sort('-createdAt');
    }

    // Pagination
    const page = parseInt(req.query.page, 10) || 1;
    const limit = parseInt(req.query.limit, 10) || 10;
    const startIndex = (page - 1) * limit;
    const endIndex = page * limit;
    const total = await Model.countDocuments(JSON.parse(queryStr));

    query = query.skip(startIndex).limit(limit);

    // Executing query
    const models = await query;

    // Pagination result
    const pagination = {};

    if (endIndex < total) {
      pagination.next = {
        page: page + 1,
        limit
      };
    }

    if (startIndex > 0) {
      pagination.prev = {
        page: page - 1,
        limit
      };
    }

    res.json({
      success: true,
      count: models.length,
      pagination,
      data: models
    });
  } catch (error) {
    console.error('Error fetching models:', error);
    res.status(500).json({
      success: false,
      error: 'Server error'
    });
  }
});

// @desc    Get model by ID
// @route   GET /api/mongodb/models/:id
// @access  Public
router.get('/models/:id', async (req, res) => {
  try {
    const { id } = req.params;

    // Validate ID
    if (!id || id === 'undefined' || id === 'null') {
      return res.status(400).json({
        success: false,
        error: 'Invalid model ID'
      });
    }

    // Check if ID is a valid MongoDB ObjectId
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid model ID format'
      });
    }

    const model = await Model.findById(id);

    if (!model) {
      return res.status(404).json({
        success: false,
        error: 'Model not found'
      });
    }

    res.json({
      success: true,
      data: model
    });
  } catch (error) {
    console.error(`Error fetching model ${req.params.id}:`, error);
    res.status(500).json({
      success: false,
      error: 'Server error'
    });
  }
});

// @desc    Get all categories
// @route   GET /api/mongodb/categories
// @access  Public
router.get('/categories', async (req, res) => {
  try {
    const categories = await Category.find();

    res.json({
      success: true,
      count: categories.length,
      data: categories
    });
  } catch (error) {
    console.error('Error fetching categories:', error);
    res.status(500).json({
      success: false,
      error: 'Server error'
    });
  }
});

export default router;
