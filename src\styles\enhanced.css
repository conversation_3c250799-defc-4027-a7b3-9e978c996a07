/* Enhanced styles for 3DSKETCHUP.NET */

/* Professional Header Fixes */
.header-professional {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  width: 100vw !important;
  z-index: 2147483647 !important;
  background: rgba(255, 255, 255, 0.98) !important;
  backdrop-filter: blur(20px) !important;
  -webkit-backdrop-filter: blur(20px) !important;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1) !important;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1) !important;
  min-height: 88px !important;
  height: auto !important;
  display: flex !important;
  align-items: center !important;
  visibility: visible !important;
  opacity: 1 !important;
  transform: translateZ(999999px) !important;
  will-change: transform !important;
  isolation: isolate !important;
  contain: layout style paint !important;
}

.dark .header-professional {
  background: rgba(17, 24, 39, 0.98) !important;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
}

/* Enhanced Hero Section */
.hero-enhanced {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 30%, #f093fb 60%, #667eea 100%);
  background-size: 400% 400%;
  animation: gradient-shift 8s ease infinite;
  min-height: 100vh;
  display: flex;
  align-items: center;
  position: relative;
  overflow: hidden;
}

.hero-enhanced::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E") repeat;
  animation: float 20s ease-in-out infinite;
}

/* Enhanced Glass Cards */
.glass-card-enhanced {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 24px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.glass-card-enhanced:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
  background: rgba(255, 255, 255, 0.15);
}

.dark .glass-card-enhanced {
  background: rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.dark .glass-card-enhanced:hover {
  background: rgba(0, 0, 0, 0.3);
}

/* Enhanced Buttons */
.btn-enhanced {
  position: relative;
  overflow: hidden;
  border-radius: 16px;
  font-weight: 600;
  padding: 16px 32px;
  transition: all 0.3s ease;
  transform: translateZ(0);
  will-change: transform;
}

.btn-enhanced::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.btn-enhanced:hover::before {
  left: 100%;
}

.btn-enhanced:hover {
  transform: translateY(-2px) scale(1.05);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

.btn-primary-enhanced {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
}

.btn-secondary-enhanced {
  background: rgba(255, 255, 255, 0.9);
  color: #333;
  border: 2px solid rgba(255, 255, 255, 0.3);
}

/* Enhanced Typography */
.heading-enhanced {
  font-family: 'Inter', sans-serif;
  font-weight: 900;
  line-height: 1.1;
  letter-spacing: -0.02em;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
  background-size: 200% 200%;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: gradient-shift 3s ease infinite;
}

.text-enhanced {
  font-family: 'Inter', sans-serif;
  line-height: 1.6;
  color: rgba(255, 255, 255, 0.9);
}

/* Enhanced Animations */
@keyframes gradient-shift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  33% { transform: translateY(-10px) rotate(1deg); }
  66% { transform: translateY(5px) rotate(-1deg); }
}

@keyframes pulse-glow {
  0%, 100% { box-shadow: 0 0 20px rgba(102, 126, 234, 0.3); }
  50% { box-shadow: 0 0 40px rgba(102, 126, 234, 0.6); }
}

/* Enhanced Search Section */
.search-enhanced {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
  backdrop-filter: blur(20px);
  border-radius: 24px;
  padding: 40px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.search-input-enhanced {
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-radius: 16px;
  padding: 16px 20px;
  font-size: 18px;
  color: white;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.search-input-enhanced::placeholder {
  color: rgba(255, 255, 255, 0.7);
}

.search-input-enhanced:focus {
  outline: none;
  border-color: rgba(255, 255, 255, 0.5);
  background: rgba(255, 255, 255, 0.15);
  box-shadow: 0 0 20px rgba(255, 255, 255, 0.2);
}

/* Enhanced Category Cards */
.category-card-enhanced {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  padding: 32px;
  text-align: center;
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.category-card-enhanced::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, transparent 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.category-card-enhanced:hover::before {
  opacity: 1;
}

.category-card-enhanced:hover {
  transform: translateY(-10px) scale(1.05);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
  border-color: rgba(255, 255, 255, 0.4);
}

.category-icon-enhanced {
  font-size: 48px;
  margin-bottom: 20px;
  color: rgba(255, 255, 255, 0.9);
  transition: all 0.3s ease;
}

.category-card-enhanced:hover .category-icon-enhanced {
  transform: scale(1.2) rotate(5deg);
  color: white;
}

/* Enhanced Stats Section */
.stats-enhanced {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 80px 0;
  position: relative;
  overflow: hidden;
}

.stats-enhanced::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3z' fill='%23ffffff' fill-opacity='0.1'/%3E%3C/svg%3E") repeat;
  animation: float 30s ease-in-out infinite;
}

.stat-item-enhanced {
  text-align: center;
  color: white;
  position: relative;
  z-index: 1;
}

.stat-number-enhanced {
  font-size: 4rem;
  font-weight: 900;
  line-height: 1;
  margin-bottom: 8px;
  background: linear-gradient(135deg, #ffffff 0%, #f0f9ff 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.stat-label-enhanced {
  font-size: 1.2rem;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.8);
}

/* Enhanced Responsive Design */
@media (max-width: 768px) {
  .hero-enhanced {
    min-height: 80vh;
    padding: 20px;
  }

  .heading-enhanced {
    font-size: 2.5rem;
  }

  .search-enhanced {
    padding: 24px;
    margin: 20px;
  }

  .category-card-enhanced {
    padding: 24px;
  }

  .stat-number-enhanced {
    font-size: 2.5rem;
  }
}

/* Enhanced Loading States */
.loading-enhanced {
  position: relative;
  overflow: hidden;
}

.loading-enhanced::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  0% { left: -100%; }
  100% { left: 100%; }
}

/* Enhanced Page Transitions */
.page-transition-enhanced {
  animation: pageSlideIn 0.6s ease-out;
}

@keyframes pageSlideIn {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Enhanced Scroll Animations */
.scroll-reveal {
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.6s ease-out;
}

.scroll-reveal.revealed {
  opacity: 1;
  transform: translateY(0);
}

/* Enhanced Interactive Elements */
.interactive-enhanced {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
}

.interactive-enhanced:hover {
  transform: translateY(-4px) scale(1.02);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

/* Enhanced Focus States */
.focus-enhanced:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.5);
  border-color: #3b82f6;
}

/* Enhanced Dark Mode Support */
.dark .hero-enhanced {
  background: linear-gradient(135deg, #1e293b 0%, #334155 30%, #475569 60%, #1e293b 100%);
}

.dark .glass-card-enhanced {
  background: rgba(15, 23, 42, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.05);
}

.dark .search-enhanced {
  background: linear-gradient(135deg, rgba(15, 23, 42, 0.2) 0%, rgba(30, 41, 59, 0.1) 100%);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Enhanced Performance Optimizations */
.gpu-accelerated {
  transform: translateZ(0);
  will-change: transform;
  backface-visibility: hidden;
  perspective: 1000px;
}

/* Enhanced Accessibility */
@media (prefers-reduced-motion: reduce) {
  .hero-enhanced,
  .glass-card-enhanced,
  .btn-enhanced,
  .interactive-enhanced {
    animation: none;
    transition: none;
  }

  .hero-enhanced::before {
    animation: none;
  }
}

/* Enhanced Print Styles */
@media print {
  .hero-enhanced,
  .glass-card-enhanced,
  .btn-enhanced {
    background: white !important;
    color: black !important;
    box-shadow: none !important;
  }
}
