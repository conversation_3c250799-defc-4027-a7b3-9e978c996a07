import { useState, useEffect, useCallback } from '../utils/reactSingleton';

/**
 * Custom hook for performance optimization
 * 
 * @param {Object} options - Configuration options
 * @param {boolean} options.enablePrefetching - Whether to enable prefetching
 * @param {boolean} options.enableLazyLoading - Whether to enable lazy loading
 * @param {boolean} options.enableCaching - Whether to enable caching
 * @param {number} options.cacheDuration - Cache duration in milliseconds
 * @param {Array} options.prefetchPaths - Paths to prefetch
 * @returns {Object} Performance optimization utilities
 */
const usePerformanceOptimization = ({
  enablePrefetching = true,
  enableLazyLoading = true,
  enableCaching = true,
  cacheDuration = 5 * 60 * 1000, // 5 minutes
  prefetchPaths = []
} = {}) => {
  const [cache, setCache] = useState({});
  const [prefetchedPaths, setPrefetchedPaths] = useState([]);
  const [isIdle, setIsIdle] = useState(false);
  
  // Clear expired cache items
  useEffect(() => {
    if (!enableCaching) return;
    
    const interval = setInterval(() => {
      const now = Date.now();
      const newCache = { ...cache };
      let hasChanges = false;
      
      Object.keys(newCache).forEach(key => {
        if (now - newCache[key].timestamp > cacheDuration) {
          delete newCache[key];
          hasChanges = true;
        }
      });
      
      if (hasChanges) {
        setCache(newCache);
      }
    }, 60000); // Check every minute
    
    return () => clearInterval(interval);
  }, [cache, cacheDuration, enableCaching]);
  
  // Detect idle time for prefetching
  useEffect(() => {
    if (!enablePrefetching) return;
    
    let idleTimeout;
    
    const handleUserActivity = () => {
      setIsIdle(false);
      clearTimeout(idleTimeout);
      
      idleTimeout = setTimeout(() => {
        setIsIdle(true);
      }, 3000); // 3 seconds of inactivity
    };
    
    // Events that indicate user activity
    const events = ['mousemove', 'mousedown', 'keypress', 'scroll', 'touchstart'];
    
    events.forEach(event => {
      window.addEventListener(event, handleUserActivity);
    });
    
    // Initial timeout
    idleTimeout = setTimeout(() => {
      setIsIdle(true);
    }, 3000);
    
    return () => {
      events.forEach(event => {
        window.removeEventListener(event, handleUserActivity);
      });
      clearTimeout(idleTimeout);
    };
  }, [enablePrefetching]);
  
  // Prefetch paths when idle
  useEffect(() => {
    if (!enablePrefetching || !isIdle || prefetchPaths.length === 0) return;
    
    const prefetchPath = async (path) => {
      if (prefetchedPaths.includes(path)) return;
      
      try {
        // For JavaScript modules
        if (path.endsWith('.js')) {
          await import(/* @vite-ignore */ path);
        } 
        // For images
        else if (/\.(jpg|jpeg|png|webp|svg)$/.test(path)) {
          const img = new Image();
          img.src = path;
          await new Promise((resolve, reject) => {
            img.onload = resolve;
            img.onerror = reject;
          });
        }
        // For other resources
        else {
          await fetch(path, { method: 'HEAD' });
        }
        
        setPrefetchedPaths(prev => [...prev, path]);
      } catch (error) {
        console.warn(`Failed to prefetch: ${path}`, error);
      }
    };
    
    // Prefetch paths one by one with a delay
    const prefetchNext = (index = 0) => {
      if (index >= prefetchPaths.length) return;
      
      const path = prefetchPaths[index];
      prefetchPath(path).then(() => {
        setTimeout(() => {
          prefetchNext(index + 1);
        }, 500); // 500ms delay between prefetches
      });
    };
    
    prefetchNext();
  }, [enablePrefetching, isIdle, prefetchPaths, prefetchedPaths]);
  
  // Cache data
  const cacheData = useCallback((key, data) => {
    if (!enableCaching) return data;
    
    setCache(prev => ({
      ...prev,
      [key]: {
        data,
        timestamp: Date.now()
      }
    }));
    
    return data;
  }, [enableCaching]);
  
  // Get cached data
  const getCachedData = useCallback((key) => {
    if (!enableCaching || !cache[key]) return null;
    
    const now = Date.now();
    if (now - cache[key].timestamp > cacheDuration) {
      // Cache expired
      const newCache = { ...cache };
      delete newCache[key];
      setCache(newCache);
      return null;
    }
    
    return cache[key].data;
  }, [cache, cacheDuration, enableCaching]);
  
  // Clear cache
  const clearCache = useCallback((key) => {
    if (!key) {
      setCache({});
    } else {
      setCache(prev => {
        const newCache = { ...prev };
        delete newCache[key];
        return newCache;
      });
    }
  }, []);
  
  // Prefetch a specific path
  const prefetch = useCallback((path) => {
    if (!enablePrefetching || prefetchedPaths.includes(path)) return;
    
    const prefetchPath = async () => {
      try {
        // For JavaScript modules
        if (path.endsWith('.js')) {
          await import(/* @vite-ignore */ path);
        } 
        // For images
        else if (/\.(jpg|jpeg|png|webp|svg)$/.test(path)) {
          const img = new Image();
          img.src = path;
          await new Promise((resolve, reject) => {
            img.onload = resolve;
            img.onerror = reject;
          });
        }
        // For other resources
        else {
          await fetch(path, { method: 'HEAD' });
        }
        
        setPrefetchedPaths(prev => [...prev, path]);
      } catch (error) {
        console.warn(`Failed to prefetch: ${path}`, error);
      }
    };
    
    prefetchPath();
  }, [enablePrefetching, prefetchedPaths]);
  
  return {
    cacheData,
    getCachedData,
    clearCache,
    prefetch,
    prefetchedPaths,
    isIdle
  };
};

export default usePerformanceOptimization;
