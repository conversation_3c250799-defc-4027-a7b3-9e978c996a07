import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { FiMail, FiPhone, FiMapPin, FiSend, FiAlertCircle, FiCheckCircle } from 'react-icons/fi';
import PageTransition from '../components/PageTransition';

const Contact = () => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    subject: '',
    message: ''
  });

  const [errors, setErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitStatus, setSubmitStatus] = useState(null);

  // Handle input changes
  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));

    // Clear error when user types
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: null
      }));
    }
  };

  // Validate form
  const validateForm = () => {
    const newErrors = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Name is required';
    }

    if (!formData.email.trim()) {
      newErrors.email = 'Email is required';
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = 'Email is invalid';
    }

    if (!formData.subject.trim()) {
      newErrors.subject = 'Subject is required';
    }

    if (!formData.message.trim()) {
      newErrors.message = 'Message is required';
    } else if (formData.message.length < 10) {
      newErrors.message = 'Message must be at least 10 characters';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);
    setSubmitStatus(null);

    try {
      // In a real app, this would be an API call
      // For now, we'll simulate a successful submission after a delay
      await new Promise(resolve => setTimeout(resolve, 1500));

      // Simulate successful submission
      setSubmitStatus({
        type: 'success',
        message: 'Your message has been sent successfully! We will get back to you soon.'
      });

      // Reset form
      setFormData({
        name: '',
        email: '',
        subject: '',
        message: ''
      });
    } catch (error) {
      setSubmitStatus({
        type: 'error',
        message: 'Failed to send message. Please try again later.'
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="flex flex-col min-h-screen bg-gradient-to-br from-gray-50 via-blue-50 to-purple-50 dark:from-gray-900 dark:via-blue-900 dark:to-purple-900">

      <PageTransition>
        {/* Hero Section */}
        <section className="relative bg-gradient-to-r from-teal-600 via-blue-600 to-indigo-600 text-white py-24 overflow-hidden">
          {/* Animated Background */}
          <div className="absolute inset-0 opacity-20">
            <div className="absolute top-10 left-10 w-40 h-40 bg-white rounded-full blur-3xl animate-float"></div>
            <div className="absolute bottom-10 right-10 w-32 h-32 bg-yellow-300 rounded-full blur-2xl animate-float" style={{animationDelay: '2s'}}></div>
            <div className="absolute top-1/2 left-1/3 w-24 h-24 bg-pink-300 rounded-full blur-xl animate-float" style={{animationDelay: '4s'}}></div>
          </div>

          <div className="container mx-auto px-4 relative z-10 text-center">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
            >
              <span className="inline-block px-4 py-2 bg-white/20 backdrop-blur-sm rounded-full text-white/90 text-sm font-medium mb-6 border border-white/30">
                📞 Liên Hệ
              </span>
              <h1 className="text-5xl md:text-7xl font-black mb-8 leading-tight">
                <span className="bg-gradient-to-r from-white to-blue-200 bg-clip-text text-transparent">
                  Liên Hệ Với Chúng Tôi
                </span>
              </h1>
              <p className="text-2xl md:text-3xl text-white/90 mb-12 max-w-4xl mx-auto leading-relaxed">
                Có câu hỏi hoặc phản hồi? Chúng tôi rất muốn nghe từ bạn
              </p>

              {/* Quick Contact Stats */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mt-12">
                {[
                  { icon: '⚡', label: 'Phản hồi nhanh', desc: 'Trong 24h' },
                  { icon: '🌍', label: 'Hỗ trợ toàn cầu', desc: '24/7' },
                  { icon: '💬', label: 'Đa ngôn ngữ', desc: 'Việt/Anh' }
                ].map((item, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: 0.2 + index * 0.1 }}
                    className="text-center"
                  >
                    <div className="text-4xl mb-2">{item.icon}</div>
                    <div className="text-xl font-bold text-white mb-1">{item.label}</div>
                    <div className="text-white/80">{item.desc}</div>
                  </motion.div>
                ))}
              </div>
            </motion.div>
          </div>
        </section>

        <main className="flex-grow py-16 relative z-10">
          <div className="container mx-auto px-4">
            <div className="max-w-6xl mx-auto">

              <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
                {/* Contact Information */}
                <motion.div
                  initial={{ opacity: 0, x: -30 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.6, delay: 0.2 }}
                  className="lg:col-span-1"
                >
                  <div className="glass-card rounded-3xl shadow-professional-lg p-8 border border-white/20">
                    <div className="flex items-center mb-8">
                      <div className="w-16 h-16 bg-gradient-to-r from-teal-500 to-blue-600 rounded-2xl flex items-center justify-center mr-4">
                        <FiMail className="h-8 w-8 text-white" />
                      </div>
                      <h2 className="text-3xl font-black text-gray-900 dark:text-white">Liên Hệ</h2>
                    </div>

                    <div className="space-y-8">
                      <div className="glass-card p-6 rounded-2xl border border-white/10">
                        <div className="flex items-start">
                          <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl flex items-center justify-center flex-shrink-0">
                            <FiMail className="h-6 w-6 text-white" />
                          </div>
                          <div className="ml-4">
                            <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-2">Email</h3>
                            <p className="text-gray-600 dark:text-gray-300 font-medium"><EMAIL></p>
                            <p className="text-gray-600 dark:text-gray-300 font-medium"><EMAIL></p>
                          </div>
                        </div>
                      </div>

                      <div className="glass-card p-6 rounded-2xl border border-white/10">
                        <div className="flex items-start">
                          <div className="w-12 h-12 bg-gradient-to-r from-green-500 to-teal-600 rounded-xl flex items-center justify-center flex-shrink-0">
                            <FiPhone className="h-6 w-6 text-white" />
                          </div>
                          <div className="ml-4">
                            <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-2">Điện Thoại</h3>
                            <p className="text-gray-600 dark:text-gray-300 font-medium">+84 (123) 456-789</p>
                            <p className="text-gray-500 dark:text-gray-400 text-sm">Thứ 2-6, 8AM-6PM GMT+7</p>
                          </div>
                        </div>
                      </div>

                      <div className="glass-card p-6 rounded-2xl border border-white/10">
                        <div className="flex items-start">
                          <div className="w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-600 rounded-xl flex items-center justify-center flex-shrink-0">
                            <FiMapPin className="h-6 w-6 text-white" />
                          </div>
                          <div className="ml-4">
                            <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-2">Địa Chỉ</h3>
                            <p className="text-gray-600 dark:text-gray-300 font-medium leading-relaxed">
                              123 Nguyễn Huệ<br />
                              Quận 1, TP.HCM<br />
                              Việt Nam
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div className="mt-8">
                      <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Follow Us</h3>
                      <div className="flex space-x-4">
                        {['facebook', 'twitter', 'instagram', 'linkedin'].map((social, index) => (
                          <a
                            key={index}
                            href={`https://${social}.com/3dsketchup`}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="bg-gray-100 dark:bg-gray-700 p-2 rounded-full hover:bg-blue-100 dark:hover:bg-blue-900 transition-colors"
                          >
                            <img
                              src={`https://cdn.jsdelivr.net/npm/simple-icons@v5/icons/${social}.svg`}
                              alt={social}
                              className="h-5 w-5 dark:invert"
                            />
                          </a>
                        ))}
                      </div>
                    </div>
                  </div>
                </motion.div>

                {/* Contact Form */}
                <motion.div
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.5, delay: 0.3 }}
                  className="lg:col-span-2"
                >
                  <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md p-6">
                    <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">Send Us a Message</h2>

                    {submitStatus && (
                      <div className={`mb-6 p-4 rounded-lg ${
                        submitStatus.type === 'success'
                          ? 'bg-green-50 dark:bg-green-900/20 text-green-800 dark:text-green-200'
                          : 'bg-red-50 dark:bg-red-900/20 text-red-800 dark:text-red-200'
                      }`}>
                        <div className="flex items-center">
                          {submitStatus.type === 'success' ? (
                            <FiCheckCircle className="h-5 w-5 mr-2" />
                          ) : (
                            <FiAlertCircle className="h-5 w-5 mr-2" />
                          )}
                          <p>{submitStatus.message}</p>
                        </div>
                      </div>
                    )}

                    <form onSubmit={handleSubmit}>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                        <div>
                          <label htmlFor="name" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                            Full Name
                          </label>
                          <input
                            type="text"
                            id="name"
                            name="name"
                            value={formData.name}
                            onChange={handleChange}
                            className={`w-full px-4 py-2 border rounded-lg focus:ring-2 focus:outline-none dark:bg-gray-700 dark:border-gray-600 dark:text-white ${
                              errors.name
                                ? 'border-red-500 focus:ring-red-500'
                                : 'border-gray-300 focus:ring-blue-500 dark:focus:ring-blue-500'
                            }`}
                            placeholder="John Doe"
                          />
                          {errors.name && (
                            <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.name}</p>
                          )}
                        </div>

                        <div>
                          <label htmlFor="email" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                            Email Address
                          </label>
                          <input
                            type="email"
                            id="email"
                            name="email"
                            value={formData.email}
                            onChange={handleChange}
                            className={`w-full px-4 py-2 border rounded-lg focus:ring-2 focus:outline-none dark:bg-gray-700 dark:border-gray-600 dark:text-white ${
                              errors.email
                                ? 'border-red-500 focus:ring-red-500'
                                : 'border-gray-300 focus:ring-blue-500 dark:focus:ring-blue-500'
                            }`}
                            placeholder="<EMAIL>"
                          />
                          {errors.email && (
                            <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.email}</p>
                          )}
                        </div>
                      </div>

                      <div className="mb-6">
                        <label htmlFor="subject" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                          Subject
                        </label>
                        <input
                          type="text"
                          id="subject"
                          name="subject"
                          value={formData.subject}
                          onChange={handleChange}
                          className={`w-full px-4 py-2 border rounded-lg focus:ring-2 focus:outline-none dark:bg-gray-700 dark:border-gray-600 dark:text-white ${
                            errors.subject
                              ? 'border-red-500 focus:ring-red-500'
                              : 'border-gray-300 focus:ring-blue-500 dark:focus:ring-blue-500'
                          }`}
                          placeholder="How can we help you?"
                        />
                        {errors.subject && (
                          <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.subject}</p>
                        )}
                      </div>

                      <div className="mb-6">
                        <label htmlFor="message" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                          Message
                        </label>
                        <textarea
                          id="message"
                          name="message"
                          value={formData.message}
                          onChange={handleChange}
                          rows="5"
                          className={`w-full px-4 py-2 border rounded-lg focus:ring-2 focus:outline-none dark:bg-gray-700 dark:border-gray-600 dark:text-white ${
                            errors.message
                              ? 'border-red-500 focus:ring-red-500'
                              : 'border-gray-300 focus:ring-blue-500 dark:focus:ring-blue-500'
                          }`}
                          placeholder="Please provide details about your inquiry..."
                        ></textarea>
                        {errors.message && (
                          <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.message}</p>
                        )}
                      </div>

                      <div className="flex justify-end">
                        <button
                          type="submit"
                          disabled={isSubmitting}
                          className="px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg shadow-md transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-70 disabled:cursor-not-allowed flex items-center"
                        >
                          {isSubmitting ? (
                            <>
                              <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                              </svg>
                              Sending...
                            </>
                          ) : (
                            <>
                              <FiSend className="mr-2" />
                              Send Message
                            </>
                          )}
                        </button>
                      </div>
                    </form>
                  </div>
                </motion.div>
              </div>

              {/* Map Section */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.4 }}
                className="mt-12"
              >
                <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md p-6">
                  <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">Our Location</h2>
                  <div className="h-96 rounded-lg overflow-hidden">
                    <iframe
                      src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3153.0910623952574!2d-122.41941548468204!3d37.7749!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x80858085d7f6159f%3A0x1bb5fe4b82f5b9c4!2sSan%20Francisco%2C%20CA%2C%20USA!5e0!3m2!1sen!2s!4v1620160768358!5m2!1sen!2s"
                      width="100%"
                      height="100%"
                      style={{ border: 0 }}
                      allowFullScreen=""
                      loading="lazy"
                      title="3DSKETCHUP.NET Office Location"
                    ></iframe>
                  </div>
                </div>
              </motion.div>
            </div>
          </div>
        </main>
      </PageTransition>

    </div>
  );
};

export default Contact;
