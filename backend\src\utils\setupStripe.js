import Stripe from 'stripe';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Initialize Stripe
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY);

// Subscription plans
const SUBSCRIPTION_PLANS = {
  basic: {
    name: 'Basic Plan',
    description: 'Access to basic features',
    monthlyPrice: 9.99,
    yearlyPrice: 99.99,
    features: {
      downloadLimit: 20,
      accessToPremiumModels: false,
      prioritySupport: false,
      commercialUse: false
    }
  },
  premium: {
    name: 'Premium Plan',
    description: 'Access to premium features',
    monthlyPrice: 19.99,
    yearlyPrice: 199.99,
    features: {
      downloadLimit: 50,
      accessToPremiumModels: true,
      prioritySupport: false,
      commercialUse: false
    }
  },
  professional: {
    name: 'Professional Plan',
    description: 'Access to all features with commercial license',
    monthlyPrice: 29.99,
    yearlyPrice: 299.99,
    features: {
      downloadLimit: 100,
      accessToPremiumModels: true,
      prioritySupport: true,
      commercialUse: true
    }
  }
};

/**
 * Setup Stripe products and prices
 */
export const setupStripe = async () => {
  try {
    console.log('Setting up Stripe products and prices...');

    // Check if Stripe is configured
    if (!process.env.STRIPE_SECRET_KEY) {
      console.error('Stripe secret key is missing. Skipping Stripe setup.');
      return;
    }

    // Create products and prices for each subscription plan
    for (const [planId, plan] of Object.entries(SUBSCRIPTION_PLANS)) {
      // Check if product already exists
      let product;
      try {
        const products = await stripe.products.list({
          active: true,
          limit: 100
        });
        product = products.data.find(p => p.name === plan.name);
      } catch (error) {
        console.error(`Error checking for existing product ${plan.name}:`, error);
      }

      // Create product if it doesn't exist
      if (!product) {
        try {
          product = await stripe.products.create({
            name: plan.name,
            description: plan.description,
            metadata: {
              planId
            }
          });
          console.log(`Created product: ${plan.name}`);
        } catch (error) {
          console.error(`Error creating product ${plan.name}:`, error);
          continue;
        }
      }

      // Check if monthly price already exists
      let monthlyPrice;
      try {
        const prices = await stripe.prices.list({
          product: product.id,
          active: true,
          limit: 100
        });
        monthlyPrice = prices.data.find(p => 
          p.recurring && 
          p.recurring.interval === 'month' && 
          p.unit_amount === Math.round(plan.monthlyPrice * 100)
        );
      } catch (error) {
        console.error(`Error checking for existing monthly price for ${plan.name}:`, error);
      }

      // Create monthly price if it doesn't exist
      if (!monthlyPrice) {
        try {
          monthlyPrice = await stripe.prices.create({
            product: product.id,
            unit_amount: Math.round(plan.monthlyPrice * 100),
            currency: 'usd',
            recurring: {
              interval: 'month'
            },
            metadata: {
              planId,
              interval: 'monthly'
            }
          });
          console.log(`Created monthly price for ${plan.name}: $${plan.monthlyPrice}/month`);
        } catch (error) {
          console.error(`Error creating monthly price for ${plan.name}:`, error);
        }
      }

      // Check if yearly price already exists
      let yearlyPrice;
      try {
        const prices = await stripe.prices.list({
          product: product.id,
          active: true,
          limit: 100
        });
        yearlyPrice = prices.data.find(p => 
          p.recurring && 
          p.recurring.interval === 'year' && 
          p.unit_amount === Math.round(plan.yearlyPrice * 100)
        );
      } catch (error) {
        console.error(`Error checking for existing yearly price for ${plan.name}:`, error);
      }

      // Create yearly price if it doesn't exist
      if (!yearlyPrice) {
        try {
          yearlyPrice = await stripe.prices.create({
            product: product.id,
            unit_amount: Math.round(plan.yearlyPrice * 100),
            currency: 'usd',
            recurring: {
              interval: 'year'
            },
            metadata: {
              planId,
              interval: 'yearly'
            }
          });
          console.log(`Created yearly price for ${plan.name}: $${plan.yearlyPrice}/year`);
        } catch (error) {
          console.error(`Error creating yearly price for ${plan.name}:`, error);
        }
      }

      // Update environment variables with price IDs
      if (monthlyPrice) {
        const envVarName = `STRIPE_${planId.toUpperCase()}_PRICE_ID`;
        console.log(`${envVarName}=${monthlyPrice.id}`);
        process.env[envVarName] = monthlyPrice.id;
      }
    }

    console.log('Stripe setup completed successfully.');
  } catch (error) {
    console.error('Error setting up Stripe:', error);
  }
};

export default setupStripe;
