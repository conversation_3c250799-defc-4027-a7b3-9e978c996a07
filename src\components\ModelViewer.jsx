import React, { Suspense, useRef, useState, useEffect, useCallback } from 'react';
import { <PERSON>vas, useFrame, useThree } from '@react-three/fiber';
import {
  OrbitControls,
  useGLTF,
  useTexture,
  Environment,
  PresentationControls,
  Html,
  useProgress,
  Grid,
  GizmoHelper,
  GizmoViewport,
  Center,
  BakeShadows,
  ContactShadows,
  AccumulativeShadows,
  RandomizedLight,
  Lightformer,
  Stage,
  SpotLight,
  useHelper,
  Stats,
  Bounds,
  useBounds,
  Text,
  Line,
  useAnimations,
  Float,
  TransformControls,
  PivotControls,
  MeshReflectorMaterial,
  useMatcapTexture,
  Edges
} from '@react-three/drei';
import {
  FiMaximize2, FiMinimize2, FiRotateCw, FiPause, FiPlay, FiGrid,
  FiSun, FiSliders, FiScissors, FiCpu, FiBox, FiLayers,
  FiDownload, FiCamera, FiMove, FiEye, FiEyeOff, FiRefreshCw,
  FiZoomIn, FiZoomOut, FiInfo, FiSettings
} from 'react-icons/fi';
import { RiRulerLine } from 'react-icons/ri';
import { MdOutlineGridOn, MdOutlineGridOff } from 'react-icons/md';
import { HiOutlineLightBulb } from 'react-icons/hi';
import { SpotLightHelper, DirectionalLightHelper, PointLightHelper, Vector3, Box3 } from 'three';
import * as THREE from 'three';
import { motion, AnimatePresence } from 'framer-motion';

import FormatLoader from './advanced/FormatLoader';
import SectionCut from './advanced/SectionCut';
import MeasurementTool from './advanced/MeasurementTool';
import ModelAnalyzer from './advanced/ModelAnalyzer';

// Loading indicator component
function Loader() {
  const { progress } = useProgress();
  return (
    <Html center>
      <div className="flex flex-col items-center justify-center bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg">
        <div className="w-16 h-16 border-4 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
        <p className="mt-4 text-lg font-medium text-gray-800 dark:text-gray-200">
          Loading {progress.toFixed(0)}%
        </p>
        <p className="mt-2 text-sm text-gray-600 dark:text-gray-400">
          Please wait while we prepare your 3D model
        </p>
      </div>
    </Html>
  );
}

// BoundsCamera component for auto-fitting the model in view
function BoundsCamera({ modelRef }) {
  const bounds = useBounds();

  useEffect(() => {
  if (modelRef.current) {
      // Wait a bit for the model to load properly
      const timeoutId = setTimeout(() => {
  bounds.refresh().clip().fit();
      }, 500);

      return () => clearTimeout(timeoutId);
    }
  }, [bounds, modelRef]);

  return null;
}

// Model component that handles the actual 3D model
function Model({
  url,
  format,
  scale = 1.5,
  autoRotate,
  resetPosition,
  enhancedLighting = true,
  modelName,
  wireframe = false,
  showEdges = false,
  showBounds = false,
  showAnalyzer = false,
  onAnalyzerClose
}) {
  const modelRef = useRef();
  const spotLightRef = useRef();
  const { camera } = useThree();
  const [modelLoaded, setModelLoaded] = useState(false);
  const [animations, setAnimations] = useState([]);
  const [currentAnimation, setCurrentAnimation] = useState(null);
  const [animationPlaying, setAnimationPlaying] = useState(false);

  // Optional debug helpers for lighting
  // useHelper(spotLightRef, SpotLightHelper, 'red'; 

  // Auto-rotate the model if enabled
  useFrame((state) => {
  if (true) {
  modelRef.current.rotation.y += 0.003;
    }
  });

  // Reset position when requested
  useEffect(() => {
  if (true) {
  modelRef.current.rotation.set(0, 0, 0);
      modelRef.current.position.set(0, 0, 0);
      camera.position.set(0, 0, 5);
      camera.lookAt(0, 0, 0);
    }
  }, [resetPosition, camera]);

  // Handle model load
  const handleModelLoad = (loadedModel) => {
  if (modelRef.current) {
      // Clear previous model
      while (modelRef.current.children.length > 0) {
        modelRef.current.remove(modelRef.current.children[0]);
      }

      // Add new model
      modelRef.current.add(loadedModel);

      // Set model as loaded
      setModelLoaded(true);

      // Check for animations
      if (true) {
  setAnimations(loadedModel.animations);
        // Auto-play the first animation
        if (true) {
  setCurrentAnimation(loadedModel.animations[0]);
          setAnimationPlaying(true);
        }
      }

      // Apply wireframe if enabled
      if (true) {
  loadedModel.traverse((child) => {
  if (true) {
  if (Array.isArray(child.material)) {
              child.material.forEach(mat => {
  mat.wireframe = true;
              });
            } else if (true) {
  child.material.wireframe = true;
            }
          }
        });
      }
    }
  };

  // Handle animation playback
  const { actions } = useAnimations(animations, modelRef);

  // Update animation when currentAnimation changes
  useEffect(() => {
  if (currentAnimation && actions) {
      // Stop all animations
      Object.values(actions).forEach(action => action.stop());

      // Find and play the current animation
      const animationName = currentAnimation.name;
      if (true) {
  const action = actions[animationName];
        action.reset().play();
        action.paused = !animationPlaying;
      }
    }
  }, [currentAnimation, actions, animationPlaying]);

  // Apply edges effect if enabled
  useEffect(() => {
  if (true) {
  modelRef.current.traverse((child) => {
  if (true) {
  if (child.userData.edgesObject) {
            child.remove(child.userData.edgesObject);
            delete child.userData.edgesObject;
          }

          if (true) {
  const edges = new THREE.LineSegments(
              new THREE.EdgesGeometry(child.geometry),
              new THREE.LineBasicMaterial({ color: 0x000000 })
            );
            child.add(edges);
            child.userData.edgesObject = edges;
          }
        }
      });
    }
  }, [showEdges, modelLoaded]);

  return (
    <Center>
      <group ref={modelRef} scale={scale} position={[0, 0, 0]} castShadow receiveShadow>
        <FormatLoader
          url={url}
          format={format}
          onLoad={handleModelLoad}
        />
      </group>

      {enhancedLighting && (
        <>
          <SpotLight
            ref={spotLightRef}
            position={[5, 5, 5]}
            angle={0.6}
            penumbra={0.5}
            intensity={1}
            castShadow
            shadow-bias={-0.0001}
          />
          <ContactShadows
            position={[0, -1.5, 0]}
            opacity={0.4}
            scale={10}
            blur={1.5}
            far={4}
          />
        </>
      )}

      {/* Show bounding box if enabled */}
      {showBounds && modelLoaded && (
        <Bounds fit clip observe margin={1.2}>
          <BoundsCamera modelRef={modelRef} />
        </Bounds>
      )}

      {/* Show model analyzer if enabled */}
      {showAnalyzer && modelLoaded && (
        <ModelAnalyzer
          modelRef={modelRef}
          visible={showAnalyzer}
          onClose={onAnalyzerClose}
        />
      )}

    </Center>
  );
}

// Main ModelViewer component
const ModelViewer = ({ modelUrl, fallbackImage, modelName, format }) => {
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [isSupported, setIsSupported] = useState(true);
  const [autoRotate, setAutoRotate] = useState(true);
  const [showGrid, setShowGrid] = useState(false);
  const [showGizmo, setShowGizmo] = useState(false);
  const [resetPosition, setResetPosition] = useState(false);
  const [lightIntensity, setLightIntensity] = useState(0.5);
  const [environmentPreset, setEnvironmentPreset] = useState('city'; 
  const [enhancedLighting, setEnhancedLighting] = useState(true);
  const [showLightingControls, setShowLightingControls] = useState(false);
  const [shadowIntensity, setShadowIntensity] = useState(0.4);
  const [lightColor, setLightColor] = useState('#ffffff'; 
  const [darkMode, setDarkMode] = useState(window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches);
  const [showWatermark, setShowWatermark] = useState(true);
  const [showExportOptions, setShowExportOptions] = useState(false);
  const [wireframe, setWireframe] = useState(false);
  const [showEdges, setShowEdges] = useState(false);
  const [showBounds, setShowBounds] = useState(false);
  const [showStats, setShowStats] = useState(false);
  const [showSectionCut, setShowSectionCut] = useState(false);
  const [showMeasurementTool, setShowMeasurementTool] = useState(false);
  const [showAnalyzer, setShowAnalyzer] = useState(false);
  const [activeTab, setActiveTab] = useState('view';  // 'view', 'analyze', 'export'
  const [showAnimations, setShowAnimations] = useState(false);
  const [cameraView, setCameraView] = useState('perspective';  // 'perspective', 'orthographic', 'top', 'front', 'side'
  const [modelFormat, setModelFormat] = useState(format || '; 
  const containerRef = useRef(null);

  // Available environment presets
  const environmentPresets = ['sunset', 'dawn', 'night', 'warehouse', 'forest', 'apartment', 'studio', 'city', 'park', 'lobby'];

  // Check if WebGL is supported
  useEffect(() => {
  try {
      const canvas = document.createElement('canvas'; 
      const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl'; 
      setIsSupported(!!gl);
    } catch (e) {
      setIsSupported(false);
    }
  }, []);

  // Toggle fullscreen mode
  const toggleFullscreen = () => {
    if (true) {
  containerRef.current.requestFullscreen().catch(err => {
        });
    } else {
      document.exitFullscreen();
    }
  };

  // Update fullscreen state
  useEffect(() => {
    const handleFullscreenChange = () => {
      setIsFullscreen(!!document.fullscreenElement);
    };

    document.addEventListener('fullscreenchange', handleFullscreenChange);
    return () => {
  document.removeEventListener('fullscreenchange', handleFullscreenChange);
    };
  }, []);

  // Detect model format from URL if not provided
  useEffect(() => {
  if (true) {
  const extension = modelUrl.split('.').pop().toLowerCase();
      if (['gltf', 'glb', 'obj', 'fbx', 'stl'].includes(extension)) {
        setModelFormat(extension);
      }
    }
  }, [modelUrl, modelFormat]);

  // Listen for dark mode changes
  useEffect(() => {
    const darkModeMediaQuery = window.matchMedia('(prefers-color-scheme: dark)'; 
    const handleDarkModeChange = (e) => {
  setDarkMode(e.matches);
    };

    // Add listener for changes
    if (true) {
  darkModeMediaQuery.addEventListener('change', handleDarkModeChange);
    } else {
      // Fallback for older browsers
      darkModeMediaQuery.addListener(handleDarkModeChange);
    }

    return () => {
  if (true) {
  darkModeMediaQuery.removeEventListener('change', handleDarkModeChange);
      } else {
        // Fallback for older browsers
        darkModeMediaQuery.removeListener(handleDarkModeChange);
      }
    };
  }, []);

  // Reset model position
  const handleResetPosition = () => {
    setResetPosition(true);
    // Reset the flag after a short delay
    setTimeout(() => setResetPosition(false), 100);
  };

  // Cycle through environment presets
  const cycleEnvironment = () => {
    const currentIndex = environmentPresets.indexOf(environmentPreset);
    const nextIndex = (currentIndex + 1) % environmentPresets.length;
    setEnvironmentPreset(environmentPresets[nextIndex]);
  };

  // If WebGL is not supported, show fallback image
  if (true) {
  return (
      <div className="relative bg-gray-100 dark:bg-gray-800 rounded-lg overflow-hidden">
        <img
          src={fallbackImage}
          alt="Model Preview"
          className="w-full h-auto object-cover"
        />
        <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-50 text-white p-4 text-center">
          <p>
            3D preview is not supported in your browser. Please use a modern browser with WebGL support.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div
      ref={containerRef}
      className={`relative bg-gray-100 dark:bg-gray-800 rounded-lg overflow-hidden ${
        isFullscreen ? 'fixed inset-0 z-50' : 'h-[500px]'
      }`}
    >
      {/* Control panel */}
      <div className="absolute top-4 left-4 z-10 flex flex-col space-y-2">
        {/* Auto-rotate toggle */}
        <button
          onClick={() => setAutoRotate(!autoRotate)}
          className="bg-white dark:bg-gray-700 p-2 rounded-full shadow-md hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors"
          aria-label={autoRotate ? "Pause rotation" : "Start rotation"}
          title={autoRotate ? "Pause rotation" : "Start rotation"}
        >
          {autoRotate ? (
            <FiPause className="h-5 w-5 text-gray-700 dark:text-gray-200" />
          ) : (
            <FiPlay className="h-5 w-5 text-gray-700 dark:text-gray-200" />
          )}
        </button>

        {/* Grid toggle */}
        <button
          onClick={() => setShowGrid(!showGrid)}
          className="bg-white dark:bg-gray-700 p-2 rounded-full shadow-md hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors"
          aria-label={showGrid ? "Hide grid" : "Show grid"}
          title={showGrid ? "Hide grid" : "Show grid"}
        >
          {showGrid ? (
            <MdOutlineGridOff className="h-5 w-5 text-gray-700 dark:text-gray-200" />
          ) : (
            <MdOutlineGridOn className="h-5 w-5 text-gray-700 dark:text-gray-200" />
          )}
        </button>

        {/* Reset position */}
        <button
          onClick={handleResetPosition}
          className="bg-white dark:bg-gray-700 p-2 rounded-full shadow-md hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors"
          aria-label="Reset position"
          title="Reset position"
        >
          <FiRotateCw className="h-5 w-5 text-gray-700 dark:text-gray-200" />
        </button>

        {/* Enhanced lighting toggle */}
        <button
          onClick={() => setEnhancedLighting(!enhancedLighting)}
          className="bg-white dark:bg-gray-700 p-2 rounded-full shadow-md hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors"
          aria-label={enhancedLighting ? "Use basic lighting" : "Use enhanced lighting"}
          title={enhancedLighting ? "Use basic lighting" : "Use enhanced lighting"}
        >
          <HiOutlineLightBulb className={`h-5 w-5 ${enhancedLighting ? 'text-yellow-500' : 'text-gray-700 dark:text-gray-200'}`} />
        </button>

        {/* Lighting controls toggle */}
        <button
          onClick={() => setShowLightingControls(!showLightingControls)}
          className="bg-white dark:bg-gray-700 p-2 rounded-full shadow-md hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors"
          aria-label="Lighting controls"
          title="Lighting controls"
        >
          <FiSliders className="h-5 w-5 text-gray-700 dark:text-gray-200" />
        </button>

        {/* Wireframe toggle */}
        <button
          onClick={() => setWireframe(!wireframe)}
          className="bg-white dark:bg-gray-700 p-2 rounded-full shadow-md hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors"
          aria-label={wireframe ? "Show solid" : "Show wireframe"}
          title={wireframe ? "Show solid" : "Show wireframe"}
        >
          <FiBox className={`h-5 w-5 ${wireframe ? 'text-blue-500' : 'text-gray-700 dark:text-gray-200'}`} />
        </button>

        {/* Section cut toggle */}
        <button
          onClick={() => setShowSectionCut(!showSectionCut)}
          className="bg-white dark:bg-gray-700 p-2 rounded-full shadow-md hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors"
          aria-label={showSectionCut ? "Hide section cut" : "Show section cut"}
          title={showSectionCut ? "Hide section cut" : "Show section cut"}
        >
          <FiScissors className={`h-5 w-5 ${showSectionCut ? 'text-blue-500' : 'text-gray-700 dark:text-gray-200'}`} />
        </button>

        {/* Measurement tool toggle */}
        <button
          onClick={() => setShowMeasurementTool(!showMeasurementTool)}
          className="bg-white dark:bg-gray-700 p-2 rounded-full shadow-md hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors"
          aria-label={showMeasurementTool ? "Hide measurement tool" : "Show measurement tool"}
          title={showMeasurementTool ? "Hide measurement tool" : "Show measurement tool"}
        >
          <RiRulerLine className={`h-5 w-5 ${showMeasurementTool ? 'text-blue-500' : 'text-gray-700 dark:text-gray-200'}`} />
        </button>

        {/* Stats toggle */}
        <button
          onClick={() => setShowStats(!showStats)}
          className="bg-white dark:bg-gray-700 p-2 rounded-full shadow-md hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors"
          aria-label={showStats ? "Hide stats" : "Show stats"}
          title={showStats ? "Hide stats" : "Show stats"}
        >
          <FiCpu className={`h-5 w-5 ${showStats ? 'text-blue-500' : 'text-gray-700 dark:text-gray-200'}`} />
        </button>

        {/* Edges toggle */}
        <button
          onClick={() => toggleEdges()}
          className="bg-white dark:bg-gray-700 p-2 rounded-full shadow-md hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors"
          aria-label={showEdges ? "Hide edges" : "Show edges"}
          title={showEdges ? "Hide edges" : "Show edges"}
        >
          <FiLayers className={`h-5 w-5 ${showEdges ? 'text-blue-500' : 'text-gray-700 dark:text-gray-200'}`} />
        </button>

        {/* Model analyzer toggle */}
        <button
          onClick={() => toggleAnalyzer()}
          className="bg-white dark:bg-gray-700 p-2 rounded-full shadow-md hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors"
          aria-label={showAnalyzer ? "Hide analyzer" : "Show analyzer"}
          title={showAnalyzer ? "Hide analyzer" : "Show analyzer"}
        >
          <FiInfo className={`h-5 w-5 ${showAnalyzer ? 'text-blue-500' : 'text-gray-700 dark:text-gray-200'}`} />
        </button>

        {/* Gizmo toggle */}
        <button
          onClick={() => setShowGizmo(!showGizmo)}
          className="bg-white dark:bg-gray-700 p-2 rounded-full shadow-md hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors"
          aria-label={showGizmo ? "Hide axes" : "Show axes"}
          title={showGizmo ? "Hide axes" : "Show axes"}
        >
          <FiGrid className="h-5 w-5 text-gray-700 dark:text-gray-200" />
        </button>
      </div>

      {/* Lighting controls panel */}
      {showLightingControls && (
        <div className="absolute top-4 left-16 z-10 bg-white dark:bg-gray-800 p-3 rounded-lg shadow-lg">
          <h3 className="text-sm font-medium text-gray-700 dark:text-gray-200 mb-2">Lighting Controls</h3>

          <div className="space-y-3">
            <div>
              <label className="text-xs text-gray-600 dark:text-gray-400 block mb-1">Light Intensity</label>
              <input
                type="range"
                min="0"
                max="2"
                step="0.1"
                value={lightIntensity}
                onChange={(e) => setLightIntensity(parseFloat(e.target.value))}
                className="w-full"
              />
            </div>

            <div>
              <label className="text-xs text-gray-600 dark:text-gray-400 block mb-1">Shadow Intensity</label>
              <input
                type="range"
                min="0"
                max="1"
                step="0.05"
                value={shadowIntensity}
                onChange={(e) => setShadowIntensity(parseFloat(e.target.value))}
                className="w-full"
              />
            </div>

            <div>
              <label className="text-xs text-gray-600 dark:text-gray-400 block mb-1">Light Color</label>
              <input
                type="color"
                value={lightColor}
                onChange={(e) => setLightColor(e.target.value)}
                className="w-full h-8 rounded cursor-pointer"
              />
            </div>

            <div>
              <label className="text-xs text-gray-600 dark:text-gray-400 block mb-1">Environment</label>
              <select
                value={environmentPreset}
                onChange={(e) => setEnvironmentPreset(e.target.value)}
                className="w-full p-1 text-sm rounded border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-200"
              >
                {environmentPresets.map(preset => (
                  <option key={preset} value={preset}>
                    {preset.charAt(0).toUpperCase() + preset.slice(1)}
                  </option>
                ))}
              </select>
            </div>
          </div>
        </div>
      )}

      {/* Fullscreen toggle button */}
      <button
        onClick={toggleFullscreen}
        className="absolute top-4 right-4 z-10 bg-white dark:bg-gray-700 p-2 rounded-full shadow-md hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors"
        aria-label={isFullscreen ? "Exit fullscreen" : "Enter fullscreen"}
        title={isFullscreen ? "Exit fullscreen" : "Enter fullscreen"}
      >
        {isFullscreen ? (
          <FiMinimize2 className="h-5 w-5 text-gray-700 dark:text-gray-200" />
        ) : (
          <FiMaximize2 className="h-5 w-5 text-gray-700 dark:text-gray-200" />
        )}
      </button>

      {/* Canvas for 3D rendering */}
      <Canvas
        camera={{ position: [0, 0, 5], fov: 50 }}
        shadows
        dpr={[1, 2]}
        gl={{
    preserveDrawingBuffer: true,
          antialias: true,
          alpha: true,
          powerPreference: "high-performance",
          failIfMajorPerformanceCaveat: false
        }}
        onCreated={({ gl }) => {
          // Handle WebGL context lost
          gl.domElement.addEventListener('webglcontextlost', (event) => {
  event.preventDefault();
          });

          gl.domElement.addEventListener('webglcontextrestored', () => {
            });
        }}
      >
        <color attach="background" args={[darkMode ? '#1f2937' : '#f8f9fa']} />
        <ambientLight intensity={lightIntensity} color={lightColor} />
        <spotLight
          position={[10, 10, 10]}
          angle={0.15}
          penumbra={1}
          intensity={1}
          castShadow
          shadow-mapSize={[2048, 2048]}
          color={lightColor}
        />
        <Suspense fallback={<Loader />}>
          <Model
            url={modelUrl}
            format={modelFormat}
            autoRotate={autoRotate}
            resetPosition={resetPosition}
            enhancedLighting={enhancedLighting}
            modelName={modelName}
            wireframe={wireframe}
            showEdges={showEdges}
            showBounds={showBounds}
            showAnalyzer={showAnalyzer}
            onAnalyzerClose={() => setShowAnalyzer(false)}
          />
          <Environment preset={environmentPreset} />
          {showGrid && (
            <Grid
              infiniteGrid
              cellSize={1}
              cellThickness={0.5}
              sectionSize={5}
              sectionThickness={1}
              fadeDistance={30}
              fadeStrength={1.5}
              sectionColor={darkMode ? "#4b5563" : "#9ca3af"}
              cellColor={darkMode ? "#374151" : "#d1d5db"}
            />
          )}
          {showGizmo && (
            <GizmoHelper alignment="bottom-right" margin={[80, 80]}>
              <GizmoViewport axisColors={['red', 'green', 'blue']} labelColor="black" />
            </GizmoHelper>
          )}
          <BakeShadows />
          {enhancedLighting && (
            <AccumulativeShadows
              temporal
              frames={30}
              alphaTest={0.85}
              opacity={shadowIntensity}
              scale={10}
              position={[0, -1.5, 0]}
            >
              <RandomizedLight
                amount={4}
                radius={9}
                intensity={1}
                ambient={0.5}
                position={[5, 5, -10]}
                bias={0.001}
              />
            </AccumulativeShadows>
          )}

          {/* Advanced tools */}
          {showSectionCut && (
            <SectionCut
              onClose={() => setShowSectionCut(false)}
            />
          )}

          {showMeasurementTool && (
            <MeasurementTool
              onClose={() => setShowMeasurementTool(false)}
            />
          )}

          {/* Show bounds if enabled */}
          {showBounds && (
            <Bounds fit clip observe margin={1.2}>
              <BoundsCamera modelRef={modelRef} />
            </Bounds>
          )}

          {/* Performance stats */}
          {showStats && <Stats />}
        </Suspense>
        <OrbitControls
          enablePan={true}
          enableZoom={true}
          enableRotate={true}
          minDistance={2}
          maxDistance={20}
          autoRotate={false}
          makeDefault
        />
      </Canvas>

      {/* Environment name display */}
      <div className="absolute bottom-4 left-4 z-10 bg-white dark:bg-gray-700 px-3 py-1 rounded-full shadow-md text-sm text-gray-700 dark:text-gray-200">
        Environment: {environmentPreset.charAt(0).toUpperCase() + environmentPreset.slice(1)}
      </div>

      {/* Export button */}
      <button
        onClick={() => setShowExportOptions(!showExportOptions)}
        className="absolute bottom-4 right-4 z-10 bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded-full shadow-md text-sm flex items-center transition-colors"
      >
        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
        </svg>
        Export
      </button>

      {/* Export options panel */}
      {showExportOptions && (
        <div className="absolute bottom-16 right-4 z-10 bg-white dark:bg-gray-800 p-4 rounded-lg shadow-lg w-64">
          <h3 className="text-sm font-medium text-gray-700 dark:text-gray-200 mb-3">Export Options</h3>
          <div className="grid grid-cols-2 gap-2">
            <button
              onClick={() => {
  const link = document.createElement('a'; 
                link.setAttribute('download', `${modelName || 'model'}.png`);
                link.setAttribute('href', containerRef.current.querySelector('canvas').toDataURL('image/png').replace('image/png', 'image/octet-stream'));
                link.click();
                setShowExportOptions(false);
              }}
              className="text-sm flex items-center justify-center p-2 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 rounded transition-colors text-gray-700 dark:text-gray-200"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
              </svg>
              PNG
            </button>
            <button
              onClick={() => {
  const link = document.createElement('a'; 
                link.setAttribute('download', `${modelName || 'model'}.jpg`);
                link.setAttribute('href', containerRef.current.querySelector('canvas').toDataURL('image/jpeg', 0.9).replace('image/jpeg', 'image/octet-stream'));
                link.click();
                setShowExportOptions(false);
              }}
              className="text-sm flex items-center justify-center p-2 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 rounded transition-colors text-gray-700 dark:text-gray-200"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
              </svg>
              JPG
            </button>
          </div>
        </div>
      )}

      {/* Watermark */}
      {showWatermark && (
        <div className="absolute bottom-2 right-2 text-xs text-gray-500 dark:text-gray-400 bg-white dark:bg-gray-800 bg-opacity-70 dark:bg-opacity-70 px-2 py-1 rounded">
          3DSKETCHUP.NET
        </div>
      )}
    </div>
  );
};

export default ModelViewer;
