import mongoose from 'mongoose';

const ModelSchema = new mongoose.Schema({
  title: {
    type: String,
    required: [true, 'Please add a title'],
    trim: true,
    maxlength: [100, 'Title cannot be more than 100 characters']
  },
  description: {
    type: String,
    required: [true, 'Please add a description'],
    maxlength: [1000, 'Description cannot be more than 1000 characters']
  },
  category: {
    type: String,
    required: [true, 'Please add a category'],
    enum: [
      'Residential',
      'Commercial',
      'Exterior',
      'Landscape/Garden',
      'Furniture',
      'Flower/Shrub/Bush',
      'Other'
    ]
  },
  subcategory: {
    type: String,
    trim: true
  },
  format: {
    type: String,
    required: [true, 'Please add a format'],
    enum: [
      'Sketchup 2020',
      'Sketchup 2021',
      'Sketchup 2022',
      'Sketchup 2023',
      '3ds Max 2020',
      '3ds Max 2021',
      '3ds Max 2022',
      '3ds Max 2023',
      'Blender',
      'FBX',
      'OBJ',
      'Other'
    ]
  },
  year: {
    type: String,
    trim: true
  },
  imageUrl: {
    type: String,
    required: [true, 'Please add an image URL']
  },
  previewImages: [{
    url: {
      type: String,
      required: true
    },
    filename: {
      type: String,
      required: true
    },
    size: {
      type: Number,
      required: true
    },
    mimetype: {
      type: String,
      required: true
    },
    uploadedAt: {
      type: Date,
      default: Date.now
    },
    isMain: {
      type: Boolean,
      default: false
    }
  }],
  fileUrl: {
    type: String,
    required: [true, 'Please add a file URL']
  },
  backupLinks: [{
    type: String,
    trim: true
  }],
  modelUrl: {
    type: String,
    // URL to the 3D model preview file (GLTF/GLB format)
  },
  fileSize: {
    type: Number,
    required: [true, 'Please add a file size']
  },
  fileFormat: {
    type: String,
    enum: ['skp', 'max', 'blend', 'fbx', 'obj', 'gltf', 'glb', 'other'],
    required: [true, 'Please specify the file format']
  },
  polygonCount: {
    type: Number,
    // Number of polygons in the 3D model
  },
  textured: {
    type: Boolean,
    default: false
  },
  rigged: {
    type: Boolean,
    default: false
  },
  animated: {
    type: Boolean,
    default: false
  },
  dimensions: {
    width: Number,
    height: Number,
    depth: Number,
    unit: {
      type: String,
      enum: ['mm', 'cm', 'm', 'in', 'ft'],
      default: 'm'
    }
  },
  tags: {
    type: [String],
    default: []
  },
  downloads: {
    type: Number,
    default: 0
  },
  views: {
    type: Number,
    default: 0
  },
  rating: {
    type: Number,
    min: [1, 'Rating must be at least 1'],
    max: [5, 'Rating cannot be more than 5']
  },
  reviews: [
    {
      user: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
        required: true
      },
      rating: {
        type: Number,
        required: true,
        min: 1,
        max: 5
      },
      comment: {
        type: String,
        required: true
      },
      createdAt: {
        type: Date,
        default: Date.now
      }
    }
  ],
  isPremium: {
    type: Boolean,
    default: false
  },
  source: {
    type: String,
    default: 'upload',
    enum: ['upload', '3d-warehouse', 'sketchucation', 'extensions', 'scraped']
  },
  warehouseInfo: {
    originalUrl: String,
    warehouseId: String,
    author: String,
    scrapedAt: Date
  },
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  // Version conversion support
  sketchupVersion: {
    type: String,
    // Extract version from format field or detect from file
    default: function() {
      if (this.format && this.format.includes('Sketchup')) {
        return this.format.replace('Sketchup ', '');
      }
      return null;
    }
  },
  availableVersions: [{
    version: String, // e.g., "2020", "2021", "2022", "2023"
    fileUrl: String, // URL to the converted file
    fileSize: Number, // Size of converted file
    convertedAt: {
      type: Date,
      default: Date.now
    },
    conversionStatus: {
      type: String,
      enum: ['pending', 'processing', 'completed', 'failed'],
      default: 'completed'
    }
  }],
  conversionHistory: [{
    fromVersion: String,
    toVersion: String,
    requestedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    status: {
      type: String,
      enum: ['pending', 'processing', 'completed', 'failed'],
      default: 'pending'
    },
    fileUrl: String,
    fileSize: Number,
    errorMessage: String,
    createdAt: {
      type: Date,
      default: Date.now
    },
    completedAt: Date
  }],
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

// Create index for search
ModelSchema.index({
  title: 'text',
  description: 'text',
  category: 'text',
  subcategory: 'text',
  tags: 'text'
});

// Update the updatedAt field before saving
ModelSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  next();
});

// Method to check if version conversion is available
ModelSchema.methods.hasVersionConversion = function(targetVersion) {
  return this.availableVersions.some(v => v.version === targetVersion && v.conversionStatus === 'completed');
};

// Method to get available conversion versions
ModelSchema.methods.getAvailableVersions = function() {
  return this.availableVersions
    .filter(v => v.conversionStatus === 'completed')
    .map(v => v.version)
    .sort();
};

// Method to add conversion request
ModelSchema.methods.addConversionRequest = function(fromVersion, toVersion, userId) {
  this.conversionHistory.push({
    fromVersion,
    toVersion,
    requestedBy: userId,
    status: 'pending'
  });
  return this.save();
};

// Method to update conversion status
ModelSchema.methods.updateConversionStatus = function(conversionId, status, fileUrl = null, fileSize = null, errorMessage = null) {
  const conversion = this.conversionHistory.id(conversionId);
  if (conversion) {
    conversion.status = status;
    if (fileUrl) conversion.fileUrl = fileUrl;
    if (fileSize) conversion.fileSize = fileSize;
    if (errorMessage) conversion.errorMessage = errorMessage;
    if (status === 'completed') conversion.completedAt = new Date();

    // If conversion completed successfully, add to available versions
    if (status === 'completed' && fileUrl) {
      const existingVersion = this.availableVersions.find(v => v.version === conversion.toVersion);
      if (!existingVersion) {
        this.availableVersions.push({
          version: conversion.toVersion,
          fileUrl,
          fileSize,
          conversionStatus: 'completed'
        });
      }
    }
  }
  return this.save();
};

export default mongoose.model('Model', ModelSchema);
