/**
 * <PERSON><PERSON><PERSON> to fix all syntax errors caused by the optimization script
 * This will fix common patterns like broken strings, imports, and destructuring
 */

const fs = require('fs');
const path = require('path');

// Common syntax error patterns and their fixes
const syntaxFixes = [
  // Fix broken strings - more comprehensive
  { pattern: /'''/g, replacement: "''" },
  { pattern: /'';/g, replacement: "';" },
  { pattern: /useState\('([^']*)''\)/g, replacement: "useState('$1')" },
  { pattern: /useState\("([^"]*)"\)/g, replacement: 'useState("$1")' },
  { pattern: /localStorage\.getItem\('([^']*)''\)/g, replacement: "localStorage.getItem('$1')" },

  // Fix missing closing parentheses and quotes
  { pattern: /useState\(';\s*$/gm, replacement: "useState('');" },
  { pattern: /useState\(";\s*$/gm, replacement: 'useState("");' },
  { pattern: /localStorage\.getItem\('([^']*)';\s*$/gm, replacement: "localStorage.getItem('$1');" },
  { pattern: /throw new Error\('([^']*)';\s*$/gm, replacement: "throw new Error('$1');" },
  { pattern: /toast\.error\('([^']*)';\s*$/gm, replacement: "toast.error('$1');" },
  { pattern: /setError\('([^']*)';\s*$/gm, replacement: "setError('$1');" },
  { pattern: /img\.removeAttribute\('([^']*)';\s*$/gm, replacement: "img.removeAttribute('$1');" },
  { pattern: /img\.classList\.add\('([^']*)';\s*$/gm, replacement: "img.classList.add('$1');" },

  // Fix broken imports - more comprehensive
  { pattern: /import \{ \/\* content \*\/ \};/g, replacement: 'import {' },
  { pattern: /import \{ \/\* content \*\/ \};\s*\n\s*([A-Za-z])/g, replacement: 'import {\n  $1' },

  // Fix broken destructuring
  { pattern: /const \{ \/\* content \*\/ \};/g, replacement: 'const {' },
  { pattern: /let \{ \/\* content \*\/ \};/g, replacement: 'let {' },
  { pattern: /var \{ \/\* content \*\/ \};/g, replacement: 'var {' },

  // Fix broken object literals
  { pattern: /headers: \{ \/\* content \*\/ \};/g, replacement: 'headers: {' },
  { pattern: /\{ \/\* content \*\/ \};/g, replacement: '{' },

  // Fix broken function calls and conditions
  { pattern: /if \(condition\) \{[\s\S]*?\/\/ Fixed content[\s\S]*?\}/g, replacement: 'if (true) {' },
  { pattern: /\} else \{ \/\* content \*\/ \};/g, replacement: '} else {' },
  { pattern: /try \{ \/\* content \*\/ \};/g, replacement: 'try {' },
  { pattern: /catch \(.*?\) \{ \/\* content \*\/ \};/g, replacement: 'catch (error) {' },
  { pattern: /finally \{ \/\* content \*\/ \};/g, replacement: 'finally {' },

  // Fix broken function definitions
  { pattern: /\{ \/\* content \*\/ \};[\s\n]*([a-zA-Z_$][a-zA-Z0-9_$]*)/g, replacement: '{\n  $1' },

  // Fix broken arrow functions
  { pattern: /=> \{[\s\n]*\/\/ Fixed content[\s\n]*\};/g, replacement: '=> {' },

  // Fix broken useEffect and other hooks
  { pattern: /useEffect\(\(\) => \{[\s\n]*\/\/ Fixed content[\s\n]*\};/g, replacement: 'useEffect(() => {' },
  { pattern: /useCallback\(async \([^)]*\) => \{[\s\n]*\/\/ Fixed content[\s\n]*\};/g, replacement: 'useCallback(async () => {' },

  // Fix broken return statements
  { pattern: /return function executedFunction\(\.\.\.args\) \{ \/\* content \*\/ \};/g, replacement: 'return function executedFunction(...args) {' },

  // Fix broken class methods
  { pattern: /constructor\(props\) \{ \/\* content \*\/ \};/g, replacement: 'constructor(props) {' },
  { pattern: /render\(\) \{ \/\* content \*\/ \};/g, replacement: 'render() {' },

  // Fix broken semicolons in class definitions
  { pattern: /class ([a-zA-Z_$][a-zA-Z0-9_$]*) extends Component \{ \/\* content \*\/ \};/g, replacement: 'class $1 extends Component {' },

  // Fix broken comments
  { pattern: /\/\/ Fixed content[\s\n]*\};/g, replacement: '' },
  { pattern: /\/\/ Fixed broken string/g, replacement: '' },

  // Fix specific broken patterns
  { pattern: /setFormData\(prev => \(\{ \/\* content \*\/ \};/g, replacement: 'setFormData(prev => ({' },
  { pattern: /useState\('''\)/g, replacement: "useState('')" },
  { pattern: /useState\('.*?''\)/g, replacement: (match) => match.replace(/''/, "'") },

  // Fix broken switch cases
  { pattern: /switch \([^)]+\) \{[\s\n]*case/g, replacement: (match) => match.replace(/\{[\s\n]*case/, '{\n      case') },

  // Fix broken template literals
  { pattern: /`([^`]*)''/g, replacement: "`$1'" },

  // Fix broken JSX
  { pattern: /<([a-zA-Z][a-zA-Z0-9]*)[^>]*\{ \/\* content \*\/ \};/g, replacement: '<$1' },

  // Fix broken exports
  { pattern: /export \{ \/\* content \*\/ \};/g, replacement: 'export {' },
  { pattern: /export default \{ \/\* content \*\/ \};/g, replacement: 'export default {' }
];

// Function to recursively find all JS/JSX files
function findJSFiles(dir, fileList = []) {
  const files = fs.readdirSync(dir);

  files.forEach(file => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);

    if (stat.isDirectory()) {
      // Skip node_modules and other unnecessary directories
      if (!['node_modules', '.git', 'dist', 'build', '.next'].includes(file)) {
        findJSFiles(filePath, fileList);
      }
    } else if (file.match(/\.(js|jsx|ts|tsx)$/)) {
      fileList.push(filePath);
    }
  });

  return fileList;
}

// Function to fix syntax errors in a file
function fixFileErrors(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let originalContent = content;
    let fixesApplied = 0;

    // Apply all syntax fixes
    syntaxFixes.forEach(fix => {
      const matches = content.match(fix.pattern);
      if (matches) {
        content = content.replace(fix.pattern, fix.replacement);
        fixesApplied += matches.length;
      }
    });

    // Additional specific fixes for common patterns

    // Fix broken useState calls
    content = content.replace(/useState\('([^']*)''\)/g, "useState('$1')");
    content = content.replace(/useState\("([^"]*)"\)/g, 'useState("$1")');

    // Fix broken localStorage calls
    content = content.replace(/localStorage\.getItem\('([^']*)''\)/g, "localStorage.getItem('$1')");
    content = content.replace(/localStorage\.setItem\('([^']*)''/g, "localStorage.setItem('$1'");

    // Fix broken console calls
    content = content.replace(/console\.(log|error|warn|info)\('([^']*)''\)/g, "console.$1('$2')");

    // Fix broken throw statements
    content = content.replace(/throw new Error\('([^']*)''\)/g, "throw new Error('$1')");

    // Fix broken toast calls
    content = content.replace(/toast\.(success|error|info|warning)\('([^']*)''\)/g, "toast.$1('$2')");

    // Fix broken API URLs
    content = content.replace(/`\$\{([^}]+)\}\/([^`]*)''/g, "`${$1}/$2'");

    // Fix broken object property access
    content = content.replace(/\.([a-zA-Z_$][a-zA-Z0-9_$]*)''/g, '.$1');

    // Fix broken array/object destructuring
    content = content.replace(/const \[\s*([^,\]]+),\s*([^,\]]+)\s*\] = ([^;]+)''/g, 'const [$1, $2] = $3;');

    // Fix broken function parameters
    content = content.replace(/function\s+([a-zA-Z_$][a-zA-Z0-9_$]*)\s*\([^)]*\)\s*\{\s*\/\/ Fixed content\s*\};/g, 'function $1() {');

    // Fix broken async/await
    content = content.replace(/async\s*\([^)]*\)\s*=>\s*\{\s*\/\/ Fixed content\s*\};/g, 'async () => {');

    // Write the fixed content back to file if changes were made
    if (content !== originalContent) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`✅ Fixed ${fixesApplied} syntax errors in: ${filePath}`);
      return fixesApplied;
    } else {
      console.log(`✓ No syntax errors found in: ${filePath}`);
      return 0;
    }

  } catch (error) {
    console.error(`❌ Error processing file ${filePath}:`, error.message);
    return 0;
  }
}

// Main function to fix all files
function fixAllSyntaxErrors() {
  console.log('🔧 Starting syntax error fix process...\n');

  const srcDir = path.join(__dirname, 'src');
  const jsFiles = findJSFiles(srcDir);

  console.log(`📁 Found ${jsFiles.length} JS/JSX files to process\n`);

  let totalFixes = 0;
  let processedFiles = 0;

  jsFiles.forEach(filePath => {
    const fixes = fixFileErrors(filePath);
    totalFixes += fixes;
    processedFiles++;
  });

  console.log(`\n🎉 Process completed!`);
  console.log(`📊 Summary:`);
  console.log(`   - Files processed: ${processedFiles}`);
  console.log(`   - Total fixes applied: ${totalFixes}`);
  console.log(`   - Files with errors fixed: ${jsFiles.filter(f => fixFileErrors(f) > 0).length}`);

  if (totalFixes > 0) {
    console.log(`\n✅ All syntax errors have been fixed!`);
    console.log(`🚀 You can now run 'npm run dev' to start the development server.`);
  } else {
    console.log(`\n✓ No syntax errors were found.`);
  }
}

// Run the fix process
if (require.main === module) {
  fixAllSyntaxErrors();
}

module.exports = { fixAllSyntaxErrors, fixFileErrors, syntaxFixes };
