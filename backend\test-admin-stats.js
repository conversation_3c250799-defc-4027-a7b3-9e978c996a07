import fetch from 'node-fetch';

const testAdminStats = async () => {
  try {
    console.log('Testing admin stats API...');

    // First login to get admin token
    console.log('1. Logging in as admin...');
    const loginResponse = await fetch('http://localhost:5002/api/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'admin123456'
      })
    });

    if (!loginResponse.ok) {
      console.log('❌ Login failed');
      return;
    }

    const loginData = await loginResponse.json();
    console.log('✅ Login successful');
    console.log('   User role:', loginData.user.role);
    console.log('   Token:', loginData.token ? 'Present' : 'Missing');

    // Test admin stats endpoint
    console.log('');
    console.log('2. Testing admin stats endpoint...');
    const statsResponse = await fetch('http://localhost:5002/api/stats/admin', {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${loginData.token}`,
        'Content-Type': 'application/json'
      }
    });

    console.log('   Response status:', statsResponse.status);
    
    if (statsResponse.ok) {
      const statsData = await statsResponse.json();
      console.log('✅ Admin stats retrieved successfully!');
      console.log('');
      console.log('📊 Statistics:');
      console.log('   Total Users:', statsData.data.totalUsers);
      console.log('   Total Models:', statsData.data.totalModels);
      console.log('   Total Downloads:', statsData.data.totalDownloads);
      console.log('   Total Revenue:', statsData.data.totalRevenue);
      console.log('   Recent Users:', statsData.data.recentUsers?.length || 0);
      console.log('   Recent Models:', statsData.data.recentModels?.length || 0);
      console.log('   Users by Month:', statsData.data.usersByMonth?.length || 0);
      console.log('   Downloads by Month:', statsData.data.downloadsByMonth?.length || 0);
      
      console.log('');
      console.log('👥 Recent Users:');
      if (statsData.data.recentUsers && statsData.data.recentUsers.length > 0) {
        statsData.data.recentUsers.forEach((user, index) => {
          console.log(`   ${index + 1}. ${user.name} (${user.email}) - ${user.role}`);
        });
      } else {
        console.log('   No recent users found');
      }

      console.log('');
      console.log('🏠 Recent Models:');
      if (statsData.data.recentModels && statsData.data.recentModels.length > 0) {
        statsData.data.recentModels.forEach((model, index) => {
          console.log(`   ${index + 1}. ${model.title} (${model.category})`);
        });
      } else {
        console.log('   No recent models found');
      }

    } else {
      const errorData = await statsResponse.text();
      console.log('❌ Admin stats failed:', errorData);
    }

    // Test public stats endpoint for comparison
    console.log('');
    console.log('3. Testing public stats endpoint...');
    const publicStatsResponse = await fetch('http://localhost:5002/api/stats');
    
    if (publicStatsResponse.ok) {
      const publicStatsData = await publicStatsResponse.json();
      console.log('✅ Public stats retrieved successfully!');
      console.log('');
      console.log('📊 Public Statistics:');
      console.log('   Total Users:', publicStatsData.data.totalUsers);
      console.log('   Total Models:', publicStatsData.data.totalModels);
      console.log('   Total Downloads:', publicStatsData.data.totalDownloads);
      console.log('   Total Categories:', publicStatsData.data.totalCategories);
    } else {
      console.log('❌ Public stats failed');
    }

    // Test MongoDB stats endpoint
    console.log('');
    console.log('4. Testing MongoDB stats endpoint...');
    const mongoStatsResponse = await fetch('http://localhost:5002/api/mongodb/statistics');
    
    if (mongoStatsResponse.ok) {
      const mongoStatsData = await mongoStatsResponse.json();
      console.log('✅ MongoDB stats retrieved successfully!');
      console.log('');
      console.log('📊 MongoDB Statistics:');
      console.log('   Models:', mongoStatsData.data.models);
      console.log('   Users:', mongoStatsData.data.users);
      console.log('   Categories:', mongoStatsData.data.categories);
      console.log('   Downloads:', mongoStatsData.data.downloads);
    } else {
      console.log('❌ MongoDB stats failed');
    }

  } catch (error) {
    console.error('Error testing admin stats:', error);
  }
};

testAdminStats();
