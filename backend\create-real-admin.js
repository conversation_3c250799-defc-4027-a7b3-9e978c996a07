import fetch from 'node-fetch';

const createRealAdmin = async () => {
  try {
    console.log('Creating a real admin user...');

    // First, register a new admin user
    const registerResponse = await fetch('http://localhost:5002/api/auth/register', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        name: 'Admin User',
        email: '<EMAIL>',
        password: 'admin123456'
      })
    });

    if (registerResponse.ok) {
      const registerData = await registerResponse.json();
      console.log('✅ Admin user registered successfully!');
      console.log('User ID:', registerData.user.id);
      console.log('Email:', registerData.user.email);
      console.log('Token:', registerData.token ? 'Present' : 'Missing');

      // Test login immediately
      console.log('Testing login with new admin...');
      
      const loginResponse = await fetch('http://localhost:5002/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: '<EMAIL>',
          password: 'admin123456'
        })
      });

      if (loginResponse.ok) {
        const loginData = await loginResponse.json();
        console.log('✅ Login successful!');
        console.log('Welcome back:', loginData.user.name);
        
        console.log('\n🎯 NEW ADMIN CREDENTIALS:');
        console.log('Email: <EMAIL>');
        console.log('Password: admin123456');
        console.log('Role: user (can be upgraded to admin later)');
        
      } else {
        const loginError = await loginResponse.text();
        console.log('❌ Login failed:', loginError);
      }

    } else {
      const registerError = await registerResponse.text();
      console.log('Registration failed:', registerError);
      
      // If user already exists, just test login
      if (registerError.includes('already exists')) {
        console.log('User already exists, testing login...');
        
        const loginResponse = await fetch('http://localhost:5002/api/auth/login', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            email: '<EMAIL>',
            password: 'admin123456'
          })
        });

        if (loginResponse.ok) {
          const loginData = await loginResponse.json();
          console.log('✅ Existing admin login successful!');
          console.log('Welcome back:', loginData.user.name);
          
          console.log('\n🎯 EXISTING ADMIN CREDENTIALS:');
          console.log('Email: <EMAIL>');
          console.log('Password: admin123456');
          
        } else {
          const loginError = await loginResponse.text();
          console.log('❌ Login failed:', loginError);
        }
      }
    }

  } catch (error) {
    console.error('Error creating admin user:', error);
  }
};

createRealAdmin();
