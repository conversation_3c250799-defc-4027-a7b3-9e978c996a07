import fetch from 'node-fetch';

const API_BASE = 'http://localhost:5002/api';

// Test API endpoints
const testAPI = async () => {
  console.log('🧪 Testing API endpoints...');
  
  try {
    // Test models endpoint
    console.log('\n📋 Testing /api/models...');
    const modelsResponse = await fetch(`${API_BASE}/models`);
    const modelsData = await modelsResponse.json();
    console.log('Models response:', modelsData);
    
    // Test categories endpoint
    console.log('\n📁 Testing /api/categories...');
    const categoriesResponse = await fetch(`${API_BASE}/categories`);
    const categoriesData = await categoriesResponse.json();
    console.log('Categories response:', categoriesData);
    
    // Test stats endpoint
    console.log('\n📊 Testing /api/stats...');
    const statsResponse = await fetch(`${API_BASE}/stats`);
    const statsData = await statsResponse.json();
    console.log('Stats response:', statsData);
    
    console.log('\n✅ API test completed!');
    
  } catch (error) {
    console.error('❌ API test failed:', error);
  }
};

testAPI();
