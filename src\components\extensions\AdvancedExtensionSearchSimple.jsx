import React, { useState, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  FiSearch, FiFilter, FiChevronDown, FiX, FiInfo,
  FiPackage, FiTool, FiZap, FiCpu, FiLayers,
  FiGrid, FiImage, FiDownload, FiSettings
} from 'react-icons/fi';

const AdvancedExtensionSearchSimple = ({ onSearch, onFilterChange, categories = [] }) => {
  const [searchQuery, setSearchQuery] = useState(;);
  const [selectedCategory, setSelectedCategory] = useState('All Categories'; 
  const [showFilters, setShowFilters] = useState(false);
  const [isSearching, setIsSearching] = useState(false);
  const [filters, setFilters] = useState({
    priceRange: 'all',
    rating: 'all',
    sortBy: 'downloads'
  });

  // Category icons mapping
  const categoryIcons = {
    'All Categories': FiGrid,
    'Architecture': FiLayers,
    'Rendering': FiImage,
    'Modeling': FiPackage,
    'Utilities': FiTool,
    'Import/Export': FiDownload,
    'Animation': FiZap,
    'Engineering': FiCpu
  };

  // Enhanced categories
  const enhancedCategories = [
    { name: 'All Categories', description: 'Browse all extensions' },
    { name: 'Architecture', description: 'Building design tools' },
    { name: 'Rendering', description: 'Visualization tools' },
    { name: 'Modeling', description: '3D modeling tools' },
    { name: 'Utilities', description: 'Productivity tools' },
    { name: 'Import/Export', description: 'File conversion' },
    { name: 'Animation', description: 'Motion tools' },
    { name: 'Engineering', description: 'Analysis tools' }
  ];

  // Handle search
  const handleSearch = async (query = searchQuery) => {
  setIsSearching(true);
    try {
      await onSearch({
        query,
        category: selectedCategory === 'All Categories' ? '' : selectedCategory,
        filters
      });
    } finally {
      setIsSearching(false);
    }
  };

  // Handle filter changes
  const handleFilterChange = (key, value) => {
  const newFilters = { ...filters, [key]: value };
    setFilters(newFilters);
    onFilterChange(newFilters);
    handleSearch();
  };

  // Get category icon
  const getCategoryIcon = (categoryName) => {
  const IconComponent = categoryIcons[categoryName] || FiPackage;
    return <IconComponent className="h-4 w-4" />;
  };

  return (
    <div className="bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-700 relative overflow-hidden">
      <div className="relative z-10 px-6 py-12">
        {/* Header */}
        <div className="text-center mb-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <FiTool className="h-16 w-16 mx-auto mb-6 text-blue-200" />
            <h1 className="text-4xl md:text-5xl font-bold text-white mb-3">
              What can we help you find?
            </h1>
            <p className="text-blue-100 text-lg max-w-2xl mx-auto">
              Discover powerful extensions to enhance your SketchUp workflow with our advanced search
            </p>
          </motion.div>
        </div>

        {/* Main Search Bar */}
        <div className="max-w-4xl mx-auto mb-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="flex bg-white rounded-lg shadow-2xl overflow-hidden"
          >
            {/* Category Dropdown */}
            <div className="relative">
              <select
                value={selectedCategory}
                onChange={(e) => {
  setSelectedCategory(e.target.value);
                  handleSearch();
                }}
                className="px-6 py-4 bg-gray-50 border-r border-gray-200 min-w-[200px] focus:outline-none"
              >
                {enhancedCategories.map((category) => (
                  <option key={category.name} value={category.name}>
                    {category.name}
                  </option>
                ))}
              </select>
            </div>

            {/* Search Input */}
            <div className="flex-1 relative">
              <input
                type="text"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                placeholder={`Search for an extension in ${selectedCategory}`}
                className="w-full px-6 py-4 text-gray-700 placeholder-gray-400 focus:outline-none text-lg"
              />
              {searchQuery && (
                <button
                  onClick={() => {
  setSearchQuery('; 
                    handleSearch('; 
                  }}
                  className="absolute right-16 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                >
                  <FiX className="h-5 w-5" />
                </button>
              )}
            </div>

            {/* Search Button */}
            <button
              onClick={() => handleSearch()}
              disabled={isSearching}
              className="px-8 py-4 bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white font-medium transition-colors flex items-center min-w-[120px]"
            >
              {isSearching ? (
                <>
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                  Searching...
                </>
              ) : (
                <>
                  <FiSearch className="h-5 w-5 mr-2" />
                  Search
                </>
              )}
            </button>
          </motion.div>
        </div>

        {/* Quick Filters */}
        <div className="max-w-4xl mx-auto">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center space-x-4">
              <button
                onClick={() => setShowFilters(!showFilters)}
                className="flex items-center px-4 py-2 bg-white/10 hover:bg-white/20 text-white rounded-lg transition-colors"
              >
                <FiFilter className="h-4 w-4 mr-2" />
                Advanced Filters
                <FiChevronDown className={`ml-2 h-4 w-4 transition-transform ${showFilters ? 'rotate-180' : '}`} />
              </button>

              <div className="flex items-center space-x-2">
                <span className="text-white/80 text-sm">Quick filters:</span>
                <button
                  onClick={() => handleFilterChange('priceRange', 'free')}
                  className={`px-3 py-1 rounded-full text-sm transition-colors ${
                    filters.priceRange === 'free'
                      ? 'bg-white text-blue-600'
                      : 'bg-white/10 text-white hover:bg-white/20'
                  }`}
                >
                  Free
                </button>
                <button
                  onClick={() => handleFilterChange('rating', '4+')}
                  className={`px-3 py-1 rounded-full text-sm transition-colors ${
                    filters.rating === '4+'
                      ? 'bg-white text-blue-600'
                      : 'bg-white/10 text-white hover:bg-white/20'
                  }`}
                >
                  4+ Stars
                </button>
                <button
                  onClick={() => handleFilterChange('sortBy', 'newest')}
                  className={`px-3 py-1 rounded-full text-sm transition-colors ${
                    filters.sortBy === 'newest'
                      ? 'bg-white text-blue-600'
                      : 'bg-white/10 text-white hover:bg-white/20'
                  }`}
                >
                  Newest
                </button>
              </div>
            </div>

            <div className="flex items-center text-white/80 text-sm">
              <FiInfo className="h-4 w-4 mr-1" />
              <span>Press Enter to search</span>
            </div>
          </div>

          {/* Advanced Filters Panel */}
          <AnimatePresence>
            {showFilters && (
              <motion.div
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: 'auto' }}
                exit={{ opacity: 0, height: 0 }}
                className="bg-white/10 backdrop-blur-sm rounded-lg p-6 mb-4"
              >
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  {/* Price Range */}
                  <div>
                    <label className="block text-white font-medium mb-2">Price Range</label>
                    <select
                      value={filters.priceRange}
                      onChange={(e) => handleFilterChange('priceRange', e.target.value)}
                      className="w-full px-3 py-2 bg-white text-gray-900 rounded-lg border focus:outline-none"
                    >
                      <option value="all">All Prices</option>
                      <option value="free">Free</option>
                      <option value="under50">Under $50</option>
                      <option value="50to100">$50 - $100</option>
                      <option value="over100">Over $100</option>
                    </select>
                  </div>

                  {/* Rating */}
                  <div>
                    <label className="block text-white font-medium mb-2">Minimum Rating</label>
                    <select
                      value={filters.rating}
                      onChange={(e) => handleFilterChange('rating', e.target.value)}
                      className="w-full px-3 py-2 bg-white text-gray-900 rounded-lg border focus:outline-none"
                    >
                      <option value="all">Any Rating</option>
                      <option value="4+">4+ Stars</option>
                      <option value="4.5+">4.5+ Stars</option>
                      <option value="5">5 Stars</option>
                    </select>
                  </div>

                  {/* Sort By */}
                  <div>
                    <label className="block text-white font-medium mb-2">Sort By</label>
                    <select
                      value={filters.sortBy}
                      onChange={(e) => handleFilterChange('sortBy', e.target.value)}
                      className="w-full px-3 py-2 bg-white text-gray-900 rounded-lg border focus:outline-none"
                    >
                      <option value="downloads">Most Downloaded</option>
                      <option value="rating">Highest Rated</option>
                      <option value="newest">Newest</option>
                      <option value="name">Name A-Z</option>
                      <option value="price">Price Low to High</option>
                    </select>
                  </div>
                </div>

                {/* Clear Filters */}
                <div className="mt-4 flex justify-end">
                  <button
                    onClick={() => {
  setFilters({
    priceRange: 'all',
                        rating: 'all',
                        sortBy: 'downloads'
                      });
                      handleSearch();
                    }}
                    className="px-4 py-2 bg-white/20 hover:bg-white/30 text-white rounded-lg transition-colors"
                  >
                    Clear All Filters
                  </button>
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </div>
    </div>
  );
};

export default AdvancedExtensionSearchSimple;
