import mongoose from 'mongoose';

const PaymentSchema = new mongoose.Schema({
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  amount: {
    type: Number,
    required: [true, 'Please add an amount']
  },
  currency: {
    type: String,
    required: [true, 'Please add a currency'],
    default: 'USD'
  },
  paymentMethod: {
    type: String,
    required: [true, 'Please add a payment method'],
    enum: ['credit_card', 'paypal', 'bank_transfer', 'crypto']
  },
  status: {
    type: String,
    required: [true, 'Please add a status'],
    enum: ['pending', 'completed', 'failed', 'refunded'],
    default: 'pending'
  },
  type: {
    type: String,
    required: [true, 'Please add a payment type'],
    enum: ['subscription', 'one_time_purchase', 'model_purchase']
  },
  description: {
    type: String,
    required: [true, 'Please add a description']
  },
  paymentId: {
    type: String
  },
  customerId: {
    type: String
  },
  subscriptionPlan: {
    type: String,
    enum: ['basic', 'premium', 'professional']
  },
  model: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Model'
  },
  billingDetails: {
    name: String,
    email: String,
    address: {
      line1: String,
      line2: String,
      city: String,
      state: String,
      postalCode: String,
      country: String
    }
  },
  receipt: {
    url: String,
    number: String
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

// Update timestamps pre-save
PaymentSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  next();
});

export default mongoose.model('Payment', PaymentSchema);
