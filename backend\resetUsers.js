import mongoose from 'mongoose';
import dotenv from 'dotenv';
import User from './src/models/User.js';

// Load environment variables
dotenv.config();

const resetUsers = async () => {
  try {
    console.log('🔄 Connecting to MongoDB...');
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('✅ MongoDB Connected');

    // Delete all existing users
    console.log('🗑️ Deleting all existing users...');
    const deleteResult = await User.deleteMany({});
    console.log(`✅ Deleted ${deleteResult.deletedCount} users`);

    // Create new users with correct passwords
    console.log('👥 Creating new users...');

    const users = [
      {
        name: 'Admin User',
        email: '<EMAIL>',
        password: 'admin123',
        role: 'admin',
        bio: 'System Administrator for 3DSKETCHUP.NET',
        subscription: {
          type: 'professional',
          status: 'active',
          startDate: new Date(),
          endDate: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000) // 1 year
        },
        downloadCredits: 1000
      },
      {
        name: 'Designer Pro',
        email: '<EMAIL>',
        password: 'designer123',
        role: 'user',
        bio: 'Professional 3D Designer and Architect',
        subscription: {
          type: 'premium',
          status: 'active',
          startDate: new Date(),
          endDate: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000)
        },
        downloadCredits: 50
      },
      {
        name: 'John Smith',
        email: '<EMAIL>',
        password: '123456',
        role: 'user',
        bio: 'Freelance 3D Artist',
        subscription: {
          type: 'basic',
          status: 'active',
          startDate: new Date(),
          endDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000) // 30 days
        },
        downloadCredits: 20
      },
      {
        name: 'Sarah Johnson',
        email: '<EMAIL>',
        password: '123456',
        role: 'user',
        bio: 'Interior Designer',
        subscription: {
          type: 'free',
          status: 'active'
        },
        downloadCredits: 5
      },
      {
        name: 'Test User',
        email: '<EMAIL>',
        password: 'test123',
        role: 'user',
        bio: 'Test account for development',
        subscription: {
          type: 'free',
          status: 'active'
        },
        downloadCredits: 5
      }
    ];

    // Create users one by one
    for (const userData of users) {
      try {
        const user = new User(userData);
        await user.save();
        console.log(`✅ Created user: ${userData.name} (${userData.email})`);
        
        // Test password immediately after creation
        const testUser = await User.findOne({ email: userData.email }).select('+password');
        const isMatch = await testUser.matchPassword(userData.password);
        console.log(`🔐 Password test for ${userData.email}: ${isMatch ? '✅ PASS' : '❌ FAIL'}`);
        
      } catch (error) {
        console.error(`❌ Error creating user ${userData.email}:`, error.message);
      }
    }

    console.log('\n🎉 User reset completed successfully!');
    console.log('\n📋 Login credentials:');
    console.log('👑 Admin: <EMAIL> / admin123');
    console.log('🎨 Designer: <EMAIL> / designer123');
    console.log('👤 Test: <EMAIL> / test123');
    console.log('👤 John: <EMAIL> / 123456');
    console.log('👤 Sarah: <EMAIL> / 123456');

    // Final verification
    console.log('\n🔍 Final verification:');
    const totalUsers = await User.countDocuments();
    console.log(`📊 Total users in database: ${totalUsers}`);

    process.exit(0);
  } catch (error) {
    console.error('❌ Error:', error);
    process.exit(1);
  }
};

console.log('🚀 Starting user reset process...');
resetUsers();
