import express from 'express';
import {
  // User management
  getUsers,
  getUser,
  createUser,
  updateUser,
  deleteUser
} from '../controllers/userController.js';
import {
  // Model management
  getModels,
  getModel,
  updateModel,
  deleteModel
} from '../controllers/modelController.js';
import {
  // Stats
  getAdminStats
} from '../controllers/statsController.js';
import { protect, authorize } from '../middleware/auth.js';

const router = express.Router();

// All admin routes require authentication and admin role
router.use(protect);
router.use(authorize('admin'));

// ===== USER MANAGEMENT =====

// Get all users with pagination and filtering
router.get('/users', getUsers);

// Get specific user
router.get('/users/:id', getUser);

// Create new user
router.post('/users', createUser);

// Update user
router.put('/users/:id', updateUser);

// Delete user
router.delete('/users/:id', deleteUser);

// Bulk delete users
router.post('/users/bulk-delete', async (req, res) => {
  try {
    const { userIds } = req.body;

    if (!userIds || !Array.isArray(userIds) || userIds.length === 0) {
      return res.status(400).json({
        success: false,
        error: 'User IDs array is required'
      });
    }

    const User = (await import('../models/User.js')).default;

    // Don't allow deleting admin users
    const adminUsers = await User.find({
      _id: { $in: userIds },
      role: 'admin'
    });

    if (adminUsers.length > 0) {
      return res.status(400).json({
        success: false,
        error: 'Cannot delete admin users'
      });
    }

    const result = await User.deleteMany({ _id: { $in: userIds } });

    res.status(200).json({
      success: true,
      message: `${result.deletedCount} users deleted successfully`,
      deletedCount: result.deletedCount
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// ===== MODEL MANAGEMENT =====

// Create new model
router.post('/models', async (req, res) => {
  try {
    const Model = (await import('../models/Model.js')).default;

    // Create new model with admin data
    const modelData = {
      ...req.body,
      createdBy: req.user._id,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    const model = new Model(modelData);
    await model.save();

    res.status(201).json({
      success: true,
      message: 'Model created successfully',
      data: model
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Get all models for admin (includes pending, rejected, etc.)
router.get('/models', async (req, res) => {
  try {
    const {
      page = 1,
      limit = 10,
      status,
      category,
      search,
      sortBy = 'createdAt',
      sortDirection = 'desc'
    } = req.query;

    const Model = (await import('../models/Model.js')).default;

    // Build query
    const query = {};

    if (status && status !== 'all') {
      query.status = status;
    }

    if (category && category !== 'all') {
      query.category = category;
    }

    if (search) {
      query.$or = [
        { title: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } },
        { tags: { $in: [new RegExp(search, 'i')] } }
      ];
    }

    // Build sort
    const sort = {};
    sort[sortBy] = sortDirection === 'desc' ? -1 : 1;

    // Execute query with pagination
    const skip = (parseInt(page) - 1) * parseInt(limit);

    const [models, total] = await Promise.all([
      Model.find(query)
        .sort(sort)
        .skip(skip)
        .limit(parseInt(limit)),
      Model.countDocuments(query)
    ]);

    res.status(200).json({
      success: true,
      data: models,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / parseInt(limit))
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Approve model
router.put('/models/:id/approve', async (req, res) => {
  try {
    const Model = (await import('../models/Model.js')).default;

    const model = await Model.findByIdAndUpdate(
      req.params.id,
      {
        status: 'approved',
        approvedAt: new Date(),
        approvedBy: req.user._id
      },
      { new: true }
    );

    if (!model) {
      return res.status(404).json({
        success: false,
        error: 'Model not found'
      });
    }

    res.status(200).json({
      success: true,
      message: 'Model approved successfully',
      data: model
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Reject model
router.put('/models/:id/reject', async (req, res) => {
  try {
    const { reason } = req.body;
    const Model = (await import('../models/Model.js')).default;

    const model = await Model.findByIdAndUpdate(
      req.params.id,
      {
        status: 'rejected',
        rejectedAt: new Date(),
        rejectedBy: req.user._id,
        rejectionReason: reason
      },
      { new: true }
    );

    if (!model) {
      return res.status(404).json({
        success: false,
        error: 'Model not found'
      });
    }

    res.status(200).json({
      success: true,
      message: 'Model rejected successfully',
      data: model
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Feature/Unfeature model
router.put('/models/:id/feature', async (req, res) => {
  try {
    const Model = (await import('../models/Model.js')).default;

    const model = await Model.findByIdAndUpdate(
      req.params.id,
      { featured: true },
      { new: true }
    );

    if (!model) {
      return res.status(404).json({
        success: false,
        error: 'Model not found'
      });
    }

    res.status(200).json({
      success: true,
      message: 'Model featured successfully',
      data: model
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

router.put('/models/:id/unfeature', async (req, res) => {
  try {
    const Model = (await import('../models/Model.js')).default;

    const model = await Model.findByIdAndUpdate(
      req.params.id,
      { featured: false },
      { new: true }
    );

    if (!model) {
      return res.status(404).json({
        success: false,
        error: 'Model not found'
      });
    }

    res.status(200).json({
      success: true,
      message: 'Model unfeatured successfully',
      data: model
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Bulk delete models
router.post('/models/bulk-delete', async (req, res) => {
  try {
    const { modelIds } = req.body;

    if (!modelIds || !Array.isArray(modelIds) || modelIds.length === 0) {
      return res.status(400).json({
        success: false,
        error: 'Model IDs array is required'
      });
    }

    const Model = (await import('../models/Model.js')).default;
    const result = await Model.deleteMany({ _id: { $in: modelIds } });

    res.status(200).json({
      success: true,
      message: `${result.deletedCount} models deleted successfully`,
      deletedCount: result.deletedCount
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Bulk update models
router.put('/models/bulk-update', async (req, res) => {
  try {
    const { modelIds, updateData } = req.body;

    if (!modelIds || !Array.isArray(modelIds) || modelIds.length === 0) {
      return res.status(400).json({
        success: false,
        error: 'Model IDs array is required'
      });
    }

    if (!updateData || Object.keys(updateData).length === 0) {
      return res.status(400).json({
        success: false,
        error: 'Update data is required'
      });
    }

    const Model = (await import('../models/Model.js')).default;
    const result = await Model.updateMany(
      { _id: { $in: modelIds } },
      { $set: updateData }
    );

    res.status(200).json({
      success: true,
      message: `${result.modifiedCount} models updated successfully`,
      modifiedCount: result.modifiedCount
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// ===== ANALYTICS =====

// Get admin analytics
router.get('/analytics', async (req, res) => {
  try {
    const { timeRange = '30d' } = req.query;

    // For now, return the same data as admin stats
    // In a real app, you would implement time-range specific analytics
    const statsResponse = await getAdminStats(req, res);

    // If getAdminStats already sent a response, don't send another
    if (res.headersSent) {
      return;
    }

    res.status(200).json({
      success: true,
      data: {
        timeRange,
        message: 'Analytics data (using admin stats for now)'
      }
    });
  } catch (error) {
    if (!res.headersSent) {
      res.status(500).json({
        success: false,
        error: error.message
      });
    }
  }
});

// ===== SETTINGS =====

// Get admin settings
router.get('/settings', async (req, res) => {
  try {
    // In a real app, you would fetch settings from database
    const defaultSettings = {
      general: {
        siteName: '3DSKETCHUP.NET',
        siteDescription: 'Premium 3D Models Repository',
        siteUrl: 'https://3dsketchup.net',
        adminEmail: '<EMAIL>',
        timezone: 'UTC',
        language: 'en',
        maintenanceMode: false
      },
      upload: {
        maxFileSize: 100,
        allowedFormats: ['skp', 'obj', 'fbx', '3ds', 'dae'],
        requireApproval: true,
        autoGenerateThumbnails: true,
        compressionEnabled: true,
        virusScanEnabled: true
      },
      payment: {
        currency: 'USD',
        taxRate: 0,
        freeDownloadsPerMonth: 5,
        basicPlanPrice: 9.99,
        premiumPlanPrice: 19.99,
        professionalPlanPrice: 39.99
      }
    };

    res.status(200).json({
      success: true,
      data: defaultSettings
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Update admin settings
router.put('/settings', async (req, res) => {
  try {
    const settings = req.body;

    // In a real app, you would save settings to database
    // For now, just return success

    res.status(200).json({
      success: true,
      message: 'Settings updated successfully',
      data: settings
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// ===== SYSTEM =====

// Get system info
router.get('/system', async (req, res) => {
  try {
    const systemInfo = {
      version: '1.0.0',
      nodeVersion: process.version,
      uptime: process.uptime(),
      memory: process.memoryUsage(),
      platform: process.platform,
      environment: process.env.NODE_ENV || 'development'
    };

    res.status(200).json({
      success: true,
      data: systemInfo
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

export default router;
