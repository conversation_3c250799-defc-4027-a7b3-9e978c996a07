import User from '../models/User.js';
import Model from '../models/Model.js';
import Category from '../models/Category.js';
import Payment from '../models/Payment.js';

/**
 * @desc    Get site statistics
 * @route   GET /api/stats
 * @access  Public
 */
export const getSiteStats = async (req, res, next) => {
  try {
    // Get total models count
    const totalModels = await Model.countDocuments();
    
    // Get total users count
    const totalUsers = await User.countDocuments();
    
    // Get total categories count
    const totalCategories = await Category.countDocuments();
    
    // Calculate total downloads (sum of downloads field across all models)
    const models = await Model.find({}, 'downloads');
    const totalDownloads = models.reduce((sum, model) => sum + (model.downloads || 0), 0);
    
    // Get recent models (optional)
    const recentModels = await Model.find()
      .sort({ createdAt: -1 })
      .limit(5)
      .select('title category createdAt downloads views');
    
    // Get popular models (optional)
    const popularModels = await Model.find()
      .sort({ downloads: -1 })
      .limit(5)
      .select('title category downloads views');
    
    // Get top categories (optional)
    const categories = await Category.find()
      .sort({ modelCount: -1 })
      .limit(5)
      .select('name slug modelCount');
    
    // Return statistics
    res.status(200).json({
      success: true,
      data: {
        totalModels,
        totalUsers,
        totalCategories,
        totalDownloads,
        recentModels,
        popularModels,
        categories
      }
    });
  } catch (error) {
    next(error);
  }
};

/**
 * @desc    Get admin dashboard statistics
 * @route   GET /api/stats/admin
 * @access  Private/Admin
 */
export const getAdminStats = async (req, res, next) => {
  try {
    // Get total models count
    const totalModels = await Model.countDocuments();
    
    // Get total users count
    const totalUsers = await User.countDocuments();
    
    // Get total downloads
    const models = await Model.find({}, 'downloads');
    const totalDownloads = models.reduce((sum, model) => sum + (model.downloads || 0), 0);
    
    // Get total revenue
    const payments = await Payment.find({ status: 'completed' }, 'amount');
    const totalRevenue = payments.reduce((sum, payment) => sum + (payment.amount || 0), 0);
    
    // Get recent users
    const recentUsers = await User.find()
      .sort({ createdAt: -1 })
      .limit(5)
      .select('name email createdAt role');
    
    // Get recent models
    const recentModels = await Model.find()
      .sort({ createdAt: -1 })
      .limit(5)
      .populate('createdBy', 'name')
      .select('title category createdAt createdBy');
    
    // Get monthly user registrations (last 6 months)
    const sixMonthsAgo = new Date();
    sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 5);
    
    const usersByMonth = await User.aggregate([
      {
        $match: {
          createdAt: { $gte: sixMonthsAgo }
        }
      },
      {
        $group: {
          _id: { 
            year: { $year: '$createdAt' },
            month: { $month: '$createdAt' }
          },
          count: { $sum: 1 }
        }
      },
      {
        $sort: { '_id.year': 1, '_id.month': 1 }
      }
    ]);
    
    // Get monthly downloads (last 6 months)
    // Note: This is an approximation as we don't track when downloads happen
    const downloadsByMonth = await Model.aggregate([
      {
        $match: {
          updatedAt: { $gte: sixMonthsAgo }
        }
      },
      {
        $group: {
          _id: { 
            year: { $year: '$updatedAt' },
            month: { $month: '$updatedAt' }
          },
          downloads: { $sum: '$downloads' }
        }
      },
      {
        $sort: { '_id.year': 1, '_id.month': 1 }
      }
    ]);
    
    // Return statistics
    res.status(200).json({
      success: true,
      data: {
        totalModels,
        totalUsers,
        totalDownloads,
        totalRevenue,
        recentUsers,
        recentModels,
        usersByMonth,
        downloadsByMonth
      }
    });
  } catch (error) {
    next(error);
  }
};
