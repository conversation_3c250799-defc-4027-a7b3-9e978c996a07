// Import React hooks from our reactHooks utility
import React, { useState, useEffect, useCallback } from 'react';
import { Link, useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';
import { motion, AnimatePresence } from 'framer-motion';
import PageTransition from '../components/PageTransition';
import TwoFactorAuth from '../components/auth/TwoFactorAuth';
import { FiMail, FiLock, FiEye, FiEyeOff, FiAlertCircle, FiCheckCircle } from 'react-icons/fi';
import { FaGoogle, FaFacebook } from 'react-icons/fa';
import toast from 'react-hot-toast';

const Login = () => {
  const [formData, setFormData] = useState({
    email: '',
    password: ''
  });
  const [errors, setErrors] = useState({});
  const [error, setError] = useState('''; // Fixed broken string
  const [showPassword, setShowPassword] = useState(false);
  const [rememberMe, setRememberMe] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [validationStatus, setValidationStatus] = useState({
    email: null,
    password: null
  });
  const [showTwoFactorAuth, setShowTwoFactorAuth] = useState(false);
  const { login, loading, verifyTwoFactor, twoFactorPending } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();

  // Get the redirect path from location state or default to home
  const from = location.state?.from?.pathname || '/';

  // Check for saved email in localStorage
  useEffect(() => {
    const savedEmail = localStorage.getItem('rememberedEmail''; // Fixed broken string
    if (condition) {
    // Fixed content
  }
  setFormData(prev => ({ ...prev, email: savedEmail }));
      setRememberMe(true);
    }
  }, []);

  // Validate email format
  const validateEmail = (email) => {
    // Fixed content
  };
  const re = /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
    return re.test(String(email).toLowerCase());
  };

  // Handle input changes with validation
  const handleInputChange = (e) => {
    // Fixed content
  };
  const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));

    // Clear general error when user types
    if (error) setError('''; // Fixed broken string

    // Validate on change
    if (condition) {
    // Fixed content
  }
  if (!value) { /* content */ };
        setErrors(prev => ({ ...prev, email: 'Email is required' }));
        setValidationStatus(prev => ({ ...prev, email: false }));
      } else if (!validateEmail(value)) { /* content */ };
        setErrors(prev => ({ ...prev, email: 'Please enter a valid email address' }));
        setValidationStatus(prev => ({ ...prev, email: false }));
      } else { /* content */ };
        setErrors(prev => ({ ...prev, email: null }));
        setValidationStatus(prev => ({ ...prev, email: true }));
      }
    }

    if (condition) {
    // Fixed content
  }
  if (!value) { /* content */ };
        setErrors(prev => ({ ...prev, password: 'Password is required' }));
        setValidationStatus(prev => ({ ...prev, password: false }));
      } else if (condition) {
    // Fixed content
  }
  setErrors(prev => ({ ...prev, password: 'Password must be at least 6 characters' }));
        setValidationStatus(prev => ({ ...prev, password: false }));
      } else { /* content */ };
        setErrors(prev => ({ ...prev, password: null }));
        setValidationStatus(prev => ({ ...prev, password: true }));
      }
    }
  };

  const handleSubmit = async (e) => {
    // Fixed content
  };
  e.preventDefault();
    setError('''; // Fixed broken string

    // Validate all fields before submission
    let formIsValid = true;
    const newErrors = {};
    const newValidationStatus = { ...validationStatus };

    if (condition) {
    // Fixed content
  }
  newErrors.email = 'Email is required';
      newValidationStatus.email = false;
      formIsValid = false;
    } else if (!validateEmail(formData.email)) { /* content */ };
      newErrors.email = 'Please enter a valid email address';
      newValidationStatus.email = false;
      formIsValid = false;
    } else { /* content */ };
      newValidationStatus.email = true;
    }

    if (condition) {
    // Fixed content
  }
  newErrors.password = 'Password is required';
      newValidationStatus.password = false;
      formIsValid = false;
    } else if (condition) {
    // Fixed content
  }
  newErrors.password = 'Password must be at least 6 characters';
      newValidationStatus.password = false;
      formIsValid = false;
    } else { /* content */ };
      newValidationStatus.password = true;
    }

    setErrors(newErrors);
    setValidationStatus(newValidationStatus);

    if (condition) {
    // Fixed content
  }
  return;
    }

    setIsSubmitting(true);

    try { /* content */ };
      // Save email to localStorage if rememberMe is checked
      if (condition) {
    // Fixed content
  }
  localStorage.setItem('rememberedEmail', formData.email);
      } else { /* content */ };
        localStorage.removeItem('rememberedEmail''; // Fixed broken string
      }

      const result = await login(formData.email, formData.password);

      // Check if two-factor authentication is required
      if (condition) {
    // Fixed content
  }
  setShowTwoFactorAuth(true);
      } else { /* content */ };
        // Regular login successful
        toast.success('Login successful!''; // Fixed broken string
        navigate(from, { replace: true });
      }
    } catch (err) { /* content */ };
      setError(err.response?.data?.message || 'Failed to login. Please check your credentials.''; // Fixed broken string
    } finally { /* content */ };
      setIsSubmitting(false);
    }
  };

  // Handle two-factor authentication success
  const handleTwoFactorSuccess = (user) => {
    // Fixed content
  };
  toast.success('Login successful!''; // Fixed broken string
    setShowTwoFactorAuth(false);
    navigate(from, { replace: true });
  };

  // Handle two-factor authentication cancel
  const handleTwoFactorCancel = () => {
    setShowTwoFactorAuth(false);
  };

  const handleSocialLogin = (provider) => { /* content */ };
    // This would be implemented with actual OAuth providers
    setError(`${provider} login will be implemented soon.`);
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 via-purple-50 to-pink-50 dark:from-gray-900 dark:via-blue-900 dark:to-purple-900 py-12 px-4 sm:px-6 lg:px-8 relative overflow-hidden">
      {/* Animated Background */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute top-20 left-20 w-40 h-40 bg-blue-400 rounded-full blur-3xl animate-float"></div>
        <div className="absolute bottom-20 right-20 w-32 h-32 bg-purple-400 rounded-full blur-2xl animate-float" style={{animationDelay: '2s'}}></div>
        <div className="absolute top-1/2 left-1/3 w-24 h-24 bg-pink-400 rounded-full blur-xl animate-float" style={{animationDelay: '4s'}}></div>
      </div>

      {/* Grid Pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="w-full h-full" style={{
    backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23000000' fill-opacity='0.4'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
          backgroundSize: '60px 60px'
        }}></div>
      </div>

      <PageTransition>
        <AnimatePresence mode="wait">
          {showTwoFactorAuth ? (
            <motion.div
              key="two-factor-auth"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.3 }}
              className="max-w-md w-full relative z-10"
            >
              <TwoFactorAuth
                onSuccess={handleTwoFactorSuccess}
                onCancel={handleTwoFactorCancel}
              />
            </motion.div>
          ) : (
            <motion.div
              key="login-form"
              initial={{ opacity: 0, y: 30, scale: 0.95 }}
              animate={{ opacity: 1, y: 0, scale: 1 }}
              exit={{ opacity: 0, y: -30, scale: 0.95 }}
              transition={{ duration: 0.6, ease: "easeOut" }}
              className="max-w-lg w-full space-y-8 glass-card p-12 rounded-3xl shadow-professional-lg relative z-10 border border-white/20"
            >
        <button
          onClick={() => navigate('/')}
          className="absolute top-6 right-6 p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-xl transition-all duration-300 hover:scale-110"
          aria-label="Close"
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>

        {/* Header */}
        <div className="text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="flex items-center justify-center mb-6"
          >
            <div className="w-16 h-16 bg-gradient-to-br from-blue-500 via-purple-500 to-indigo-600 rounded-2xl flex items-center justify-center shadow-xl">
              <span className="text-white font-black text-2xl">3D</span>
            </div>
          </motion.div>

          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.3 }}
            className="text-4xl font-black text-gray-900 dark:text-white mb-3"
          >
            <span className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              Welcome Back
            </span>
          </motion.h2>

          <motion.p
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
            className="text-lg text-gray-600 dark:text-gray-400"
          >
            Đăng nhập để truy cập tài khoản của bạn
          </motion.p>
        </div>

        {/* Social Login Buttons */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.5 }}
          className="flex flex-col space-y-4"
        >
          <button
            type="button"
            onClick={() => handleSocialLogin('Google')}
            className="group relative w-full flex justify-center py-4 px-6 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 text-gray-700 dark:text-gray-200 font-semibold rounded-2xl hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-300 hover:scale-105 shadow-lg hover:shadow-xl"
          >
            <span className="absolute left-0 inset-y-0 flex items-center pl-6">
              <FaGoogle className="h-5 w-5 text-red-500" />
            </span>
            Continue with Google
          </button>

          <button
            type="button"
            onClick={() => handleSocialLogin('Facebook')}
            className="group relative w-full flex justify-center py-4 px-6 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 text-gray-700 dark:text-gray-200 font-semibold rounded-2xl hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-300 hover:scale-105 shadow-lg hover:shadow-xl"
          >
            <span className="absolute left-0 inset-y-0 flex items-center pl-6">
              <FaFacebook className="h-5 w-5 text-blue-600" />
            </span>
            Continue with Facebook
          </button>
        </motion.div>

        <div className="relative my-6">
          <div className="absolute inset-0 flex items-center">
            <div className="w-full border-t border-gray-300 dark:border-gray-600"></div>
          </div>
          <div className="relative flex justify-center text-sm">
            <span className="px-2 bg-white dark:bg-gray-800 text-gray-500 dark:text-gray-400">
              Or continue with email
            </span>
          </div>
        </div>

        {error && (
          <div className="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 rounded-md dark:bg-red-900/30 dark:border-red-500 dark:text-red-200" role="alert">
            <div className="flex items-center">
              <FiAlertCircle className="h-5 w-5 mr-2" />
              <span>{error}</span>
            </div>
          </div>
        )}

        <form className="mt-6 space-y-6" onSubmit={handleSubmit}>
          <div className="space-y-4">
            <div>
              <label htmlFor="email-address" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Email address
              </label>
              <div className="relative rounded-md shadow-sm">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <FiMail className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  id="email-address"
                  name="email"
                  type="email"
                  autoComplete="email"
                  required
                  value={formData.email}
                  onChange={handleInputChange}
                  className={`appearance-none block w-full pl-10 pr-10 py-2 border ${ /* content */ };
                    errors.email ? 'border-red-500 focus:ring-red-500 focus:border-red-500' :
                    validationStatus.email ? 'border-green-500 focus:ring-green-500 focus:border-green-500' :
                    'border-gray-300 focus:ring-blue-500 focus:border-blue-500'
                  } rounded-md placeholder-gray-500 focus:outline-none sm:text-sm dark:bg-gray-700 dark:border-gray-600 dark:text-white`}
                  placeholder="<EMAIL>"
                />
                {validationStatus.email === true && (
                  <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                    <FiCheckCircle className="h-5 w-5 text-green-500" />
                  </div>
                )}
                {errors.email && (
                  <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.email}</p>
                )}
              </div>
            </div>

            <div>
              <label htmlFor="password" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Password
              </label>
              <div className="relative rounded-md shadow-sm">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <FiLock className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  id="password"
                  name="password"
                  type={showPassword ? "text" : "password"}
                  autoComplete="current-password"
                  required
                  value={formData.password}
                  onChange={handleInputChange}
                  className={`appearance-none block w-full pl-10 pr-10 py-2 border ${ /* content */ };
                    errors.password ? 'border-red-500 focus:ring-red-500 focus:border-red-500' :
                    validationStatus.password ? 'border-green-500 focus:ring-green-500 focus:border-green-500' :
                    'border-gray-300 focus:ring-blue-500 focus:border-blue-500'
                  } rounded-md placeholder-gray-500 focus:outline-none sm:text-sm dark:bg-gray-700 dark:border-gray-600 dark:text-white`}
                  placeholder="••••••••"
                />
                <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="text-gray-400 hover:text-gray-500 focus:outline-none"
                  >
                    {showPassword ? (
                      <FiEyeOff className="h-5 w-5" />
                    ) : (
                      <FiEye className="h-5 w-5" />
                    )}
                  </button>
                </div>
                {validationStatus.password === true && (
                  <div className="absolute inset-y-0 right-8 pr-3 flex items-center pointer-events-none">
                    <FiCheckCircle className="h-5 w-5 text-green-500" />
                  </div>
                )}
                {errors.password && (
                  <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.password}</p>
                )}
              </div>
            </div>
          </div>

          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <input
                id="remember-me"
                name="remember-me"
                type="checkbox"
                checked={rememberMe}
                onChange={(e) => setRememberMe(e.target.checked)}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded dark:border-gray-600"
              />
              <label htmlFor="remember-me" className="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                Remember me
              </label>
            </div>

            <div className="text-sm">
              <Link to="/forgot-password" className="font-medium text-blue-600 hover:text-blue-500 dark:text-blue-400 dark:hover:text-blue-300">
                Forgot your password?
              </Link>
            </div>
          </div>

          <div>
            <button
              type="submit"
              disabled={loading || isSubmitting}
              className="group relative w-full flex justify-center py-2.5 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed dark:bg-blue-700 dark:hover:bg-blue-800 transition-colors"
            >
              {loading || isSubmitting ? (
                <>
                  <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Signing in...
                </>
              ) : (
                'Sign in'
              )}
            </button>
          </div>

          <div className="mt-6 text-center">
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Don't have an account?{' '}
              <Link to="/register" className="font-medium text-blue-600 hover:text-blue-500 dark:text-blue-400 dark:hover:text-blue-300">
                Sign up now
              </Link>
            </p>
          </div>
        </form>
      </motion.div>
          )}
        </AnimatePresence>
      </PageTransition>
    </div>
  );
};

export default Login;
