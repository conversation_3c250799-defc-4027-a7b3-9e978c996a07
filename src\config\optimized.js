// Optimized configuration for production
export const config = {
  // Performance settings
  enableCache: true,
  cacheTimeout: 10 * 60 * 1000, // 10 minutes
  enableRequestDeduplication: true,
  
  // API settings
  apiTimeout: 10000, // 10 seconds
  retryAttempts: 3,
  retryDelay: 1000,
  
  // UI settings
  enableAnimations: true,
  lazyLoadImages: true,
  virtualScrolling: true,
  
  // Debug settings (disabled in production)
  enableLogging: process.env.NODE_ENV !== 'production',
  enableDebugMode: process.env.NODE_ENV !== 'production',
  
  // Feature flags
  enableMockData: false, // Always use real API data
  enableTestMode: false,
  enableDevTools: process.env.NODE_ENV !== 'production'
};

export default config;