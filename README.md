# 3DSKETCHUP.NET - Nền Tảng Mô Hình 3D Thông Minh

Một nền tảng kho mô hình 3D toàn diện với các tính năng AI tiên tiến, bao gồm trợ lý AI mô hình, phòng trưng bày ảo, studio thiết kế tương tác và nhiều tính năng đột phá khác.

## 🚀 Tính Năng Chính

### 🎯 Tính Năng Cốt Lõi
- ✅ Hệ thống xác thực và quản lý hồ sơ người dùng
- ✅ Upload/download mô hình 3D với hỗ trợ đa định dạng
- ✅ Trình xem 3D nâng cao với tương tác thời gian thực
- ✅ Tìm kiếm và lọc thông minh
- ✅ Hệ thống thanh toán và đăng ký gói
- ✅ Thiết kế responsive cho mọi thiết bị
- ✅ Dashboard quản trị viên toàn diện
- ✅ Chatbot AI Gemini với chức năng giọng nói
- ✅ Tối ưu SEO và chia sẻ xã hội

### 🧠 Tính Năng AI Tiên Tiến
- 🆕 **Trợ Lý AI Mô Hình**: Phân tích kỹ thuật và gợi ý cải thiện
- 🆕 **Phát hiện tự động vấn đề hình học**: Kiểm tra chất lượng mô hình
- 🆕 **Tối ưu hóa thông minh**: Gợi ý cho các mục đích sử dụng khác nhau
- 🆕 **Bộ sưu tập thông minh**: Tự động cập nhật dựa trên AI
- 🆕 **Tìm kiếm bằng hình ảnh**: Sử dụng computer vision

### 🌍 Trải Nghiệm Nhập Vai
- 🆕 **Phòng Trưng Bày Ảo**: Môi trường 3D để trưng bày bộ sưu tập
- 🆕 **Tích hợp VR/AR**: Tour ảo và xem mô hình trong thực tế tăng cường
- 🆕 **Studio Thiết Kế Tương Tác**: Không gian làm việc 3D collaborative
- 🆕 **Chế độ offline PWA**: Truy cập không cần internet

### 🔧 Tính Năng Kỹ Thuật
- 🆕 **API công khai**: RESTful API cho nhà phát triển
- 🆕 **Hệ thống plugin**: Marketplace cho extension
- 🆕 **Bảo mật nâng cao**: 2FA, mã hóa end-to-end
- 🆕 **Đa ngôn ngữ**: Hỗ trợ tiếng Việt và quốc tế
- 🆕 **CDN tối ưu**: Tải nhanh toàn cầu

## Getting Started

### Prerequisites

- Node.js 18+ and npm 9+
- Git
- Visual Studio Code (recommended)
- React DevTools browser extension

### Installation

```bash
# Clone the repository
git clone [repository-url]
cd 3dsketchup

# Install dependencies
npm install

# Start development server
npm run dev
```

## React Singleton Pattern

This project uses a React singleton pattern to prevent "Invalid Hook Call" errors caused by multiple React instances.

### Why Use the React Singleton?

- Prevents "Invalid Hook Call" errors
- Ensures consistent React behavior across the application
- Makes it easier to track React usage

### How to Use the React Singleton

Always import React hooks and APIs from our singleton utility instead of directly from 'react':

```jsx
// INCORRECT - Don't do this
import React from 'react';
import { useState, useEffect } from 'react';

// CORRECT - Do this instead
import { useState, useEffect } from './utils/reactSingleton';
```

## Documentation

- [Developer Guide](./DEVELOPER_GUIDE.md) - Comprehensive guide for developers
- [React Best Practices](./REACT_BEST_PRACTICES.md) - Best practices for React development
- [React DevTools Setup](./REACT_DEVTOOLS_SETUP.md) - Guide for setting up React DevTools

---

## 🚀 **TÍNH NĂNG MỚI ĐÃ TRIỂN KHAI**

### **✅ ĐÃ HOÀN THÀNH - SẴN SÀNG SỬ DỤNG**

#### **🧠 Trợ Lý AI Mô Hình**
- ✅ Phân tích chất lượng mô hình tự động với Gemini AI
- ✅ Phát hiện vấn đề hình học và topology
- ✅ Gợi ý tối ưu hóa hiệu suất
- ✅ Đánh giá tương thích phần mềm
- ✅ Điểm sức khỏe mô hình (0-100)
- ✅ Lịch sử phân tích chi tiết

#### **🌍 Phòng Trưng Bày Ảo**
- ✅ Môi trường 3D tương tác với React Three Fiber
- ✅ Hỗ trợ VR/AR với WebXR
- ✅ Nhiều chủ đề và layout tùy chỉnh
- ✅ Điều khiển ánh sáng và camera
- ✅ Chia sẻ và cộng tác real-time
- ✅ Thống kê và analytics chi tiết

#### **🔄 Bộ Sưu Tập Thông Minh**
- ✅ AI tự động tuyển chọn dựa trên tiêu chí
- ✅ Gợi ý mô hình phù hợp với độ tin cậy
- ✅ Cộng tác và bình chọn cộng đồng
- ✅ Cập nhật tự động theo thời gian thực
- ✅ Phân tích hành vi người dùng

#### **🎮 Studio Thiết Kế Tương Tác**
- ✅ Không gian làm việc 3D collaborative
- ✅ Chỉnh sửa real-time với WebRTC
- ✅ Quản lý phiên bản và snapshot
- ✅ Xuất đa định dạng (GLTF, FBX, OBJ, STL...)
- ✅ Hệ thống vật liệu và ánh sáng nâng cao

---

## 💡 **DEMO TÍNH NĂNG**

🎯 **Truy cập Demo**: [http://localhost:5173/features-demo](http://localhost:5173/features-demo)

### **Các Tính Năng Có Thể Demo Ngay**
1. **AI Model Assistant** - Phân tích mô hình với AI
2. **Virtual Showroom** - Trải nghiệm 3D nhập vai
3. **Smart Collection** - Bộ sưu tập thông minh
4. **Design Studio** - Studio thiết kế tương tác

### **Cách Sử Dụng Demo**
1. Khởi động server: `npm run dev`
2. Vào trang chủ và nhấn banner "Tính năng mới"
3. Hoặc truy cập trực tiếp `/features-demo`
4. Chọn tính năng muốn demo và tương tác

---

## 🔗 **API ENDPOINTS MỚI**

### **AI Assistant API**
```
POST /api/ai/analyze/:modelId     - Phân tích mô hình
GET  /api/ai/health/:modelId      - Điểm sức khỏe mô hình
GET  /api/ai/recommendations/:id  - Gợi ý cải thiện
GET  /api/ai/history/:modelId     - Lịch sử phân tích
```

### **Virtual Showroom API**
```
GET    /api/showrooms             - Danh sách phòng trưng bày
POST   /api/showrooms             - Tạo phòng trưng bày mới
GET    /api/showrooms/:id         - Chi tiết phòng trưng bày
PUT    /api/showrooms/:id         - Cập nhật phòng trưng bày
DELETE /api/showrooms/:id         - Xóa phòng trưng bày
POST   /api/showrooms/:id/models  - Thêm mô hình vào phòng
```

### **Smart Collection API**
```
GET    /api/collections/smart     - Danh sách bộ sưu tập thông minh
POST   /api/collections/smart     - Tạo bộ sưu tập mới
GET    /api/collections/smart/:id - Chi tiết bộ sưu tập
PUT    /api/collections/smart/:id/criteria - Cập nhật tiêu chí AI
GET    /api/collections/smart/:id/suggestions - Gợi ý AI
```

---

## 🎨 **CÔNG NGHỆ SỬ DỤNG**

### **Frontend**
- **React 18** với hooks và context
- **React Three Fiber** cho 3D rendering
- **@react-three/drei** cho 3D components
- **@react-three/xr** cho VR/AR support
- **Framer Motion** cho animations
- **Tailwind CSS** cho styling

### **Backend**
- **Node.js + Express** cho API server
- **MongoDB** với Mongoose ODM
- **Google Gemini AI** cho AI analysis
- **WebRTC** cho real-time collaboration
- **Socket.io** cho real-time updates

### **AI & Machine Learning**
- **Google Gemini Pro** cho model analysis
- **Computer Vision** cho image similarity
- **Natural Language Processing** cho content analysis
- **Recommendation Engine** cho smart suggestions
