import { defineConfig, loadEnv } from 'vite'
import react from '@vitejs/plugin-react'
import { resolve } from 'path'
import { visualizer } from 'rollup-plugin-visualizer'
import { VitePWA } from 'vite-plugin-pwa'
import compression from 'vite-plugin-compression'

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => {
  // Load env variables based on mode
  const env = loadEnv(mode, process.cwd(), '');

  return {
  plugins: [
    react({
      // Ensure React 18 compatibility
      jsxRuntime: 'automatic',
      babel: {
        plugins: [
          ['@babel/plugin-transform-react-jsx', { runtime: 'automatic' }]
        ],
        // Add more options for better JSX handling
        presets: [
          ['@babel/preset-react', { runtime: 'automatic' }]
        ]
      }
    }),
    // Generate bundle size visualization
    visualizer({
      filename: './dist/stats.html',
      open: false,
      gzipSize: true,
      brotliSize: true,
    }),
    // Add PWA support with service worker completely disabled
    VitePWA({
      registerType: null, // Disable service worker registration
      injectRegister: null, // Don't inject any registration code
      strategies: 'injectManifest', // Use injectManifest strategy but we won't actually create a service worker
      disable: true, // Completely disable the plugin in development
      includeAssets: ['favicon.ico', 'robots.txt', 'apple-touch-icon.png'],
      manifest: {
        name: '3DSKETCHUP.NET',
        short_name: '3DSKETCHUP',
        description: 'Your premier destination for high-quality 3D models',
        theme_color: '#3b82f6',
        background_color: '#f8fafc',
        icons: [
          {
            src: 'pwa-192x192.png',
            sizes: '192x192',
            type: 'image/png',
          },
          {
            src: 'pwa-512x512.png',
            sizes: '512x512',
            type: 'image/png',
          },
          {
            src: 'pwa-512x512.png',
            sizes: '512x512',
            type: 'image/png',
            purpose: 'maskable',
          },
        ],
      },
      // Disable workbox completely
      workbox: false,
    }),
    // Add compression
    compression({
      algorithm: 'brotliCompress',
      ext: '.br',
    }),
  ],
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src'),
      // Ensure single React instance
      'react': resolve(__dirname, 'node_modules/react'),
      'react-dom': resolve(__dirname, 'node_modules/react-dom'),
      'react-router-dom': resolve(__dirname, 'node_modules/react-router-dom'),
      '@react-three/fiber': resolve(__dirname, 'node_modules/@react-three/fiber'),
      '@react-three/drei': resolve(__dirname, 'node_modules/@react-three/drei'),
    },
    // Deduplicate packages
    dedupe: ['react', 'react-dom', 'react-router-dom', '@react-three/fiber', '@react-three/drei'],
    // Prevent multiple React instances
    preserveSymlinks: false,
  },
  build: {
    outDir: 'dist',
    sourcemap: process.env.NODE_ENV !== 'production',
    minify: 'terser',
    terserOptions: {
      compress: {
        drop_console: process.env.NODE_ENV === 'production',
        drop_debugger: process.env.NODE_ENV === 'production',
      },
    },
    rollupOptions: {
      output: {
        manualChunks: (id) => {
          // Core vendor libraries
          if (id.includes('node_modules/react/') ||
              id.includes('node_modules/react-dom/') ||
              id.includes('node_modules/scheduler/')) {
            return 'vendor-react';
          }

          // Router related
          if (id.includes('node_modules/react-router') ||
              id.includes('node_modules/history/') ||
              id.includes('node_modules/@remix-run/')) {
            return 'vendor-router';
          }

          // UI libraries
          if (id.includes('node_modules/framer-motion/') ||
              id.includes('node_modules/popmotion/')) {
            return 'vendor-animation';
          }

          if (id.includes('node_modules/react-icons/')) {
            return 'vendor-icons';
          }

          if (id.includes('node_modules/react-slick/') ||
              id.includes('node_modules/slick-carousel/')) {
            return 'vendor-carousel';
          }

          // 3D rendering
          if (id.includes('node_modules/three/') ||
              id.includes('node_modules/@react-three/')) {
            return 'vendor-three';
          }

          // Utilities
          if (id.includes('node_modules/axios/') ||
              id.includes('node_modules/jwt-decode/')) {
            return 'vendor-utils';
          }

          // Form libraries
          if (id.includes('node_modules/react-hook-form/') ||
              id.includes('node_modules/yup/')) {
            return 'vendor-forms';
          }

          // Other node_modules
          if (id.includes('node_modules/')) {
            return 'vendor-others';
          }

          // Group by feature for app code
          if (id.includes('/components/')) {
            return 'app-components';
          }

          if (id.includes('/pages/admin/')) {
            return 'app-admin';
          }

          if (id.includes('/pages/')) {
            return 'app-pages';
          }

          if (id.includes('/context/')) {
            return 'app-context';
          }

          if (id.includes('/utils/') || id.includes('/hooks/')) {
            return 'app-utils';
          }
        },
        // Optimize chunk size
        chunkFileNames: 'assets/js/[name]-[hash].js',
        entryFileNames: 'assets/js/[name]-[hash].js',
        assetFileNames: ({name}) => {
          if (/\.(gif|jpe?g|png|svg|webp)$/.test(name ?? '')) {
            return 'assets/images/[name]-[hash][extname]';
          }

          if (/\.(woff|woff2|eot|ttf|otf)$/.test(name ?? '')) {
            return 'assets/fonts/[name]-[hash][extname]';
          }

          if (/\.css$/.test(name ?? '')) {
            return 'assets/css/[name]-[hash][extname]';
          }

          return 'assets/[ext]/[name]-[hash][extname]';
        },
      },
    },
    // Optimize chunk size
    chunkSizeWarningLimit: 1000,
    // Enable CSS code splitting
    cssCodeSplit: true,
  },
  server: {
    port: 5173,
    strictPort: true, // Don't try other ports if 5173 is in use
    host: true, // Listen on all addresses
    hmr: false, // Disable HMR to avoid WebSocket issues
    proxy: {
      '/api': {
        target: 'http://localhost:5002', // Updated to match backend port
        changeOrigin: true,
        secure: false,
        ws: false, // Disable WebSocket for proxy
        rewrite: (path) => path.replace(/^\/api/, '/api'),
      },
    },
    cors: true,
    open: true,
  },
  // Optimize dev experience
  optimizeDeps: {
    include: [
      'react',
      'react-dom',
      'react-router-dom',
      'framer-motion',
      'axios',
      'three',
      '@react-three/fiber',
      '@react-three/drei'
    ],
    esbuildOptions: {
      target: 'es2020',
      loader: {
        '.js': 'jsx'
      }
    },
    // Force a single React instance
    force: true,
  },

  // Removed automatic JSX injection to avoid duplicate React imports
  // Improve performance by disabling brotli compression in development
  ...(mode === 'development' ? {
    build: {
      ...(!env.VITE_ENABLE_BROTLI && {
        brotliSize: false,
      }),
    },
  } : {}),
  }
})
