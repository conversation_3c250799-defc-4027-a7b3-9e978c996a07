import sharp from 'sharp';
import { GoogleGenerativeAI } from '@google/generative-ai';

// Initialize Gemini AI for vision processing
const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY);

class VisualSimilarityService {
  constructor() {
    this.supportedFormats = ['jpeg', 'jpg', 'png', 'webp'];
    this.maxFileSize = 5 * 1024 * 1024; // 5MB for extensions
  }

  /**
   * Calculate visual similarity between two images using feature comparison
   * @param {Buffer} image1 - First image buffer
   * @param {Buffer} image2 - Second image buffer
   * @returns {Promise<number>} - Similarity score (0-1)
   */
  async calculateImageSimilarity(image1, image2) {
    try {
      // Process both images to standard format
      const processedImage1 = await this.preprocessImageForComparison(image1);
      const processedImage2 = await this.preprocessImageForComparison(image2);

      // Extract features using Gemini Vision API
      const features1 = await this.extractImageFeatures(processedImage1);
      const features2 = await this.extractImageFeatures(processedImage2);

      // Calculate similarity based on features
      const similarity = this.compareFeatures(features1, features2);

      return similarity;

    } catch (error) {
      console.error('Error calculating image similarity:', error);
      return 0;
    }
  }

  /**
   * Preprocess image for comparison
   * @param {Buffer} imageBuffer - Image buffer
   * @returns {Promise<Buffer>} - Processed image buffer
   */
  async preprocessImageForComparison(imageBuffer) {
    try {
      return await sharp(imageBuffer)
        .resize(256, 256, { fit: 'cover' })
        .grayscale()
        .normalize()
        .jpeg({ quality: 80 })
        .toBuffer();
    } catch (error) {
      throw new Error(`Image preprocessing failed: ${error.message}`);
    }
  }

  /**
   * Extract visual features from image using Gemini Vision API
   * @param {Buffer} imageBuffer - Processed image buffer
   * @returns {Promise<Object>} - Extracted features
   */
  async extractImageFeatures(imageBuffer) {
    try {
      const base64Image = imageBuffer.toString('base64');

      const model = genAI.getGenerativeModel({ model: "gemini-1.5-flash" });

      const prompt = `Analyze this image and extract key visual features. Focus on:
1. UI elements and interface components
2. Color scheme and dominant colors
3. Layout and composition
4. Text and typography elements
5. Icons and symbols
6. Overall design style
7. Geometric shapes and patterns

Provide a structured analysis of these visual features.`;

      const imagePart = {
        inlineData: {
          data: base64Image,
          mimeType: "image/jpeg"
        }
      };

      const result = await model.generateContent([prompt, imagePart]);
      const response = await result.response;
      const analysisText = response.text();

      // Parse the analysis into structured features
      return this.parseVisualFeatures(analysisText);

    } catch (error) {
      console.error('Feature extraction failed:', error);
      return {
        colors: [],
        elements: [],
        style: 'unknown',
        layout: 'unknown',
        complexity: 0.5
      };
    }
  }

  /**
   * Parse visual features from Gemini analysis
   * @param {string} analysisText - Raw analysis text
   * @returns {Object} - Structured features
   */
  parseVisualFeatures(analysisText) {
    const text = analysisText.toLowerCase();

    const features = {
      colors: [],
      elements: [],
      style: 'modern',
      layout: 'standard',
      complexity: 0.5,
      keywords: []
    };

    // Extract colors
    const colors = [
      'red', 'blue', 'green', 'yellow', 'orange', 'purple', 'pink',
      'black', 'white', 'gray', 'grey', 'brown', 'cyan', 'magenta'
    ];

    colors.forEach(color => {
      if (text.includes(color)) {
        features.colors.push(color);
      }
    });

    // Extract UI elements
    const uiElements = [
      'button', 'menu', 'toolbar', 'panel', 'dialog', 'window',
      'icon', 'text', 'image', 'chart', 'graph', 'table',
      'form', 'input', 'dropdown', 'slider', 'checkbox'
    ];

    uiElements.forEach(element => {
      if (text.includes(element)) {
        features.elements.push(element);
      }
    });

    // Extract design styles
    const styles = [
      'modern', 'classic', 'minimal', 'flat', 'material',
      'skeuomorphic', 'gradient', 'clean', 'professional'
    ];

    for (const style of styles) {
      if (text.includes(style)) {
        features.style = style;
        break;
      }
    }

    // Extract layout types
    const layouts = [
      'grid', 'list', 'card', 'sidebar', 'header', 'footer',
      'centered', 'split', 'tabbed', 'accordion'
    ];

    for (const layout of layouts) {
      if (text.includes(layout)) {
        features.layout = layout;
        break;
      }
    }

    // Estimate complexity based on number of elements mentioned
    const complexityIndicators = [
      'complex', 'detailed', 'simple', 'minimal', 'busy',
      'cluttered', 'clean', 'organized', 'structured'
    ];

    let complexityScore = 0.5;
    if (text.includes('complex') || text.includes('detailed') || text.includes('busy')) {
      complexityScore = 0.8;
    } else if (text.includes('simple') || text.includes('minimal') || text.includes('clean')) {
      complexityScore = 0.3;
    }
    features.complexity = complexityScore;

    // Extract general keywords
    features.keywords = [...features.colors, ...features.elements, features.style, features.layout];

    return features;
  }

  /**
   * Compare two feature sets and calculate similarity
   * @param {Object} features1 - First image features
   * @param {Object} features2 - Second image features
   * @returns {number} - Similarity score (0-1)
   */
  compareFeatures(features1, features2) {
    let totalScore = 0;
    let maxScore = 0;

    // Compare colors (weight: 25%)
    maxScore += 25;
    const colorSimilarity = this.calculateArraySimilarity(features1.colors, features2.colors);
    totalScore += colorSimilarity * 25;

    // Compare UI elements (weight: 30%)
    maxScore += 30;
    const elementSimilarity = this.calculateArraySimilarity(features1.elements, features2.elements);
    totalScore += elementSimilarity * 30;

    // Compare style (weight: 20%)
    maxScore += 20;
    if (features1.style === features2.style) {
      totalScore += 20;
    } else if (this.areStylesSimilar(features1.style, features2.style)) {
      totalScore += 10;
    }

    // Compare layout (weight: 15%)
    maxScore += 15;
    if (features1.layout === features2.layout) {
      totalScore += 15;
    } else if (this.areLayoutsSimilar(features1.layout, features2.layout)) {
      totalScore += 7;
    }

    // Compare complexity (weight: 10%)
    maxScore += 10;
    const complexityDiff = Math.abs(features1.complexity - features2.complexity);
    const complexitySimilarity = 1 - complexityDiff;
    totalScore += complexitySimilarity * 10;

    return maxScore > 0 ? Math.min(totalScore / maxScore, 1) : 0;
  }

  /**
   * Calculate similarity between two arrays
   * @param {Array} arr1 - First array
   * @param {Array} arr2 - Second array
   * @returns {number} - Similarity score (0-1)
   */
  calculateArraySimilarity(arr1, arr2) {
    if (!arr1.length && !arr2.length) return 1;
    if (!arr1.length || !arr2.length) return 0;

    const intersection = arr1.filter(item => arr2.includes(item));
    const union = [...new Set([...arr1, ...arr2])];

    return intersection.length / union.length;
  }

  /**
   * Check if two styles are similar
   * @param {string} style1 - First style
   * @param {string} style2 - Second style
   * @returns {boolean} - Whether styles are similar
   */
  areStylesSimilar(style1, style2) {
    const similarStyles = {
      'modern': ['flat', 'minimal', 'clean'],
      'flat': ['modern', 'minimal', 'material'],
      'minimal': ['modern', 'flat', 'clean'],
      'material': ['flat', 'modern'],
      'classic': ['professional', 'traditional'],
      'professional': ['classic', 'clean']
    };

    return similarStyles[style1]?.includes(style2) || false;
  }

  /**
   * Check if two layouts are similar
   * @param {string} layout1 - First layout
   * @param {string} layout2 - Second layout
   * @returns {boolean} - Whether layouts are similar
   */
  areLayoutsSimilar(layout1, layout2) {
    const similarLayouts = {
      'grid': ['card', 'list'],
      'card': ['grid', 'list'],
      'list': ['grid', 'card'],
      'sidebar': ['split', 'header'],
      'split': ['sidebar', 'tabbed'],
      'tabbed': ['split', 'accordion']
    };

    return similarLayouts[layout1]?.includes(layout2) || false;
  }

  /**
   * Batch compare an image against multiple extension images
   * @param {Buffer} queryImage - Query image buffer
   * @param {Array} extensionImages - Array of extension image data
   * @returns {Promise<Array>} - Array of similarity scores
   */
  async batchCompareImages(queryImage, extensionImages) {
    try {
      const queryFeatures = await this.extractImageFeatures(
        await this.preprocessImageForComparison(queryImage)
      );

      const similarities = await Promise.all(
        extensionImages.map(async (extImage) => {
          try {
            if (!extImage.imageBuffer) {
              return { id: extImage.id, similarity: 0 };
            }

            const extFeatures = await this.extractImageFeatures(
              await this.preprocessImageForComparison(extImage.imageBuffer)
            );

            const similarity = this.compareFeatures(queryFeatures, extFeatures);

            return {
              id: extImage.id,
              similarity,
              features: extFeatures
            };

          } catch (error) {
            console.error(`Error comparing with extension ${extImage.id}:`, error);
            return { id: extImage.id, similarity: 0 };
          }
        })
      );

      return similarities.sort((a, b) => b.similarity - a.similarity);

    } catch (error) {
      console.error('Batch comparison failed:', error);
      return [];
    }
  }
}

export default VisualSimilarityService;
