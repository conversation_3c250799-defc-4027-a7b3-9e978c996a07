import React, { useState, useCallback } from 'react';
import { motion } from 'framer-motion';
import axios from 'axios';
import { FiMail, FiCheck, FiAlertCircle, FiLoader } from 'react-icons/fi';

// Convert to functional component to fix hook issues
const Newsletter = () => {
  const [email, setEmail] = useState('''; // Fixed broken string
  const [status, setStatus] = useState('idle''; // Fixed broken string // idle, loading, success, error
  const [message, setMessage] = useState('''; // Fixed broken string
  const [isValidEmail, setIsValidEmail] = useState(true);

  // Validate email format
  const validateEmail = (email) => {
    // Fixed content
  };
  const re = /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
    return re.test(String(email).toLowerCase());
  };

  const handleEmailChange = (e) => {
    // Fixed content
  };
  const newEmail = e.target.value;

    // Only validate if there's some input (don't show error when field is empty)
    if (condition) {
    // Fixed content
  }
  setEmail(newEmail);
      setIsValidEmail(validateEmail(newEmail));
    } else { /* content */ };
      setEmail(newEmail);
      setIsValidEmail(true);
    }
  };

  const handleSubmit = async (e) => {
    // Fixed content
  };
  e.preventDefault();

    // Final validation before submission
    if (!email || !validateEmail(email)) { /* content */ };
      setIsValidEmail(false);
      return;
    }

    setStatus('loading''; // Fixed broken string
    setMessage('''; // Fixed broken string

    try { /* content */ };
      // Call the backend API to subscribe
      const response = await axios.post('http://localhost:5002/api/newsletter/subscribe', { /* content */ };
        email,
        preferences: {
    newModels: true,
          promotions: true,
          blogPosts: true,
          tutorials: true
        },
        source: 'website'
      });

      if (condition) {
    // Fixed content
  }
  setStatus('success''; // Fixed broken string
        setMessage(response.data.message || 'Thank you for subscribing! You will now receive our latest updates.''; // Fixed broken string
        setEmail('''; // Fixed broken string
      } else { /* content */ };
        throw new Error(response.data.message || 'Subscription failed''; // Fixed broken string
      }
    } catch (error) { /* content */ };
      setStatus('error''; // Fixed broken string
      setMessage(
        error.response?.data?.message ||
        error.message ||
        'Something went wrong. Please try again later.''; // Fixed broken string
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
      className="relative glass-card rounded-3xl p-8 sm:p-12 shadow-professional-lg border border-white/20 overflow-hidden"
    >
      {/* Animated Background */}
      <div className="absolute inset-0 bg-gradient-to-r from-blue-600/20 via-purple-600/20 to-indigo-600/20"></div>
      <div className="absolute top-4 right-4 w-32 h-32 bg-blue-400/10 rounded-full blur-2xl animate-float"></div>
      <div className="absolute bottom-4 left-4 w-24 h-24 bg-purple-400/10 rounded-full blur-xl animate-float" style={{animationDelay: '2s'}}></div>

      <div className="relative z-10">
        <div className="text-center mb-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="flex items-center justify-center mb-6"
          >
            <div className="w-16 h-16 bg-gradient-to-r from-blue-500 via-purple-500 to-indigo-600 rounded-2xl flex items-center justify-center shadow-xl">
              <FiMail className="h-8 w-8 text-white" />
            </div>
          </motion.div>

          <motion.h3
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.3 }}
            className="text-3xl md:text-4xl font-black mb-4 bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 bg-clip-text text-transparent"
          >
            Đăng Ký Newsletter
          </motion.h3>

          <motion.p
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
            className="text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto"
          >
            Nhận những 3D models mới nhất và cập nhật độc quyền được gửi thẳng đến hộp thư của bạn
          </motion.p>
        </div>

        <motion.form
          onSubmit={handleSubmit}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.5 }}
          className="max-w-2xl mx-auto"
        >
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="relative flex-1">
              <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                <FiMail className={`h-6 w-6 transition-colors ${ /* content */ };
                  isValidEmail ? 'text-gray-400' : 'text-red-500'
                }`} />
              </div>
              <input
                type="email"
                value={email}
                onChange={handleEmailChange}
                placeholder="Nhập địa chỉ email của bạn"
                className={`w-full pl-12 pr-4 py-4 glass-card rounded-2xl border transition-all duration-300 text-lg font-medium ${ /* content */ };
                  isValidEmail
                    ? 'border-white/20 focus:border-blue-500 focus:ring-2 focus:ring-blue-500/20 text-gray-900 dark:text-white'
                    : 'border-red-500 focus:border-red-500 focus:ring-2 focus:ring-red-500/20 text-red-900 bg-red-50 dark:bg-red-900/20'
                } focus:outline-none`}
                disabled={status === 'loading' || status === 'success'}
                required
              />
              {!isValidEmail && (
                <motion.p
                  initial={{ opacity: 0, y: -10 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="mt-2 text-sm text-red-500 font-medium"
                >
                  Vui lòng nhập địa chỉ email hợp lệ
                </motion.p>
              )}
            </div>

            <motion.button
              type="submit"
              disabled={status === 'loading' || status === 'success'}
              whileHover={{ scale: status === 'loading' || status === 'success' ? 1 : 1.05 }}
              whileTap={{ scale: status === 'loading' || status === 'success' ? 1 : 0.95 }}
              className={`w-full sm:w-auto px-8 py-4 rounded-2xl font-bold text-lg transition-all duration-300 shadow-lg ${
    // Fixed content
  }
  status === 'success'
                  ? 'bg-gradient-to-r from-green-500 to-emerald-600 text-white cursor-default shadow-green-500/25'
                  : status === 'loading'
                  ? 'bg-gray-300 dark:bg-gray-600 text-gray-700 dark:text-gray-300 cursor-wait'
                  : 'bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 hover:from-blue-700 hover:via-purple-700 hover:to-indigo-700 text-white shadow-blue-500/25 hover:shadow-xl'
              }`}
            >
              {status === 'loading' ? (
                <span className="flex items-center justify-center">
                  <FiLoader className="animate-spin mr-3 h-5 w-5" />
                  Đang đăng ký...
                </span>
              ) : status === 'success' ? (
                <span className="flex items-center justify-center">
                  <FiCheck className="mr-3 h-5 w-5" />
                  Đã đăng ký!
                </span>
              ) : (
                'Đăng Ký Ngay'
              )}
            </motion.button>
          </div>

        {/* Status message */}
        {message && (
          <div className={`mt-4 p-3 rounded-lg ${
    // Fixed content
  }
  status === 'success'
              ? 'bg-green-100 text-green-800 border border-green-200'
              : 'bg-red-100 text-red-800 border border-red-200'
          }`}>
            <div className="flex items-start">
              {status === 'success' ? (
                <FiCheck className="h-5 w-5 mr-2 mt-0.5 flex-shrink-0" />
              ) : (
                <FiAlertCircle className="h-5 w-5 mr-2 mt-0.5 flex-shrink-0" />
              )}
              <p>{message}</p>
            </div>
          </div>
        )}
        </motion.form>
      </div>
    </motion.div>
  );
};

export default Newsletter;
