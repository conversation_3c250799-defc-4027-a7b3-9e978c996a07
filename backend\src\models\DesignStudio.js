import mongoose from 'mongoose';

const DesignStudioSchema = new mongoose.Schema({
  name: {
    type: String,
    required: [true, 'Please add a studio name'],
    trim: true,
    maxlength: [100, 'Name cannot be more than 100 characters']
  },
  description: {
    type: String,
    maxlength: [1000, 'Description cannot be more than 1000 characters']
  },
  owner: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  type: {
    type: String,
    enum: ['interior', 'exterior', 'landscape', 'architectural', 'product', 'mixed'],
    default: 'mixed'
  },
  // Scene configuration
  scene: {
    environment: {
      type: String,
      enum: ['indoor', 'outdoor', 'studio', 'custom'],
      default: 'studio'
    },
    lighting: {
      type: String,
      enum: ['natural', 'artificial', 'mixed', 'custom'],
      default: 'natural'
    },
    background: {
      type: String,
      enum: ['skybox', 'solid', 'gradient', 'hdri', 'transparent'],
      default: 'skybox'
    },
    floor: {
      enabled: { type: Boolean, default: true },
      material: String,
      size: { type: Number, default: 100 },
      height: { type: Number, default: 0 }
    },
    physics: {
      enabled: { type: Boolean, default: false },
      gravity: { type: Number, default: -9.81 }
    }
  },
  // Models in the scene
  sceneModels: [{
    model: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Model',
      required: true
    },
    transform: {
      position: {
        x: { type: Number, default: 0 },
        y: { type: Number, default: 0 },
        z: { type: Number, default: 0 }
      },
      rotation: {
        x: { type: Number, default: 0 },
        y: { type: Number, default: 0 },
        z: { type: Number, default: 0 }
      },
      scale: {
        x: { type: Number, default: 1 },
        y: { type: Number, default: 1 },
        z: { type: Number, default: 1 }
      }
    },
    materials: [{
      name: String,
      type: {
        type: String,
        enum: ['standard', 'physical', 'toon', 'lambert', 'phong']
      },
      properties: {
        color: String,
        metalness: Number,
        roughness: Number,
        opacity: Number,
        transparent: Boolean,
        texture: String,
        normalMap: String,
        roughnessMap: String,
        metalnessMap: String
      }
    }],
    animations: [{
      name: String,
      type: {
        type: String,
        enum: ['rotation', 'position', 'scale', 'custom']
      },
      duration: Number,
      loop: Boolean,
      autoPlay: Boolean
    }],
    interactions: [{
      type: {
        type: String,
        enum: ['click', 'hover', 'proximity', 'collision']
      },
      action: {
        type: String,
        enum: ['highlight', 'info', 'animate', 'sound', 'navigate']
      },
      parameters: mongoose.Schema.Types.Mixed
    }],
    layer: { type: Number, default: 0 },
    visible: { type: Boolean, default: true },
    locked: { type: Boolean, default: false },
    addedAt: { type: Date, default: Date.now }
  }],
  // Cameras and viewpoints
  cameras: [{
    name: String,
    position: {
      x: Number,
      y: Number,
      z: Number
    },
    target: {
      x: Number,
      y: Number,
      z: Number
    },
    fov: { type: Number, default: 50 },
    isDefault: { type: Boolean, default: false }
  }],
  // Lighting setup
  lights: [{
    name: String,
    type: {
      type: String,
      enum: ['directional', 'point', 'spot', 'ambient', 'hemisphere']
    },
    position: {
      x: Number,
      y: Number,
      z: Number
    },
    color: { type: String, default: '#ffffff' },
    intensity: { type: Number, default: 1 },
    castShadow: { type: Boolean, default: true },
    visible: { type: Boolean, default: true }
  }],
  // Collaboration features
  collaboration: {
    enabled: { type: Boolean, default: false },
    realTimeEditing: { type: Boolean, default: false },
    collaborators: [{
      user: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User'
      },
      role: {
        type: String,
        enum: ['viewer', 'editor', 'admin'],
        default: 'viewer'
      },
      permissions: [{
        type: String,
        enum: ['add', 'edit', 'delete', 'move', 'material', 'lighting', 'camera']
      }],
      joinedAt: { type: Date, default: Date.now },
      lastActive: Date
    }],
    chat: {
      enabled: { type: Boolean, default: true },
      messages: [{
        user: {
          type: mongoose.Schema.Types.ObjectId,
          ref: 'User'
        },
        message: String,
        timestamp: { type: Date, default: Date.now },
        type: {
          type: String,
          enum: ['text', 'system', 'annotation'],
          default: 'text'
        }
      }]
    }
  },
  // Export settings
  exportSettings: {
    formats: [{
      type: String,
      enum: ['gltf', 'fbx', 'obj', 'dae', 'stl', 'ply', 'x3d', 'usd']
    }],
    quality: {
      type: String,
      enum: ['low', 'medium', 'high', 'ultra'],
      default: 'high'
    },
    includeTextures: { type: Boolean, default: true },
    includeLighting: { type: Boolean, default: true },
    includeAnimations: { type: Boolean, default: true },
    compression: { type: Boolean, default: true }
  },
  // Version control
  versions: [{
    version: String,
    description: String,
    snapshot: mongoose.Schema.Types.Mixed, // Serialized scene state
    createdBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    createdAt: { type: Date, default: Date.now },
    size: Number // in bytes
  }],
  // Analytics
  analytics: {
    totalViews: { type: Number, default: 0 },
    totalEdits: { type: Number, default: 0 },
    collaborationTime: { type: Number, default: 0 }, // in minutes
    exportCount: { type: Number, default: 0 },
    lastAccessed: Date
  },
  // Access control
  access: {
    isPublic: { type: Boolean, default: false },
    password: String,
    allowedUsers: [{
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    }]
  },
  tags: [String],
  featured: { type: Boolean, default: false },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

// Indexes for performance
DesignStudioSchema.index({ owner: 1 });
DesignStudioSchema.index({ type: 1 });
DesignStudioSchema.index({ 'access.isPublic': 1 });
DesignStudioSchema.index({ featured: 1 });
DesignStudioSchema.index({ tags: 1 });
DesignStudioSchema.index({ createdAt: -1 });

// Update timestamps pre-save
DesignStudioSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  next();
});

// Virtual for model count
DesignStudioSchema.virtual('modelCount').get(function() {
  return this.sceneModels.length;
});

// Method to create version snapshot
DesignStudioSchema.methods.createSnapshot = function(description, userId) {
  const snapshot = {
    scene: this.scene,
    sceneModels: this.sceneModels,
    cameras: this.cameras,
    lights: this.lights
  };
  
  this.versions.push({
    version: `v${this.versions.length + 1}`,
    description,
    snapshot,
    createdBy: userId,
    size: JSON.stringify(snapshot).length
  });
  
  return this.save();
};

export default mongoose.model('DesignStudio', DesignStudioSchema);
