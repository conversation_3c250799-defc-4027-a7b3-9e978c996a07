import crypto from 'crypto';
import NewsletterSubscriber from '../models/NewsletterSubscriber.js';

// @desc    Subscribe to newsletter
// @route   POST /api/newsletter/subscribe
// @access  Public
export const subscribeToNewsletter = async (req, res, next) => {
  try {
    const { email } = req.body;

    if (!email) {
      return res.status(400).json({
        success: false,
        message: 'Please provide an email address'
      });
    }

    // Check if email already exists
    let subscriber = await NewsletterSubscriber.findOne({ email });

    if (subscriber) {
      // If already subscribed and active, return success
      if (subscriber.status === 'active') {
        return res.status(200).json({
          success: true,
          message: 'You are already subscribed to our newsletter'
        });
      }

      // If previously unsubscribed, reactivate
      if (subscriber.status === 'unsubscribed') {
        subscriber.status = 'active';
        subscriber.unsubscribedAt = undefined;
        await subscriber.save();

        return res.status(200).json({
          success: true,
          message: 'Welcome back! Your subscription has been reactivated'
        });
      }
    }

    // Generate unsubscribe token
    const unsubscribeToken = crypto.randomBytes(20).toString('hex');

    // Create new subscriber
    subscriber = await NewsletterSubscriber.create({
      email,
      unsubscribeToken,
      source: req.body.source || 'website'
    });

    // In a real application, you would send a welcome email here
    // sendWelcomeEmail(subscriber.email, subscriber.unsubscribeToken);

    res.status(201).json({
      success: true,
      message: 'Thank you for subscribing to our newsletter!',
      data: {
        email: subscriber.email,
        status: subscriber.status,
        subscribedAt: subscriber.subscribedAt
      }
    });
  } catch (error) {
    // Handle duplicate key error
    if (error.code === 11000) {
      return res.status(400).json({
        success: false,
        message: 'This email is already subscribed to our newsletter'
      });
    }

    next(error);
  }
};

// @desc    Unsubscribe from newsletter
// @route   GET /api/newsletter/unsubscribe/:token
// @access  Public
export const unsubscribeFromNewsletter = async (req, res, next) => {
  try {
    const { token } = req.params;

    if (!token) {
      return res.status(400).json({
        success: false,
        message: 'Invalid unsubscribe link'
      });
    }

    // Find subscriber by token
    const subscriber = await NewsletterSubscriber.findOne({ unsubscribeToken: token });

    if (!subscriber) {
      return res.status(404).json({
        success: false,
        message: 'Invalid unsubscribe link'
      });
    }

    // Update subscriber status
    subscriber.status = 'unsubscribed';
    subscriber.unsubscribedAt = Date.now();
    await subscriber.save();

    res.status(200).json({
      success: true,
      message: 'You have been successfully unsubscribed from our newsletter'
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Update newsletter preferences
// @route   PUT /api/newsletter/preferences/:token
// @access  Public
export const updateNewsletterPreferences = async (req, res, next) => {
  try {
    const { token } = req.params;
    const { preferences } = req.body;

    if (!token) {
      return res.status(400).json({
        success: false,
        message: 'Invalid token'
      });
    }

    // Find subscriber by token
    const subscriber = await NewsletterSubscriber.findOne({ unsubscribeToken: token });

    if (!subscriber) {
      return res.status(404).json({
        success: false,
        message: 'Subscriber not found'
      });
    }

    // Update preferences
    if (preferences) {
      subscriber.preferences = {
        ...subscriber.preferences,
        ...preferences
      };
    }

    await subscriber.save();

    res.status(200).json({
      success: true,
      message: 'Your newsletter preferences have been updated',
      data: {
        email: subscriber.email,
        preferences: subscriber.preferences
      }
    });
  } catch (error) {
    next(error);
  }
};
