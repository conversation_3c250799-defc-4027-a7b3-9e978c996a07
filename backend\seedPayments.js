import mongoose from 'mongoose';
import dotenv from 'dotenv';
import Payment from './src/models/Payment.js';
import User from './src/models/User.js';

// Load environment variables
dotenv.config();

const seedPayments = async () => {
  try {
    console.log('🔄 Connecting to MongoDB...');
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('✅ MongoDB Connected');

    // Get users for payment references
    const users = await User.find();
    if (users.length === 0) {
      console.log('❌ No users found. Please seed users first.');
      process.exit(1);
    }

    // Delete existing payments
    console.log('🗑️ Deleting existing payments...');
    const deleteResult = await Payment.deleteMany({});
    console.log(`✅ Deleted ${deleteResult.deletedCount} payments`);

    // Create sample payments
    console.log('💳 Creating sample payments...');

    const payments = [
      {
        user: users[0]._id, // Admin
        amount: 29.99,
        currency: 'USD',
        status: 'completed',
        paymentMethod: 'credit_card',
        type: 'subscription',
        subscriptionPlan: 'premium',
        description: 'Premium Monthly Subscription',
        createdAt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) // 30 days ago
      },
      {
        user: users[1]._id, // Designer
        amount: 99.99,
        currency: 'USD',
        status: 'completed',
        paymentMethod: 'paypal',
        type: 'subscription',
        subscriptionPlan: 'professional',
        description: 'Professional Yearly Subscription',
        createdAt: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000) // 15 days ago
      },
      {
        user: users[2]._id, // John
        amount: 9.99,
        currency: 'USD',
        status: 'completed',
        paymentMethod: 'credit_card',
        type: 'subscription',
        subscriptionPlan: 'basic',
        description: 'Basic Monthly Subscription',
        createdAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) // 7 days ago
      },
      {
        user: users[3]._id, // Sarah
        amount: 19.99,
        currency: 'USD',
        status: 'pending',
        paymentMethod: 'credit_card',
        type: 'subscription',
        subscriptionPlan: 'premium',
        description: 'Premium Monthly Subscription',
        createdAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000) // 2 days ago
      },
      {
        user: users[0]._id, // Admin (previous month)
        amount: 29.99,
        currency: 'USD',
        status: 'completed',
        paymentMethod: 'credit_card',
        type: 'subscription',
        subscriptionPlan: 'premium',
        description: 'Premium Monthly Subscription',
        createdAt: new Date(Date.now() - 60 * 24 * 60 * 60 * 1000) // 60 days ago
      }
    ];

    // Create payments
    for (const paymentData of payments) {
      try {
        const payment = new Payment(paymentData);
        await payment.save();
        console.log(`✅ Created payment: $${paymentData.amount} - ${paymentData.status}`);
      } catch (error) {
        console.error(`❌ Error creating payment:`, error.message);
      }
    }

    // Calculate total revenue
    const completedPayments = await Payment.find({ status: 'completed' });
    const totalRevenue = completedPayments.reduce((sum, payment) => sum + payment.amount, 0);

    console.log('\n🎉 Payment seeding completed successfully!');
    console.log(`💰 Total Revenue: $${totalRevenue.toFixed(2)}`);
    console.log(`📊 Total Payments: ${completedPayments.length} completed, ${payments.length} total`);

    process.exit(0);
  } catch (error) {
    console.error('❌ Error:', error);
    process.exit(1);
  }
};

console.log('🚀 Starting payment seeding process...');
seedPayments();
