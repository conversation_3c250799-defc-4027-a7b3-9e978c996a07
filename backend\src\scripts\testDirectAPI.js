import fetch from 'node-fetch';

async function testDirectAPI() {
  try {
    console.log('🧪 Testing Direct API Call...');
    
    const testData = {
      title: 'Sketchup Living Room, Kitchen Interior 3d Model free download 081511088',
      description: 'Free download Sketchup models, 3d models & Archviz resource',
      category: 'Residential',
      tags: ['living room', 'kitchen', 'interior', 'furniture', 'modern'],
      fileUrl: 'https://icedrive.net/s/N4xCufPNAZub4RSDYSYSWaiBS28g',
      imageUrl: 'https://i.imgur.com/kE3hrU7.gif',
      fileSize: 345,
      format: 'Sketchup 2023',
      fileFormat: 'skp'
    };

    console.log('📤 Sending request to /api/models/test...');
    console.log('Data:', JSON.stringify(testData, null, 2));

    const response = await fetch('http://localhost:5002/api/models/test', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(testData)
    });

    console.log(`📥 Response status: ${response.status}`);
    
    const result = await response.json();
    console.log('📋 Response data:', JSON.stringify(result, null, 2));

    if (result.success) {
      console.log('✅ Model created successfully!');
      console.log(`   Model ID: ${result.data._id}`);
      console.log(`   Title: ${result.data.title}`);
    } else {
      console.log('❌ Failed to create model');
      console.log(`   Error: ${result.error}`);
    }

  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

testDirectAPI();
