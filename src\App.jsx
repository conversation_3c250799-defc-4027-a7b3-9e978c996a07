import React, { lazy, useEffect, Suspense } from 'react';

// Import other dependencies
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { HelmetProvider } from 'react-helmet-async';
import { AuthProvider } from './context/AuthContext';
import { ModelProvider } from './context/ModelContext';
import { PaymentProvider } from './context/PaymentContext';

// Import components
import Header from './components/Header';
import Footer from './components/Footer';
import Chatbot from './components/Chatbot';
import ErrorBoundary from './components/ErrorBoundary';
import ClientErrorBoundary from './components/ClientErrorBoundary';
import ProtectedRoute from './components/ProtectedRoute';

// Import performance optimization
import { initializePerformanceOptimizations } from './utils/performanceOptimizer';

// Import styles
import './App.css';

// Loading component with skeleton UI
const LoadingFallback = () => (
  <div className="flex items-center justify-center min-h-screen bg-gray-50 dark:bg-gray-900">
    <div className="w-full max-w-7xl mx-auto px-4">
      <div className="animate-pulse">
        <div className="h-8 bg-gray-300 rounded w-1/4 mb-4"></div>
        <div className="h-4 bg-gray-300 rounded w-1/2 mb-2"></div>
        <div className="h-4 bg-gray-300 rounded w-1/3"></div>
      </div>
    </div>
  </div>
);

// Lazy load pages with improved performance
const Home = lazy(() => import('./pages/Home'));
const NewHome = lazy(() => import('./pages/NewHome'));
const Login = lazy(() => import('./pages/Login'));
const Register = lazy(() => import('./pages/Register'));
const ForgotPassword = lazy(() => import('./pages/ForgotPassword'));
const ResetPassword = lazy(() => import('./pages/ResetPassword'));
const ModelDetail = lazy(() => import('./pages/ModelDetail'));
const Profile = lazy(() => import('./pages/Profile'));
const SavedModels = lazy(() => import('./pages/SavedModels'));
const Downloads = lazy(() => import('./pages/Downloads'));
const Subscription = lazy(() => import('./pages/Subscription'));
const UploadModel = lazy(() => import('./pages/UploadModel'));
const CategoryPage = lazy(() => import('./pages/CategoryPage'));
const SearchResults = lazy(() => import('./pages/SearchResults'));
const SearchPage = lazy(() => import('./pages/SearchPage'));
const VirtualShowrooms = lazy(() => import('./pages/VirtualShowrooms'));
const SmartCollections = lazy(() => import('./pages/SmartCollections'));
const Warehouse = lazy(() => import('./pages/Warehouse'));
const Plugins = lazy(() => import('./pages/Plugins'));
const FeaturesDemo = lazy(() => import('./pages/FeaturesDemo'));
const NotFound = lazy(() => import('./pages/NotFound'));

// Admin pages
const AdminDashboard = lazy(() => import('./pages/admin/AdminDashboard'));
const DashboardOverview = lazy(() => import('./pages/admin/DashboardOverview'));
const UserManagement = lazy(() => import('./pages/admin/UserManagement'));
const AddUser = lazy(() => import('./pages/admin/AddUser'));
const UserRoles = lazy(() => import('./pages/admin/UserRoles'));
const ModelManagement = lazy(() => import('./pages/admin/ModelManagement'));
const AddModel = lazy(() => import('./pages/admin/AddModel'));
const ModelCategories = lazy(() => import('./pages/admin/ModelCategories'));
const Analytics = lazy(() => import('./pages/admin/Analytics'));
const DownloadAnalytics = lazy(() => import('./pages/admin/DownloadAnalytics'));
const RevenueAnalytics = lazy(() => import('./pages/admin/RevenueAnalytics'));
const Settings = lazy(() => import('./pages/admin/Settings'));

// Information pages
const About = lazy(() => import('./pages/About'));
const Contact = lazy(() => import('./pages/Contact'));
const Terms = lazy(() => import('./pages/Terms'));
const Privacy = lazy(() => import('./pages/Privacy'));

// Test pages
const ImageSearchTest = lazy(() => import('./components/test/ImageSearchTest'));
const AdminAddModel = lazy(() => import('./pages/AdminAddModel'));
const AdminAddModelSimple = lazy(() => import('./pages/AdminAddModelSimple'));

// AppRoutes component
function AppRoutes() {
  return (
    <Routes>
      <Route path="/" element={<NewHome />} />
      <Route path="/old" element={<Home />} />
      <Route path="/login" element={<Login />} />
      <Route path="/register" element={<Register />} />
      <Route path="/forgot-password" element={<ForgotPassword />} />
      <Route path="/reset-password/:token" element={<ResetPassword />} />
      <Route path="/model/:id" element={<ModelDetail />} />
      <Route path="/profile" element={
        <ProtectedRoute>
          <Profile />
        </ProtectedRoute>
      } />
      <Route path="/saved-models" element={
        <ProtectedRoute>
          <SavedModels />
        </ProtectedRoute>
      } />
      <Route path="/downloads" element={
        <ProtectedRoute>
          <Downloads />
        </ProtectedRoute>
      } />
      <Route path="/subscription" element={
        <ProtectedRoute>
          <Subscription />
        </ProtectedRoute>
      } />
      <Route path="/upload" element={
        <ProtectedRoute
          adminOnly={true}
          requiredPermission="upload"
          unauthorizedMessage="Only administrators can upload models"
        >
          <UploadModel />
        </ProtectedRoute>
      } />

      {/* Category Routes */}
      <Route path="/models" element={<CategoryPage />} />
      <Route path="/category/:category" element={<CategoryPage />} />
      <Route path="/search" element={<SearchPage />} />
      <Route path="/search-results" element={<SearchResults />} />
      <Route path="/showrooms" element={<VirtualShowrooms />} />
      <Route path="/collections" element={<SmartCollections />} />
      <Route path="/warehouse" element={<Warehouse />} />
      <Route path="/plugins" element={<Plugins />} />
      <Route path="/features-demo" element={<FeaturesDemo />} />

      {/* Admin Routes */}
      <Route path="/admin" element={
        <ProtectedRoute adminOnly={true}>
          <AdminDashboard />
        </ProtectedRoute>
      }>
        <Route index element={<DashboardOverview />} />
        <Route path="users" element={<UserManagement />} />
        <Route path="users/add" element={<AddUser />} />
        <Route path="users/roles" element={<UserRoles />} />
        <Route path="models" element={<ModelManagement />} />
        <Route path="models/add" element={<AddModel />} />
        <Route path="models/categories" element={<ModelCategories />} />
        <Route path="analytics" element={<Analytics />} />
        <Route path="analytics/downloads" element={<DownloadAnalytics />} />
        <Route path="analytics/revenue" element={<RevenueAnalytics />} />
        <Route path="settings" element={<Settings />} />
      </Route>

      {/* Information pages */}
      <Route path="/about" element={<About />} />
      <Route path="/contact" element={<Contact />} />
      <Route path="/terms" element={<Terms />} />
      <Route path="/privacy" element={<Privacy />} />

      {/* Test pages */}
      <Route path="/test/image-search" element={<ImageSearchTest />} />
      <Route path="/admin/add-model" element={
        <ProtectedRoute adminOnly={true}>
          <AdminAddModel />
        </ProtectedRoute>
      } />
      <Route path="/admin/add-model-simple" element={
        <ProtectedRoute adminOnly={true}>
          <AdminAddModelSimple />
        </ProtectedRoute>
      } />

      <Route path="*" element={<NotFound />} />
    </Routes>
  );
}

// Main App component
function App() {
  useEffect(() => {
    // Initialize performance optimizations
    initializePerformanceOptimizations();

    // Add dark mode class to html element based on localStorage or system preference
    const darkMode = localStorage.getItem('darkMode');
    if (darkMode === 'true' || (!darkMode && window.matchMedia('(prefers-color-scheme: dark)').matches)) {
      document.documentElement.classList.add('dark');
    } else {
      document.documentElement.classList.remove('dark');
    }

    // Add page transition class
    document.body.classList.add('page-transition-enhanced');

    // Unregister all service workers
    if ('serviceWorker' in navigator) {
      navigator.serviceWorker.getRegistrations().then(registrations => {
        for (let registration of registrations) {
          registration.unregister();
        }
      }).catch(error => {
        console.error('Error unregistering service workers:', error);
      });

      // Clear any service worker caches
      if ('caches' in window) {
        caches.keys().then(cacheNames => {
          cacheNames.forEach(cacheName => {
            caches.delete(cacheName);
          });
        });
      }
    }
  }, []);

  return (
    <ErrorBoundary>
      <HelmetProvider>
        <AuthProvider>
          <ModelProvider>
            <PaymentProvider>
              <Router>
                <div className="flex flex-col min-h-screen transition-colors duration-200">
                  {/* Header */}
                  <Header />

                  {/* Main Content with proper spacing */}
                  <main className="flex-1 main-content">
                    <Suspense fallback={<LoadingFallback />}>
                      <ErrorBoundary>
                        <AppRoutes />
                      </ErrorBoundary>
                    </Suspense>
                  </main>

                  {/* Footer */}
                  <Footer />

                  {/* Chatbot component wrapped in ClientErrorBoundary */}
                  <ClientErrorBoundary>
                    <Chatbot />
                  </ClientErrorBoundary>
                </div>
              </Router>
            </PaymentProvider>
          </ModelProvider>
        </AuthProvider>
      </HelmetProvider>
    </ErrorBoundary>
  );
}

export default App;