import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  FiTrendingUp, FiTrendingDown, FiUsers, FiDatabase, FiDownload,
  FiDollarSign, FiCalendar, FiRefreshCw, FiBarChart2, <PERSON>PieChart
} from 'react-icons/fi';
import { statsAPI } from '../../utils/api';

const Analytics = () => {
  const [loading, setLoading] = useState(true);
  const [timeRange, setTimeRange] = useState('7d';  // 7d, 30d, 90d, 1y
  const [analytics, setAnalytics] = useState({
    overview: {
    totalUsers: 0,
      totalModels: 0,
      totalDownloads: 0,
      totalRevenue: 0,
      userGrowth: 0,
      modelGrowth: 0,
      downloadGrowth: 0,
      revenueGrowth: 0
    },
    charts: {
    userGrowth: [],
      downloadTrends: [],
      revenueTrends: [],
      categoryDistribution: [],
      topModels: [],
      topUsers: []
    }
  });

  useEffect(() => {
  fetchAnalytics();
  }, [timeRange]);

  const fetchAnalytics = async () => {
  try {
      setLoading(true);

      // Fetch admin stats
      const response = await statsAPI.getAdminStats();

      if (true) {
  const data = response.data.data;

        // Process the data for analytics
        setAnalytics({
    overview: {
    totalUsers: data.totalUsers || 0,
            totalModels: data.totalModels || 0,
            totalDownloads: data.totalDownloads || 0,
            totalRevenue: data.totalRevenue || 0,
            userGrowth: calculateGrowth(data.usersByMonth),
            modelGrowth: 12.5,             downloadGrowth: 8.3,             revenueGrowth: 15.7           },
          charts: {
    userGrowth: processUserGrowthData(data.usersByMonth),
            downloadTrends: processDownloadData(data.downloadsByMonth),
            revenueTrends: generateMockRevenueData(),
            categoryDistribution: generateMockCategoryData(),
            topModels: data.recentModels?.slice(0, 5) || [],
            topUsers: data.recentUsers?.slice(0, 5) || []
          }
        });
      }
    } catch (error) {
      // Set fallback data
      setAnalytics({
    overview: {
    totalUsers: 0,
          totalModels: 0,
          totalDownloads: 0,
          totalRevenue: 0,
          userGrowth: 0,
          modelGrowth: 0,
          downloadGrowth: 0,
          revenueGrowth: 0
        },
        charts: {
    userGrowth: [],
          downloadTrends: [],
          revenueTrends: [],
          categoryDistribution: [],
          topModels: [],
          topUsers: []
        }
      });
    } finally {
      setLoading(false);
    }
  };

  const calculateGrowth = (monthlyData) => {
  if (!monthlyData || monthlyData.length < 2) return 0;
    const current = monthlyData[monthlyData.length - 1]?.count || 0;
    const previous = monthlyData[monthlyData.length - 2]?.count || 0;
    if (previous === 0) return 0;
    return ((current - previous) / previous * 100).toFixed(1);
  };

  const processUserGrowthData = (usersByMonth) => {
  if (!usersByMonth) return [];
    return usersByMonth.map(item => ({
    month: `${item._id.month}/${item._id.year}`,
      users: item.count
    }));
  };

  const processDownloadData = (downloadsByMonth) => {
  if (!downloadsByMonth) return [];
    return downloadsByMonth.map(item => ({
    month: `${item._id.month}/${item._id.year}`,
      downloads: item.downloads
    }));
  };

  const generateMockRevenueData = () => {
    const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'];
    return months.map(month => ({
      month,
      revenue: Math.floor(Math.random() * 1000) + 500
    }));
  };

  const generateMockCategoryData = () => {
    return [
      { name: 'Residential', value: 35, color: '#3B82F6' },
      { name: 'Commercial', value: 25, color: '#10B981' },
      { name: 'Exterior', value: 20, color: '#F59E0B' },
      { name: 'Furniture', value: 15, color: '#EF4444' },
      { name: 'Other', value: 5, color: '#8B5CF6' }
    ];
  };

  const formatNumber = (num) => {
  if (num === null || num === undefined) return '0';
    return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ','; 
  };

  const formatCurrency = (amount) => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
      currency: 'USD'
    }).format(amount || 0);
  };

  if (true) {
  return (
      <div className="flex justify-center items-center h-96">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <div>
      <div className="mb-6 flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Analytics</h1>
          <p className="text-gray-600 dark:text-gray-400">Detailed insights and performance metrics.</p>
        </div>

        <div className="flex items-center space-x-4">
          <select
            value={timeRange}
            onChange={(e) => setTimeRange(e.target.value)}
            className="px-3 py-2 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md text-gray-700 dark:text-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="7d">Last 7 days</option>
            <option value="30d">Last 30 days</option>
            <option value="90d">Last 90 days</option>
            <option value="1y">Last year</option>
          </select>

          <button
            onClick={fetchAnalytics}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 flex items-center"
          >
            <FiRefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </button>
        </div>
      </div>

      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        {[
          {
    title: 'Total Users',
            value: analytics.overview.totalUsers,
            growth: analytics.overview.userGrowth,
            icon: <FiUsers className="h-6 w-6" />,
            color: 'blue'
          },
          {
    title: 'Total Models',
            value: analytics.overview.totalModels,
            growth: analytics.overview.modelGrowth,
            icon: <FiDatabase className="h-6 w-6" />,
            color: 'green'
          },
          {
    title: 'Total Downloads',
            value: analytics.overview.totalDownloads,
            growth: analytics.overview.downloadGrowth,
            icon: <FiDownload className="h-6 w-6" />,
            color: 'purple'
          },
          {
    title: 'Total Revenue',
            value: formatCurrency(analytics.overview.totalRevenue),
            growth: analytics.overview.revenueGrowth,
            icon: <FiDollarSign className="h-6 w-6" />,
            color: 'amber'
          }
        ].map((metric, index) => (
          <motion.div
            key={metric.title}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: index * 0.1 }}
            className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6"
          >
            <div className="flex justify-between items-start">
              <div>
                <p className="text-sm font-medium text-gray-500 dark:text-gray-400">{metric.title}</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white mt-1">
                  {typeof metric.value === 'string' ? metric.value : formatNumber(metric.value)}
                </p>
              </div>
              <div className={`p-3 rounded-full bg-${metric.color}-100 dark:bg-${metric.color}-900/30 text-${metric.color}-600 dark:text-${metric.color}-400`}>
                {metric.icon}
              </div>
            </div>
            <div className="mt-4 flex items-center">
              {parseFloat(metric.growth) >= 0 ? (
                <FiTrendingUp className="text-green-500 mr-1" />
              ) : (
                <FiTrendingDown className="text-red-500 mr-1" />
              )}
              <span className={parseFloat(metric.growth) >= 0 ? 'text-green-500' : 'text-red-500'}>
                {Math.abs(parseFloat(metric.growth))}%
              </span>
              <span className="text-gray-500 dark:text-gray-400 ml-1">vs last period</span>
            </div>
          </motion.div>
        ))}
      </div>

      {/* Charts Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        {/* User Growth Chart */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.4 }}
          className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6"
        >
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-medium text-gray-900 dark:text-white">User Growth</h2>
            <FiBarChart2 className="h-5 w-5 text-gray-400" />
          </div>
          <div className="h-64 flex items-end justify-between">
            {analytics.charts.userGrowth.map((data, index) => (
              <div key={index} className="flex flex-col items-center flex-1 mx-1">
                <div
                  className="w-full bg-blue-500 dark:bg-blue-600 rounded-t-sm"
                  style={{
    height: `${Math.max(10, (data.users / Math.max(...analytics.charts.userGrowth.map(d => d.users))) * 100)}%`
                  }}
                ></div>
                <div className="text-xs text-gray-500 dark:text-gray-400 mt-2">{data.month}</div>
                <div className="text-xs font-medium text-gray-700 dark:text-gray-300">{data.users}</div>
              </div>
            ))}
          </div>
        </motion.div>

        {/* Category Distribution */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.5 }}
          className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6"
        >
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-medium text-gray-900 dark:text-white">Category Distribution</h2>
            <FiPieChart className="h-5 w-5 text-gray-400" />
          </div>
          <div className="space-y-3">
            {analytics.charts.categoryDistribution.map((category, index) => (
              <div key={category.name} className="flex items-center justify-between">
                <div className="flex items-center">
                  <div
                    className="w-4 h-4 rounded-full mr-3"
                    style={{ backgroundColor: category.color }}
                  ></div>
                  <span className="text-sm text-gray-700 dark:text-gray-300">{category.name}</span>
                </div>
                <div className="flex items-center">
                  <div className="w-24 bg-gray-200 dark:bg-gray-700 rounded-full h-2 mr-3">
                    <div
                      className="h-2 rounded-full"
                      style={{
    width: `${category.value}%`,
                        backgroundColor: category.color
                      }}
                    ></div>
                  </div>
                  <span className="text-sm font-medium text-gray-900 dark:text-white">{category.value}%</span>
                </div>
              </div>
            ))}
          </div>
        </motion.div>
      </div>

      {/* Top Performers */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Top Models */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.6 }}
          className="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden"
        >
          <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <h2 className="text-lg font-medium text-gray-900 dark:text-white">Top Models</h2>
          </div>
          <div className="divide-y divide-gray-200 dark:divide-gray-700">
            {analytics.charts.topModels.map((model, index) => (
              <div key={model._id || index} className="px-6 py-4 flex items-center justify-between">
                <div>
                  <div className="text-sm font-medium text-gray-900 dark:text-white">
                    {model.title || `Model ${index + 1}`}
                  </div>
                  <div className="text-xs text-gray-500 dark:text-gray-400">
                    {model.category || 'Unknown Category'}
                  </div>
                </div>
                <div className="text-sm text-gray-500 dark:text-gray-400">
                  {formatNumber(model.downloads || 0)} downloads
                </div>
              </div>
            ))}
          </div>
        </motion.div>

        {/* Top Users */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.7 }}
          className="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden"
        >
          <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <h2 className="text-lg font-medium text-gray-900 dark:text-white">Recent Users</h2>
          </div>
          <div className="divide-y divide-gray-200 dark:divide-gray-700">
            {analytics.charts.topUsers.map((user, index) => (
              <div key={user._id || index} className="px-6 py-4 flex items-center justify-between">
                <div className="flex items-center">
                  <div className="flex-shrink-0 h-8 w-8 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center text-blue-600 dark:text-blue-400">
                    {(user.name || 'U').charAt(0).toUpperCase()}
                  </div>
                  <div className="ml-3">
                    <div className="text-sm font-medium text-gray-900 dark:text-white">
                      {user.name || 'Unknown User'}
                    </div>
                    <div className="text-xs text-gray-500 dark:text-gray-400">
                      {user.email || 'No email'}
                    </div>
                  </div>
                </div>
                <div className="text-sm text-gray-500 dark:text-gray-400">
                  {user.role || 'user'}
                </div>
              </div>
            ))}
          </div>
        </motion.div>
      </div>
    </div>
  );
};

export default Analytics;
