import React, { useEffect, useState } from 'react';
import { FiSun, FiMoon } from 'react-icons/fi';
import { motion } from 'framer-motion';

const DarkModeToggle = () => {
  const [darkMode, setDarkMode] = useState(() => {
    // Check if user has a preference stored
    const savedMode = localStorage.getItem('darkMode');
    if (savedMode !== null) {
      return savedMode === 'true';
    }
    // Check if user prefers dark mode
    return window.matchMedia('(prefers-color-scheme: dark)').matches;
  });

  useEffect(() => {
    // Update the document class when dark mode changes
    if (darkMode) {
      document.documentElement.classList.add('dark');
    } else {
      document.documentElement.classList.remove('dark');
    }

    // Save preference to localStorage
    localStorage.setItem('darkMode', darkMode);
  }, [darkMode]);

  const toggleDarkMode = () => {
    setDarkMode(prevMode => !prevMode);
  };

  return (
    <motion.button
      onClick={toggleDarkMode}
      whileHover={{ scale: 1.1 }}
      whileTap={{ scale: 0.9 }}
      className="relative p-3 glass-card rounded-2xl border border-white/20 shadow-professional hover:shadow-professional-lg transition-all duration-300 group overflow-hidden"
      aria-label={darkMode ? 'Switch to light mode' : 'Switch to dark mode'}
    >
      {/* Background gradient */}
      <div className={`absolute inset-0 transition-all duration-500 ${
        darkMode
          ? 'bg-gradient-to-r from-indigo-600 via-purple-600 to-blue-600'
          : 'bg-gradient-to-r from-yellow-400 via-orange-500 to-red-500'
      }`} />

      {/* Animated background particles */}
      <div className="absolute inset-0">
        {[...Array(3)].map((_, i) => (
          <motion.div
            key={i}
            className={`absolute w-1 h-1 rounded-full ${
              darkMode ? 'bg-white/30' : 'bg-white/50'
            }`}
            animate={{
              x: [0, 20, 0],
              y: [0, -15, 0],
              opacity: [0, 1, 0]
            }}
            transition={{
              duration: 2,
              repeat: Infinity,
              delay: i * 0.5
            }}
            style={{
              left: `${20 + i * 20}%`,
              top: `${30 + i * 10}%`
            }}
          />
        ))}
      </div>

      {/* Icon container */}
      <motion.div
        className="relative z-10 flex items-center justify-center"
        initial={false}
        animate={{
          rotate: darkMode ? 180 : 0,
          scale: darkMode ? 1.1 : 1
        }}
        transition={{
          duration: 0.6,
          type: "spring",
          stiffness: 200,
          damping: 15
        }}
      >
        {darkMode ? (
          <motion.div
            initial={{ opacity: 0, scale: 0 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.3 }}
            className="text-white"
          >
            <FiSun className="h-6 w-6 drop-shadow-lg" />
          </motion.div>
        ) : (
          <motion.div
            initial={{ opacity: 0, scale: 0 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.3 }}
            className="text-white"
          >
            <FiMoon className="h-6 w-6 drop-shadow-lg" />
          </motion.div>
        )}
      </motion.div>

      {/* Glow effect */}
      <div className={`absolute inset-0 rounded-2xl transition-all duration-500 ${
        darkMode
          ? 'shadow-[0_0_20px_rgba(99,102,241,0.5)]'
          : 'shadow-[0_0_20px_rgba(251,191,36,0.5)]'
      } opacity-0 group-hover:opacity-100`} />
    </motion.button>
  );
};

export default DarkModeToggle;
