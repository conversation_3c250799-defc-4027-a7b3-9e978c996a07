import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  FiUsers, FiDatabase, FiDownload, FiDollarSign,
  FiTrendingUp, FiTrendingDown, FiAlertCircle, FiCheckCircle
} from 'react-icons/fi';
import { statsAPI } from '../../utils/api';

// Generate chart data from real API data
const generateChartData = (apiData) => {
  const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];

  if (!apiData) {
    return {
      userStats: { labels: [], data: [] },
      downloadStats: { labels: [], data: [] },
      revenueStats: { labels: [], data: [] },
      modelStats: { labels: [], data: [] }
    };
  }

  // Process real data from API
  const usersByMonth = apiData.usersByMonth || [];
  const downloadsByMonth = apiData.downloadsByMonth || [];

  const labels = usersByMonth.map(item => months[item._id.month - 1]);
  const userData = usersByMonth.map(item => item.count);
  const downloadData = downloadsByMonth.map(item => item.downloads);

  return {
    userStats: {
      labels,
      data: userData,
    },
    downloadStats: {
      labels,
      data: downloadData,
    },
    revenueStats: {
      labels,
      data: downloadData.map(d => d * 2), // Estimate revenue from downloads
    },
    modelStats: {
      labels,
      data: userData.map(u => Math.floor(u / 10)), // Estimate models from users
    }
  };
};

const DashboardOverview = () => {
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState({
    totalUsers: 0,
    totalModels: 0,
    totalDownloads: 0,
    totalRevenue: 0,
    recentUsers: [],
    recentModels: [],
    alerts: []
  });
  const [chartData, setChartData] = useState(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        // Fetch real statistics from MongoDB
        const response = await statsAPI.getAdminStats();

        if (response.data && response.data.success) {
          const realStats = response.data.data;

          // Process user growth data for chart
          const userChartData = {
            labels: [],
            data: []
          };

          // Process download data for chart
          const downloadChartData = {
            labels: [],
            data: []
          };

          // Get last 6 months for labels
          const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
          const currentDate = new Date();
          const currentMonth = currentDate.getMonth();

          // Create labels for last 6 months
          for (let i = 5; i >= 0; i--) {
            const monthIndex = (currentMonth - i + 12) % 12;
            userChartData.labels.push(months[monthIndex]);
            downloadChartData.labels.push(months[monthIndex]);

            // Initialize with zeros (will be replaced with real data if available)
            userChartData.data.push(0);
            downloadChartData.data.push(0);
          }

          // Fill in real user data if available
          if (realStats.usersByMonth && realStats.usersByMonth.length > 0) {
            realStats.usersByMonth.forEach(item => {
              const monthIndex = item._id.month - 1; // MongoDB months are 1-based
              const monthName = months[monthIndex];
              const labelIndex = userChartData.labels.indexOf(monthName);

              if (labelIndex !== -1) {
                userChartData.data[labelIndex] = item.count;
              }
            });
          }

          // Fill in real download data if available
          if (realStats.downloadsByMonth && realStats.downloadsByMonth.length > 0) {
            realStats.downloadsByMonth.forEach(item => {
              const monthIndex = item._id.month - 1; // MongoDB months are 1-based
              const monthName = months[monthIndex];
              const labelIndex = downloadChartData.labels.indexOf(monthName);

              if (labelIndex !== -1) {
                downloadChartData.data[labelIndex] = item.downloads;
              }
            });
          }

          // Set chart data
          setChartData({
            userStats: userChartData,
            downloadStats: downloadChartData,
            // Generate revenue and model stats from real data
            revenueStats: {
              labels: downloadChartData.labels,
              data: downloadChartData.data.map(d => d * 2) // Estimate revenue from downloads
            },
            modelStats: {
              labels: userChartData.labels,
              data: userChartData.data.map(u => Math.floor(u / 10)) // Estimate models from users
            }
          });

          // Set stats with real data
          setStats({
            totalUsers: realStats.totalUsers || 0,
            totalModels: realStats.totalModels || 0,
            totalDownloads: realStats.totalDownloads || 0,
            totalRevenue: realStats.totalRevenue || 0,
            recentUsers: realStats.recentUsers || [],
            recentModels: realStats.recentModels || [],
            // Real alerts from API (empty for now)
            alerts: realStats.alerts || []
          });
        } else {
          // If API fails, set empty data instead of mock data
          console.error('Failed to fetch admin stats from API');
          setStats({
            totalUsers: 0,
            totalModels: 0,
            totalDownloads: 0,
            totalRevenue: 0,
            recentUsers: [],
            recentModels: [],
            alerts: []
          });

          setChartData(null);
        }
      } catch (error) {
        console.error('Error fetching dashboard data:', error);

        // Set empty data if API fails
        setStats({
          totalUsers: 0,
          totalModels: 0,
          totalDownloads: 0,
          totalRevenue: 0,
          recentUsers: [],
          recentModels: [],
          alerts: []
        });

        setChartData(null);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  // Format number with commas
  const formatNumber = (num) => {
    if (num === null || num === undefined || num === '') {
      return '0';
    }
    return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
  };

  // Format date
  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';

    try {
      const date = new Date(dateString);
      if (isNaN(date.getTime())) return 'N/A';

      const options = { year: 'numeric', month: 'short', day: 'numeric' };
      return date.toLocaleDateString(undefined, options);
    } catch (error) {
      return 'N/A';
    }
  };

  // Calculate percentage change (mock data)
  const getPercentageChange = () => {
    return (Math.random() * 20 - 10).toFixed(1);
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-96">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <div>
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Dashboard Overview</h1>
        <p className="text-gray-600 dark:text-gray-400">Welcome back, Admin! Here's what's happening with your platform today.</p>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        {[
          {
            title: 'Total Users',
            value: stats.totalUsers || 0,
            icon: <FiUsers className="h-6 w-6" />,
            change: getPercentageChange(),
            color: 'blue'
          },
          {
            title: 'Total Models',
            value: stats.totalModels || 0,
            icon: <FiDatabase className="h-6 w-6" />,
            change: getPercentageChange(),
            color: 'green'
          },
          {
            title: 'Total Downloads',
            value: stats.totalDownloads || 0,
            icon: <FiDownload className="h-6 w-6" />,
            change: getPercentageChange(),
            color: 'purple'
          },
          {
            title: 'Total Revenue',
            value: `$${formatNumber(stats.totalRevenue || 0)}`,
            icon: <FiDollarSign className="h-6 w-6" />,
            change: getPercentageChange(),
            color: 'amber'
          }
        ].map((stat, index) => (
          <motion.div
            key={stat.title}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: index * 0.1 }}
            className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6"
          >
            <div className="flex justify-between items-start">
              <div>
                <p className="text-sm font-medium text-gray-500 dark:text-gray-400">{stat.title}</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white mt-1">{formatNumber(stat.value)}</p>
              </div>
              <div className={`p-3 rounded-full bg-${stat.color}-100 dark:bg-${stat.color}-900/30 text-${stat.color}-600 dark:text-${stat.color}-400`}>
                {stat.icon}
              </div>
            </div>
            <div className="mt-4 flex items-center">
              {parseFloat(stat.change) >= 0 ? (
                <FiTrendingUp className="text-green-500 mr-1" />
              ) : (
                <FiTrendingDown className="text-red-500 mr-1" />
              )}
              <span className={parseFloat(stat.change) >= 0 ? 'text-green-500' : 'text-red-500'}>
                {Math.abs(parseFloat(stat.change))}%
              </span>
              <span className="text-gray-500 dark:text-gray-400 ml-1">from last month</span>
            </div>
          </motion.div>
        ))}
      </div>

      {/* Charts Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        {/* User Growth Chart */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.4 }}
          className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6"
        >
          <h2 className="text-lg font-medium text-gray-900 dark:text-white mb-4">User Growth</h2>
          <div className="h-64 flex items-end">
            {chartData && chartData.userStats && chartData.userStats.data && chartData.userStats.data.map((value, index) => (
              <div key={index} className="flex-1 flex flex-col items-center">
                <div
                  className="w-full bg-blue-500 dark:bg-blue-600 rounded-t-sm"
                  style={{ height: `${(value / Math.max(...chartData.userStats.data)) * 100}%` }}
                ></div>
                <div className="text-xs text-gray-500 dark:text-gray-400 mt-2">{chartData.userStats.labels[index]}</div>
              </div>
            ))}
          </div>
        </motion.div>

        {/* Downloads Chart */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.5 }}
          className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6"
        >
          <h2 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Downloads</h2>
          <div className="h-64 flex items-end">
            {chartData && chartData.downloadStats && chartData.downloadStats.data && chartData.downloadStats.data.map((value, index) => (
              <div key={index} className="flex-1 flex flex-col items-center">
                <div
                  className="w-full bg-purple-500 dark:bg-purple-600 rounded-t-sm"
                  style={{ height: `${(value / Math.max(...chartData.downloadStats.data)) * 100}%` }}
                ></div>
                <div className="text-xs text-gray-500 dark:text-gray-400 mt-2">{chartData.downloadStats.labels[index]}</div>
              </div>
            ))}
          </div>
        </motion.div>
      </div>

      {/* Recent Activity Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        {/* Recent Users */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.6 }}
          className="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden"
        >
          <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <h2 className="text-lg font-medium text-gray-900 dark:text-white">Recent Users</h2>
          </div>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
              <thead className="bg-gray-50 dark:bg-gray-700">
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Name</th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Join Date</th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Status</th>
                </tr>
              </thead>
              <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                {(stats.recentUsers || []).map((user, index) => (
                  <tr key={user.id || user._id || index} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="flex-shrink-0 h-8 w-8 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center text-blue-600 dark:text-blue-400">
                          {user.name.charAt(0)}
                        </div>
                        <div className="ml-4">
                          <div className="text-sm font-medium text-gray-900 dark:text-white">{user.name}</div>
                          <div className="text-sm text-gray-500 dark:text-gray-400">{user.email}</div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                      {formatDate(user.createdAt || user.joinDate)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                        user.status === 'active'
                          ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
                          : user.status === 'pending'
                            ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300'
                            : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300'
                      }`}>
                        {user.status}
                      </span>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </motion.div>

        {/* Recent Models */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.7 }}
          className="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden"
        >
          <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <h2 className="text-lg font-medium text-gray-900 dark:text-white">Recent Models</h2>
          </div>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
              <thead className="bg-gray-50 dark:bg-gray-700">
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Title</th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Category</th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Downloads</th>
                </tr>
              </thead>
              <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                {(stats.recentModels || []).map((model, index) => (
                  <tr key={model.id || model._id || index} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900 dark:text-white">{model.title}</div>
                      <div className="text-xs text-gray-500 dark:text-gray-400">{formatDate(model.createdAt || model.uploadDate)}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                      {model.category}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                      {formatNumber(model.downloads || 0)}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </motion.div>
      </div>

      {/* System Alerts */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3, delay: 0.8 }}
        className="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden"
      >
        <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
          <h2 className="text-lg font-medium text-gray-900 dark:text-white">System Alerts</h2>
        </div>
        <div className="divide-y divide-gray-200 dark:divide-gray-700">
          {(stats.alerts || []).map((alert) => (
            <div key={alert.id} className="px-6 py-4 flex items-start">
              <div className="flex-shrink-0">
                {alert.type === 'warning' && <FiAlertCircle className="h-5 w-5 text-yellow-500" />}
                {alert.type === 'success' && <FiCheckCircle className="h-5 w-5 text-green-500" />}
                {alert.type === 'error' && <FiAlertCircle className="h-5 w-5 text-red-500" />}
                {alert.type === 'info' && <FiAlertCircle className="h-5 w-5 text-blue-500" />}
              </div>
              <div className="ml-3 w-0 flex-1">
                <p className="text-sm font-medium text-gray-900 dark:text-white">{alert.message}</p>
                <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">{alert.time}</p>
              </div>
            </div>
          ))}
        </div>
      </motion.div>
    </div>
  );
};

export default DashboardOverview;
