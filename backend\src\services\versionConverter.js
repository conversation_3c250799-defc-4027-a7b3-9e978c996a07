import fs from 'fs';
import path from 'path';
import { spawn } from 'child_process';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

class VersionConverter {
  constructor() {
    this.supportedVersions = ['2020', '2021', '2022', '2023', '2024'];
    this.conversionDir = path.join(__dirname, '../../downloads/conversions');
    this.tempDir = path.join(__dirname, '../../temp');
    
    // Ensure directories exist
    this.ensureDirectories();
  }

  ensureDirectories() {
    [this.conversionDir, this.tempDir].forEach(dir => {
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
      }
    });
  }

  /**
   * Check if conversion is possible between versions
   * @param {string} fromVersion - Source SketchUp version
   * @param {string} toVersion - Target SketchUp version
   * @returns {boolean} - Whether conversion is possible
   */
  canConvert(fromVersion, toVersion) {
    // Can only downgrade (convert from higher to lower version)
    // Cannot upgrade (convert from lower to higher version)
    const fromVersionNum = parseInt(fromVersion);
    const toVersionNum = parseInt(toVersion);
    
    return fromVersionNum > toVersionNum && 
           this.supportedVersions.includes(fromVersion) && 
           this.supportedVersions.includes(toVersion);
  }

  /**
   * Get available target versions for conversion
   * @param {string} currentVersion - Current SketchUp version
   * @returns {string[]} - Array of available target versions
   */
  getAvailableTargetVersions(currentVersion) {
    const currentVersionNum = parseInt(currentVersion);
    return this.supportedVersions
      .filter(version => parseInt(version) < currentVersionNum)
      .sort((a, b) => parseInt(b) - parseInt(a)); // Sort descending
  }

  /**
   * Convert SketchUp file to target version
   * @param {string} sourceFilePath - Path to source SKP file
   * @param {string} targetVersion - Target SketchUp version
   * @param {string} outputFileName - Output file name
   * @returns {Promise<Object>} - Conversion result
   */
  async convertFile(sourceFilePath, targetVersion, outputFileName) {
    try {
      console.log(`🔄 Starting conversion to SketchUp ${targetVersion}`);
      console.log(`📁 Source: ${sourceFilePath}`);
      
      // Generate output path
      const outputPath = path.join(this.conversionDir, outputFileName);
      
      // Check if source file exists
      if (!fs.existsSync(sourceFilePath)) {
        throw new Error('Source file not found');
      }

      // For now, we'll simulate the conversion process
      // In a real implementation, you would use:
      // 1. SketchUp Ruby API for conversion
      // 2. External conversion tools
      // 3. Cloud-based conversion services
      
      const result = await this.simulateConversion(sourceFilePath, targetVersion, outputPath);
      
      return {
        success: true,
        outputPath,
        fileSize: result.fileSize,
        conversionTime: result.conversionTime,
        targetVersion
      };

    } catch (error) {
      console.error('❌ Conversion failed:', error);
      throw new Error(`Conversion failed: ${error.message}`);
    }
  }

  /**
   * Simulate conversion process (for demonstration)
   * In production, replace with actual SketchUp conversion logic
   */
  async simulateConversion(sourceFilePath, targetVersion, outputPath) {
    return new Promise((resolve, reject) => {
      const startTime = Date.now();
      
      // Simulate conversion time (2-10 seconds)
      const conversionTime = Math.random() * 8000 + 2000;
      
      setTimeout(() => {
        try {
          // Copy source file to output (simulation)
          fs.copyFileSync(sourceFilePath, outputPath);
          
          // Get file size
          const stats = fs.statSync(outputPath);
          
          resolve({
            fileSize: stats.size,
            conversionTime: Date.now() - startTime
          });
        } catch (error) {
          reject(error);
        }
      }, conversionTime);
    });
  }

  /**
   * Real SketchUp conversion using Ruby API (for future implementation)
   * This would require SketchUp to be installed on the server
   */
  async convertWithSketchUpAPI(sourceFilePath, targetVersion, outputPath) {
    return new Promise((resolve, reject) => {
      // Ruby script for SketchUp conversion
      const rubyScript = `
        require 'sketchup.rb'
        
        # Load the model
        model = Sketchup.active_model
        model.definitions.load_from_url('${sourceFilePath}')
        
        # Set target version
        options = {
          :version => ${targetVersion}
        }
        
        # Save in target version format
        model.save('${outputPath}', options)
        
        puts "Conversion completed successfully"
      `;
      
      // Write Ruby script to temp file
      const scriptPath = path.join(this.tempDir, `convert_${Date.now()}.rb`);
      fs.writeFileSync(scriptPath, rubyScript);
      
      // Execute SketchUp with Ruby script
      const sketchupProcess = spawn('sketchup', ['-ruby', scriptPath], {
        stdio: ['pipe', 'pipe', 'pipe']
      });
      
      let output = '';
      let errorOutput = '';
      
      sketchupProcess.stdout.on('data', (data) => {
        output += data.toString();
      });
      
      sketchupProcess.stderr.on('data', (data) => {
        errorOutput += data.toString();
      });
      
      sketchupProcess.on('close', (code) => {
        // Clean up temp script
        fs.unlinkSync(scriptPath);
        
        if (code === 0) {
          const stats = fs.statSync(outputPath);
          resolve({
            fileSize: stats.size,
            output
          });
        } else {
          reject(new Error(`SketchUp conversion failed: ${errorOutput}`));
        }
      });
      
      sketchupProcess.on('error', (error) => {
        reject(new Error(`Failed to start SketchUp: ${error.message}`));
      });
    });
  }

  /**
   * Clean up old conversion files
   * @param {number} maxAgeHours - Maximum age in hours
   */
  cleanupOldFiles(maxAgeHours = 24) {
    const maxAge = maxAgeHours * 60 * 60 * 1000; // Convert to milliseconds
    const now = Date.now();
    
    try {
      const files = fs.readdirSync(this.conversionDir);
      
      files.forEach(file => {
        const filePath = path.join(this.conversionDir, file);
        const stats = fs.statSync(filePath);
        
        if (now - stats.mtime.getTime() > maxAge) {
          fs.unlinkSync(filePath);
          console.log(`🗑️ Cleaned up old conversion file: ${file}`);
        }
      });
    } catch (error) {
      console.error('Failed to cleanup old files:', error);
    }
  }

  /**
   * Get conversion statistics
   */
  getConversionStats() {
    try {
      const files = fs.readdirSync(this.conversionDir);
      const totalSize = files.reduce((size, file) => {
        const filePath = path.join(this.conversionDir, file);
        const stats = fs.statSync(filePath);
        return size + stats.size;
      }, 0);
      
      return {
        totalFiles: files.length,
        totalSize,
        supportedVersions: this.supportedVersions
      };
    } catch (error) {
      return {
        totalFiles: 0,
        totalSize: 0,
        supportedVersions: this.supportedVersions
      };
    }
  }
}

export default new VersionConverter();
