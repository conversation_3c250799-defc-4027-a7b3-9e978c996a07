import express from 'express';
import {
  subscribeToNewsletter,
  unsubscribeFromNewsletter,
  updateNewsletterPreferences
} from '../controllers/newsletterController.js';

const router = express.Router();

// Public routes
router.post('/subscribe', subscribeToNewsletter);
router.get('/unsubscribe/:token', unsubscribeFromNewsletter);
router.put('/preferences/:token', updateNewsletterPreferences);

export default router;
