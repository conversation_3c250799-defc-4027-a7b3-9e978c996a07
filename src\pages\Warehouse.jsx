import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { toast } from 'react-hot-toast';
import {
  FiSearch, FiExternalLink, FiDownload, FiLoader,
  FiTrendingUp, FiEye, FiHeart, FiUser, FiInfo,
  FiLink, FiRefreshCw, FiGlobe, FiTool, FiZap
} from 'react-icons/fi';
import WarehouseSearch from '../components/WarehouseSearch';
import axios from 'axios';

const Warehouse = () => {
  const [isWarehouseSearchOpen, setIsWarehouseSearchOpen] = useState(false);
  const [featuredModels, setFeaturedModels] = useState([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
  loadFeaturedModels();
  }, []);

  const loadFeaturedModels = async () => {
  setIsLoading(true);
    try {
      const response = await axios.get('/api/download/warehouse/trending');
      if (true) {
  setFeaturedModels(response.data.data.models.slice(0, 6));
      }
    } catch (error) {
            setFeaturedModels([
        {
    title: 'Modern Living Room Set',
          author: 'SketchUp Team',
          thumbnail: '/images/placeholder.jpg',
          modelUrl: 'https://3dwarehouse.sketchup.com/model/123',
          description: 'Complete modern living room furniture set',
          stats: { views: '12.5K', likes: '890' }
        },
        {
    title: 'Kitchen Cabinet Collection',
          author: 'Interior Designer',
          thumbnail: '/images/placeholder.jpg',
          modelUrl: 'https://3dwarehouse.sketchup.com/model/456',
          description: 'Professional kitchen cabinet designs',
          stats: { views: '8.2K', likes: '654' }
        },
        {
    title: 'Office Furniture Pack',
          author: 'Workspace Pro',
          thumbnail: '/images/placeholder.jpg',
          modelUrl: 'https://3dwarehouse.sketchup.com/model/789',
          description: 'Complete office furniture collection',
          stats: { views: '15.1K', likes: '1.2K' }
        }
      ]);
    } finally {
      setIsLoading(false);
    }
  };

  const features = [
    {
    icon: <FiGlobe className="h-8 w-8" />,
      title: 'Global Library',
      description: 'Access millions of 3D models from Google\'s vast 3D Warehouse'
    },
    {
    icon: <FiDownload className="h-8 w-8" />,
      title: 'Smart Download',
      description: 'Download models through our proxy for better speed and reliability'
    },
    {
    icon: <FiLink className="h-8 w-8" />,
      title: 'Backup Links',
      description: 'Multiple download links ensure you always get your files'
    },
    {
    icon: <FiRefreshCw className="h-8 w-8" />,
      title: 'Auto Check',
      description: 'Automatically verify link status and find working alternatives'
    }
  ];

  const resources = [
    {
    title: 'Imported 3D Models',
      description: 'Models imported from Google 3D Warehouse into our library',
      icon: <FiGlobe className="h-6 w-6" />,
      action: () => setIsWarehouseSearchOpen(true)
    },
    {
    title: 'SketchUp Plugins',
      description: 'Essential plugins and tools for SketchUp available in our library',
      icon: <FiTool className="h-6 w-6" />,
      action: () => window.location.href = '/plugins'
    },
    {
    title: 'Smart Collections',
      description: 'AI-curated collections of related 3D models',
      icon: <FiZap className="h-6 w-6" />,
      action: () => window.location.href = '/collections'
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">

      <main className="pt-24 pb-16">
        {/* Hero Section */}
        <section className="bg-gradient-to-br from-blue-600 to-indigo-700 text-white py-20">
          <div className="container mx-auto px-4 text-center">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
            >
              <h1 className="text-4xl md:text-6xl font-bold mb-6">
                3D Warehouse Integration
              </h1>
              <p className="text-xl md:text-2xl mb-8 text-blue-100 max-w-3xl mx-auto">
                Access millions of 3D models from Google 3D Warehouse with our smart download system
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <button
                  onClick={() => setIsWarehouseSearchOpen(true)}
                  className="px-8 py-4 bg-white text-blue-600 rounded-lg font-semibold hover:bg-gray-100 transition-colors flex items-center justify-center"
                >
                  <FiSearch className="h-5 w-5 mr-2" />
                  Search 3D Warehouse
                </button>
                <button
                  onClick={loadFeaturedModels}
                  className="px-8 py-4 border-2 border-white text-white rounded-lg font-semibold hover:bg-white hover:text-blue-600 transition-colors flex items-center justify-center"
                >
                  <FiRefreshCw className="h-5 w-5 mr-2" />
                  Refresh Models
                </button>
              </div>
            </motion.div>
          </div>
        </section>

        {/* Features Section */}
        <section className="py-16">
          <div className="container mx-auto px-4">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
                Why Use Our Integration?
              </h2>
              <p className="text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
                Our smart download system enhances your 3D Warehouse experience with reliability and speed
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
              {features.map((feature, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  className="text-center p-6 bg-white dark:bg-gray-800 rounded-lg shadow-md"
                >
                  <div className="text-blue-600 dark:text-blue-400 mb-4 flex justify-center">
                    {feature.icon}
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                    {feature.title}
                  </h3>
                  <p className="text-gray-600 dark:text-gray-400 text-sm">
                    {feature.description}
                  </p>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* Featured Models */}
        <section className="py-16 bg-white dark:bg-gray-800">
          <div className="container mx-auto px-4">
            <div className="flex justify-between items-center mb-8">
              <div>
                <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
                  Featured Models
                </h2>
                <p className="text-gray-600 dark:text-gray-400">
                  Popular models from 3D Warehouse
                </p>
              </div>
              <button
                onClick={() => setIsWarehouseSearchOpen(true)}
                className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center"
              >
                <FiSearch className="h-4 w-4 mr-2" />
                Browse More
              </button>
            </div>

            {isLoading ? (
              <div className="text-center py-12">
                <FiLoader className="h-8 w-8 text-blue-600 mx-auto mb-4 animate-spin" />
                <p className="text-gray-600 dark:text-gray-400">Loading featured models...</p>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {featuredModels.map((model, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: index * 0.1 }}
                    className="bg-gray-50 dark:bg-gray-700 rounded-lg overflow-hidden shadow-md hover:shadow-lg transition-shadow"
                  >
                    <img
                      src={model.thumbnail}
                      alt={model.title}
                      className="w-full h-48 object-cover"
                      onError={(e) => {
  e.target.src = '/images/placeholder.jpg';
                      }}
                    />
                    <div className="p-4">
                      <h3 className="font-semibold text-gray-900 dark:text-white mb-2 line-clamp-2">
                        {model.title}
                      </h3>
                      {model.author && (
                        <div className="flex items-center text-sm text-gray-600 dark:text-gray-400 mb-2">
                          <FiUser className="h-4 w-4 mr-1" />
                          <span>{model.author}</span>
                        </div>
                      )}
                      {model.description && (
                        <p className="text-sm text-gray-600 dark:text-gray-400 mb-3 line-clamp-2">
                          {model.description}
                        </p>
                      )}
                      <div className="flex justify-between items-center">
                        <div className="flex items-center space-x-4 text-xs text-gray-500 dark:text-gray-400">
                          {model.stats?.views && (
                            <div className="flex items-center">
                              <FiEye className="h-3 w-3 mr-1" />
                              <span>{model.stats.views}</span>
                            </div>
                          )}
                          {model.stats?.likes && (
                            <div className="flex items-center">
                              <FiHeart className="h-3 w-3 mr-1" />
                              <span>{model.stats.likes}</span>
                            </div>
                          )}
                        </div>
                        <button
                          onClick={() => {
                            // Navigate to model detail page
                            window.location.href = `/model/${model._id || model.id}`;
                          }}
                          className="text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300"
                          title="View Details"
                        >
                          <FiEye className="h-4 w-4" />
                        </button>
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>
            )}
          </div>
        </section>

        {/* Resources Section */}
        <section className="py-16">
          <div className="container mx-auto px-4">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
                Related Resources
              </h2>
              <p className="text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
                Explore these essential resources for SketchUp users
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {resources.map((resource, index) => (
                <motion.button
                  key={index}
                  onClick={resource.action}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  className="block p-6 bg-white dark:bg-gray-800 rounded-lg shadow-md hover:shadow-lg transition-all hover:scale-105 text-left w-full"
                >
                  <div className="flex items-center mb-4">
                    <div className="text-blue-600 dark:text-blue-400 mr-3">
                      {resource.icon}
                    </div>
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                      {resource.title}
                    </h3>
                  </div>
                  <p className="text-gray-600 dark:text-gray-400 text-sm">
                    {resource.description}
                  </p>
                </motion.button>
              ))}
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-16 bg-gradient-to-r from-blue-600 to-indigo-600 text-white">
          <div className="container mx-auto px-4 text-center">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
            >
              <h2 className="text-3xl font-bold mb-4">
                Ready to Explore 3D Warehouse?
              </h2>
              <p className="text-xl mb-8 text-blue-100 max-w-2xl mx-auto">
                Start searching and downloading 3D models with our enhanced integration
              </p>
              <button
                onClick={() => setIsWarehouseSearchOpen(true)}
                className="px-8 py-4 bg-white text-blue-600 rounded-lg font-semibold hover:bg-gray-100 transition-colors inline-flex items-center"
              >
                <FiSearch className="h-5 w-5 mr-2" />
                Start Searching Now
              </button>
            </motion.div>
          </div>
        </section>
      </main>

      {/* Warehouse Search Modal */}
      {isWarehouseSearchOpen && (
        <WarehouseSearch
          onClose={() => setIsWarehouseSearchOpen(false)}
          onDownload={(model) => {
  toast.success(`Downloaded: ${model.title}`);
          }}
        />
      )}
    </div>
  );
};

export default Warehouse;
