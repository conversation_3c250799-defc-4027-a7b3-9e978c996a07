import mongoose from 'mongoose';
import dotenv from 'dotenv';
import Model from '../models/Model.js';
import User from '../models/User.js';

// Load environment variables
dotenv.config();

// Sample models data
const sampleModels = [
  {
    title: 'Modern Living Room Set',
    description: 'Complete modern living room furniture set including sofa, coffee table, TV stand, and decorative elements. Perfect for contemporary interior design projects.',
    category: 'Furniture',
    format: 'Sketchup 2023',
    fileFormat: 'skp',
    imageUrl: 'https://example.com/previews/modern-living-room.jpg',
    fileUrl: 'https://example.com/models/modern-living-room.skp',
    tags: ['living room', 'modern', 'sofa', 'coffee table', 'contemporary', 'interior'],
    fileSize: 15.2,
    downloads: 2340,
    rating: 4.7,
    isPremium: false,
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    title: 'Traditional Vietnamese House',
    description: 'Authentic traditional Vietnamese house with typical architectural elements including wooden structure, tile roof, and courtyard design.',
    category: 'Residential',
    format: 'Sketchup 2023',
    fileFormat: 'skp',
    imageUrl: 'https://example.com/previews/vietnamese-house.jpg',
    fileUrl: 'https://example.com/models/vietnamese-house.skp',
    tags: ['vietnamese', 'traditional', 'house', 'architecture', 'wooden', 'tile roof'],
    fileSize: 28.5,
    downloads: 1890,
    rating: 4.5,
    isPremium: false,
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    title: 'Bedroom Furniture Collection',
    description: 'Complete bedroom furniture set with bed, wardrobe, nightstands, dresser, and decorative accessories for modern bedroom design.',
    category: 'Furniture',
    format: 'Sketchup 2023',
    fileFormat: 'skp',
    imageUrl: 'https://example.com/previews/bedroom-collection.jpg',
    fileUrl: 'https://example.com/models/bedroom-collection.skp',
    tags: ['bedroom', 'bed', 'wardrobe', 'nightstand', 'dresser', 'furniture'],
    fileSize: 22.1,
    downloads: 3120,
    rating: 4.8,
    isPremium: false,
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    title: 'Kitchen Cabinet System',
    description: 'Modern kitchen cabinet system with upper and lower cabinets, kitchen island, and built-in appliances for contemporary kitchen design.',
    category: 'Furniture',
    format: 'Sketchup 2023',
    fileFormat: 'skp',
    imageUrl: 'https://example.com/previews/kitchen-cabinets.jpg',
    fileUrl: 'https://example.com/models/kitchen-cabinets.skp',
    tags: ['kitchen', 'cabinet', 'island', 'appliances', 'modern', 'cooking'],
    fileSize: 31.7,
    downloads: 2780,
    rating: 4.6,
    isPremium: false,
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    title: 'Office Workspace Setup',
    description: 'Professional office workspace with desk, chair, shelving, computer setup, and office accessories for modern workplace design.',
    category: 'Furniture',
    format: 'Sketchup 2023',
    fileFormat: 'skp',
    imageUrl: 'https://example.com/previews/office-workspace.jpg',
    fileUrl: 'https://example.com/models/office-workspace.skp',
    tags: ['office', 'desk', 'chair', 'workspace', 'professional', 'computer'],
    fileSize: 18.9,
    downloads: 1650,
    rating: 4.4,
    isPremium: false,
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    title: 'Garden Landscape Elements',
    description: 'Collection of garden and landscape elements including trees, plants, garden furniture, pathways, and outdoor decorations.',
    category: 'Landscape/Garden',
    format: 'Sketchup 2023',
    fileFormat: 'skp',
    imageUrl: 'https://example.com/previews/garden-landscape.jpg',
    fileUrl: 'https://example.com/models/garden-landscape.skp',
    tags: ['garden', 'landscape', 'trees', 'plants', 'outdoor', 'nature'],
    fileSize: 25.3,
    downloads: 2100,
    rating: 4.3,
    isPremium: false,
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    title: 'Bathroom Fixtures Set',
    description: 'Complete bathroom fixtures including bathtub, shower, toilet, sink, mirror, and bathroom accessories for modern bathroom design.',
    category: 'Furniture',
    format: 'Sketchup 2023',
    fileFormat: 'skp',
    imageUrl: 'https://example.com/previews/bathroom-fixtures.jpg',
    fileUrl: 'https://example.com/models/bathroom-fixtures.skp',
    tags: ['bathroom', 'bathtub', 'shower', 'toilet', 'sink', 'fixtures'],
    fileSize: 19.8,
    downloads: 2450,
    rating: 4.5,
    isPremium: false,
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    title: 'Lighting Collection',
    description: 'Diverse lighting collection with ceiling lights, floor lamps, table lamps, pendant lights, and decorative lighting fixtures.',
    category: 'Furniture',
    format: 'Sketchup 2023',
    fileFormat: 'skp',
    imageUrl: 'https://example.com/previews/lighting-collection.jpg',
    fileUrl: 'https://example.com/models/lighting-collection.skp',
    tags: ['lighting', 'lamps', 'ceiling', 'floor', 'pendant', 'decorative'],
    fileSize: 12.4,
    downloads: 1820,
    rating: 4.2,
    isPremium: false,
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    title: 'Staircase Designs',
    description: 'Various staircase designs including straight, spiral, and L-shaped stairs with different materials and railing styles.',
    category: 'Residential',
    format: 'Sketchup 2023',
    fileFormat: 'skp',
    imageUrl: 'https://example.com/previews/staircase-designs.jpg',
    fileUrl: 'https://example.com/models/staircase-designs.skp',
    tags: ['staircase', 'stairs', 'spiral', 'railing', 'architecture', 'design'],
    fileSize: 16.7,
    downloads: 1340,
    rating: 4.1,
    isPremium: false,
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    title: 'Window and Door Collection',
    description: 'Comprehensive collection of windows and doors in various styles including modern, traditional, and contemporary designs.',
    category: 'Residential',
    format: 'Sketchup 2023',
    fileFormat: 'skp',
    imageUrl: 'https://example.com/previews/windows-doors.jpg',
    fileUrl: 'https://example.com/models/windows-doors.skp',
    tags: ['window', 'door', 'modern', 'traditional', 'contemporary', 'entrance'],
    fileSize: 21.6,
    downloads: 2890,
    rating: 4.6,
    isPremium: false,
    createdAt: new Date(),
    updatedAt: new Date()
  }
];

async function populateModels() {
  try {
    console.log('🔄 Connecting to MongoDB...');
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('✅ Connected to MongoDB');

    // Get a sample user to assign as creator
    const sampleUser = await User.findOne({ role: 'admin' });
    if (!sampleUser) {
      console.log('❌ No admin user found. Creating sample user...');
      const newUser = new User({
        name: 'Sample Admin',
        email: '<EMAIL>',
        password: 'hashedpassword',
        role: 'admin',
        isVerified: true
      });
      await newUser.save();
      console.log('✅ Sample admin user created');
    }

    const creatorUser = sampleUser || await User.findOne({ role: 'admin' });

    // Add creator to each model
    const modelsWithCreator = sampleModels.map(model => ({
      ...model,
      createdBy: creatorUser._id
    }));

    // Clear existing sample data
    console.log('🧹 Clearing existing sample models...');
    await Model.deleteMany({
      title: { $in: sampleModels.map(m => m.title) }
    });

    // Insert new sample data
    console.log('📦 Inserting sample models...');
    const result = await Model.insertMany(modelsWithCreator);

    console.log(`✅ Successfully inserted ${result.length} sample models`);

    // Display summary
    console.log('\n📊 Sample Models Summary:');
    sampleModels.forEach((model, index) => {
      console.log(`${index + 1}. ${model.title} (${model.category}) - ${model.downloads} downloads`);
    });

    console.log('\n🎉 Sample models population completed!');

  } catch (error) {
    console.error('❌ Error populating models:', error);
  } finally {
    await mongoose.disconnect();
    console.log('🔌 Disconnected from MongoDB');
    process.exit(0);
  }
}

// Run the script
populateModels();
