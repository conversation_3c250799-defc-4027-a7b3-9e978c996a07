import React from 'react';
import { motion } from 'framer-motion';

const LoadingSpinner = ({ size = 'medium', text = 'Đang tải...', fullScreen = false }) => {
  const sizeClasses = {
    small: 'w-6 h-6',
    medium: 'w-12 h-12',
    large: 'w-16 h-16',
    xl: 'w-24 h-24'
  };

  const textSizes = {
    small: 'text-sm',
    medium: 'text-base',
    large: 'text-lg',
    xl: 'text-xl'
  };

  const SpinnerContent = () => (
    <div className="flex flex-col items-center justify-center space-y-4">
      {/* Main Spinner */}
      <div className="relative">
        {/* Outer Ring */}
        <motion.div
          className={`${sizeClasses[size]} border-4 border-gray-200 dark:border-gray-700 rounded-full`}
          animate={{ rotate: 360 }}
          transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
        />

        {/* Inner Ring */}
        <motion.div
          className={`absolute inset-0 ${sizeClasses[size]} border-4 border-transparent border-t-blue-600 border-r-purple-600 rounded-full`}
          animate={{ rotate: -360 }}
          transition={{ duration: 1.5, repeat: Infinity, ease: "linear" }}
        />

        {/* Center Dot */}
        <motion.div
          className="absolute inset-0 flex items-center justify-center"
          animate={{ scale: [1, 1.2, 1] }}
          transition={{ duration: 1, repeat: Infinity }}
        >
          <div className="w-2 h-2 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full"></div>
        </motion.div>
      </div>

      {/* Loading Text */}
      {text && (
        <motion.p
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.2 }}
          className={`${textSizes[size]} font-medium text-gray-600 dark:text-gray-300`}
        >
          {text}
        </motion.p>
      )}

      {/* Loading Dots */}
      <div className="flex space-x-1">
        {[0, 1, 2].map((index) => (
          <motion.div
            key={index}
            className="w-2 h-2 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full"
            animate={{ scale: [1, 1.5, 1], opacity: [0.5, 1, 0.5] }}
            transition={{
              duration: 1,
              repeat: Infinity,
              delay: index * 0.2
            }}
          />
        ))}
      </div>
    </div>
  );

  if (fullScreen) {
    return (
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm flex items-center justify-center z-50"
      >
        <div className="glass-card p-8 rounded-3xl border border-white/20 shadow-professional-lg">
          <SpinnerContent />
        </div>
      </motion.div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.8 }}
      animate={{ opacity: 1, scale: 1 }}
      exit={{ opacity: 0, scale: 0.8 }}
      className="flex items-center justify-center p-8"
    >
      <SpinnerContent />
    </motion.div>
  );
};

// Skeleton Loading Component
export const SkeletonLoader = ({ className = ', lines = 3 }) => (
  <div className={`animate-pulse ${className}`}>
    {Array.from({ length: lines }).map((_, index) => (
      <motion.div
        key={index}
        initial={{ opacity: 0.3 }}
        animate={{ opacity: [0.3, 0.7, 0.3] }}
        transition={{ duration: 1.5, repeat: Infinity, delay: index * 0.1 }}
        className={`bg-gray-200 dark:bg-gray-700 rounded-lg mb-3 ${
          index === lines - 1 ? 'w-3/4' : 'w-full'
        } h-4`}
      />
    ))}
  </div>
);

// Card Skeleton
export const CardSkeleton = () => (
  <div className="glass-card rounded-3xl p-6 border border-white/20 animate-pulse">
    <div className="bg-gray-200 dark:bg-gray-700 rounded-2xl h-48 mb-4"></div>
    <div className="space-y-3">
      <div className="bg-gray-200 dark:bg-gray-700 rounded-lg h-4 w-3/4"></div>
      <div className="bg-gray-200 dark:bg-gray-700 rounded-lg h-4 w-1/2"></div>
      <div className="bg-gray-200 dark:bg-gray-700 rounded-lg h-4 w-2/3"></div>
    </div>
  </div>
);

// Button Loading
export const ButtonSpinner = ({ size = 'small' }) => (
  <motion.div
    className={`${sizeClasses[size]} border-2 border-white border-t-transparent rounded-full`}
    animate={{ rotate: 360 }}
    transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
  />
);

export default LoadingSpinner;
