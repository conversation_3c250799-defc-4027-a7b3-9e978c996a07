import Activity from '../models/Activity.js';
import User from '../models/User.js';

// @desc    Create activity log
// @route   POST /api/activity
// @access  Private
export const createActivity = async (req, res, next) => {
  try {
    const { type, model, review, payment, subscription, details } = req.body;
    
    // Create activity
    const activity = await Activity.create({
      user: req.user.id,
      type,
      model,
      review,
      payment,
      subscription,
      details,
      ipAddress: req.ip,
      userAgent: req.headers['user-agent']
    });
    
    res.status(201).json({
      success: true,
      data: activity
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Get user's activity history
// @route   GET /api/users/activity
// @access  Private
export const getUserActivity = async (req, res, next) => {
  try {
    const page = parseInt(req.query.page, 10) || 1;
    const limit = parseInt(req.query.limit, 10) || 10;
    const startIndex = (page - 1) * limit;
    
    // Get activities for the current user
    const activities = await Activity.find({ user: req.user.id })
      .populate({
        path: 'model',
        select: 'title imageUrl category'
      })
      .sort({ createdAt: -1 })
      .skip(startIndex)
      .limit(limit);
    
    // Get total count
    const total = await Activity.countDocuments({ user: req.user.id });
    
    // Format activities for frontend
    const formattedActivities = activities.map(activity => ({
      id: activity._id,
      type: activity.type,
      title: activity.model ? activity.model.title : activity.details?.title || '',
      modelId: activity.model ? activity.model._id : null,
      imageUrl: activity.model ? activity.model.imageUrl : null,
      category: activity.model ? activity.model.category : null,
      date: activity.createdAt,
      details: activity.details || {}
    }));
    
    res.status(200).json({
      success: true,
      count: activities.length,
      total,
      pagination: {
        page,
        limit,
        pages: Math.ceil(total / limit)
      },
      data: formattedActivities
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Get all activities (admin only)
// @route   GET /api/activity
// @access  Private/Admin
export const getAllActivities = async (req, res, next) => {
  try {
    const page = parseInt(req.query.page, 10) || 1;
    const limit = parseInt(req.query.limit, 10) || 20;
    const startIndex = (page - 1) * limit;
    
    // Filter options
    const filter = {};
    
    if (req.query.type) {
      filter.type = req.query.type;
    }
    
    if (req.query.user) {
      filter.user = req.query.user;
    }
    
    // Date range filter
    if (req.query.startDate && req.query.endDate) {
      filter.createdAt = {
        $gte: new Date(req.query.startDate),
        $lte: new Date(req.query.endDate)
      };
    }
    
    // Get activities
    const activities = await Activity.find(filter)
      .populate({
        path: 'user',
        select: 'name email'
      })
      .populate({
        path: 'model',
        select: 'title imageUrl'
      })
      .sort({ createdAt: -1 })
      .skip(startIndex)
      .limit(limit);
    
    // Get total count
    const total = await Activity.countDocuments(filter);
    
    res.status(200).json({
      success: true,
      count: activities.length,
      total,
      pagination: {
        page,
        limit,
        pages: Math.ceil(total / limit)
      },
      data: activities
    });
  } catch (error) {
    next(error);
  }
};

// Helper function to log activity (for internal use)
export const logActivity = async (userId, type, data = {}) => {
  try {
    const { model, review, payment, subscription, details } = data;
    
    await Activity.create({
      user: userId,
      type,
      model,
      review,
      payment,
      subscription,
      details,
      ipAddress: data.ipAddress || '',
      userAgent: data.userAgent || ''
    });
    
    return true;
  } catch (error) {
    console.error('Error logging activity:', error);
    return false;
  }
};
