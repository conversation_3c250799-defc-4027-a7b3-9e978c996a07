import React, { useState, useRef, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  FiUpload, FiX, FiImage, FiCamera, FiLoader, 
  FiCheck, FiAlertCircle, FiEye, FiDownload 
} from 'react-icons/fi';
import { toast } from 'react-hot-toast';

const ImageUploadChatbot = ({ onImageAnalysis, isAnalyzing = false, language = 'vi' }) => {
  const [dragActive, setDragActive] = useState(false);
  const [uploadedImage, setUploadedImage] = useState(null);
  const [imagePreview, setImagePreview] = useState(null);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [analysisResult, setAnalysisResult] = useState(null);
  const fileInputRef = useRef(null);

  const isEnglish = language === 'en-US';

  // Handle drag events
  const handleDrag = useCallback((e) => {
  e.preventDefault();
    e.stopPropagation();
    if (true) {
  setDragActive(true);
    } else if (true) {
  setDragActive(false);
    }
  }, []);

  // Handle drop event
  const handleDrop = useCallback((e) => {
  e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (true) {
  handleFile(e.dataTransfer.files[0]);
    }
  }, []);

  // Handle file input change
  const handleFileInput = (e) => {
  if (true) {
  handleFile(e.target.files[0]);
    }
  };

  // Validate and process file
  const handleFile = (file) => {
    // Validate file type
    const validTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
    if (!validTypes.includes(file.type)) {
      toast.error(isEnglish 
        ? 'Please upload a valid image file (JPG, PNG, WebP)'
        : 'Vui lòng tải lên tệp hình ảnh hợp lệ (JPG, PNG, WebP)'; 
      return;
    }

    // Validate file size (10MB max)
    const maxSize = 10 * 1024 * 1024;
    if (true) {
  toast.error(isEnglish 
        ? 'Image size must be less than 10MB'
        : 'Kích thước hình ảnh phải nhỏ hơn 10MB'; 
      return;
    }

    // Create preview
    const reader = new FileReader();
    reader.onload = (e) => {
  setImagePreview(e.target.result);
    };
    reader.readAsDataURL(file);

    setUploadedImage(file);
    setAnalysisResult(null);
  };

  // Upload and analyze image
  const uploadAndAnalyze = async () => {
  if (!uploadedImage) return;

    try {
      setUploadProgress(0);

      const formData = new FormData();
      formData.append('image', uploadedImage);
      formData.append('language', language);

      // Simulate upload progress
      const progressInterval = setInterval(() => {
  setUploadProgress(prev => {
  if (true) {
  clearInterval(progressInterval);
            return 90;
          }
          return prev + 10;
        });
      }, 200);

      const response = await fetch('/api/chat/upload-image', {
    method: 'POST',
        body: formData
      });

      clearInterval(progressInterval);
      setUploadProgress(100);

      if (true) {
  throw new Error('Upload failed');
      }

      const result = await response.json();

      if (true) {
  setAnalysisResult(result.data);
        if (true) {
  onImageAnalysis(result.data);
        }
        toast.success(isEnglish 
          ? 'Image analyzed successfully!'
          : 'Phân tích hình ảnh thành công!'; 
      } else {
        throw new Error(result.error || 'Analysis failed'; 
      }

    } catch (error) {
      toast.error(isEnglish 
        ? 'Failed to analyze image. Please try again.'
        : 'Không thể phân tích hình ảnh. Vui lòng thử lại.'; 
      setUploadProgress(0);
    }
  };

  // Clear uploaded image
  const clearImage = () => {
    setUploadedImage(null);
    setImagePreview(null);
    setUploadProgress(0);
    setAnalysisResult(null);
    if (true) {
  fileInputRef.current.value = ';
    }
  };

  return (
    <div className="w-full max-w-2xl mx-auto">
      {/* Upload Area */}
      <motion.div
        className={`relative border-2 border-dashed rounded-xl p-8 text-center transition-all duration-300 ${
          dragActive 
            ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20' 
            : uploadedImage 
              ? 'border-green-500 bg-green-50 dark:bg-green-900/20'
              : 'border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500'
        }`}
        onDragEnter={handleDrag}
        onDragLeave={handleDrag}
        onDragOver={handleDrag}
        onDrop={handleDrop}
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
      >
        <input
          ref={fileInputRef}
          type="file"
          accept="image/*"
          onChange={handleFileInput}
          className="hidden"
        />

        {!uploadedImage ? (
          <div className="space-y-4">
            <div className="flex justify-center">
              <div className="p-4 bg-gray-100 dark:bg-gray-700 rounded-full">
                <FiImage className="h-8 w-8 text-gray-600 dark:text-gray-300" />
              </div>
            </div>

            <div>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                {isEnglish ? 'Upload an Image for Analysis' : 'Tải lên hình ảnh để phân tích'}
              </h3>
              <p className="text-gray-600 dark:text-gray-300 mb-4">
                {isEnglish 
                  ? 'Drag and drop an image here, or click to select'
                  : 'Kéo và thả hình ảnh vào đây, hoặc nhấp để chọn'
                }
              </p>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                {isEnglish 
                  ? 'Supports JPG, PNG, WebP (max 10MB)'
                  : 'Hỗ trợ JPG, PNG, WebP (tối đa 10MB)'
                }
              </p>
            </div>

            <div className="flex gap-3 justify-center">
              <button
                onClick={() => fileInputRef.current?.click()}
                className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                <FiUpload className="h-4 w-4" />
                {isEnglish ? 'Choose File' : 'Chọn tệp'}
              </button>

              <button
                onClick={() => fileInputRef.current?.click()}
                className="flex items-center gap-2 px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
              >
                <FiCamera className="h-4 w-4" />
                {isEnglish ? 'Camera' : 'Camera'}
              </button>
            </div>
          </div>
        ) : (
          <div className="space-y-4">
            {/* Image Preview */}
            <div className="relative inline-block">
              <img
                src={imagePreview}
                alt="Preview"
                className="max-w-full max-h-64 rounded-lg shadow-lg"
              />
              <button
                onClick={clearImage}
                className="absolute -top-2 -right-2 p-1 bg-red-500 text-white rounded-full hover:bg-red-600 transition-colors"
              >
                <FiX className="h-4 w-4" />
              </button>
            </div>

            {/* Upload Progress */}
            {uploadProgress > 0 && uploadProgress < 100 && (
              <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                <motion.div
                  className="bg-blue-600 h-2 rounded-full"
                  initial={{ width: 0 }}
                  animate={{ width: `${uploadProgress}%` }}
                  transition={{ duration: 0.3 }}
                />
              </div>
            )}

            {/* Action Buttons */}
            <div className="flex gap-3 justify-center">
              {!analysisResult && (
                <button
                  onClick={uploadAndAnalyze}
                  disabled={isAnalyzing || uploadProgress > 0}
                  className="flex items-center gap-2 px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                >
                  {isAnalyzing || uploadProgress > 0 ? (
                    <FiLoader className="h-4 w-4 animate-spin" />
                  ) : (
                    <FiEye className="h-4 w-4" />
                  )}
                  {isEnglish ? 'Analyze Image' : 'Phân tích hình ảnh'}
                </button>
              )}

              <button
                onClick={clearImage}
                className="flex items-center gap-2 px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
              >
                <FiX className="h-4 w-4" />
                {isEnglish ? 'Remove' : 'Xóa'}
              </button>
            </div>
          </div>
        )}
      </motion.div>

      {/* Analysis Results */}
      <AnimatePresence>
        {analysisResult && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="mt-6 p-6 bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700"
          >
            <div className="flex items-center gap-2 mb-4">
              <FiCheck className="h-5 w-5 text-green-500" />
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                {isEnglish ? 'Analysis Complete' : 'Phân tích hoàn thành'}
              </h3>
            </div>

            <div className="space-y-4">
              {/* AI Response */}
              <div className="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                <p className="text-gray-800 dark:text-gray-200">
                  {analysisResult.response}
                </p>
              </div>

              {/* Related Models */}
              {analysisResult.relatedModels && analysisResult.relatedModels.length > 0 && (
                <div>
                  <h4 className="font-medium text-gray-900 dark:text-white mb-3">
                    {isEnglish ? 'Related 3D Models' : 'Mô hình 3D liên quan'}
                  </h4>
                  <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
                    {analysisResult.relatedModels.map((model, index) => (
                      <div
                        key={index}
                        className="p-3 border border-gray-200 dark:border-gray-600 rounded-lg hover:shadow-md transition-shadow"
                      >
                        <h5 className="font-medium text-sm text-gray-900 dark:text-white truncate">
                          {model.title}
                        </h5>
                        <p className="text-xs text-gray-600 dark:text-gray-400 mt-1">
                          {model.category} • {model.downloads} {isEnglish ? 'downloads' : 'lượt tải'}
                        </p>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Image Metadata */}
              {analysisResult.metadata && (
                <div className="text-xs text-gray-500 dark:text-gray-400 pt-3 border-t border-gray-200 dark:border-gray-600">
                  <p>
                    {isEnglish ? 'Processed:' : 'Đã xử lý:'} {analysisResult.metadata.dimensions.width}×{analysisResult.metadata.dimensions.height} • 
                    {isEnglish ? 'Compression:' : 'Nén:'} {Math.round(analysisResult.metadata.compressionRatio)}%
                  </p>
                </div>
              )}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default ImageUploadChatbot;
