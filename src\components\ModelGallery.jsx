import React from 'react';
import ModelCard from './ModelCard';
import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import { FiDownload, FiEye, FiHeart, FiStar } from 'react-icons/fi';
import ImageWithFallback from './ui/ImageWithFallback';

const ModelListItem = ({ model }) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden flex flex-col sm:flex-row"
    >
      <div className="sm:w-48 h-48 sm:h-auto flex-shrink-0">
        <Link to={`/model/${model._id || model.id}`}>
          <ImageWithFallback
            src={model.imageUrl}
            alt={model.title}
            className="w-full h-full object-cover"
            lowResSrc="/images/placeholder-tiny.jpg"
          />
        </Link>
      </div>

      <div className="p-4 flex-grow">
        <div className="flex justify-between items-start">
          <div>
            <Link to={`/model/${model._id || model.id}`} className="text-lg font-semibold text-gray-900 dark:text-white hover:text-blue-600 dark:hover:text-blue-400">
              {model.title}
            </Link>
            <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
              {model.category} {model.subcategory ? `/ ${model.subcategory}` : ''}
            </p>
          </div>

          <div className="flex items-center">
            {model.isPremium ? (
              <span className="bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200 text-xs font-medium px-2.5 py-0.5 rounded">
                Premium
              </span>
            ) : (
              <span className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200 text-xs font-medium px-2.5 py-0.5 rounded">
                Free
              </span>
            )}
          </div>
        </div>

        <p className="text-gray-600 dark:text-gray-300 text-sm mt-2 line-clamp-2">
          {model.description}
        </p>

        <div className="mt-4 flex flex-wrap gap-2">
          <span className="inline-flex items-center text-xs text-gray-500 dark:text-gray-400">
            <FiStar className="w-3.5 h-3.5 mr-1 text-yellow-500" />
            {model.rating || '4.5'} ({model.reviewCount || '12'})
          </span>

          <span className="inline-flex items-center text-xs text-gray-500 dark:text-gray-400">
            <FiDownload className="w-3.5 h-3.5 mr-1" />
            {model.downloads || '234'} downloads
          </span>

          <span className="inline-flex items-center text-xs text-gray-500 dark:text-gray-400">
            <FiEye className="w-3.5 h-3.5 mr-1" />
            {model.views || '1.2k'} views
          </span>

          <span className="inline-flex items-center text-xs text-gray-500 dark:text-gray-400">
            <FiHeart className="w-3.5 h-3.5 mr-1" />
            {model.likes || '56'} likes
          </span>
        </div>

        <div className="mt-4 flex flex-wrap gap-2">
          {model.format && (
            <span className="bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300 text-xs px-2 py-1 rounded">
              {model.format}
            </span>
          )}

          {model.textured && (
            <span className="bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300 text-xs px-2 py-1 rounded">
              Textured
            </span>
          )}

          {model.rigged && (
            <span className="bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300 text-xs px-2 py-1 rounded">
              Rigged
            </span>
          )}

          {model.animated && (
            <span className="bg-pink-100 text-pink-800 dark:bg-pink-900 dark:text-pink-300 text-xs px-2 py-1 rounded">
              Animated
            </span>
          )}
        </div>
      </div>

      <div className="p-4 flex flex-row sm:flex-col justify-between items-center sm:items-end border-t sm:border-t-0 sm:border-l border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-900">
        {model.price ? (
          <div className="text-lg font-bold text-gray-900 dark:text-white">
            ${model.price.toFixed(2)}
          </div>
        ) : (
          <div className="text-lg font-bold text-green-600 dark:text-green-400">
            Free
          </div>
        )}

        <Link
          to={`/model/${model._id || model.id}`}
          className="mt-2 inline-flex items-center px-3 py-2 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 rounded-md shadow-sm transition-colors"
        >
          View Details
        </Link>
      </div>
    </motion.div>
  );
};

const ModelGallery = ({ models, viewMode = 'grid' }) => {
  // Animation variants for container
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  return (
    <div className="container mx-auto">
      {viewMode === 'grid' ? (
        <motion.div
          className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6"
          variants={containerVariants}
          initial="hidden"
          animate="visible"
        >
          {models.map((model) => (
            <ModelCard key={model._id || model.id} model={model} />
          ))}
        </motion.div>
      ) : (
        <motion.div
          className="flex flex-col space-y-4"
          variants={containerVariants}
          initial="hidden"
          animate="visible"
        >
          {models.map((model) => (
            <ModelListItem key={model._id || model.id} model={model} />
          ))}
        </motion.div>
      )}

      {models.length === 0 && (
        <motion.div
          className="text-center py-12 bg-white dark:bg-gray-800 rounded-lg shadow-md"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <h3 className="mt-2 text-xl font-medium text-gray-900 dark:text-white">No models found</h3>
          <p className="mt-1 text-gray-500 dark:text-gray-400">Try adjusting your filters or search term</p>
          <div className="mt-6">
            <button
              onClick={() => window.location.reload()}
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              Reset all filters
            </button>
          </div>
        </motion.div>
      )}
    </div>
  );
};

export default ModelGallery;
