import React from 'react';
import { motion } from 'framer-motion';
import { FiTarget, FiAward, FiGlobe, FiCode, FiUsers, FiTrendingUp } from 'react-icons/fi';
import Header from '../components/Header';
import Footer from '../components/Footer';
import PageTransition from '../components/PageTransition';

const About = () => {
  const teamMembers = [
    {
      name: "<PERSON>",
      role: "Founder & CEO",
      bio: "Passionate about democratizing 3D design with over 10 years in the industry.",
      image: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80"
    },
    {
      name: "<PERSON>",
      role: "Head of Design",
      bio: "Leading our design vision with expertise in 3D modeling and user experience.",
      image: "https://images.unsplash.com/photo-1494790108755-2616b612b786?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80"
    },
    {
      name: "<PERSON>",
      role: "CT<PERSON>",
      bio: "Building the technical infrastructure that powers our platform.",
      image: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80"
    },
    {
      name: "Emily Davis",
      role: "Community Manager",
      bio: "Fostering our vibrant community of creators and designers.",
      image: "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80"
    }
  ];

  return (
    <div className="flex flex-col min-h-screen bg-gradient-to-br from-gray-50 via-blue-50 to-purple-50 dark:from-gray-900 dark:via-blue-900 dark:to-purple-900">
      <Header />

      <PageTransition>
        {/* Hero Section */}
        <section className="relative overflow-hidden py-20 bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600">
          <div className="absolute inset-0 bg-black/20"></div>
          <div className="container mx-auto px-4 relative z-10">
            <div className="max-w-4xl mx-auto text-center text-white">
              <motion.h1
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8 }}
                className="text-5xl md:text-6xl font-black mb-6"
              >
                About 3DSKETCHUP.NET
              </motion.h1>
              <motion.p
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.2 }}
                className="text-xl md:text-2xl leading-relaxed"
              >
                Empowering creators worldwide with premium 3D models and cutting-edge design tools
              </motion.p>
            </div>
          </div>
        </section>

        {/* Company Overview */}
        <section className="py-16">
          <div className="container mx-auto px-4">
            <div className="max-w-6xl mx-auto">
              <motion.div
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.5, delay: 0.2 }}
                className="relative h-80 md:h-96 rounded-xl overflow-hidden shadow-xl"
              >
                <img
                  src="https://images.unsplash.com/photo-1581094794329-c8112a89af12?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80"
                  alt="3D modeling workspace"
                  className="w-full h-full object-cover"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent flex items-end">
                  <div className="p-6 text-white">
                    <p className="text-lg font-medium">Established in 2020</p>
                    <p>Serving over 50,000 designers and architects worldwide</p>
                  </div>
                </div>
              </motion.div>
            </div>
          </div>
        </section>

        {/* Our Mission */}
        <section className="container mx-auto px-4 py-16 mb-16">
          <div className="max-w-6xl mx-auto">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
              <motion.div
                initial={{ opacity: 0, x: -30 }}
                whileInView={{ opacity: 1, x: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6 }}
                className="glass-card p-8 rounded-3xl border border-white/20 shadow-professional"
              >
                <div className="flex items-center mb-6">
                  <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center mr-4">
                    <FiTarget className="h-8 w-8 text-white" />
                  </div>
                  <h2 className="text-3xl font-black text-gray-900 dark:text-white">Sứ Mệnh</h2>
                </div>
                <p className="text-lg text-gray-600 dark:text-gray-300 mb-6 leading-relaxed">
                  Tại 3DSKETCHUP.NET, sứ mệnh của chúng tôi là dân chủ hóa việc tiếp cận các mô hình 3D chất lượng cao,
                  giúp các nhà sáng tạo ở mọi cấp độ kỹ năng hiện thực hóa tầm nhìn của họ mà không gặp rào cản kỹ thuật.
                </p>
                <p className="text-lg text-gray-600 dark:text-gray-300 leading-relaxed">
                  Chúng tôi tin rằng mọi người đều nên có quyền truy cập vào các công cụ và tài nguyên cần thiết để tạo ra
                  những hình ảnh 3D tuyệt đẹp, dù là cho dự án chuyên nghiệp, mục đích giáo dục hay sáng tạo cá nhân.
                </p>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, x: 30 }}
                whileInView={{ opacity: 1, x: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6 }}
                className="space-y-6"
              >
                {/* Vision Card */}
                <div className="glass-card p-6 rounded-2xl border border-white/20 bg-gradient-to-r from-blue-600 to-purple-600 text-white">
                  <div className="flex items-center mb-4">
                    <FiTarget className="h-8 w-8 mr-3" />
                    <h3 className="text-2xl font-bold">Tầm Nhìn</h3>
                  </div>
                  <p className="text-lg leading-relaxed">
                    Trở thành nền tảng hàng đầu thế giới về nội dung 3D, nơi các nhà sáng tạo có thể tìm kiếm, chia sẻ và
                    hợp tác trên các mô hình đẩy ranh giới của thiết kế kỹ thuật số.
                  </p>
                </div>

                {/* Values Card */}
                <div className="glass-card p-6 rounded-2xl border border-white/20">
                  <div className="flex items-center mb-4">
                    <div className="w-12 h-12 bg-gradient-to-r from-green-500 to-teal-600 rounded-xl flex items-center justify-center mr-3">
                      <FiAward className="h-6 w-6 text-white" />
                    </div>
                    <h3 className="text-2xl font-bold text-gray-900 dark:text-white">Giá Trị Cốt Lõi</h3>
                  </div>
                  <div className="grid grid-cols-2 gap-3">
                    {[
                      '🏆 Chất lượng trên số lượng',
                      '🌍 Khả năng tiếp cận và bao gồm',
                      '💪 Trao quyền cho nhà sáng tạo',
                      '🚀 Đổi mới liên tục'
                    ].map((value, index) => (
                      <div key={index} className="flex items-center p-3 bg-gray-50 dark:bg-gray-700 rounded-xl">
                        <span className="text-sm font-medium text-gray-700 dark:text-gray-300">{value}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </motion.div>
            </div>
          </div>
        </section>

        {/* What We Offer */}
        <section className="bg-white dark:bg-gray-800 py-16 mb-16">
          <div className="container mx-auto px-4">
            <div className="max-w-4xl mx-auto text-center mb-12">
              <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">What We Offer</h2>
              <p className="text-xl text-gray-600 dark:text-gray-300">
                Comprehensive solutions for all your 3D modeling needs
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {[
                {
                  icon: <FiGlobe className="h-10 w-10 text-blue-600" />,
                  title: "Extensive Model Library",
                  description: "Access thousands of high-quality 3D models across various categories, from architecture to product design."
                },
                {
                  icon: <FiCode className="h-10 w-10 text-blue-600" />,
                  title: "Multiple Format Support",
                  description: "Download models in various formats including OBJ, FBX, GLTF, and STL to suit your workflow."
                },
                {
                  icon: <FiUsers className="h-10 w-10 text-blue-600" />,
                  title: "Creator Community",
                  description: "Join a vibrant community of designers, architects, and 3D enthusiasts to share ideas and get inspired."
                },
                {
                  icon: <FiTrendingUp className="h-10 w-10 text-blue-600" />,
                  title: "Regular Updates",
                  description: "Our library is constantly growing with new models added weekly to keep up with the latest trends."
                }
              ].map((item, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  className="bg-gray-50 dark:bg-gray-700 p-6 rounded-xl shadow-md"
                >
                  <div className="flex flex-col items-center text-center">
                    <div className="mb-4">{item.icon}</div>
                    <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-2">{item.title}</h3>
                    <p className="text-gray-600 dark:text-gray-300">{item.description}</p>
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* Our Team */}
        <section className="container mx-auto px-4 py-12 mb-16">
          <div className="max-w-4xl mx-auto text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">Meet Our Team</h2>
            <p className="text-xl text-gray-600 dark:text-gray-300">
              The passionate professionals behind 3DSKETCHUP.NET
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {teamMembers.map((member, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                className="bg-white dark:bg-gray-800 rounded-xl shadow-md overflow-hidden"
              >
                <img
                  src={member.image}
                  alt={member.name}
                  className="w-full h-48 object-cover"
                />
                <div className="p-6">
                  <h3 className="text-xl font-bold text-gray-900 dark:text-white">{member.name}</h3>
                  <p className="text-blue-600 dark:text-blue-400 mb-2">{member.role}</p>
                  <p className="text-gray-600 dark:text-gray-300 text-sm">{member.bio}</p>
                </div>
              </motion.div>
            ))}
          </div>
        </section>
      </PageTransition>

      <Footer />
    </div>
  );
};

export default About;
