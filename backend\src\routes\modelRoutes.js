import express from 'express';
import {
  getModels,
  getModel,
  createModel,
  createEnhancedModel,
  updateModel,
  deleteModel,
  downloadModel,
  getFeaturedModels,
  getPopularModels,
  getRecentModels
} from '../controllers/modelController.js';
import { protect, authorize, authorizePermission } from '../middleware/auth.js';

const router = express.Router();

router.route('/')
  .get(getModels)
  .post(protect, authorizePermission('upload'), createModel);

// Special routes should come before parameterized routes to avoid conflicts
router.route('/featured')
  .get(getFeaturedModels);

router.route('/popular')
  .get(getPopularModels);

router.route('/recent')
  .get(getRecentModels);

// Enhanced model creation with multiple images
router.route('/enhanced')
  .post(protect, authorize('admin'), createEnhancedModel);

// Test endpoint without authentication
router.route('/test')
  .post(createModel);

router.route('/:id')
  .get(getModel)
  .put(protect, updateModel)
  .delete(protect, authorize('admin', 'moderator'), deleteModel);

router.route('/:id/download')
  .get(protect, downloadModel);

export default router;
