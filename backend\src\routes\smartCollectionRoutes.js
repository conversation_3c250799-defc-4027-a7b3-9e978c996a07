import express from 'express';
import {
  createSmartCollection,
  getSmartCollections,
  getSmartCollection,
  updateSmartCollection,
  deleteSmartCollection,
  addModelToCollection,
  removeModelFromCollection,
  updateSmartCriteria,
  getAISuggestions,
  applyAISuggestion,
  voteOnSuggestion
} from '../controllers/smartCollectionController.js';
import { protect, authorize } from '../middleware/auth.js';

const router = express.Router();

// Public routes
router.get('/', getSmartCollections);
router.get('/:id', getSmartCollection);

// Protected routes
router.use(protect);

router.post('/', createSmartCollection);
router.put('/:id', updateSmartCollection);
router.delete('/:id', deleteSmartCollection);

// Model management in collections
router.post('/:id/models', addModelToCollection);
router.delete('/:id/models/:modelId', removeModelFromCollection);

// Smart criteria management
router.put('/:id/criteria', updateSmartCriteria);

// AI suggestions
router.get('/:id/suggestions', getAISuggestions);
router.post('/:id/suggestions/:suggestionId/apply', applyAISuggestion);
router.post('/:id/suggestions/:suggestionId/vote', voteOnSuggestion);

export default router;
