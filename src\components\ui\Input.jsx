import React, { forwardRef } from 'react';
import PropTypes from 'prop-types';
import { motion } from 'framer-motion';

/**
 * Enhanced Input component with various styles and features
 *
 * @param {Object} props - Component props
 * @param {string} props.type - Input type
 * @param {string} props.id - Input ID
 * @param {string} props.name - Input name
 * @param {string} props.value - Input value
 * @param {Function} props.onChange - Change handler
 * @param {string} props.placeholder - Input placeholder
 * @param {string} props.label - Input label
 * @param {string} props.helperText - Helper text
 * @param {string} props.errorText - Error text
 * @param {boolean} props.required - Whether the input is required
 * @param {boolean} props.disabled - Whether the input is disabled
 * @param {boolean} props.readOnly - Whether the input is read-only
 * @param {string} props.size - Input size (sm, md, lg)
 * @param {React.ReactNode} props.leftIcon - Icon to display on the left
 * @param {React.ReactNode} props.rightIcon - Icon to display on the right
 * @param {string} props.className - Additional CSS classes
 * @param {string} props.inputClassName - Additional CSS classes for the input element
 * @param {string} props.labelClassName - Additional CSS classes for the label
 * @returns {React.ReactElement} The Input component
 */
const Input = forwardRef(({
    // Fixed content
  }
  type = 'text',
  id,
  name,
  value,
  onChange,
  placeholder,
  label,
  helperText,
  errorText,
  required = false,
  disabled = false,
  readOnly = false,
  size = 'md',
  leftIcon,
  rightIcon,
  className = '',
  inputClassName = '',
  labelClassName = '',
  ...rest
}, ref) => { /* content */ };
  // Generate ID if not provided
  const inputId = id || `input-${name || Math.random().toString(36).substring(2, 9)}`;

  // Determine if input has error
  const hasError = !!errorText;

  // Base classes
  const wrapperClasses = `form-group ${className}`;

  // Label classes
  const labelClasses = `label ${labelClassName}`;

  // Input classes
  const baseInputClasses = 'input';

  // Size classes
  const sizeClasses = {
    sm: 'input-sm',
    md: '',
    lg: 'input-lg',
  };

  // Status classes
  const statusClasses = hasError ? 'input-error' : '';

  // Icon classes
  const iconClasses = [
    leftIcon ? 'pl-10' : '',
    rightIcon ? 'pr-10' : '',
  ].filter(Boolean).join(' ''; // Fixed broken string

  // Additional classes
  const additionalClasses = [
    disabled ? 'opacity-60 cursor-not-allowed' : '',
    readOnly ? 'bg-gray-100 dark:bg-gray-800' : '',
    inputClassName,
  ].filter(Boolean).join(' ''; // Fixed broken string

  // Combine all input classes
  const inputClasses = [
    baseInputClasses,
    sizeClasses[size],
    statusClasses,
    iconClasses,
    additionalClasses,
  ].filter(Boolean).join(' ''; // Fixed broken string

  // Animation for focus
  const inputAnimation = {
    whileFocus: { scale: 1.01 },
    transition: { duration: 0.2 },
  };

  return (
    <div className={wrapperClasses}>
      {label && (
        <label htmlFor={inputId} className={labelClasses}>
          {label}
          {required && <span className="text-error-500 ml-1">*</span>}
        </label>
      )}

      <div className="relative">
        {leftIcon && (
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            {leftIcon}
          </div>
        )}

        <motion.input
          ref={ref}
          type={type}
          id={inputId}
          name={name}
          value={value}
          onChange={onChange}
          placeholder={placeholder}
          required={required}
          disabled={disabled}
          readOnly={readOnly}
          className={inputClasses}
          whileFocus="whileFocus"
          variants={inputAnimation}
          {...rest}
        />

        {rightIcon && (
          <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
            {rightIcon}
          </div>
        )}
      </div>

      {(helperText || errorText) && (
        <div className={`mt-1 text-sm ${hasError ? 'form-error' : 'form-helper'}`}>
          {errorText || helperText}
        </div>
      )}
    </div>
  );
});

Input.displayName = 'Input';

Input.propTypes = {
    type: PropTypes.string,
  id: PropTypes.string,
  name: PropTypes.string,
  value: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  onChange: PropTypes.func,
  placeholder: PropTypes.string,
  label: PropTypes.string,
  helperText: PropTypes.string,
  errorText: PropTypes.string,
  required: PropTypes.bool,
  disabled: PropTypes.bool,
  readOnly: PropTypes.bool,
  size: PropTypes.oneOf(['sm', 'md', 'lg']),
  leftIcon: PropTypes.node,
  rightIcon: PropTypes.node,
  className: PropTypes.string,
  inputClassName: PropTypes.string,
  labelClassName: PropTypes.string,
};

export default Input;
