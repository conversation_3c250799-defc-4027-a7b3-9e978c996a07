import mongoose from 'mongoose';
import dotenv from 'dotenv';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

// Load environment variables
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
dotenv.config({ path: join(__dirname, '../../.env') });

// Import models
import Model from '../src/models/Model.js';
import User from '../src/models/User.js';

// Connect to MongoDB
const connectDB = async () => {
  try {
    console.log('Connecting to MongoDB...');
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('MongoDB Connected for adding model...');
  } catch (error) {
    console.error('Error connecting to MongoDB:', error);
    process.exit(1);
  }
};

// Luxury Bedroom Model Data
const luxuryBedroomModel = {
  title: 'Luxury Bedroom Interior',
  description: 'Elegant luxury bedroom with premium furniture and lighting. Complete interior design with high-quality textures and materials. Perfect for architectural visualization and interior design projects.',
  category: 'Residential', // Using valid category from schema
  subcategory: 'Bedroom',
  format: 'Sketchup 2023', // Using valid format from schema
  fileFormat: 'skp', // Using valid fileFormat from schema
  tags: ['luxury', 'bedroom', 'interior', 'premium', 'furniture', 'lighting', 'enscape', 'sketchup'],

  // File information
  fileUrl: 'https://3dsketchup.net/free-download/luxury-bedroom-interior-skp_model-enscape-0401080323/',
  imageUrl: 'https://images.unsplash.com/photo-1560448204-e02f11c3d0e2?w=400&h=300&fit=crop', // Required field

  // Technical specifications
  fileSize: 25.8, // MB

  // Model specifications (using schema fields)
  polygonCount: 85000,
  textured: true,
  rigged: false,
  animated: false,
  dimensions: {
    width: 6,
    height: 3.2,
    depth: 4.5,
    unit: 'm'
  },

  // Metrics (using schema fields)
  downloads: 1450,
  views: 3200,
  rating: 4.8,

  // Status and permissions (using schema fields)
  isPremium: false
};

// Add model function
const addLuxuryBedroomModel = async () => {
  try {
    console.log('Starting to add luxury bedroom model...');

    // Check if model already exists
    const existingModel = await Model.findOne({
      title: luxuryBedroomModel.title
    });

    if (existingModel) {
      console.log('Model already exists:', existingModel.title);
      return existingModel;
    }

    // Find an admin user to assign as uploader
    let adminUser = await User.findOne({ role: 'admin' });

    if (!adminUser) {
      // Create admin user if doesn't exist
      console.log('Creating admin user...');
      adminUser = await User.create({
        name: 'Admin User',
        email: '<EMAIL>',
        password: 'admin123',
        role: 'admin',
        bio: 'System Administrator'
      });
      console.log('Admin user created');
    }

    // Assign creator (using schema field)
    luxuryBedroomModel.createdBy = adminUser._id;

    // Create the model
    const newModel = await Model.create(luxuryBedroomModel);
    console.log('✅ Luxury bedroom model added successfully!');
    console.log('Model ID:', newModel._id);
    console.log('Title:', newModel.title);
    console.log('Category:', newModel.category);
    console.log('File URL:', newModel.fileUrl);

    return newModel;

  } catch (error) {
    console.error('❌ Error adding luxury bedroom model:', error);
    throw error;
  }
};

// Run the script
const main = async () => {
  try {
    await connectDB();
    await addLuxuryBedroomModel();
    console.log('\n🎉 Script completed successfully!');
    console.log('\nThe luxury bedroom model is now available on the website.');
    console.log('You can find it in the Interior Design > Bedroom category.');
  } catch (error) {
    console.error('❌ Script failed:', error);
  } finally {
    mongoose.connection.close();
    console.log('Database connection closed.');
  }
};

// Execute
main();
