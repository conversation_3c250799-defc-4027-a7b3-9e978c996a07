import axios from 'axios';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

/**
 * Link Checker and Proxy Download Utility
 * Checks multiple backup links and downloads files through proxy
 */
class LinkChecker {
  constructor() {
    this.downloadDir = path.join(__dirname, '../../downloads');
    this.ensureDownloadDir();
  }

  ensureDownloadDir() {
    if (!fs.existsSync(this.downloadDir)) {
      fs.mkdirSync(this.downloadDir, { recursive: true });
    }
  }

  /**
   * Check if a link is still valid and accessible
   * @param {string} url - URL to check
   * @returns {Promise<boolean>} - True if link is valid
   */
  async checkLink(url) {
    try {
      const response = await axios.head(url, {
        timeout: 10000,
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
      });
      
      return response.status === 200;
    } catch (error) {
      console.log(`Link check failed for ${url}:`, error.message);
      return false;
    }
  }

  /**
   * Check multiple backup links and return the first working one
   * @param {Array<string>} links - Array of backup links
   * @returns {Promise<string|null>} - First working link or null
   */
  async findWorkingLink(links) {
    console.log(`Checking ${links.length} backup links...`);
    
    for (let i = 0; i < links.length; i++) {
      const link = links[i];
      console.log(`Checking link ${i + 1}/${links.length}: ${link}`);
      
      const isWorking = await this.checkLink(link);
      if (isWorking) {
        console.log(`✅ Working link found: ${link}`);
        return link;
      }
    }
    
    console.log('❌ No working links found');
    return null;
  }

  /**
   * Download file from external source and serve through our server
   * @param {string} url - External file URL
   * @param {string} filename - Local filename to save as
   * @returns {Promise<Object>} - Download result
   */
  async proxyDownload(url, filename) {
    try {
      console.log(`Starting proxy download from: ${url}`);
      
      const response = await axios({
        method: 'GET',
        url: url,
        responseType: 'stream',
        timeout: 60000, // 1 minute timeout
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
      });

      const filePath = path.join(this.downloadDir, filename);
      const writer = fs.createWriteStream(filePath);

      response.data.pipe(writer);

      return new Promise((resolve, reject) => {
        writer.on('finish', () => {
          const stats = fs.statSync(filePath);
          console.log(`✅ File downloaded successfully: ${filename} (${stats.size} bytes)`);
          
          resolve({
            success: true,
            filePath: filePath,
            filename: filename,
            size: stats.size,
            localUrl: `/downloads/${filename}`
          });
        });

        writer.on('error', (error) => {
          console.error(`❌ Download failed: ${error.message}`);
          reject(error);
        });
      });

    } catch (error) {
      console.error(`❌ Proxy download failed: ${error.message}`);
      throw error;
    }
  }

  /**
   * Smart download with backup links
   * @param {Array<string>} backupLinks - Array of backup download links
   * @param {string} filename - Desired filename
   * @returns {Promise<Object>} - Download result
   */
  async smartDownload(backupLinks, filename) {
    try {
      // Find working link
      const workingLink = await this.findWorkingLink(backupLinks);
      
      if (!workingLink) {
        throw new Error('No working download links available');
      }

      // Download through proxy
      const result = await this.proxyDownload(workingLink, filename);
      
      return {
        ...result,
        sourceUrl: workingLink,
        backupLinksChecked: backupLinks.length
      };

    } catch (error) {
      console.error('Smart download failed:', error);
      throw error;
    }
  }

  /**
   * Get file info without downloading
   * @param {string} url - File URL
   * @returns {Promise<Object>} - File information
   */
  async getFileInfo(url) {
    try {
      const response = await axios.head(url, {
        timeout: 10000,
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
      });

      return {
        size: response.headers['content-length'] || 'Unknown',
        type: response.headers['content-type'] || 'Unknown',
        lastModified: response.headers['last-modified'] || 'Unknown',
        isAccessible: true
      };
    } catch (error) {
      return {
        size: 'Unknown',
        type: 'Unknown',
        lastModified: 'Unknown',
        isAccessible: false,
        error: error.message
      };
    }
  }

  /**
   * Clean up old downloaded files (older than 24 hours)
   */
  async cleanupOldFiles() {
    try {
      const files = fs.readdirSync(this.downloadDir);
      const now = Date.now();
      const maxAge = 24 * 60 * 60 * 1000; // 24 hours

      for (const file of files) {
        const filePath = path.join(this.downloadDir, file);
        const stats = fs.statSync(filePath);
        
        if (now - stats.mtime.getTime() > maxAge) {
          fs.unlinkSync(filePath);
          console.log(`🗑️ Cleaned up old file: ${file}`);
        }
      }
    } catch (error) {
      console.error('Cleanup failed:', error);
    }
  }
}

export default LinkChecker;
