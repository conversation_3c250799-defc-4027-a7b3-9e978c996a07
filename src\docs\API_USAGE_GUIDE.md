# API Usage Guide for 3DSKETCHUP.NET

## ✅ CORRECT: Use Real API Services

### 1. Use realDataService for all data fetching:
```javascript
import realDataService from '../services/realDataService';

// Get all models
const models = await realDataService.getAllModels();

// Get featured models
const featured = await realDataService.getFeaturedModels();

// Get popular models
const popular = await realDataService.getPopularModels();

// Get recent models
const recent = await realDataService.getRecentModels();

// Get categories
const categories = await realDataService.getCategories();

// Get statistics
const stats = await realDataService.getStats();
```

### 2. Use mongoService for direct MongoDB access:
```javascript
import mongoService from '../services/mongoService';

// Get models with parameters
const models = await mongoService.getModels({ limit: 10 });

// Get model by ID
const model = await mongoService.getModelById(id);
```

### 3. Use apiService for REST API calls:
```javascript
import apiService from '../services/api';

// Get models via API
const response = await apiService.models.getAll();
const models = response.data;
```

## ❌ AVOID: Mock Data

### Don't use hardcoded data:
```javascript
// ❌ BAD
const mockModels = [
  { id: 1, title: 'Test Model' },
  // ...
];

// ❌ BAD
const hardcodedStats = {
  models: 100,
  downloads: 1000
};
```

### Instead, always fetch from API:
```javascript
// ✅ GOOD
const models = await realDataService.getAllModels();
const stats = await realDataService.getStats();
```

## Performance Tips

1. Use caching (realDataService has built-in caching)
2. Use React.memo for components
3. Use useCallback for event handlers
4. Batch API calls with Promise.all when possible

## Error Handling

Always handle errors gracefully:
```javascript
try {
  const models = await realDataService.getAllModels();
  setModels(models);
} catch (error) {
  setError('Failed to load models');
  setModels([]); // Fallback to empty array
}
```
