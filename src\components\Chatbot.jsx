import React, { useState, useRef, useEffect, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import axios from 'axios';
import {
  FiMessageSquare as ChatIcon,
  FiX as XIcon,
  FiSend as PaperAirplaneIcon,
  FiMic as MicIcon,
  FiVolume2 as SpeakerIcon,
  FiMinimize2 as MinimizeIcon,
  FiMaximize2 as MaximizeIcon,
  FiGlobe as LanguageIcon,
  FiWifi as ConnectionIcon,
  FiWifiOff as NoConnectionIcon,
  FiRefreshCw as RefreshIcon,
  FiHelpCircle as HelpIcon,
  FiCopy as CopyIcon,
  FiCheck as CheckIcon,
  FiTrash2 as TrashIcon,
  FiZap as ZapIcon,
  FiSearch as SearchIcon,
  FiFilter as FilterIcon,
  FiSettings as SettingsIcon,
  FiStar as StarIcon,
  FiTrendingUp as TrendingIcon,
  FiUser as UserIcon,
  FiGrid as GridIcon,
  FiImage as ImageIcon,
  FiCamera as CameraIcon,
  FiUpload as UploadIcon,
  <PERSON><PERSON><PERSON>clip as AttachIcon
} from 'react-icons/fi';
import { FaRobot } from 'react-icons/fa';
import ReactMarkdown from 'react-markdown';
import ImageUploadChatbot from './ImageUploadChatbot';
import ImageMessage from './chat/ImageMessage';

// API URL for chat - use environment variable if available
const API_URL = import.meta.env.VITE_CHAT_API_URL || 'http://localhost:5002/api/chat';

// Create axios instance with timeout and error handling
const chatApi = axios.create({
    baseURL: API_URL,
  timeout: 30000, // 30 seconds
  headers: {
    'Content-Type': 'application/json'
  }
});

// Add request interceptor for debugging
chatApi.interceptors.request.use(
  config => {
  return config;
  },
  error => {
  return Promise.reject(error);
  }
);

// Add response interceptor for debugging
chatApi.interceptors.response.use(
  response => {
  return response;
  },
  error => {
  return Promise.reject(error);
  }
);

const Chatbot = () => {
  // UI state
  const [isOpen, setIsOpen] = useState(false);
  const [isMinimized, setIsMinimized] = useState(false);
  const [inputValue, setInputValue] = useState('');
  const [debouncedInputValue, setDebouncedInputValue] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const [theme, setTheme] = useState(() => {
    // Check if user prefers dark mode
    if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
      return 'dark';
    }
    return 'light';
  });
  const [copiedMessageId, setCopiedMessageId] = useState(null);
  const [showSuggestions, setShowSuggestions] = useState(true);

  // Language state - moved up to be available for assistantModes
  const [language, setLanguage] = useState(() => {
    // Try to load language preference from localStorage
    const savedLanguage = localStorage.getItem('chatLanguage');
    return savedLanguage || 'en-US'; // 'en-US' or 'vi-VN'
  });

  // AI Assistant modes
  const [currentMode, setCurrentMode] = useState('general');
  const [showModeSelector, setShowModeSelector] = useState(false);
  const [showAdvancedSearch, setShowAdvancedSearch] = useState(false);
  const [searchFilters, setSearchFilters] = useState({
    category: '',
    tags: [],
    dateRange: { start: '', end: '' },
    fileFormat: '',
    minRating: 0,
    minDownloads: 0,
    author: '',
    sortBy: 'relevance',
    sortOrder: 'desc'
  });

  // Debounce input to prevent rapid-fire requests
  useEffect(() => {
    const handler = setTimeout(() => {
  setDebouncedInputValue(inputValue);
    }, 300); // 300ms delay

    return () => {
  clearTimeout(handler);
    };
  }, [inputValue]);

  // Rate limiting state - kept for compatibility but not used
  const [isRateLimited, setIsRateLimited] = useState(false);
  const [rateLimitCountdown, setRateLimitCountdown] = useState(0);
  const [retryCount, setRetryCount] = useState(0);
  const [lastMessage, setLastMessage] = useState('');
  // Chat state
  const [messages, setMessages] = useState(() => {
    // Try to load messages from localStorage
    const savedMessages = localStorage.getItem('chatMessages');
    return savedMessages ? JSON.parse(savedMessages) : [
      {
    id: 1,
        text: "Hello! I'm your 3D model assistant for 3DSKETCHUP.NET. How can I help you today?",
        sender: 'bot',
        timestamp: new Date(),
        intent: 'greeting'
      }
    ];
  });

  // AI Assistant modes
  const assistantModes = {
    general: {
    name: language === 'en-US' ? 'General Chat' : 'Trò chuyện chung',
      icon: <ChatIcon className="w-4 h-4" />,
      color: 'blue',
      description: language === 'en-US' ? 'General questions and help' : 'Câu hỏi chung và trợ giúp'
    },
    search: {
    name: language === 'en-US' ? 'Smart Search' : 'Tìm kiếm thông minh',
      icon: <SearchIcon className="w-4 h-4" />,
      color: 'purple',
      description: language === 'en-US' ? 'Find 3D models with AI assistance' : 'Tìm mô hình 3D với trợ lý AI'
    },
    analysis: {
    name: language === 'en-US' ? 'Model Analysis' : 'Phân tích mô hình',
      icon: <ZapIcon className="w-4 h-4" />,
      color: 'green',
      description: language === 'en-US' ? 'Analyze and compare 3D models' : 'Phân tích và so sánh mô hình 3D'
    },
    recommendations: {
    name: language === 'en-US' ? 'Recommendations' : 'Gợi ý',
      icon: <StarIcon className="w-4 h-4" />,
      color: 'orange',
      description: language === 'en-US' ? 'Get personalized model recommendations' : 'Nhận gợi ý mô hình cá nhân hóa'
    }
  };

  // Mode-specific suggestions
  const getModeSuggestions = (mode) => {
  const suggestions = {
    general: {
        'en-US': [
          "What 3D models are popular right now?",
          "How do I download a model?",
          "Tell me about subscription plans",
          "What file formats are supported?",
          "How can I upload my own models?"
        ],
        'vi-VN': [
          "Những mô hình 3D nào đang phổ biến?",
          "Làm thế nào để tải xuống mô hình?",
          "Cho tôi biết về các gói đăng ký",
          "Những định dạng file nào được hỗ trợ?",
          "Làm thế nào để tải lên mô hình của tôi?"
        ]
      },
      search: {
        'en-US': [
          "Find modern kitchen models",
          "Search for outdoor furniture",
          "Show me high-quality interior scenes",
          "Find models with specific file formats",
          "Search by category and rating"
        ],
        'vi-VN': [
          "Tìm mô hình nhà bếp hiện đại",
          "Tìm kiếm đồ nội thất ngoài trời",
          "Hiển thị cảnh nội thất chất lượng cao",
          "Tìm mô hình với định dạng file cụ thể",
          "Tìm kiếm theo danh mục và đánh giá"
        ]
      },
      analysis: {
        'en-US': [
          "Analyze this model's quality",
          "Compare different furniture styles",
          "What makes a good 3D model?",
          "Technical specifications analysis",
          "Model optimization suggestions"
        ],
        'vi-VN': [
          "Phân tích chất lượng mô hình này",
          "So sánh các phong cách nội thất khác nhau",
          "Điều gì tạo nên một mô hình 3D tốt?",
          "Phân tích thông số kỹ thuật",
          "Gợi ý tối ưu hóa mô hình"
        ]
      },
      recommendations: {
        'en-US': [
          "Recommend models for my project",
          "Suggest trending collections",
          "Find models similar to this one",
          "What's popular in my category?",
          "Personalized model suggestions"
        ],
        'vi-VN': [
          "Gợi ý mô hình cho dự án của tôi",
          "Đề xuất bộ sưu tập thịnh hành",
          "Tìm mô hình tương tự như cái này",
          "Cái gì đang phổ biến trong danh mục của tôi?",
          "Gợi ý mô hình cá nhân hóa"
        ]
      }
    };
    return suggestions[mode]?.[language] || suggestions.general[language];
  };

  // Suggested questions based on current mode
  const [suggestions, setSuggestions] = useState(() => getModeSuggestions('general'));

  // State for additional data from responses
  const [modelSearchResults, setModelSearchResults] = useState([]);
  const [conversationId, setConversationId] = useState(null);

  // Speech recognition and synthesis state
  const [isListening, setIsListening] = useState(false);
  const [isSpeaking, setIsSpeaking] = useState(false);

  // Image upload state
  const [showImageUpload, setShowImageUpload] = useState(false);
  const [uploadedImage, setUploadedImage] = useState(null);
  const [isAnalyzingImage, setIsAnalyzingImage] = useState(false);
  const [imageAnalysisResult, setImageAnalysisResult] = useState(null);

  // Refs
  const messagesEndRef = useRef(null);
  const inputRef = useRef(null);
  const recognitionRef = useRef(null);
  const speechSynthesisRef = useRef(window.speechSynthesis);

  // Scroll to bottom of messages
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  // Save messages to localStorage when they change
  useEffect(() => {
  localStorage.setItem('chatMessages', JSON.stringify(messages.slice(-50))); // Only store last 50 messages
    scrollToBottom();
  }, [messages]);

  // Save language preference to localStorage when it changes
  useEffect(() => {
  localStorage.setItem('chatLanguage', language);
  }, [language]);

  // Initialize speech recognition
  useEffect(() => {
    // Check if browser supports speech recognition
    if (cachedData && !isExpired(cachedData)) {
  const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
      recognitionRef.current = new SpeechRecognition();
      recognitionRef.current.continuous = false;
      recognitionRef.current.interimResults = false;

      recognitionRef.current.onresult = (event) => {
  const transcript = event.results[0][0].transcript;
        setInputValue(transcript);
        // Auto-submit if the transcript is longer than 5 words
        if (transcript.split(' ').length > 5) {
          setTimeout(() => {
  handleSendMessage();
          }, 500); // Small delay to allow user to see what was transcribed
        }
        setIsListening(false);
      };

      recognitionRef.current.onerror = (event) => {
  setIsListening(false);
      };

      recognitionRef.current.onend = () => {
  setIsListening(false);
      };
    }

    return () => {
  if (cachedData && !isExpired(cachedData)) {
  recognitionRef.current.abort();
      }
      if (cachedData && !isExpired(cachedData)) {
  speechSynthesisRef.current.cancel();
      }
    };
  }, []);

  // Focus input when chat is opened
  useEffect(() => {
  if (cachedData && !isExpired(cachedData)) {
  setTimeout(() => {
  inputRef.current.focus();
      }, 300);
    }
  }, [isOpen, isMinimized]);

  // Toggle speech recognition
  const toggleListening = () => {
    if (cachedData && !isExpired(cachedData)) {
  recognitionRef.current?.abort();
      setIsListening(false);
    } else {
      if (cachedData && !isExpired(cachedData)) {
  recognitionRef.current.lang = language;
        recognitionRef.current.start();
        setIsListening(true);
      }
    }
  };

  // Speak text using speech synthesis
  const speakText = (text) => {
  if (cachedData && !isExpired(cachedData)) {
  speechSynthesisRef.current.cancel();

      // Clean up text for speech - remove markdown and other formatting
      const cleanText = text
        .replace(/\*\*(.*?)\*\*/g, '$1') // Remove bold markers
        .replace(/\[(.*?)\]\(.*?\)/g, '$1') // Remove links but keep text
        .replace(/#{1,6}\s/g, '') // Remove heading markers
        .replace(/```.*?```/gs, '') // Remove code blocks
        .replace(/`(.*?)`/g, '$1');  // Remove inline code markers

      const utterance = new SpeechSynthesisUtterance(cleanText);
      utterance.lang = language;
      utterance.rate = 1.0; // Normal speed
      utterance.pitch = 1.0; // Normal pitch

      // Find a voice that matches the language
      const voices = speechSynthesisRef.current.getVoices();
      const voice = voices.find(v => v.lang.includes(language.split('-')[0]));
      if (cachedData && !isExpired(cachedData)) {
  utterance.voice = voice;
      }

      utterance.onstart = () => setIsSpeaking(true);
      utterance.onend = () => setIsSpeaking(false);
      utterance.onerror = () => setIsSpeaking(false);

      speechSynthesisRef.current.speak(utterance);
    }
  };

  // Toggle language between English and Vietnamese
  const toggleLanguage = () => {
    const newLanguage = language === 'en-US' ? 'vi-VN' : 'en-US';
    setLanguage(newLanguage);

    // Update greeting message based on language
    const greeting = newLanguage === 'en-US'
      ? "Hello! I'm your 3D model assistant for 3DSKETCHUP.NET. How can I help you today?"
      : "Xin chào! Tôi là trợ lý mô hình 3D cho 3DSKETCHUP.NET. Tôi có thể giúp gì cho bạn hôm nay?";

    // Update first message if it's the greeting
    if (cachedData && !isExpired(cachedData)) {
  setMessages(prev => [
        { ...prev[0], text: greeting },
        ...prev.slice(1)
      ]);
    }

    // Update suggestions for current mode
    setSuggestions(getModeSuggestions(currentMode));
  };

  // Handle mode change
  const handleModeChange = (newMode) => {
  setCurrentMode(newMode);
    setSuggestions(getModeSuggestions(newMode));
    setShowModeSelector(false);

    // Add a system message about mode change
    const modeChangeMessage = {
    id: Date.now(),
      text: language === 'en-US'
        ? `Switched to ${assistantModes[newMode].name} mode. ${assistantModes[newMode].description}`
        : `Đã chuyển sang chế độ ${assistantModes[newMode].name}. ${assistantModes[newMode].description}`,
      sender: 'bot',
      timestamp: new Date(),
      intent: 'mode_change',
      mode: newMode
    };

    setMessages(prev => [...prev, modeChangeMessage]);
  };

  // Handle advanced search
  const handleAdvancedSearch = (searchParams) => {
  setSearchFilters(searchParams);
    setShowAdvancedSearch(false);

    // Create search message
    const searchMessage = {
    id: Date.now(),
      text: language === 'en-US'
        ? `Advanced search with filters: ${JSON.stringify(searchParams, null, 2)}`
        : `Tìm kiếm nâng cao với bộ lọc: ${JSON.stringify(searchParams, null, 2)}`,
      sender: 'user',
      timestamp: new Date(),
      intent: 'advanced_search',
      searchParams
    };

    setMessages(prev => [...prev, searchMessage]);

    // Process the search
    processAdvancedSearch(searchParams);
  };

  // Process advanced search
  const processAdvancedSearch = async (searchParams) => {
  setIsTyping(true);

    try {
      // Convert search params to natural language query
      const searchQuery = buildSearchQuery(searchParams);

      // Get AI response for the search
      const response = await getGeminiResponse(searchQuery);

      const botMessage = {
    id: Date.now() + 1,
        text: response.text,
        sender: 'bot',
        timestamp: new Date(),
        intent: response.intent,
        additionalData: response.additionalData,
        searchResults: response.additionalData?.models || []
      };

      setMessages(prev => [...prev, botMessage]);
    } catch (error) {
      const errorMessage = {
    id: Date.now() + 1,
        text: language === 'en-US'
          ? "Sorry, I encountered an error while processing your search. Please try again."
          : "Xin lỗi, tôi gặp lỗi khi xử lý tìm kiếm của bạn. Vui lòng thử lại.",
        sender: 'bot',
        timestamp: new Date(),
        intent: 'error'
      };

      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsTyping(false);
    }
  };

  // Build search query from parameters
  const buildSearchQuery = (params) => {
  const parts = [];

    if (params.category) parts.push(`category: ${params.category}`);
    if (params.tags.length > 0) parts.push(`tags: ${params.tags.join(', ')}`);
    if (params.fileFormat) parts.push(`format: ${params.fileFormat}`);
    if (params.minRating > 0) parts.push(`minimum rating: ${params.minRating}`);
    if (params.minDownloads > 0) parts.push(`minimum downloads: ${params.minDownloads}`);
    if (params.author) parts.push(`author: ${params.author}`);
    if (params.dateRange.start) parts.push(`from: ${params.dateRange.start}`);
    if (params.dateRange.end) parts.push(`to: ${params.dateRange.end}`);

    const baseQuery = language === 'en-US'
      ? "Find 3D models with the following criteria:"
      : "Tìm mô hình 3D với các tiêu chí sau:";

    return `${baseQuery} ${parts.join(', ')}. Sort by ${params.sortBy} in ${params.sortOrder} order.`;
  };

  // Copy message text to clipboard
  const copyMessageToClipboard = (messageId, text) => {
  navigator.clipboard.writeText(text).then(() => {
  setCopiedMessageId(messageId);
      setTimeout(() => setCopiedMessageId(null), 2000);
    });
  };

  // Handle image upload and analysis
  const handleImageAnalysis = async (analysisData) => {
  setIsAnalyzingImage(false);
    setImageAnalysisResult(analysisData);

    // Add user message with image
    const userMessage = {
    id: Date.now(),
      text: language === 'en-US' ? 'I uploaded an image for analysis' : 'Tôi đã tải lên một hình ảnh để phân tích',
      sender: 'user',
      timestamp: new Date(),
      intent: 'image_upload',
      imageUrl: analysisData.imageUrl,
      hasImage: true
    };

    // Add bot response with analysis
    const botMessage = {
    id: Date.now() + 1,
      text: analysisData.response,
      sender: 'bot',
      timestamp: new Date(),
      intent: 'image_analysis',
      additionalData: {
    analysis: analysisData.analysis,
        relatedModels: analysisData.relatedModels,
        imageMetadata: analysisData.metadata
      }
    };

    setMessages(prev => [...prev, userMessage, botMessage]);
    setShowImageUpload(false);
  };

  // Toggle image upload panel
  const toggleImageUpload = () => {
    setShowImageUpload(!showImageUpload);
    if (cachedData && !isExpired(cachedData)) {
  setUploadedImage(null);
      setImageAnalysisResult(null);
    }
  };

  // Rate limit countdown effect - only for Gemini API rate limits
  useEffect(() => {
  let timer;
    if (cachedData && !isExpired(cachedData)) {
  timer = setTimeout(() => {
  setRateLimitCountdown(prev => prev - 1);
      }, 1000);
    } else if (cachedData && !isExpired(cachedData)) {
  setIsRateLimited(false);
    }

    return () => clearTimeout(timer);
  }, [rateLimitCountdown, isRateLimited]);

  // Note: All server-side rate limiting has been removed

  // Check connection status with the API
  const [isConnected, setIsConnected] = useState(true);
  const [connectionError, setConnectionError] = useState(null);

  // Check API connection on component mount
  useEffect(() => {
    const checkConnection = async () => {
  try {
        // First try to ping the server root to check if it's running
        try {
          await axios.get('http://localhost:5002/');
          } catch (rootError) {
          }

        // Then try the actual API endpoint
        await chatApi.get('/rate-limit-status');
        setIsConnected(true);
        setConnectionError(null);
        } catch (error) {
        setIsConnected(false);

        // Store detailed error information
        if (cachedData && !isExpired(cachedData)) {
  setConnectionError({
    code: error.code,
            message: error.message,
            type: 'network'
          });
        } else if (cachedData && !isExpired(cachedData)) {
  setConnectionError({
    status: error.response.status,
            message: error.response.data?.error || error.message,
            type: 'api'
          });
        } else {
          setConnectionError({
    message: error.message,
            type: 'unknown'
          });
        }
      }
    };

    checkConnection();

    // Check connection every 30 seconds
    const interval = setInterval(checkConnection, 30000);

    return () => clearInterval(interval);
  }, []);

  // Get response from Chat API
  const getGeminiResponse = async (userMessage) => {
  setIsTyping(true);
    // Check if we're connected to the API
    if (cachedData && !isExpired(cachedData)) {
  setIsTyping(false);
      return {
    text: language === 'en-US'
          ? "I'm having trouble connecting to the server. Please check your internet connection and try again later."
          : "Tôi đang gặp sự cố kết nối đến máy chủ. Vui lòng kiểm tra kết nối internet của bạn và thử lại sau.",
        intent: 'error',
        additionalData: { errorType: 'connection' }
      };
    }

    try {
      // Prepare chat history - limit to last 10 messages to reduce token usage
      const chatHistory = messages
        .filter(msg => msg.id > 1) // Skip the initial greeting
        .slice(-10) // Only use the last 10 messages to reduce token count
        .map(msg => ({
    role: msg.sender === 'user' ? 'user' : 'model',
          parts: [{ text: msg.text }]
        }));

      // Send request to our backend API
      const response = await chatApi.post('/generate', {
    message: userMessage,
        chatHistory,
        language,
        conversationId, // Include conversation ID if we have one
        resetConversation: false // Don't reset the conversation by default
      });

      // Check if the request was successful
      if (cachedData && !isExpired(cachedData)) {
  setIsTyping(false);

        // Store conversation ID if provided
        if (cachedData && !isExpired(cachedData)) {
  setConversationId(response.data.conversationId);
          }

        // Handle additional data based on intent
        if (cachedData && !isExpired(cachedData)) {
  setModelSearchResults(response.data.additionalData.models);
        } else {
          setModelSearchResults([]);
        }

        // Generate new suggestions based on the conversation context
        if (cachedData && !isExpired(cachedData)) {
  generateContextualSuggestions(userMessage, response.data.response);
        }

        return {
    text: response.data.response,
          intent: response.data.intent || 'general',
          additionalData: response.data.additionalData
        };
      } else {
        throw new Error(response.data.error || 'Unknown error');
      }
    } catch (error) {
      // Handle any error with immediate retry - no limits
      if (error.response) {
        // Always retry immediately regardless of the error
        // Set typing state to show we're still working
        setIsTyping(true);

        // Don't show rate limit messages to the user
        setIsRateLimited(false);

        // Retry immediately with a small delay
        setTimeout(() => {
  handleRetry();
        }, 500); // 0.5 second delay

        setIsTyping(false);
        return {
    text: language === 'en-US'
            ? "Processing your request, please wait a moment..."
            : "Đang xử lý yêu cầu của bạn, vui lòng đợi trong giây lát...",
          intent: 'processing',
          additionalData: {
    willRetry: true,
            retryAttempt: retryCount + 1
          }
        };
      }

      // Check if it's an authentication error
      if (cachedData && !isExpired(cachedData)) {
  setIsTyping(false);
        return {
    text: language === 'en-US'
            ? "I'm having trouble authenticating with the AI service. Please contact support."
            : "Tôi đang gặp sự cố xác thực với dịch vụ AI. Vui lòng liên hệ hỗ trợ.",
          intent: 'error',
          additionalData: { errorType: 'authentication' }
        };
      }

      // Check if it's a server error
      if (cachedData && !isExpired(cachedData)) {
  setIsTyping(false);
        return {
    text: language === 'en-US'
            ? "The server encountered an error processing your request. Please try again later."
            : "Máy chủ gặp lỗi khi xử lý yêu cầu của bạn. Vui lòng thử lại sau.",
          intent: 'error',
          additionalData: { errorType: 'server', status: error.response.status }
        };
      }

      // Check if it's a connection error
      if (cachedData && !isExpired(cachedData)) {
  setIsConnected(false);
        setIsTyping(false);
        return {
    text: language === 'en-US'
            ? "I'm having trouble connecting to the server. Please check your internet connection and try again later."
            : "Tôi đang gặp sự cố kết nối đến máy chủ. Vui lòng kiểm tra kết nối internet của bạn và thử lại sau.",
          intent: 'error',
          additionalData: { errorType: 'connection', code: error.code }
        };
      }

      setIsTyping(false);
      return {
    text: language === 'en-US'
          ? "I'm sorry, I encountered an error. Please try again later."
          : "Xin lỗi, tôi gặp lỗi. Vui lòng thử lại sau.",
        intent: 'error',
        additionalData: { errorType: 'unknown', message: error.message }
      };
    }
  };

  // Generate contextual suggestions based on conversation
  const generateContextualSuggestions = (userMessage, botResponse) => {
    // Skip if we're using Vietnamese (we'll use the static suggestions)
    if (language !== 'en-US') return;

    // Extract potential topics from the conversation
    const topics = [];

    // Check if user asked about models
    if (userMessage.toLowerCase().includes('model') || botResponse.toLowerCase().includes('model')) {
      topics.push('models';
    }

    // Check if user asked about subscription
    if (userMessage.toLowerCase().includes('subscription') || userMessage.toLowerCase().includes('plan') ||
        botResponse.toLowerCase().includes('subscription') || botResponse.toLowerCase().includes('plan')) {
      topics.push('subscription';
    }

    // Check if user asked about file formats
    if (userMessage.toLowerCase().includes('format') || userMessage.toLowerCase().includes('file') ||
        botResponse.toLowerCase().includes('format') || botResponse.toLowerCase().includes('file')) {
      topics.push('formats';
    }

    // Generate new suggestions based on topics
    const newSuggestions = [];

    if (topics.includes('models')) {
      newSuggestions.push("Show me the most popular interior models");
      newSuggestions.push("How can I preview models before downloading?");
    }

    if (topics.includes('subscription')) {
      newSuggestions.push("What's included in the Professional plan?");
      newSuggestions.push("How do I upgrade my subscription?");
    }

    if (topics.includes('formats')) {
      newSuggestions.push("Can I convert SKP files to FBX?");
      newSuggestions.push("Which software can open OBJ files?");
    }

    // If we have new suggestions, update the state
    if (newSuggestions.length > 0) {
      // Add some general suggestions if we don't have enough
      while (newSuggestions.length < 3) {
        const generalSuggestions = [
          "What are the benefits of a premium account?",
          "How do I contact support?",
          "Can I upload my own models?",
          "How do I search for specific models?"
        ];

        const randomSuggestion = generalSuggestions[Math.floor(Math.random() * generalSuggestions.length)];
        if (!newSuggestions.includes(randomSuggestion)) {
          newSuggestions.push(randomSuggestion);
        }
      }

      setSuggestions(newSuggestions.slice(0, 5)); // Limit to 5 suggestions
    }
  };

  // Handle retry with immediate retry - no limits
  const handleRetry = () => {
    if (!lastMessage) return;

    // Increment retry count (just for logging)
    const newRetryCount = retryCount + 1;
    setRetryCount(newRetryCount);

    // Use a very short delay
    const delay = 500; // 0.5 second

    - no limits`);

    // Don't show any rate limit messages
    setIsRateLimited(false);

    // Just retry immediately
    setTimeout(() => {
  getGeminiResponse(lastMessage).then(response => {
        // Handle response as in handleSendMessage
        const isErrorResponse = typeof response === 'string';

        // If we get a processing message, retry again
        if (cachedData && !isExpired(cachedData)) {
  setTimeout(handleRetry, 1000);
          return;
        }

        const botMessage = {
    id: Date.now() + 1,
          text: isErrorResponse ? response : response.text,
          sender: 'bot',
          timestamp: new Date(),
          intent: isErrorResponse ? 'error' : response.intent,
          additionalData: isErrorResponse ? null : response.additionalData
        };

        // Replace the "processing" message with the actual response
        setMessages(prev => {
  const lastMsg = prev[prev.length - 1];
          if (cachedData && !isExpired(cachedData)) {
  return [...prev.slice(0, -1), botMessage];
          } else {
            return [...prev, botMessage];
          }
        });

        // Speak the response if speech synthesis is enabled
        if (cachedData && !isExpired(cachedData)) {
  speakText(isErrorResponse ? response : response.text);
        }

        // Reset retry count on success
        if (cachedData && !isExpired(cachedData)) {
  setRetryCount(0);
          setIsRateLimited(false);
        }
      }).catch(error => {
        // Always retry on error
        setTimeout(handleRetry, 1000);
      });
    }, delay);
  };

  const handleSendMessage = async (e) => {
  e?.preventDefault();

    if (!inputValue.trim()) return;

    // Store the current input value to use throughout this function
    // This prevents issues if the input changes during processing
    const currentInput = inputValue;

    // Save for potential retries
    setLastMessage(currentInput);

    // Add user message
    const userMessage = {
    id: Date.now(),
      text: currentInput,
      sender: 'user',
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setInputValue(';

    // Hide suggestions after user sends a message
    setShowSuggestions(false);

    // Get bot response
    const response = await getGeminiResponse(currentInput);

    // Check if response is an error message (string) or a proper response object
    const isErrorResponse = typeof response === 'string';

    // Add bot message
    const botMessage = {
    id: Date.now() + 1,
      text: isErrorResponse ? response : response.text,
      sender: 'bot',
      timestamp: new Date(),
      intent: isErrorResponse ? 'error' : response.intent,
      additionalData: isErrorResponse ? null : response.additionalData
    };

    setMessages(prev => [...prev, botMessage]);

    // Speak the response if speech synthesis is enabled
    if (cachedData && !isExpired(cachedData)) {
  speakText(isErrorResponse ? response : response.text);
    }

    // Show suggestions again after bot responds
    setTimeout(() => setShowSuggestions(true), 1000);
  };

  // Handle clicking on a suggestion
  const handleSuggestionClick = (suggestion) => {
  setInputValue(suggestion);
    handleSendMessage();
  };

  const toggleChat = () => {
    setIsOpen(!isOpen);

    // If opening the chat, focus the input field
    if (cachedData && !isExpired(cachedData)) {
  setTimeout(() => {
  inputRef.current?.focus();
      }, 300);
    }
  };

  const toggleMinimize = () => {
    setIsMinimized(!isMinimized);

    // If un-minimizing, focus the input field
    if (cachedData && !isExpired(cachedData)) {
  setTimeout(() => {
  inputRef.current?.focus();
      }, 300);
    }
  };

  const clearChat = () => {
    // Create a new greeting message
    const greeting = language === 'en-US'
      ? "Hello! I'm your 3D model assistant for 3DSKETCHUP.NET. How can I help you today?"
      : "Xin chào! Tôi là trợ lý mô hình 3D cho 3DSKETCHUP.NET. Tôi có thể giúp gì cho bạn hôm nay?";

    const initialMessage = {
    id: Date.now(),
      text: greeting,
      sender: 'bot',
      timestamp: new Date(),
      intent: 'greeting'
    };

    setMessages([initialMessage]);
    localStorage.removeItem('chatMessages');
    setConversationId(null);

    // Reset to default suggestions
    if (cachedData && !isExpired(cachedData)) {
  setSuggestions([
        "What 3D models are popular right now?",
        "How do I download a model?",
        "Tell me about subscription plans",
        "What file formats are supported?",
        "How can I upload my own models?"
      ]);
    }

    // Show suggestions after clearing chat
    setShowSuggestions(true);
  };

  // Check if the component is mounted (for speech synthesis)
  const [isMounted, setIsMounted] = useState(false);
  useEffect(() => {
  setIsMounted(true);
    return () => setIsMounted(false);
  }, []);

  // Network status check
  useEffect(() => {
    const checkConnection = () => {
      setIsConnected(navigator.onLine);
    };

    // Initial check
    checkConnection();

    // Add event listeners for online/offline events
    window.addEventListener('online', checkConnection);
    window.addEventListener('offline', checkConnection);

    // Cleanup
    return () => {
  window.removeEventListener('online', checkConnection);
      window.removeEventListener('offline', checkConnection);
    };
  }, []);

  return (
    <div className="fixed bottom-4 right-4 z-50" data-chatbot="true">
      {/* Chat toggle button */}
      <button
        onClick={toggleChat}
        className="bg-blue-600 hover:bg-blue-700 text-white rounded-full p-3 shadow-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-all duration-200 flex items-center justify-center"
        aria-label={isOpen ? 'Close chat' : 'Open chat'}
      >
        {isOpen ? (
          <XIcon className="h-6 w-6" />
        ) : (
          <>
            <ChatIcon className="h-6 w-6" />
            {/* Notification dot for new chat */}
            {messages.length <= 1 && (
              <span className="absolute top-0 right-0 h-3 w-3 bg-red-500 rounded-full animate-pulse"></span>
            )}
          </>
        )}
      </button>

      {/* Chat window */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0, y: 20, scale: 0.9 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: 20, scale: 0.9 }}
            transition={{ duration: 0.2 }}
            className={`absolute bottom-16 right-0 ${
              isMinimized ? 'w-80' : 'w-80 sm:w-96 md:w-[450px] h-[500px]'
            } bg-white dark:bg-gray-800 rounded-lg shadow-2xl flex flex-col overflow-hidden border border-gray-200 dark:border-gray-700`}
          >
            {/* Chat header */}
            <div className="bg-gradient-to-r from-blue-600 to-blue-700 text-white px-4 py-3 flex justify-between items-center">
              <div className="flex items-center">
                <FaRobot className="h-5 w-5 mr-2" />
                <h3 className="font-medium">
                  {language === 'en-US' ? '3DSKETCHUP.NET Assistant' : 'Trợ lý 3DSKETCHUP.NET'}
                </h3>
              </div>
              <div className="flex items-center space-x-2">
                {/* Language toggle */}
                <button
                  onClick={toggleLanguage}
                  className="text-white hover:text-gray-200 focus:outline-none p-1 rounded-full hover:bg-blue-500 transition-colors"
                  aria-label={language === 'en-US' ? 'Switch to Vietnamese' : 'Switch to English'}
                  title={language === 'en-US' ? 'Switch to Vietnamese' : 'Switch to English'}
                >
                  <LanguageIcon className="h-5 w-5" />
                </button>

                {/* Minimize/Maximize button */}
                <button
                  onClick={toggleMinimize}
                  className="text-white hover:text-gray-200 focus:outline-none p-1 rounded-full hover:bg-blue-500 transition-colors"
                  aria-label={isMinimized ? 'Maximize chat' : 'Minimize chat'}
                  title={isMinimized ? 'Maximize chat' : 'Minimize chat'}
                >
                  {isMinimized ? (
                    <MaximizeIcon className="h-5 w-5" />
                  ) : (
                    <MinimizeIcon className="h-5 w-5" />
                  )}
                </button>

                {/* Clear chat button */}
                <button
                  onClick={clearChat}
                  className="text-white hover:text-gray-200 focus:outline-none p-1 rounded-full hover:bg-blue-500 transition-colors"
                  aria-label={language === 'en-US' ? 'Clear chat' : 'Xóa cuộc trò chuyện'}
                  title={language === 'en-US' ? 'Clear chat' : 'Xóa cuộc trò chuyện'}
                >
                  <TrashIcon className="h-5 w-5" />
                </button>

                {/* Close button */}
                <button
                  onClick={toggleChat}
                  className="text-white hover:text-gray-200 focus:outline-none p-1 rounded-full hover:bg-blue-500 transition-colors"
                  aria-label="Close chat"
                  title={language === 'en-US' ? 'Close' : 'Đóng'}
                >
                  <XIcon className="h-5 w-5" />
                </button>
              </div>
            </div>

            {/* Status indicator and mode selector */}
            <div className="bg-gray-50 dark:bg-gray-750 px-4 py-2 border-b border-gray-200 dark:border-gray-700">
              {/* Top row: Status and Speech */}
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center">
                  <div className={`h-2 w-2 rounded-full ${
                    !isConnected
                      ? 'bg-gray-500'
                      : isRateLimited
                        ? 'bg-red-500'
                        : 'bg-green-500'
                  } mr-2`}></div>
                  <span className="text-sm text-gray-600 dark:text-gray-300">
                    {!isConnected
                      ? (language === 'en-US' ? 'Disconnected' : 'Mất kết nối')
                      : isRateLimited
                        ? (language === 'en-US'
                            ? `Rate limited (${rateLimitCountdown}s)`
                            : `Giới hạn tốc độ (${rateLimitCountdown}s)`)
                        : (language === 'en-US' ? 'Online' : 'Trực tuyến')
                    }
                  </span>
                  {!isConnected && (
                    <NoConnectionIcon className="ml-1 h-4 w-4 text-gray-500" />
                  )}
                  {isConnected && !isRateLimited && (
                    <ConnectionIcon className="ml-1 h-4 w-4 text-green-500" />
                  )}
                </div>

                {/* Speech toggle */}
                <button
                  onClick={() => setIsSpeaking(!isSpeaking)}
                  className={`text-xs flex items-center ${
                    isSpeaking
                      ? 'text-blue-500 dark:text-blue-400'
                      : 'text-gray-500 dark:text-gray-400 hover:text-blue-500 dark:hover:text-blue-400'
                  }`}
                  title={language === 'en-US' ? 'Toggle speech output' : 'Bật/tắt đầu ra giọng nói'}
                >
                  <SpeakerIcon className={`h-4 w-4 mr-1 ${isSpeaking ? 'text-blue-500' : '}`} />
                  <span className="hidden sm:inline">
                    {language === 'en-US'
                      ? (isSpeaking ? 'Speech on' : 'Speech off')
                      : (isSpeaking ? 'Bật giọng nói' : 'Tắt giọng nói')}
                  </span>
                </button>
              </div>

              {/* Bottom row: Mode selector and Advanced Search */}
              <div className="flex items-center justify-between">
                {/* Current mode display and selector */}
                <div className="relative">
                  <button
                    onClick={() => setShowModeSelector(!showModeSelector)}
                    className={`flex items-center space-x-2 px-3 py-1.5 rounded-lg text-sm transition-colors ${
                      assistantModes[currentMode].color === 'blue' ? 'bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300' :
                      assistantModes[currentMode].color === 'purple' ? 'bg-purple-100 dark:bg-purple-900/30 text-purple-700 dark:text-purple-300' :
                      assistantModes[currentMode].color === 'green' ? 'bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300' :
                      assistantModes[currentMode].color === 'orange' ? 'bg-orange-100 dark:bg-orange-900/30 text-orange-700 dark:text-orange-300' :
                      'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300'
                    } hover:opacity-80`}
                  >
                    {assistantModes[currentMode].icon}
                    <span className="font-medium">{assistantModes[currentMode].name}</span>
                    <SettingsIcon className="w-3 h-3" />
                  </button>

                  {/* Mode selector dropdown */}
                  <AnimatePresence>
                    {showModeSelector && (
                      <motion.div
                        initial={{ opacity: 0, y: -10 }}
                        animate={{ opacity: 1, y: 0 }}
                        exit={{ opacity: 0, y: -10 }}
                        className="absolute top-full left-0 mt-1 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg z-50 min-w-[200px]"
                      >
                        {Object.entries(assistantModes).map(([mode, config]) => (
                          <button
                            key={mode}
                            onClick={() => handleModeChange(mode)}
                            className={`w-full flex items-center space-x-3 px-4 py-3 text-left hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors first:rounded-t-lg last:rounded-b-lg ${
    currentMode === mode ? 'bg-gray-50 dark:bg-gray-700' : '
                            }`}
                          >
                            <div className={`p-1.5 rounded-lg ${
                              config.color === 'blue' ? 'bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400' :
                              config.color === 'purple' ? 'bg-purple-100 dark:bg-purple-900/30 text-purple-600 dark:text-purple-400' :
                              config.color === 'green' ? 'bg-green-100 dark:bg-green-900/30 text-green-600 dark:text-green-400' :
                              config.color === 'orange' ? 'bg-orange-100 dark:bg-orange-900/30 text-orange-600 dark:text-orange-400' :
                              'bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400'
                            }`}>
                              {config.icon}
                            </div>
                            <div>
                              <div className="font-medium text-gray-900 dark:text-white">{config.name}</div>
                              <div className="text-xs text-gray-500 dark:text-gray-400">{config.description}</div>
                            </div>
                          </button>
                        ))}
                      </motion.div>
                    )}
                  </AnimatePresence>
                </div>

                {/* Advanced Search button */}
                <button
                  onClick={() => setShowAdvancedSearch(true)}
                  className="flex items-center space-x-2 px-3 py-1.5 bg-purple-100 dark:bg-purple-900/30 text-purple-700 dark:text-purple-300 rounded-lg text-sm hover:opacity-80 transition-colors"
                  title={language === 'en-US' ? 'Advanced Search' : 'Tìm kiếm nâng cao'}
                >
                  <FilterIcon className="w-4 h-4" />
                  <span className="hidden sm:inline">
                    {language === 'en-US' ? 'Advanced' : 'Nâng cao'}
                  </span>
                </button>
              </div>
            </div>

            {!isMinimized && (
              <>
                {/* Chat messages */}
                <div className="flex-1 p-4 overflow-y-auto bg-gray-50 dark:bg-gray-850">
                  {messages.map((message) => (
                    message.hasImage || message.imageUrl || message.intent === 'image_upload' || message.intent === 'image_analysis' ? (
                      <ImageMessage
                        key={message.id}
                        message={message}
                        isUser={message.sender === 'user'}
                        onModelClick={(model) => {
                          // Handle model click - could navigate to model page
                          }}
                        language={language}
                      />
                    ) : (
                      <div
                        key={message.id}
                        className={`mb-4 flex ${message.sender === 'user' ? 'justify-end' : 'justify-start'}`}
                      >
                        {/* Bot avatar for bot messages */}
                        {message.sender === 'bot' && (
                          <div className="flex-shrink-0 h-8 w-8 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center mr-2">
                            <FaRobot className="h-4 w-4 text-blue-600 dark:text-blue-300" />
                          </div>
                        )}

                        <div
                          className={`relative max-w-[85%] rounded-lg px-4 py-3 shadow-sm ${
                            message.sender === 'user'
                              ? 'bg-blue-600 text-white rounded-tr-none'
                              : message.intent === 'error'
                                ? 'bg-red-50 dark:bg-red-900/30 text-red-800 dark:text-red-200 border border-red-200 dark:border-red-800 rounded-tl-none'
                                : message.intent === 'model_search'
                                  ? 'bg-green-50 dark:bg-green-900/30 text-gray-800 dark:text-gray-200 border border-green-200 dark:border-green-800 rounded-tl-none'
                                : message.intent === 'subscription'
                                  ? 'bg-purple-50 dark:bg-purple-900/30 text-gray-800 dark:text-gray-200 border border-purple-200 dark:border-purple-800 rounded-tl-none'
                                : message.intent === 'processing'
                                  ? 'bg-yellow-50 dark:bg-yellow-900/30 text-gray-800 dark:text-gray-200 border border-yellow-200 dark:border-yellow-800 rounded-tl-none'
                                  : 'bg-white dark:bg-gray-800 text-gray-800 dark:text-gray-200 border border-gray-200 dark:border-gray-700 rounded-tl-none'
                          }`}
                        >
                          {/* Display intent badge for bot messages */}
                          {message.sender === 'bot' && message.intent && message.intent !== 'general' && message.intent !== 'greeting' && (
                            <div className="mb-1">
                              <span className={`text-xs px-2 py-0.5 rounded-full ${
                                message.intent === 'error' ? 'bg-red-100 dark:bg-red-800/50 text-red-800 dark:text-red-200' :
                                message.intent === 'model_search' ? 'bg-green-100 dark:bg-green-800/50 text-green-800 dark:text-green-200' :
                                message.intent === 'subscription' ? 'bg-purple-100 dark:bg-purple-800/50 text-purple-800 dark:text-purple-200' :
                                message.intent === 'technical_support' ? 'bg-orange-100 dark:bg-orange-800/50 text-orange-800 dark:text-orange-200' :
                                message.intent === 'processing' ? 'bg-yellow-100 dark:bg-yellow-800/50 text-yellow-800 dark:text-yellow-200' :
                                'bg-blue-100 dark:bg-blue-800/50 text-blue-800 dark:text-blue-200'
                              }`}>
                                {message.intent === 'model_search' ? (language === 'en-US' ? 'Model Search' : 'Tìm kiếm mô hình') :
                                 message.intent === 'subscription' ? (language === 'en-US' ? 'Subscription Info' : 'Thông tin đăng ký') :
                                 message.intent === 'technical_support' ? (language === 'en-US' ? 'Technical Support' : 'Hỗ trợ kỹ thuật') :
                                 message.intent === 'error' ? (language === 'en-US' ? 'Error' : 'Lỗi') :
                                 message.intent === 'processing' ? (language === 'en-US' ? 'Processing' : 'Đang xử lý') :
                                 message.intent.charAt(0).toUpperCase() + message.intent.slice(1)}
                              </span>
                            </div>
                          )}

                          {/* Message text with markdown support */}
                          <div className="prose prose-sm dark:prose-invert max-w-none">
                            {message.intent === 'processing' ? (
                              <div className="flex items-center space-x-2">
                                <span>{message.text}</span>
                                <div className="flex space-x-1 ml-2">
                                  <div className="w-2 h-2 rounded-full bg-blue-500 dark:bg-blue-400 animate-bounce" style={{ animationDelay: '0ms' }}></div>
                                  <div className="w-2 h-2 rounded-full bg-blue-500 dark:bg-blue-400 animate-bounce" style={{ animationDelay: '150ms' }}></div>
                                  <div className="w-2 h-2 rounded-full bg-blue-500 dark:bg-blue-400 animate-bounce" style={{ animationDelay: '300ms' }}></div>
                                </div>
                              </div>
                            ) : (
                              <ReactMarkdown>
                                {message.text}
                              </ReactMarkdown>
                            )}
                          </div>

                          {/* Model search results */}
                          {message.sender === 'bot' && message.intent === 'model_search' && message.additionalData?.models && message.additionalData.models.length > 0 && (
                            <div className="mt-3 border-t border-gray-200 dark:border-gray-700 pt-2">
                              <p className="font-medium text-sm">
                                {language === 'en-US'
                                  ? `Found ${message.additionalData.models.length} models:`
                                  : `Tìm thấy ${message.additionalData.models.length} mô hình:`}
                              </p>
                              <div className="mt-2 space-y-2">
                                {message.additionalData.models.slice(0, 3).map((model, idx) => (
                                  <div key={idx} className="text-sm bg-white dark:bg-gray-700 p-2 rounded border border-gray-200 dark:border-gray-600 hover:border-blue-300 dark:hover:border-blue-500 transition-colors">
                                    <p className="font-medium">{model.title}</p>
                                    <div className="flex flex-wrap gap-2 mt-1">
                                      {model.category && (
                                        <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                                          {model.category}
                                        </span>
                                      )}
                                      {model.downloads && (
                                        <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                                          {model.downloads} {language === 'en-US' ? 'downloads' : 'lượt tải'}
                                        </span>
                                      )}
                                      {model.rating && (
                                        <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200">
                                          ★ {model.rating.toFixed(1)}
                                        </span>
                                      )}
                                    </div>
                                  </div>
                                ))}
                              </div>
                            </div>
                          )}

                          {/* Subscription info */}
                          {message.sender === 'bot' && message.intent === 'subscription' && message.additionalData?.subscriptionInfo && (
                            <div className="mt-3 border-t border-gray-200 dark:border-gray-700 pt-2">
                              <p className="font-medium text-sm">
                                {language === 'en-US' ? 'Available Plans:' : 'Các gói đăng ký:'}
                              </p>
                              <div className="mt-2 space-y-2">
                                {Object.entries(message.additionalData.subscriptionInfo).map(([planId, plan]) => (
                                  <div key={planId} className="text-sm bg-white dark:bg-gray-700 p-2 rounded border border-gray-200 dark:border-gray-600">
                                    <div className="flex justify-between items-center">
                                      <p className="font-medium">{plan.name}</p>
                                      <span className={`px-2 py-0.5 rounded text-xs font-medium ${
                                        plan.price > 0
                                          ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
                                          : 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                                      }`}>
                                        {plan.price > 0 ? `$${plan.price}/month` : (language === 'en-US' ? 'Free' : 'Miễn phí')}
                                      </span>
                                    </div>
                                  </div>
                                ))}
                              </div>
                            </div>
                          )}

                          {/* Message footer with timestamp and controls */}
                          <div className={`flex justify-between items-center mt-2 ${
                            message.sender === 'user' ? 'text-blue-200' : 'text-gray-500 dark:text-gray-400'
                          }`}>
                            <span className="text-xs">
                              {new Date(message.timestamp).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                            </span>

                            <div className="flex items-center space-x-2">
                              {message.sender === 'bot' && (
                                <>
                                  <button
                                    onClick={() => speakText(message.text)}
                                    className="text-gray-500 dark:text-gray-400 hover:text-blue-500 dark:hover:text-blue-400"
                                    title={language === 'en-US' ? 'Listen' : 'Nghe'}
                                  >
                                    <SpeakerIcon className="h-3.5 w-3.5" />
                                  </button>
                                  <button
                                    onClick={() => copyMessageToClipboard(message.id, message.text)}
                                    className="text-gray-500 dark:text-gray-400 hover:text-blue-500 dark:hover:text-blue-400"
                                    title={language === 'en-US' ? 'Copy to clipboard' : 'Sao chép vào clipboard'}
                                  >
                                    {copiedMessageId === message.id ? (
                                      <CheckIcon className="h-3.5 w-3.5 text-green-500" />
                                    ) : (
                                      <CopyIcon className="h-3.5 w-3.5" />
                                    )}
                                  </button>
                                </>
                              )}
                            </div>
                          </div>
                        </div>

                        {/* User avatar for user messages */}
                        {message.sender === 'user' && (
                          <div className="flex-shrink-0 h-8 w-8 rounded-full bg-blue-600 flex items-center justify-center ml-2">
                            <span className="text-xs font-medium text-white">You</span>
                          </div>
                        )}
                      </div>
                    )
                  ))}

                  {isTyping && (
                    <div className="flex justify-start mb-3">
                      <div className="flex-shrink-0 h-8 w-8 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center mr-2">
                        <FaRobot className="h-4 w-4 text-blue-600 dark:text-blue-300" />
                      </div>
                      <div className="bg-white dark:bg-gray-800 rounded-lg px-4 py-3 text-gray-800 dark:text-gray-200 border border-gray-200 dark:border-gray-700 rounded-tl-none shadow-sm">
                        <div className="flex space-x-1">
                          <div className="w-2 h-2 rounded-full bg-blue-500 dark:bg-blue-400 animate-bounce" style={{ animationDelay: '0ms' }}></div>
                          <div className="w-2 h-2 rounded-full bg-blue-500 dark:bg-blue-400 animate-bounce" style={{ animationDelay: '150ms' }}></div>
                          <div className="w-2 h-2 rounded-full bg-blue-500 dark:bg-blue-400 animate-bounce" style={{ animationDelay: '300ms' }}></div>
                        </div>
                      </div>
                    </div>
                  )}

                  <div ref={messagesEndRef} />
                </div>

                {/* Suggestions */}
                {showSuggestions && messages.length > 0 && !isTyping && (
                  <div className="px-4 py-3 bg-gray-50 dark:bg-gray-850 border-t border-gray-200 dark:border-gray-700">
                    <div className="flex items-center justify-between mb-2">
                      <p className="text-xs text-gray-500 dark:text-gray-400">
                        {language === 'en-US' ? `${assistantModes[currentMode].name} suggestions:` : `Gợi ý ${assistantModes[currentMode].name}:`}
                      </p>
                      <div className={`px-2 py-1 rounded-full text-xs ${
                        assistantModes[currentMode].color === 'blue' ? 'bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400' :
                        assistantModes[currentMode].color === 'purple' ? 'bg-purple-100 dark:bg-purple-900/30 text-purple-600 dark:text-purple-400' :
                        assistantModes[currentMode].color === 'green' ? 'bg-green-100 dark:bg-green-900/30 text-green-600 dark:text-green-400' :
                        assistantModes[currentMode].color === 'orange' ? 'bg-orange-100 dark:bg-orange-900/30 text-orange-600 dark:text-orange-400' :
                        'bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400'
                      }`}>
                        {assistantModes[currentMode].icon}
                      </div>
                    </div>
                    <div className="flex flex-wrap gap-2">
                      {suggestions.map((suggestion, index) => (
                        <button
                          key={index}
                          onClick={() => handleSuggestionClick(suggestion)}
                          className={`text-xs bg-white dark:bg-gray-800 px-3 py-1.5 rounded-full border transition-colors ${
                            assistantModes[currentMode].color === 'blue' ? 'text-blue-600 dark:text-blue-400 border-blue-200 dark:border-blue-800 hover:bg-blue-50 dark:hover:bg-blue-900/30' :
                            assistantModes[currentMode].color === 'purple' ? 'text-purple-600 dark:text-purple-400 border-purple-200 dark:border-purple-800 hover:bg-purple-50 dark:hover:bg-purple-900/30' :
                            assistantModes[currentMode].color === 'green' ? 'text-green-600 dark:text-green-400 border-green-200 dark:border-green-800 hover:bg-green-50 dark:hover:bg-green-900/30' :
                            assistantModes[currentMode].color === 'orange' ? 'text-orange-600 dark:text-orange-400 border-orange-200 dark:border-orange-800 hover:bg-orange-50 dark:hover:bg-orange-900/30' :
                            'text-gray-600 dark:text-gray-400 border-gray-200 dark:border-gray-800 hover:bg-gray-50 dark:hover:bg-gray-900/30'
                          }`}
                        >
                          {suggestion}
                        </button>
                      ))}
                    </div>
                  </div>
                )}

                {/* Connection warning */}
                {!isConnected && (
                  <div className="px-4 py-3 bg-yellow-50 dark:bg-yellow-900/20 text-yellow-800 dark:text-yellow-200 text-sm border-l-4 border-yellow-500">
                    <div className="flex items-start">
                      <NoConnectionIcon className="h-5 w-5 mr-2 flex-shrink-0 mt-0.5" />
                      <div>
                        <p className="font-medium">
                          {language === 'en-US'
                            ? "Connection Issue"
                            : "Sự cố kết nối"}
                        </p>
                        <p className="mt-1">
                          {language === 'en-US'
                            ? "Unable to connect to the chat service. Please check your internet connection."
                            : "Không thể kết nối đến dịch vụ trò chuyện. Vui lòng kiểm tra kết nối internet của bạn."}
                        </p>

                        {/* Show detailed error information if available */}
                        {connectionError && (
                          <div className="mt-2 p-2 bg-yellow-100 dark:bg-yellow-800/50 rounded text-xs">
                            <p className="font-medium">Error details:</p>
                            {connectionError.type === 'network' && (
                              <p>Network error: {connectionError.code} - {connectionError.message}</p>
                            )}
                            {connectionError.type === 'api' && (
                              <p>API error: {connectionError.status} - {connectionError.message}</p>
                            )}
                            {connectionError.type === 'unknown' && (
                              <p>Unknown error: {connectionError.message}</p>
                            )}
                            <p className="mt-1">
                              Make sure the backend server is running on port 5002.
                            </p>
                          </div>
                        )}

                        <p className="mt-1 text-xs">
                          {language === 'en-US'
                            ? "The system will automatically try to reconnect."
                            : "Hệ thống sẽ tự động thử kết nối lại."}
                        </p>
                        <button
                          onClick={() => {
  const checkConnection = async () => {
  try {
                                // First try to ping the server root
                                try {
                                  await axios.get('http://localhost:5002/');
                                  } catch (rootError) {
                                  }

                                // Then try the actual API endpoint
                                await chatApi.get('/rate-limit-status');
                                setIsConnected(true);
                                setConnectionError(null);
                                } catch (error) {
                                setIsConnected(false);

                                // Store detailed error information
                                if (cachedData && !isExpired(cachedData)) {
  setConnectionError({
    code: error.code,
                                    message: error.message,
                                    type: 'network'
                                  });
                                } else if (cachedData && !isExpired(cachedData)) {
  setConnectionError({
    status: error.response.status,
                                    message: error.response.data?.error || error.message,
                                    type: 'api'
                                  });
                                } else {
                                  setConnectionError({
    message: error.message,
                                    type: 'unknown'
                                  });
                                }
                              }
                            };
                            checkConnection();
                          }}
                          className="mt-2 px-3 py-1 bg-yellow-100 dark:bg-yellow-800 text-yellow-800 dark:text-yellow-200 rounded-md text-xs font-medium hover:bg-yellow-200 dark:hover:bg-yellow-700 transition-colors flex items-center"
                        >
                          <RefreshIcon className="h-3 w-3 mr-1" />
                          {language === 'en-US' ? "Try reconnecting now" : "Thử kết nối lại ngay"}
                        </button>
                      </div>
                    </div>
                  </div>
                )}

                {/* Chat input */}
                <form onSubmit={handleSendMessage} className="border-t border-gray-200 dark:border-gray-700 p-3 bg-white dark:bg-gray-800">
                  <div className="flex items-center rounded-lg border border-gray-300 dark:border-gray-600 focus-within:ring-2 focus-within:ring-blue-500 focus-within:border-blue-500 dark:focus-within:border-blue-500 overflow-hidden">
                    <input
                      ref={inputRef}
                      type="text"
                      value={inputValue}
                      onChange={(e) => setInputValue(e.target.value)}
                      placeholder={
                        !isConnected
                          ? (language === 'en-US' ? 'Disconnected...' : 'Mất kết nối...')
                          : isRateLimited
                            ? (language === 'en-US' ? `Please wait ${rateLimitCountdown}s...` : `Vui lòng đợi ${rateLimitCountdown}s...`)
                            : (language === 'en-US' ? "Type your message..." : "Nhập tin nhắn của bạn...")
                      }
                      className={`flex-1 px-4 py-2.5 focus:outline-none dark:bg-gray-700 dark:text-white ${
                        (isRateLimited || !isConnected) ? 'bg-gray-100 dark:bg-gray-800' : '
                      }`}
                      disabled={isRateLimited || !isConnected}
                    />

                    <div className="flex items-center pr-2">
                      <button
                        type="button"
                        onClick={toggleImageUpload}
                        className={`p-2 rounded-full focus:outline-none ${
                          showImageUpload
                            ? 'bg-purple-500 text-white'
                            : 'text-gray-500 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-600'
                        } ${(isRateLimited || !isConnected) ? 'opacity-50 cursor-not-allowed' : '}`}
                        title={language === 'en-US' ? 'Upload image' : 'Tải lên hình ảnh'}
                        disabled={isRateLimited || !isConnected}
                      >
                        <ImageIcon className="h-5 w-5" />
                      </button>

                      <button
                        type="button"
                        onClick={toggleListening}
                        className={`p-2 rounded-full focus:outline-none ${
                          isListening
                            ? 'bg-red-500 text-white'
                            : 'text-gray-500 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-600'
                        } ${(isRateLimited || !isConnected) ? 'opacity-50 cursor-not-allowed' : '}`}
                        title={language === 'en-US' ? 'Voice input' : 'Nhập giọng nói'}
                        disabled={isRateLimited || !isConnected}
                      >
                        <MicIcon className="h-5 w-5" />
                      </button>

                      <button
                        type="submit"
                        disabled={!inputValue.trim() || isTyping || isRateLimited || !isConnected}
                        className={`p-2 rounded-full focus:outline-none ${
                          !inputValue.trim() || isTyping || isRateLimited || !isConnected
                            ? 'text-gray-400 dark:text-gray-600 cursor-not-allowed'
                            : 'text-blue-600 dark:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/30'
                        }`}
                        title={language === 'en-US' ? 'Send message' : 'Gửi tin nhắn'}
                      >
                        <PaperAirplaneIcon className="h-5 w-5" />
                      </button>
                    </div>
                  </div>

                  {/* Help text */}
                  <div className="mt-2 flex justify-between items-center text-xs text-gray-500 dark:text-gray-400 px-1">
                    <span>
                      {language === 'en-US'
                        ? "Ask about 3D models, file formats, or subscriptions"
                        : "Hỏi về mô hình 3D, định dạng tệp hoặc đăng ký"}
                    </span>
                    <button
                      type="button"
                      onClick={() => setShowSuggestions(!showSuggestions)}
                      className="flex items-center hover:text-blue-500 dark:hover:text-blue-400"
                      title={language === 'en-US' ? 'Toggle suggestions' : 'Bật/tắt gợi ý'}
                    >
                      <HelpIcon className="h-3.5 w-3.5 mr-1" />
                      <span>{language === 'en-US' ? 'Suggestions' : 'Gợi ý'}</span>
                    </button>
                  </div>
                </form>
              </>
            )}
          </motion.div>
        )}
      </AnimatePresence>

      {/* Advanced Search Modal */}
      <AnimatePresence>
        {showAdvancedSearch && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 backdrop-blur-sm z-[60] flex items-center justify-center p-4"
            onClick={() => setShowAdvancedSearch(false)}
          >
            <motion.div
              initial={{ opacity: 0, scale: 0.9, y: 20 }}
              animate={{ opacity: 1, scale: 1, y: 0 }}
              exit={{ opacity: 0, scale: 0.9, y: 20 }}
              className="bg-white dark:bg-gray-800 rounded-xl shadow-2xl w-full max-w-2xl max-h-[80vh] overflow-hidden"
              onClick={(e) => e.stopPropagation()}
            >
              {/* Header */}
              <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
                <div className="flex items-center space-x-3">
                  <FilterIcon className="w-5 h-5 text-purple-600" />
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                    {language === 'en-US' ? 'Advanced Search' : 'Tìm kiếm nâng cao'}
                  </h3>
                </div>
                <button
                  onClick={() => setShowAdvancedSearch(false)}
                  className="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700"
                >
                  <XIcon className="w-5 h-5" />
                </button>
              </div>

              {/* Content */}
              <div className="p-4 overflow-y-auto max-h-[60vh]">
                <div className="space-y-4">
                  {/* Search Query */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      {language === 'en-US' ? 'Search Query' : 'Từ khóa tìm kiếm'}
                    </label>
                    <input
                      type="text"
                      value={searchFilters.query || '}
                      onChange={(e) => setSearchFilters(prev => ({ ...prev, query: e.target.value }))}
                      placeholder={language === 'en-US' ? 'Enter search terms...' : 'Nhập từ khóa...'}
                      className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    />
                  </div>

                  {/* Category */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      {language === 'en-US' ? 'Category' : 'Danh mục'}
                    </label>
                    <select
                      value={searchFilters.category}
                      onChange={(e) => setSearchFilters(prev => ({ ...prev, category: e.target.value }))}
                      className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    >
                      <option value="">{language === 'en-US' ? 'All Categories' : 'Tất cả danh mục'}</option>
                      <option value="interior">Interior Scenes</option>
                      <option value="exterior">Exterior Scenes</option>
                      <option value="furniture">Furniture</option>
                      <option value="lighting">Lighting</option>
                      <option value="textures">Textures</option>
                      <option value="landscape">Landscape</option>
                    </select>
                  </div>

                  {/* File Format */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      {language === 'en-US' ? 'File Format' : 'Định dạng file'}
                    </label>
                    <select
                      value={searchFilters.fileFormat}
                      onChange={(e) => setSearchFilters(prev => ({ ...prev, fileFormat: e.target.value }))}
                      className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    >
                      <option value="">{language === 'en-US' ? 'All Formats' : 'Tất cả định dạng'}</option>
                      <option value="SKP">SKP</option>
                      <option value="OBJ">OBJ</option>
                      <option value="FBX">FBX</option>
                      <option value="DAE">DAE</option>
                      <option value="3DS">3DS</option>
                    </select>
                  </div>

                  {/* Rating */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      {language === 'en-US' ? `Minimum Rating: ${searchFilters.minRating} stars` : `Đánh giá tối thiểu: ${searchFilters.minRating} sao`}
                    </label>
                    <input
                      type="range"
                      min="0"
                      max="5"
                      step="0.5"
                      value={searchFilters.minRating}
                      onChange={(e) => setSearchFilters(prev => ({ ...prev, minRating: parseFloat(e.target.value) }))}
                      className="w-full"
                    />
                  </div>

                  {/* Sort By */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      {language === 'en-US' ? 'Sort By' : 'Sắp xếp theo'}
                    </label>
                    <select
                      value={searchFilters.sortBy}
                      onChange={(e) => setSearchFilters(prev => ({ ...prev, sortBy: e.target.value }))}
                      className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    >
                      <option value="relevance">{language === 'en-US' ? 'Relevance' : 'Độ liên quan'}</option>
                      <option value="date">{language === 'en-US' ? 'Date' : 'Ngày tạo'}</option>
                      <option value="downloads">{language === 'en-US' ? 'Downloads' : 'Lượt tải'}</option>
                      <option value="rating">{language === 'en-US' ? 'Rating' : 'Đánh giá'}</option>
                    </select>
                  </div>
                </div>
              </div>

              {/* Footer */}
              <div className="flex items-center justify-between p-4 border-t border-gray-200 dark:border-gray-700">
                <button
                  onClick={() => setSearchFilters({
    category: ',
                    tags: [],
                    dateRange: { start: ', end: ' },
                    fileFormat: ',
                    minRating: 0,
                    minDownloads: 0,
                    author: ',
                    sortBy: 'relevance',
                    sortOrder: 'desc'
                  })}
                  className="px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors"
                >
                  {language === 'en-US' ? 'Reset' : 'Đặt lại'}
                </button>

                <div className="flex items-center space-x-3">
                  <button
                    onClick={() => setShowAdvancedSearch(false)}
                    className="px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                  >
                    {language === 'en-US' ? 'Cancel' : 'Hủy'}
                  </button>
                  <button
                    onClick={() => handleAdvancedSearch(searchFilters)}
                    className="flex items-center space-x-2 px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
                  >
                    <SearchIcon className="w-4 h-4" />
                    <span>{language === 'en-US' ? 'Search' : 'Tìm kiếm'}</span>
                  </button>
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Image Upload Modal */}
      <AnimatePresence>
        {showImageUpload && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 backdrop-blur-sm z-[60] flex items-center justify-center p-4"
            onClick={() => setShowImageUpload(false)}
          >
            <motion.div
              initial={{ opacity: 0, scale: 0.9, y: 20 }}
              animate={{ opacity: 1, scale: 1, y: 0 }}
              exit={{ opacity: 0, scale: 0.9, y: 20 }}
              className="bg-white dark:bg-gray-800 rounded-xl shadow-2xl w-full max-w-2xl max-h-[80vh] overflow-hidden"
              onClick={(e) => e.stopPropagation()}
            >
              {/* Header */}
              <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
                <div className="flex items-center space-x-3">
                  <ImageIcon className="w-5 h-5 text-purple-600" />
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                    {language === 'en-US' ? 'Image Analysis' : 'Phân tích hình ảnh'}
                  </h3>
                </div>
                <button
                  onClick={() => setShowImageUpload(false)}
                  className="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700"
                >
                  <XIcon className="w-5 h-5" />
                </button>
              </div>

              {/* Content */}
              <div className="p-4 overflow-y-auto max-h-[60vh]">
                <div className="space-y-4">
                  <p className="text-gray-600 dark:text-gray-300 text-center">
                    {language === 'en-US'
                      ? 'Upload an image to get AI-powered analysis and find related 3D models'
                      : 'Tải lên hình ảnh để nhận phân tích AI và tìm các mô hình 3D liên quan'
                    }
                  </p>

                  {/* Image Upload Component */}
                  <div className="mt-4">
                    <ImageUploadChatbot
                      onImageAnalysis={handleImageAnalysis}
                      isAnalyzing={isAnalyzingImage}
                      language={language}
                    />
                  </div>
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

// Default props
Chatbot.defaultProps = {
    initialLanguage: 'en-US',
  initialOpen: false,
  initialMinimized: false,
  position: 'bottom-right',
  theme: 'auto',
};

export default Chatbot;
