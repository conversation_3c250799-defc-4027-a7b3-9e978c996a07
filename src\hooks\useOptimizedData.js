import { useState, useEffect, useCallback, useRef } from 'react';
import axios from 'axios';

/**
 * Custom hook for optimized data fetching and caching
 *
 * @param {Object} options - Configuration options
 * @param {boolean} options.enableCaching - Whether to enable caching
 * @param {number} options.cacheDuration - Cache duration in milliseconds
 * @param {boolean} options.enablePrefetching - Whether to enable prefetching
 * @param {boolean} options.enableRetry - Whether to enable retry on failure
 * @param {number} options.maxRetries - Maximum number of retries
 * @param {number} options.retryDelay - Delay between retries in milliseconds
 * @returns {Object} Optimized data utilities
 */
const useOptimizedData = ({
  enableCaching = true,
  cacheDuration = 5 * 60 * 1000, // 5 minutes
  enablePrefetching = true,
  enableRetry = true,
  maxRetries = 3,
  retryDelay = 1000
} = {}) => {
  const [dataCache, setDataCache] = useState({});
  const [prefetchedUrls, setPrefetchedUrls] = useState([]);
  const [isIdle, setIsIdle] = useState(false);
  const abortControllers = useRef({});

  // Clear expired cache items
  useEffect(() => {
    if (!enableCaching) return;

    const interval = setInterval(() => {
      const now = Date.now();
      const newCache = { ...dataCache };
      let hasChanges = false;

      Object.keys(newCache).forEach(key => {
        if (now - newCache[key].timestamp > cacheDuration) {
          delete newCache[key];
          hasChanges = true;
        }
      });

      if (hasChanges) {
        setDataCache(newCache);
      }
    }, 60000); // Check every minute

    return () => clearInterval(interval);
  }, [dataCache, cacheDuration, enableCaching]);

  // Detect idle time for prefetching
  useEffect(() => {
    if (!enablePrefetching) return;

    let idleTimeout;

    const handleUserActivity = () => {
      setIsIdle(false);
      clearTimeout(idleTimeout);

      idleTimeout = setTimeout(() => {
        setIsIdle(true);
      }, 3000); // 3 seconds of inactivity
    };

    // Events that indicate user activity
    const events = ['mousemove', 'mousedown', 'keypress', 'scroll', 'touchstart'];

    events.forEach(event => {
      window.addEventListener(event, handleUserActivity);
    });

    // Initial timeout
    idleTimeout = setTimeout(() => {
      setIsIdle(true);
    }, 3000);

    return () => {
      events.forEach(event => {
        window.removeEventListener(event, handleUserActivity);
      });
      clearTimeout(idleTimeout);
    };
  }, [enablePrefetching]);

  // Generate cache key from URL and params
  const generateCacheKey = useCallback((url, params = {}) => {
    const sortedParams = Object.keys(params)
      .sort()
      .reduce((acc, key) => {
        acc[key] = params[key];
        return acc;
      }, {});

    return `${url}:${JSON.stringify(sortedParams)}`;
  }, []);

  // Fetch data with caching and retry
  const fetchData = useCallback(async (url, options = {}) => {
    const {
      method = 'GET',
      params = {},
      data = null,
      headers = {},
      useCache = enableCaching,
      bypassCache = false,
      retries = maxRetries,
      requestId = Date.now().toString()
    } = options;

    // Generate cache key
    const cacheKey = generateCacheKey(url, params);

    // Check cache if enabled and not bypassed
    if (useCache && !bypassCache && dataCache[cacheKey]) {
      const now = Date.now();
      if (now - dataCache[cacheKey].timestamp <= cacheDuration) {
        return dataCache[cacheKey].data;
      }
    }

    // Create abort controller
    const controller = new AbortController();
    abortControllers.current[requestId] = controller;

    // Retry function
    const fetchWithRetry = async (retriesLeft) => {
      try {
        const response = await axios({
          method,
          url,
          params,
          data,
          headers,
          signal: controller.signal
        });

        // Cache successful response
        if (useCache) {
          setDataCache(prev => ({
            ...prev,
            [cacheKey]: {
              data: response.data,
              timestamp: Date.now()
            }
          }));
        }

        // Remove abort controller
        delete abortControllers.current[requestId];

        return response.data;
      } catch (error) {
        // If request was aborted, don't retry
        if (error.name === 'AbortError' || error.name === 'CanceledError') {
          throw error;
        }

        // If no retries left or retry is disabled, throw error
        if (retriesLeft <= 0 || !enableRetry) {
          // Remove abort controller
          delete abortControllers.current[requestId];
          throw error;
        }

        // Wait before retrying
        await new Promise(resolve => setTimeout(resolve, retryDelay));

        // Retry with one less retry
        return fetchWithRetry(retriesLeft - 1);
      }
    };

    return fetchWithRetry(retries);
  }, [dataCache, enableCaching, cacheDuration, generateCacheKey, enableRetry, maxRetries, retryDelay]);

  // Prefetch data
  const prefetchData = useCallback((url, options = {}) => {
    if (!enablePrefetching) return;

    const cacheKey = generateCacheKey(url, options.params || {});

    // Check if already prefetched or in cache
    if (prefetchedUrls.includes(cacheKey) || (dataCache[cacheKey] && Date.now() - dataCache[cacheKey].timestamp <= cacheDuration)) {
      return;
    }

    // Add to prefetched URLs
    setPrefetchedUrls(prev => [...prev, cacheKey]);

    // Fetch data with low priority
    fetchData(url, {
      ...options,
      headers: {
        ...options.headers,
        'Priority': 'low'
      }
    }).catch(() => {
      // Remove from prefetched URLs on error
      setPrefetchedUrls(prev => prev.filter(u => u !== cacheKey));
    });
  }, [enablePrefetching, prefetchedUrls, dataCache, cacheDuration, generateCacheKey, fetchData]);

  // Cancel request
  const cancelRequest = useCallback((requestId) => {
    if (abortControllers.current[requestId]) {
      abortControllers.current[requestId].abort();
      delete abortControllers.current[requestId];
    }
  }, []);

  // Cancel all requests
  const cancelAllRequests = useCallback(() => {
    Object.keys(abortControllers.current).forEach(requestId => {
      abortControllers.current[requestId].abort();
    });
    abortControllers.current = {};
  }, []);

  // Clear cache
  const clearCache = useCallback((url, params) => {
    if (url && params) {
      const cacheKey = generateCacheKey(url, params);
      setDataCache(prev => {
        const newCache = { ...prev };
        delete newCache[cacheKey];
        return newCache;
      });
    } else if (url) {
      // Clear all cache entries for this URL
      setDataCache(prev => {
        const newCache = { ...prev };
        Object.keys(newCache).forEach(key => {
          if (key.startsWith(`${url}:`)) {
            delete newCache[key];
          }
        });
        return newCache;
      });
    } else {
      // Clear all cache
      setDataCache({});
    }
  }, [generateCacheKey]);

  return {
    fetchData,
    prefetchData,
    cancelRequest,
    cancelAllRequests,
    clearCache,
    isIdle,
    prefetchedUrls,
    cache: dataCache
  };
};

export default useOptimizedData;
