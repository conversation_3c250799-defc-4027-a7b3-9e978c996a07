import mongoose from 'mongoose';
import bcrypt from 'bcryptjs';
import User from './src/models/User.js';

// Connect to MongoDB using the same connection string as the server
const connectDB = async () => {
  try {
    const conn = await mongoose.connect('mongodb://localhost:27017/3dsketchup', {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    console.log(`MongoDB Connected: ${conn.connection.host}`);
  } catch (error) {
    console.error('Database connection error:', error);
    process.exit(1);
  }
};

const createRealAdmin = async () => {
  try {
    await connectDB();
    
    console.log('Creating real admin user with admin role...');

    // Check if admin already exists
    const existingAdmin = await User.findOne({ email: '<EMAIL>' });
    
    if (existingAdmin) {
      console.log('Admin user already exists, updating role...');
      
      // Update existing user to admin role
      existingAdmin.role = 'admin';
      existingAdmin.subscription = {
        type: 'professional',
        status: 'active',
        startDate: new Date(),
        endDate: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000) // 1 year
      };
      existingAdmin.downloadCredits = 1000;
      existingAdmin.bio = 'Administrator of 3DSKETCHUP.NET';
      existingAdmin.location = 'Vietnam';
      
      await existingAdmin.save();
      
      console.log('✅ Existing user updated to admin!');
      console.log('User ID:', existingAdmin._id);
      console.log('Name:', existingAdmin.name);
      console.log('Email:', existingAdmin.email);
      console.log('Role:', existingAdmin.role);
      console.log('Subscription:', existingAdmin.subscription.type);
      
    } else {
      // Create new admin user
      console.log('Creating new admin user...');
      
      const adminUser = new User({
        name: 'Admin User',
        email: '<EMAIL>',
        password: 'admin123456', // Will be hashed by pre-save middleware
        role: 'admin',
        bio: 'Administrator of 3DSKETCHUP.NET',
        location: 'Vietnam',
        subscription: {
          type: 'professional',
          status: 'active',
          startDate: new Date(),
          endDate: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000) // 1 year
        },
        downloadCredits: 1000,
        emailVerified: true,
        isActive: true
      });

      const savedAdmin = await adminUser.save();
      
      console.log('✅ New admin user created successfully!');
      console.log('User ID:', savedAdmin._id);
      console.log('Name:', savedAdmin.name);
      console.log('Email:', savedAdmin.email);
      console.log('Role:', savedAdmin.role);
      console.log('Subscription:', savedAdmin.subscription.type);
    }

    // Also create a backup admin
    const backupAdmin = await User.findOne({ email: '<EMAIL>' });
    
    if (!backupAdmin) {
      console.log('Creating backup admin user...');
      
      const backupAdminUser = new User({
        name: 'Super Admin',
        email: '<EMAIL>',
        password: 'superadmin123', // Will be hashed by pre-save middleware
        role: 'admin',
        bio: 'Backup Administrator of 3DSKETCHUP.NET',
        location: 'Vietnam',
        subscription: {
          type: 'professional',
          status: 'active',
          startDate: new Date(),
          endDate: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000) // 1 year
        },
        downloadCredits: 1000,
        emailVerified: true,
        isActive: true
      });

      const savedBackupAdmin = await backupAdminUser.save();
      
      console.log('✅ Backup admin user created successfully!');
      console.log('Backup User ID:', savedBackupAdmin._id);
      console.log('Backup Email:', savedBackupAdmin.email);
    }

    // Update the existing testadmin to admin role as well
    const testAdmin = await User.findOne({ email: '<EMAIL>' });
    if (testAdmin) {
      testAdmin.role = 'admin';
      testAdmin.subscription = {
        type: 'professional',
        status: 'active',
        startDate: new Date(),
        endDate: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000)
      };
      testAdmin.downloadCredits = 1000;
      await testAdmin.save();
      console.log('✅ <EMAIL> upgraded to admin role!');
    }

    console.log('\n🎯 ADMIN ACCOUNTS READY:');
    console.log('1. <EMAIL> / admin123456 (Primary Admin)');
    console.log('2. <EMAIL> / superadmin123 (Backup Admin)');
    console.log('3. <EMAIL> / password123 (Test Admin)');
    console.log('\nAll accounts have:');
    console.log('- Role: admin');
    console.log('- Subscription: professional');
    console.log('- Download Credits: 1000');
    console.log('- Full admin privileges');

    process.exit(0);
  } catch (error) {
    console.error('Error creating admin user:', error);
    process.exit(1);
  }
};

createRealAdmin();
