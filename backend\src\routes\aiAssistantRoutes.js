import express from 'express';
import {
  analyzeModel,
  getModelHealth,
  getRecommendations,
  getAnalysisHistory
} from '../controllers/aiAssistantController.js';
import { protect, authorize } from '../middleware/auth.js';

const router = express.Router();

// AI Analysis routes
router.post('/analyze/:modelId', analyzeModel); // Remove auth for testing
router.get('/health/:modelId', getModelHealth);
router.get('/recommendations/:modelId', getRecommendations); // Remove auth for testing
router.get('/history/:modelId', getAnalysisHistory); // Remove auth for testing

export default router;
