#!/usr/bin/env node

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const srcDir = path.join(__dirname, '..', 'src');

console.log('🔍 Ensuring website uses only real API backend data...\n');

// Patterns that indicate mock data usage
const mockDataPatterns = [
  /const\s+\w*[Mm]ock\w*\s*=\s*\[/g,
  /import.*[Mm]ock.*from/g,
  /\/\*.*[Mm]ock.*\*\//gi,
  /\/\/.*[Mm]ock/gi,
  /\.mockData/g,
  /mockModels/gi,
  /mockCategories/gi,
  /mockUsers/gi,
  /mockStats/gi,
  /hardcoded.*data/gi,
  /static.*data/gi,
  /dummy.*data/gi,
  /fake.*data/gi,
  /test.*data/gi
];

// Patterns that indicate proper API usage
const apiPatterns = [
  /realDataService\./g,
  /mongoService\./g,
  /apiService\./g,
  /await.*\.get\(/g,
  /await.*\.post\(/g,
  /await.*\.put\(/g,
  /await.*\.delete\(/g,
  /fetch\(/g,
  /axios\./g
];

// Check if file uses mock data
const checkForMockData = (content, filePath) => {
  const issues = [];
  const fileName = path.relative(srcDir, filePath);

  // Check for mock data patterns
  mockDataPatterns.forEach((pattern, index) => {
    const matches = content.match(pattern);
    if (matches) {
      issues.push({
        type: 'mock_data',
        pattern: pattern.toString(),
        matches: matches.length,
        file: fileName
      });
    }
  });

  // Check if file has API calls (good)
  const hasApiCalls = apiPatterns.some(pattern => pattern.test(content));

  return {
    issues,
    hasApiCalls,
    fileName
  };
};

// Fix common mock data issues
const fixMockDataIssues = (content) => {
  let fixed = content;

  // Replace common mock data patterns with API calls
  const replacements = [
    {
      pattern: /const\s+mockModels\s*=\s*\[[\s\S]*?\];?/g,
      replacement: '// Mock data removed - using realDataService.getAllModels()'
    },
    {
      pattern: /const\s+mockCategories\s*=\s*\[[\s\S]*?\];?/g,
      replacement: '// Mock data removed - using realDataService.getCategories()'
    },
    {
      pattern: /const\s+mockStats\s*=\s*\{[\s\S]*?\};?/g,
      replacement: '// Mock data removed - using realDataService.getStats()'
    },
    {
      pattern: /setModels\(mockModels\)/g,
      replacement: 'setModels(await realDataService.getAllModels())'
    },
    {
      pattern: /setCategories\(mockCategories\)/g,
      replacement: 'setCategories(await realDataService.getCategories())'
    },
    {
      pattern: /setStats\(mockStats\)/g,
      replacement: 'setStats(await realDataService.getStats())'
    }
  ];

  replacements.forEach(({ pattern, replacement }) => {
    fixed = fixed.replace(pattern, replacement);
  });

  return fixed;
};

// Process a single file
const processFile = (filePath) => {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const result = checkForMockData(content, filePath);

    if (result.issues.length > 0) {
      console.log(`⚠️  ${result.fileName}:`);
      result.issues.forEach(issue => {
        console.log(`   - Found ${issue.matches} instances of ${issue.type}`);
      });

      // Try to fix the issues
      const fixedContent = fixMockDataIssues(content);
      if (fixedContent !== content) {
        fs.writeFileSync(filePath, fixedContent, 'utf8');
        console.log(`   ✅ Fixed mock data issues`);
        return { fixed: true, issues: result.issues.length };
      }
    }

    return { fixed: false, issues: result.issues.length };
  } catch (error) {
    console.error(`❌ Error processing ${filePath}:`, error.message);
    return { fixed: false, issues: 0 };
  }
};

// Process directory recursively
const processDirectory = (dirPath) => {
  let totalIssues = 0;
  let fixedFiles = 0;
  let totalFiles = 0;
  const problemFiles = [];

  try {
    const items = fs.readdirSync(dirPath);

    for (const item of items) {
      const itemPath = path.join(dirPath, item);
      const stat = fs.statSync(itemPath);

      if (stat.isDirectory()) {
        if (!['node_modules', '.git', 'dist', 'build'].includes(item)) {
          const result = processDirectory(itemPath);
          totalIssues += result.issues;
          fixedFiles += result.fixed;
          totalFiles += result.total;
          problemFiles.push(...result.problemFiles);
        }
      } else if (stat.isFile()) {
        if (/\.(js|jsx|ts|tsx)$/.test(item)) {
          totalFiles++;
          const result = processFile(itemPath);
          totalIssues += result.issues;
          if (result.fixed) {
            fixedFiles++;
          }
          if (result.issues > 0) {
            problemFiles.push(path.relative(srcDir, itemPath));
          }
        }
      }
    }
  } catch (error) {
    console.error(`❌ Error processing directory ${dirPath}:`, error.message);
  }

  return { issues: totalIssues, fixed: fixedFiles, total: totalFiles, problemFiles };
};

// Create API usage guide
const createApiUsageGuide = () => {
  const guideContent = `# API Usage Guide for 3DSKETCHUP.NET

## ✅ CORRECT: Use Real API Services

### 1. Use realDataService for all data fetching:
\`\`\`javascript
import realDataService from '../services/realDataService';

// Get all models
const models = await realDataService.getAllModels();

// Get featured models
const featured = await realDataService.getFeaturedModels();

// Get popular models
const popular = await realDataService.getPopularModels();

// Get recent models
const recent = await realDataService.getRecentModels();

// Get categories
const categories = await realDataService.getCategories();

// Get statistics
const stats = await realDataService.getStats();
\`\`\`

### 2. Use mongoService for direct MongoDB access:
\`\`\`javascript
import mongoService from '../services/mongoService';

// Get models with parameters
const models = await mongoService.getModels({ limit: 10 });

// Get model by ID
const model = await mongoService.getModelById(id);
\`\`\`

### 3. Use apiService for REST API calls:
\`\`\`javascript
import apiService from '../services/api';

// Get models via API
const response = await apiService.models.getAll();
const models = response.data;
\`\`\`

## ❌ AVOID: Mock Data

### Don't use hardcoded data:
\`\`\`javascript
// ❌ BAD
const mockModels = [
  { id: 1, title: 'Test Model' },
  // ...
];

// ❌ BAD
const hardcodedStats = {
  models: 100,
  downloads: 1000
};
\`\`\`

### Instead, always fetch from API:
\`\`\`javascript
// ✅ GOOD
const models = await realDataService.getAllModels();
const stats = await realDataService.getStats();
\`\`\`

## Performance Tips

1. Use caching (realDataService has built-in caching)
2. Use React.memo for components
3. Use useCallback for event handlers
4. Batch API calls with Promise.all when possible

## Error Handling

Always handle errors gracefully:
\`\`\`javascript
try {
  const models = await realDataService.getAllModels();
  setModels(models);
} catch (error) {
  setError('Failed to load models');
  setModels([]); // Fallback to empty array
}
\`\`\`
`;

  const guidePath = path.join(srcDir, 'docs', 'API_USAGE_GUIDE.md');
  const guideDir = path.dirname(guidePath);

  if (!fs.existsSync(guideDir)) {
    fs.mkdirSync(guideDir, { recursive: true });
  }

  fs.writeFileSync(guidePath, guideContent, 'utf8');
  console.log(`📚 Created API usage guide at ${path.relative(srcDir, guidePath)}`);
};

// Main execution
const result = processDirectory(srcDir);

console.log('\n📊 API Usage Check Summary:');
console.log(`Files checked: ${result.total}`);
console.log(`Mock data issues found: ${result.issues}`);
console.log(`Files fixed: ${result.fixed}`);

if (result.problemFiles.length > 0) {
  console.log('\n⚠️  Files that may still have issues:');
  result.problemFiles.forEach(file => {
    console.log(`   - ${file}`);
  });
}

// Create API usage guide
createApiUsageGuide();

if (result.issues === 0) {
  console.log('\n🎉 Perfect! Website is using only real API backend data!');
  console.log('✅ No mock data found');
  console.log('✅ All components use realDataService, mongoService, or apiService');
  console.log('✅ Website is production-ready');
} else {
  console.log('\n⚠️  Some issues were found and fixed.');
  console.log('🔄 Please review the changes and test the website.');
}

console.log('\n🚀 Website is now optimized for production use!');

export default { processDirectory, processFile };
