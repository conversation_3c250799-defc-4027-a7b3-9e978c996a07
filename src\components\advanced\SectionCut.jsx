import React, { useState, useRef, useEffect } from 'react';
import { useThree } from '@react-three/fiber';
import { Plane, Box } from '@react-three/drei';
import * as THREE from 'three';
import { FiMove, FiRotateCw, FiX } from 'react-icons/fi';

// SectionCut component for creating cross-sections of 3D models
const SectionCut = ({ modelRef, onClose }) => {
  const { scene, camera } = useThree();
  const planeRef = useRef();
  const [position, setPosition] = useState([0, 0, 0]);
  const [rotation, setRotation] = useState([0, 0, 0]);
  const [visible, setVisible] = useState(true);
  const [mode, setMode] = useState('move'); // 'move' or 'rotate'
  const [planeNormal, setPlaneNormal] = useState(new THREE.Vector3(0, 1, 0));
  const [clippingPlane, setClippingPlane] = useState(new THREE.Plane(new THREE.Vector3(0, 1, 0), 0));

  // Initialize clipping plane
  useEffect(() => {
    if (!scene) return;

    // Create clipping plane
    const plane = new THREE.Plane(new THREE.Vector3(0, 1, 0), 0);
    setClippingPlane(plane);

    // Apply clipping plane to all materials in the scene
    scene.traverse((node) => {
      if (node.isMesh && node.material) {
        if (Array.isArray(node.material)) {
          node.material.forEach(mat => {
            mat.clippingPlanes = [plane];
            mat.clipShadows = true;
            mat.needsUpdate = true;
          });
        } else {
          node.material.clippingPlanes = [plane];
          node.material.clipShadows = true;
          node.material.needsUpdate = true;
        }
      }
    });

    // Enable clipping in the renderer
    const renderer = scene.userData.renderer;
    if (renderer) {
      renderer.localClippingEnabled = true;
    }

    return () => {
      // Remove clipping planes when component unmounts
      scene.traverse((node) => {
        if (node.isMesh && node.material) {
          if (Array.isArray(node.material)) {
            node.material.forEach(mat => {
              mat.clippingPlanes = [];
              mat.needsUpdate = true;
            });
          } else {
            node.material.clippingPlanes = [];
            node.material.needsUpdate = true;
          }
        }
      });

      if (renderer) {
        renderer.localClippingEnabled = false;
      }
    };
  }, [scene]);

  // Update clipping plane when position or rotation changes
  useEffect(() => {
    if (!planeRef.current) return;

    // Update plane normal based on rotation
    const normal = new THREE.Vector3(0, 1, 0);
    const rotationMatrix = new THREE.Matrix4().makeRotationFromEuler(
      new THREE.Euler(rotation[0], rotation[1], rotation[2], 'XYZ')
    );
    normal.applyMatrix4(rotationMatrix).normalize();

    // Update plane position (constant)
    const constant = normal.dot(new THREE.Vector3(position[0], position[1], position[2]));

    // Update clipping plane
    clippingPlane.normal.copy(normal);
    clippingPlane.constant = constant;

    setPlaneNormal(normal);

  }, [position, rotation, clippingPlane]);

  // Handle position change
  const handlePositionChange = (axis, value) => {
    const newPosition = [...position];
    newPosition[axis] = parseFloat(value);
    setPosition(newPosition);
  };

  // Handle rotation change
  const handleRotationChange = (axis, value) => {
    const newRotation = [...rotation];
    newRotation[axis] = parseFloat(value);
    setRotation(newRotation);
  };

  // Toggle visibility
  const toggleVisibility = () => {
    setVisible(!visible);
  };

  // Toggle mode (move or rotate)
  const toggleMode = () => {
    setMode(mode === 'move' ? 'rotate' : 'move');
  };

  return (
    <>
      {/* Section cut plane visualization */}
      {visible && (
        <Plane
          ref={planeRef}
          args={[10, 10]}
          position={position}
          rotation={rotation}
        >
          <meshBasicMaterial
            color="#3b82f6"
            transparent
            opacity={0.2}
            side={THREE.DoubleSide}
            depthWrite={false}
          />
        </Plane>
      )}

      {/* Controls UI */}
      <Html position={[0, 0, 0]} center portal>
        <div className="absolute left-4 top-20 bg-white dark:bg-gray-800 p-4 rounded-lg shadow-lg z-20">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-sm font-medium">Section Cut</h3>
            <div className="flex space-x-2">
              <button
                onClick={toggleMode}
                className="p-1 rounded hover:bg-gray-100 dark:hover:bg-gray-700"
                title={mode === 'move' ? 'Switch to Rotate' : 'Switch to Move'}
              >
                {mode === 'move' ? <FiMove size={16} /> : <FiRotateCw size={16} />}
              </button>
              <button
                onClick={onClose}
                className="p-1 rounded hover:bg-gray-100 dark:hover:bg-gray-700"
                title="Close Section Cut"
              >
                <FiX size={16} />
              </button>
            </div>
          </div>

          {mode === 'move' ? (
            <div className="space-y-3">
              <div>
                <label className="block text-xs mb-1">X Position</label>
                <input
                  type="range"
                  min="-5"
                  max="5"
                  step="0.1"
                  value={position[0]}
                  onChange={(e) => handlePositionChange(0, e.target.value)}
                  className="w-full"
                />
                <div className="text-xs text-right">{position[0].toFixed(1)}</div>
              </div>

              <div>
                <label className="block text-xs mb-1">Y Position</label>
                <input
                  type="range"
                  min="-5"
                  max="5"
                  step="0.1"
                  value={position[1]}
                  onChange={(e) => handlePositionChange(1, e.target.value)}
                  className="w-full"
                />
                <div className="text-xs text-right">{position[1].toFixed(1)}</div>
              </div>

              <div>
                <label className="block text-xs mb-1">Z Position</label>
                <input
                  type="range"
                  min="-5"
                  max="5"
                  step="0.1"
                  value={position[2]}
                  onChange={(e) => handlePositionChange(2, e.target.value)}
                  className="w-full"
                />
                <div className="text-xs text-right">{position[2].toFixed(1)}</div>
              </div>
            </div>
          ) : (
            <div className="space-y-3">
              <div>
                <label className="block text-xs mb-1">X Rotation</label>
                <input
                  type="range"
                  min="0"
                  max={Math.PI * 2}
                  step="0.1"
                  value={rotation[0]}
                  onChange={(e) => handleRotationChange(0, e.target.value)}
                  className="w-full"
                />
                <div className="text-xs text-right">{(rotation[0] * 180 / Math.PI).toFixed(0)}°</div>
              </div>

              <div>
                <label className="block text-xs mb-1">Y Rotation</label>
                <input
                  type="range"
                  min="0"
                  max={Math.PI * 2}
                  step="0.1"
                  value={rotation[1]}
                  onChange={(e) => handleRotationChange(1, e.target.value)}
                  className="w-full"
                />
                <div className="text-xs text-right">{(rotation[1] * 180 / Math.PI).toFixed(0)}°</div>
              </div>

              <div>
                <label className="block text-xs mb-1">Z Rotation</label>
                <input
                  type="range"
                  min="0"
                  max={Math.PI * 2}
                  step="0.1"
                  value={rotation[2]}
                  onChange={(e) => handleRotationChange(2, e.target.value)}
                  className="w-full"
                />
                <div className="text-xs text-right">{(rotation[2] * 180 / Math.PI).toFixed(0)}°</div>
              </div>
            </div>
          )}

          <div className="mt-4">
            <button
              onClick={toggleVisibility}
              className="w-full py-1 px-2 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 rounded text-xs font-medium"
            >
              {visible ? 'Hide Plane' : 'Show Plane'}
            </button>
          </div>
        </div>
      </Html>
    </>
  );
};

export default SectionCut;
