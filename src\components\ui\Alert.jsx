import React, { useState, useCallback } from 'react';
import PropTypes from 'prop-types';
import { motion, AnimatePresence } from 'framer-motion';
import {
  FiInfo,
  FiAlertCircle,
  FiCheckCircle,
  FiAlertTriangle,
  FiX
} from 'react-icons/fi';

/**
 * Alert component for displaying messages, notifications, or feedback
 *
 * @param {Object} props - Component props
 * @param {React.ReactNode} props.children - Alert content
 * @param {string} props.variant - Alert variant (primary, secondary, accent, success, warning, error, info)
 * @param {string} props.title - Alert title
 * @param {boolean} props.dismissible - Whether the alert can be dismissed
 * @param {Function} props.onDismiss - Callback when alert is dismissed
 * @param {boolean} props.icon - Whether to show an icon
 * @param {React.ReactNode} props.customIcon - Custom icon to display
 * @param {string} props.className - Additional CSS classes
 * @returns {React.ReactElement} The Alert component
 */
const Alert = ({
  children,
  variant = 'primary',
  title,
  dismissible = false,
  onDismiss,
  icon = true,
  customIcon,
  className = '',
  ...rest
}) => {
  const [visible, setVisible] = useState(true);

  // Handle dismiss
  const handleDismiss = () => {
    setVisible(false);
    if (onDismiss) onDismiss();
  };

  // Base classes
  const baseClasses = 'alert';

  // Variant classes
  const variantClasses = `alert-${variant}`;

  // Additional classes
  const additionalClasses = className;

  // Combine all classes
  const alertClasses = [
    baseClasses,
    variantClasses,
    additionalClasses,
  ].filter(Boolean).join(' ');
  // Get icon based on variant
  const getIcon = () => {
    if (customIcon) return customIcon;

    switch (variant) {
      case 'success':
        return <FiCheckCircle className="h-5 w-5" />;
      case 'warning':
        return <FiAlertTriangle className="h-5 w-5" />;
      case 'error':
        return <FiAlertCircle className="h-5 w-5" />;
      case 'info':
      case 'primary':
      case 'secondary':
      case 'accent':
      default:
        return <FiInfo className="h-5 w-5" />;
    }
  };

  // Animation variants
  const animationVariants = {
    initial: { opacity: 0, y: -10, height: 'auto' },
    animate: { opacity: 1, y: 0, height: 'auto' },
    exit: { opacity: 0, height: 0, marginTop: 0, marginBottom: 0, overflow: 'hidden' },
  };

  return (
    <AnimatePresence>
      {visible && (
        <motion.div
          className={alertClasses}
          initial="initial"
          animate="animate"
          exit="exit"
          variants={animationVariants}
          transition={{ duration: 0.2 }}
          {...rest}
        >
          <div className="flex">
            {icon && (
              <div className="flex-shrink-0 mr-3">
                {getIcon()}
              </div>
            )}

            <div className="flex-1">
              {title && (
                <h3 className="text-sm font-medium mb-1">
                  {title}
                </h3>
              )}

              <div className="text-sm">
                {children}
              </div>
            </div>

            {dismissible && (
              <div className="ml-auto pl-3">
                <div className="-mx-1.5 -my-1.5">
                  <button
                    type="button"
                    className="inline-flex rounded-md p-1.5 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-transparent"
                    onClick={handleDismiss}
                    aria-label="Dismiss"
                  >
                    <span className="sr-only">Dismiss</span>
                    <FiX className="h-4 w-4" />
                  </button>
                </div>
              </div>
            )}
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

Alert.propTypes = {
    children: PropTypes.node.isRequired,
  variant: PropTypes.oneOf(['primary', 'secondary', 'accent', 'success', 'warning', 'error', 'info']),
  title: PropTypes.string,
  dismissible: PropTypes.bool,
  onDismiss: PropTypes.func,
  icon: PropTypes.bool,
  customIcon: PropTypes.node,
  className: PropTypes.string,
};

export default Alert;
