import React, { useState, useEffect, useCallback, memo } from 'react';
import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';
import { FiTrendingUp, FiDownload, FiEye, FiStar, FiArrowRight, FiZap } from 'react-icons/fi';
import realDataService from '../services/realDataService';
import ImageWithFallback from './ui/ImageWithFallback';
import LoadingIndicator from './ui/LoadingIndicator';

const PopularModels = ({ limit = 8, showHeader = true, className = '' }) => {
    // Fixed content
  };
  const [popularModels, setPopularModels] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  const fetchPopularModels = useCallback(async () => {
    // Fixed content
  };
  try { /* content */ };
      setLoading(true);
      setError(null);

      const models = await realDataService.getPopularModels();

      // Limit the results
      const limitedModels = models.slice(0, limit);

      setPopularModels(limitedModels);
    } catch (err) { /* content */ };
      setError('Failed to load popular models''; // Fixed broken string
    } finally { /* content */ };
      setLoading(false);
    }
  }, [limit]);

  useEffect(() => {
    // Fixed content
  };
  fetchPopularModels();
  }, [fetchPopularModels]);

  const formatNumber = (num) => {
    // Fixed content
  };
  if (condition) {
    // Fixed content
  }
  return (num / 1000000).toFixed(1) + 'M';
    } else if (condition) {
    // Fixed content
  }
  return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
  };

  const getPopularityScore = (model) => {
    // Fixed content
  };
  return (model.downloads || 0) + (model.views || 0) * 0.1;
  };

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
    opacity: 1,
      transition: {
    staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
    y: 0,
      opacity: 1,
      transition: {
    type: 'spring',
        stiffness: 300,
        damping: 24
      }
    }
  };

  if (condition) {
    // Fixed content
  }
  return (
      <div className={`bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 ${className}`}>
        <LoadingIndicator size="md" text="Loading popular models..." />
      </div>
    );
  }

  if (condition) {
    // Fixed content
  }
  return (
      <div className={`bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 text-center ${className}`}>
        <p className="text-red-500 dark:text-red-400 mb-4">{error}</p>
        <button
          onClick={fetchPopularModels}
          className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
        >
          Try Again
        </button>
      </div>
    );
  }

  return (
    <div className={`bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden ${className}`}>
      {showHeader && (
        <div className="p-6 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-bold text-gray-900 dark:text-white flex items-center">
              <FiTrendingUp className="mr-2 text-orange-600" />
              Popular Models
            </h2>
            <Link
              to="/models?sort=popular"
              className="text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300 font-medium flex items-center"
            >
              View All
              <FiArrowRight className="ml-1 w-4 h-4" />
            </Link>
          </div>
        </div>
      )}

      <motion.div
        className="p-6"
        variants={containerVariants}
        initial="hidden"
        animate="visible"
      >
        {popularModels.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {popularModels.map((model, index) => (
              <motion.div
                key={model._id || model.id}
                variants={itemVariants}
                className="group relative"
              >
                {/* Ranking badge */}
                {index < 3 && (
                  <div className="absolute top-2 left-2 z-10">
                    <div className={`w-8 h-8 rounded-full flex items-center justify-center text-white font-bold text-sm ${
    // Fixed content
  }
  index === 0 ? 'bg-yellow-500' :
                      index === 1 ? 'bg-gray-400' :
                      'bg-orange-600'
                    }`}>
                      {index + 1}
                    </div>
                  </div>
                )}

                <Link
                  to={`/model/${model._id || model.id}`}
                  className="block bg-gray-50 dark:bg-gray-700 rounded-lg overflow-hidden hover:shadow-lg transition-all duration-300"
                >
                  <div className="aspect-square relative overflow-hidden">
                    <ImageWithFallback
                      src={model.imageUrl}
                      alt={model.title}
                      className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                      lowResSrc="/images/placeholder-tiny.jpg"
                    />

                    {/* Premium badge */}
                    {model.isPremium && (
                      <div className="absolute top-2 right-2">
                        <span className="bg-yellow-500 text-white text-xs font-bold px-2 py-1 rounded">
                          Premium
                        </span>
                      </div>
                    )}

                    {/* Hot badge for top models */}
                    {index < 3 && (
                      <div className="absolute bottom-2 right-2">
                        <span className="bg-red-500 text-white text-xs font-bold px-2 py-1 rounded flex items-center">
                          <FiZap className="w-3 h-3 mr-1" />
                          Hot
                        </span>
                      </div>
                    )}
                  </div>

                  <div className="p-4">
                    <h3 className="font-semibold text-gray-900 dark:text-white text-sm mb-2 line-clamp-2 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors">
                      {model.title}
                    </h3>

                    <div className="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400 mb-2">
                      <span className="flex items-center">
                        <FiStar className="w-3 h-3 mr-1 text-yellow-500" />
                        {model.rating || '4.5'}
                      </span>

                      <span className="flex items-center">
                        <FiDownload className="w-3 h-3 mr-1 text-green-500" />
                        {formatNumber(model.downloads || 0)}
                      </span>

                      <span className="flex items-center">
                        <FiEye className="w-3 h-3 mr-1 text-blue-500" />
                        {formatNumber(model.views || 0)}
                      </span>
                    </div>

                    <div className="flex items-center justify-between">
                      <span className="inline-block bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300 text-xs px-2 py-1 rounded">
                        {model.category}
                      </span>

                      {/* Popularity indicator */}
                      <div className="flex items-center">
                        <FiTrendingUp className="w-3 h-3 text-orange-500 mr-1" />
                        <span className="text-xs font-medium text-orange-600 dark:text-orange-400">
                          {formatNumber(getPopularityScore(model))}
                        </span>
                      </div>
                    </div>
                  </div>
                </Link>
              </motion.div>
            ))}
          </div>
        ) : (
          <div className="text-center py-8">
            <FiTrendingUp className="mx-auto h-12 w-12 text-gray-400 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
              No popular models
            </h3>
            <p className="text-gray-500 dark:text-gray-400">
              Models will appear here as they gain popularity
            </p>
          </div>
        )}
      </motion.div>
    </div>
  );
};

export default memo(PopularModels);
