import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { FiChevronDown, FiChevronUp, FiFilter, FiRefreshCw, FiStar } from 'react-icons/fi';

const FilterSidebar = ({ filters, onFilterChange, categories, onClose }) => {
  // Collapsible sections state
  const [expandedSections, setExpandedSections] = useState({
    sort: true,
    subcategory: true,
    format: false,
    type: true,
    rating: false
  });

  // Available subcategories
  const subcategories = [
    'Interior',
    'Exterior',
    'Kitchen',
    'Bathroom',
    'Living Room',
    'Bedroom',
    'Office',
    'Garden',
    'Landscape',
    'Furniture',
    'Decoration',
    'Other'
  ];

  // Available formats
  const formats = [
    'Sketchup 2023',
    'Sketchup 2022',
    'Sketchup 2021',
    'Sketchup 2020',
    'Sketchup 2019',
    'Sketchup 2018',
    'Sketchup 2017',
    'Sketchup 8',
    'Sketchup 7',
    'FBX',
    'OBJ',
    'DAE',
    '3DS',
    'MAX'
  ];

  // Handle checkbox change for array filters
  const handleArrayFilterChange = (filterName, value) => {
    const currentValues = [...filters[filterName]];
    const index = currentValues.indexOf(value);

    if (index === -1) {
      currentValues.push(value);
    } else {
      currentValues.splice(index, 1);
    }

    onFilterChange({
      ...filters,
      [filterName]: currentValues
    });
  };

  // Handle radio button change
  const handleRadioChange = (filterName, value) => {
    onFilterChange({
      ...filters,
      [filterName]: value
    });
  };

  // Handle range change
  const handleRangeChange = (filterName, value) => {
    onFilterChange({
      ...filters,
      [filterName]: value
    });
  };

  // Handle sort change
  const handleSortChange = (value) => {
    onFilterChange({
      ...filters,
      sortBy: value
    });
  };

  // Toggle section expansion
  const toggleSection = (section) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  // Reset all filters
  const resetFilters = () => {
    onFilterChange({
      subcategory: [],
      format: [],
      isPremium: null,
      minRating: 0,
      maxRating: 5,
      sortBy: 'newest'
    });
  };

  // Filter section component
  const FilterSection = ({ title, section, icon, children }) => (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      className="mb-6"
    >
      <button
        onClick={() => toggleSection(section)}
        className="w-full flex items-center justify-between p-3 glass-card rounded-2xl border border-white/10 hover:bg-white/20 transition-all duration-300 group"
      >
        <div className="flex items-center">
          <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl flex items-center justify-center mr-3 group-hover:scale-110 transition-transform">
            {icon}
          </div>
          <h4 className="font-bold text-gray-900 dark:text-white">{title}</h4>
        </div>
        {expandedSections[section] ? (
          <FiChevronUp className="text-gray-500 group-hover:text-blue-600 transition-colors" />
        ) : (
          <FiChevronDown className="text-gray-500 group-hover:text-blue-600 transition-colors" />
        )}
      </button>

      {expandedSections[section] && (
        <motion.div
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: 'auto' }}
          exit={{ opacity: 0, height: 0 }}
          transition={{ duration: 0.3 }}
          className="mt-3 pl-3"
        >
          {children}
        </motion.div>
      )}
    </motion.div>
  );

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between p-4 glass-card rounded-2xl border border-white/20">
        <div className="flex items-center">
          <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center mr-3">
            <FiFilter className="h-6 w-6 text-white" />
          </div>
          <h3 className="text-xl font-black text-gray-900 dark:text-white">Bộ Lọc</h3>
        </div>
        <motion.button
          onClick={resetFilters}
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          className="flex items-center px-3 py-2 bg-gradient-to-r from-red-500 to-pink-600 text-white rounded-xl font-medium hover:shadow-lg transition-all duration-300"
        >
          <FiRefreshCw className="mr-2 h-4 w-4" />
          Reset
        </motion.button>
      </div>

      {/* Sort By */}
      <div className="mb-6">
        <h4 className="font-medium text-gray-900 dark:text-white mb-2 flex items-center">
          Sort By
          <FiChevronDown className="ml-1" />
        </h4>
        <div className="space-y-2">
          <label className="flex items-center">
            <input
              type="radio"
              name="sortBy"
              checked={filters.sortBy === 'newest'}
              onChange={() => handleSortChange('newest')}
              className="form-radio text-blue-600"
            />
            <span className="ml-2 text-gray-700 dark:text-gray-300">Newest</span>
          </label>
          <label className="flex items-center">
            <input
              type="radio"
              name="sortBy"
              checked={filters.sortBy === 'popular'}
              onChange={() => handleSortChange('popular')}
              className="form-radio text-blue-600"
            />
            <span className="ml-2 text-gray-700 dark:text-gray-300">Most Popular</span>
          </label>
          <label className="flex items-center">
            <input
              type="radio"
              name="sortBy"
              checked={filters.sortBy === 'rating'}
              onChange={() => handleSortChange('rating')}
              className="form-radio text-blue-600"
            />
            <span className="ml-2 text-gray-700 dark:text-gray-300">Highest Rated</span>
          </label>
        </div>
      </div>

      {/* Subcategory Filter */}
      <div className="mb-6">
        <h4 className="font-medium text-gray-900 dark:text-white mb-2 flex items-center">
          Subcategory
          <FiChevronDown className="ml-1" />
        </h4>
        <div className="space-y-2 max-h-48 overflow-y-auto">
          {subcategories.map(subcategory => (
            <label key={subcategory} className="flex items-center">
              <input
                type="checkbox"
                checked={filters.subcategory.includes(subcategory)}
                onChange={() => handleArrayFilterChange('subcategory', subcategory)}
                className="form-checkbox text-blue-600"
              />
              <span className="ml-2 text-gray-700 dark:text-gray-300">{subcategory}</span>
            </label>
          ))}
        </div>
      </div>

      {/* Format Filter */}
      <div className="mb-6">
        <h4 className="font-medium text-gray-900 dark:text-white mb-2 flex items-center">
          Format
          <FiChevronDown className="ml-1" />
        </h4>
        <div className="space-y-2 max-h-48 overflow-y-auto">
          {formats.map(format => (
            <label key={format} className="flex items-center">
              <input
                type="checkbox"
                checked={filters.format.includes(format)}
                onChange={() => handleArrayFilterChange('format', format)}
                className="form-checkbox text-blue-600"
              />
              <span className="ml-2 text-gray-700 dark:text-gray-300">{format}</span>
            </label>
          ))}
        </div>
      </div>

      {/* Premium Filter */}
      <div className="mb-6">
        <h4 className="font-medium text-gray-900 dark:text-white mb-2 flex items-center">
          Type
          <FiChevronDown className="ml-1" />
        </h4>
        <div className="space-y-2">
          <label className="flex items-center">
            <input
              type="radio"
              name="isPremium"
              checked={filters.isPremium === null}
              onChange={() => handleRadioChange('isPremium', null)}
              className="form-radio text-blue-600"
            />
            <span className="ml-2 text-gray-700 dark:text-gray-300">All</span>
          </label>
          <label className="flex items-center">
            <input
              type="radio"
              name="isPremium"
              checked={filters.isPremium === false}
              onChange={() => handleRadioChange('isPremium', false)}
              className="form-radio text-blue-600"
            />
            <span className="ml-2 text-gray-700 dark:text-gray-300">Free</span>
          </label>
          <label className="flex items-center">
            <input
              type="radio"
              name="isPremium"
              checked={filters.isPremium === true}
              onChange={() => handleRadioChange('isPremium', true)}
              className="form-radio text-blue-600"
            />
            <span className="ml-2 text-gray-700 dark:text-gray-300">Premium</span>
          </label>
        </div>
      </div>

      {/* Rating Filter */}
      <div className="mb-6">
        <h4 className="font-medium text-gray-900 dark:text-white mb-2 flex items-center">
          Rating
          <FiChevronDown className="ml-1" />
        </h4>
        <div className="space-y-4">
          <div>
            <label className="block text-sm text-gray-700 dark:text-gray-300 mb-1">
              Minimum Rating: {filters.minRating}
            </label>
            <input
              type="range"
              min="0"
              max="5"
              step="0.5"
              value={filters.minRating}
              onChange={(e) => handleRangeChange('minRating', parseFloat(e.target.value))}
              className="w-full"
            />
          </div>
          <div>
            <label className="block text-sm text-gray-700 dark:text-gray-300 mb-1">
              Maximum Rating: {filters.maxRating}
            </label>
            <input
              type="range"
              min="0"
              max="5"
              step="0.5"
              value={filters.maxRating}
              onChange={(e) => handleRangeChange('maxRating', parseFloat(e.target.value))}
              className="w-full"
            />
          </div>
        </div>
      </div>

      {/* Apply button for mobile */}
      {onClose && (
        <button
          onClick={onClose}
          className="w-full btn btn-primary mt-4"
        >
          Apply Filters
        </button>
      )}
    </div>
  );
};

export default FilterSidebar;
