import express from 'express';
import {
  createShowroom,
  getShowrooms,
  getShowroom,
  updateShowroom,
  deleteShowroom,
  addModelToShowroom,
  removeModelFromShowroom,
  updateModelInShowroom,
  getFeaturedShowrooms,
  getShowroomAnalytics
} from '../controllers/virtualShowroomController.js';
import { protect, authorize } from '../middleware/auth.js';

const router = express.Router();

// Public routes
router.get('/', getShowrooms);
router.get('/featured', getFeaturedShowrooms);
router.get('/:id', getShowroom);

// Protected routes
router.use(protect);

router.post('/', createShowroom);
router.put('/:id', updateShowroom);
router.delete('/:id', deleteShowroom);

// Model management in showrooms
router.post('/:id/models', addModelToShowroom);
router.put('/:id/models/:modelId', updateModelInShowroom);
router.delete('/:id/models/:modelId', removeModelFromShowroom);

// Analytics
router.get('/:id/analytics', getShowroomAnalytics);

export default router;
