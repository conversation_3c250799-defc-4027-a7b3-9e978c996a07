import imageUploadService from '../services/imageUploadService.js';
import Model from '../models/Model.js';

// @desc    Upload multiple preview images for a model
// @route   POST /api/images/upload/:modelId
// @access  Private (Admin)
export const uploadModelImages = async (req, res) => {
  try {
    const { modelId } = req.params;

    // Check if model exists
    const model = await Model.findById(modelId);
    if (!model) {
      return res.status(404).json({
        success: false,
        error: 'Model not found'
      });
    }

    // Check if files were uploaded
    if (!req.files || !req.files.images) {
      return res.status(400).json({
        success: false,
        error: 'No images provided'
      });
    }

    // Handle single or multiple files
    const files = Array.isArray(req.files.images) ? req.files.images : [req.files.images];

    // Process images
    const result = await imageUploadService.processMultipleImages(files, modelId);

    if (!result.success) {
      return res.status(400).json({
        success: false,
        error: 'Failed to process images',
        details: result.errors
      });
    }

    // Format images for database
    const previewImages = result.data.map((imageData, index) => 
      imageUploadService.formatImageForDatabase(imageData, index === 0)
    );

    // Update model with new images
    model.previewImages.push(...previewImages);
    
    // Set main image if this is the first image
    if (!model.imageUrl && previewImages.length > 0) {
      model.imageUrl = previewImages[0].url;
    }

    await model.save();

    res.status(200).json({
      success: true,
      data: {
        uploadedImages: previewImages,
        totalImages: model.previewImages.length
      }
    });

  } catch (error) {
    console.error('Image upload error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to upload images',
      details: error.message
    });
  }
};

// @desc    Delete a preview image
// @route   DELETE /api/images/:modelId/:imageId
// @access  Private (Admin)
export const deleteModelImage = async (req, res) => {
  try {
    const { modelId, imageId } = req.params;

    const model = await Model.findById(modelId);
    if (!model) {
      return res.status(404).json({
        success: false,
        error: 'Model not found'
      });
    }

    // Find image in model
    const imageIndex = model.previewImages.findIndex(img => img._id.toString() === imageId);
    if (imageIndex === -1) {
      return res.status(404).json({
        success: false,
        error: 'Image not found'
      });
    }

    const imageToDelete = model.previewImages[imageIndex];

    // Delete physical files
    await imageUploadService.deleteImage({
      original: { path: `uploads/models/${imageToDelete.filename}` },
      thumbnails: imageToDelete.thumbnails || {}
    });

    // Remove from model
    model.previewImages.splice(imageIndex, 1);

    // Update main image if deleted image was main
    if (imageToDelete.isMain && model.previewImages.length > 0) {
      model.previewImages[0].isMain = true;
      model.imageUrl = model.previewImages[0].url;
    } else if (model.previewImages.length === 0) {
      model.imageUrl = '';
    }

    await model.save();

    res.status(200).json({
      success: true,
      message: 'Image deleted successfully',
      data: {
        remainingImages: model.previewImages.length
      }
    });

  } catch (error) {
    console.error('Image deletion error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to delete image',
      details: error.message
    });
  }
};

// @desc    Set main preview image
// @route   PUT /api/images/:modelId/:imageId/set-main
// @access  Private (Admin)
export const setMainImage = async (req, res) => {
  try {
    const { modelId, imageId } = req.params;

    const model = await Model.findById(modelId);
    if (!model) {
      return res.status(404).json({
        success: false,
        error: 'Model not found'
      });
    }

    // Find image
    const imageIndex = model.previewImages.findIndex(img => img._id.toString() === imageId);
    if (imageIndex === -1) {
      return res.status(404).json({
        success: false,
        error: 'Image not found'
      });
    }

    // Update main image flags
    model.previewImages.forEach((img, index) => {
      img.isMain = index === imageIndex;
    });

    // Update model's main imageUrl
    model.imageUrl = model.previewImages[imageIndex].url;

    await model.save();

    res.status(200).json({
      success: true,
      message: 'Main image updated successfully',
      data: {
        mainImageUrl: model.imageUrl
      }
    });

  } catch (error) {
    console.error('Set main image error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to set main image',
      details: error.message
    });
  }
};

// @desc    Get model images
// @route   GET /api/images/:modelId
// @access  Public
export const getModelImages = async (req, res) => {
  try {
    const { modelId } = req.params;

    const model = await Model.findById(modelId).select('previewImages imageUrl title');
    if (!model) {
      return res.status(404).json({
        success: false,
        error: 'Model not found'
      });
    }

    res.status(200).json({
      success: true,
      data: {
        modelTitle: model.title,
        mainImage: model.imageUrl,
        previewImages: model.previewImages,
        totalImages: model.previewImages.length
      }
    });

  } catch (error) {
    console.error('Get images error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get images',
      details: error.message
    });
  }
};

// @desc    Reorder preview images
// @route   PUT /api/images/:modelId/reorder
// @access  Private (Admin)
export const reorderImages = async (req, res) => {
  try {
    const { modelId } = req.params;
    const { imageOrder } = req.body; // Array of image IDs in new order

    const model = await Model.findById(modelId);
    if (!model) {
      return res.status(404).json({
        success: false,
        error: 'Model not found'
      });
    }

    // Validate image order
    if (!Array.isArray(imageOrder) || imageOrder.length !== model.previewImages.length) {
      return res.status(400).json({
        success: false,
        error: 'Invalid image order'
      });
    }

    // Reorder images
    const reorderedImages = imageOrder.map(imageId => {
      const image = model.previewImages.find(img => img._id.toString() === imageId);
      if (!image) {
        throw new Error(`Image with ID ${imageId} not found`);
      }
      return image;
    });

    model.previewImages = reorderedImages;
    await model.save();

    res.status(200).json({
      success: true,
      message: 'Images reordered successfully',
      data: {
        previewImages: model.previewImages
      }
    });

  } catch (error) {
    console.error('Reorder images error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to reorder images',
      details: error.message
    });
  }
};
