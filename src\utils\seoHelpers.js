/**
 * Generate structured data for a product (3D model)
 * 
 * @param {Object} model - The model object
 * @param {string} model.id - Model ID
 * @param {string} model.title - Model title
 * @param {string} model.description - Model description
 * @param {string} model.imageUrl - Model image URL
 * @param {number} model.price - Model price (if applicable)
 * @param {string} model.currency - Currency code (default: USD)
 * @param {string} model.category - Model category
 * @param {string} model.subcategory - Model subcategory
 * @param {string} model.format - Model format
 * @param {number} model.rating - Model rating
 * @param {number} model.reviewCount - Number of reviews
 * @param {string} baseUrl - Base URL of the website
 * @returns {Object} Structured data object
 */
export const generateProductStructuredData = (model, baseUrl = 'https://3dsketchup.net') => {
  const modelUrl = `${baseUrl}/model/${model.id}`;

  return {
    '@context': 'https://schema.org',
    '@type': 'Product',
    name: model.title,
    description: model.description,
    image: model.imageUrl,
    url: modelUrl,
    sku: `MODEL-${model.id}`,
    mpn: `3DSKETCHUP-${model.id}`,
    category: `${model.category}${model.subcategory ? ` > ${model.subcategory}` : ''}`,
    ...(model.price && {
      offers: {
        '@type': 'Offer',
        price: model.price,
        priceCurrency: model.currency || 'USD',
        availability: 'https://schema.org/InStock',
        url: modelUrl,
        seller: {
          '@type': 'Organization',
          name: '3DSKETCHUP.NET'
        }
      }
    }),
    ...(model.rating && {
      aggregateRating: {
        '@type': 'AggregateRating',
        ratingValue: model.rating,
        reviewCount: model.reviewCount || 0,
        bestRating: '5',
        worstRating: '1'
      }
    })
  };
};

/**
 * Generate structured data for a category page
 * 
 * @param {Object} category - The category object
 * @param {string} category.name - Category name
 * @param {string} category.description - Category description
 * @param {string} category.imageUrl - Category image URL
 * @param {Array} category.subcategories - Subcategories
 * @param {string} baseUrl - Base URL of the website
 * @returns {Object} Structured data object
 */
export const generateCategoryStructuredData = (category, baseUrl = 'https://3dsketchup.net') => {
  const categoryUrl = `${baseUrl}/category/${category.name.toLowerCase()}`;

  return {
    '@context': 'https://schema.org',
    '@type': 'CollectionPage',
    name: `${category.name} 3D Models`,
    description: category.description || `Browse our collection of ${category.name} 3D models`,
    url: categoryUrl,
    image: category.imageUrl,
    ...(category.subcategories && category.subcategories.length > 0 && {
      hasPart: category.subcategories.map(subcategory => ({
        '@type': 'CollectionPage',
        name: `${subcategory.name} 3D Models`,
        url: `${baseUrl}/subcategory/${subcategory.name.toLowerCase()}`
      }))
    })
  };
};

/**
 * Generate structured data for the organization
 * 
 * @param {string} baseUrl - Base URL of the website
 * @returns {Object} Structured data object
 */
export const generateOrganizationStructuredData = (baseUrl = 'https://3dsketchup.net') => {
  return {
    '@context': 'https://schema.org',
    '@type': 'Organization',
    name: '3DSKETCHUP.NET',
    url: baseUrl,
    logo: `${baseUrl}/images/logo.png`,
    sameAs: [
      'https://facebook.com/3dsketchup',
      'https://twitter.com/3dsketchup',
      'https://instagram.com/3dsketchup',
      'https://pinterest.com/3dsketchup'
    ],
    contactPoint: {
      '@type': 'ContactPoint',
      telephone: '******-123-4567',
      contactType: 'customer service',
      email: '<EMAIL>',
      availableLanguage: ['English', 'Vietnamese']
    }
  };
};

/**
 * Generate structured data for a blog article
 * 
 * @param {Object} article - The article object
 * @param {string} article.title - Article title
 * @param {string} article.description - Article description
 * @param {string} article.imageUrl - Article image URL
 * @param {string} article.publishedDate - Published date (ISO format)
 * @param {string} article.modifiedDate - Modified date (ISO format)
 * @param {string} article.authorName - Author name
 * @param {string} article.authorUrl - Author URL
 * @param {Array} article.tags - Article tags
 * @param {string} baseUrl - Base URL of the website
 * @returns {Object} Structured data object
 */
export const generateArticleStructuredData = (article, baseUrl = 'https://3dsketchup.net') => {
  const articleUrl = `${baseUrl}/blog/${article.slug}`;

  return {
    '@context': 'https://schema.org',
    '@type': 'BlogPosting',
    headline: article.title,
    description: article.description,
    image: article.imageUrl,
    datePublished: article.publishedDate,
    dateModified: article.modifiedDate || article.publishedDate,
    author: {
      '@type': 'Person',
      name: article.authorName,
      url: article.authorUrl
    },
    publisher: {
      '@type': 'Organization',
      name: '3DSKETCHUP.NET',
      logo: {
        '@type': 'ImageObject',
        url: `${baseUrl}/images/logo.png`
      }
    },
    url: articleUrl,
    mainEntityOfPage: articleUrl,
    keywords: article.tags.join(', ')
  };
};
