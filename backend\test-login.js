import fetch from 'node-fetch';

const testLogin = async () => {
  try {
    console.log('Testing login API...');

    const response = await fetch('http://localhost:5002/api/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'password123'
      })
    });

    console.log('Response status:', response.status);
    console.log('Response headers:', response.headers.raw());

    const data = await response.text();
    console.log('Response body:', data);

    if (response.ok) {
      const jsonData = JSON.parse(data);
      console.log('Login successful!');
      console.log('Token:', jsonData.token ? 'Present' : 'Missing');
      console.log('User:', jsonData.user ? jsonData.user.name : 'Missing');
    } else {
      console.log('Login failed with status:', response.status);
    }

  } catch (error) {
    console.error('Error testing login:', error);
  }
};

testLogin();
