import mongoose from 'mongoose';
import dotenv from 'dotenv';
import { connectDB } from '../config/db.js';
import User from '../models/User.js';
import Model from '../models/Model.js';
import Activity from '../models/Activity.js';

// Load environment variables
dotenv.config();

// Connect to database
await connectDB();

// Generate activity data
const generateActivityData = async () => {
  try {
    // Clear existing activities
    await Activity.deleteMany({});
    console.log('Cleared existing activity data');

    // Get users and models
    const users = await User.find();
    const models = await Model.find();

    if (users.length === 0 || models.length === 0) {
      console.error('No users or models found. Please seed users and models first.');
      process.exit(1);
    }

    const activities = [];
    const activityTypes = ['download', 'upload', 'save', 'comment', 'view', 'login', 'register'];
    
    // Generate random activities for each user
    for (const user of users) {
      // Number of activities per user (between 5 and 15)
      const numActivities = Math.floor(Math.random() * 10) + 5;
      
      for (let i = 0; i < numActivities; i++) {
        // Random activity type
        const type = activityTypes[Math.floor(Math.random() * activityTypes.length)];
        
        // Random model (for model-related activities)
        const model = models[Math.floor(Math.random() * models.length)];
        
        // Random date in the last 30 days
        const date = new Date();
        date.setDate(date.getDate() - Math.floor(Math.random() * 30));
        
        const activity = {
          user: user._id,
          type,
          createdAt: date
        };
        
        // Add model reference for model-related activities
        if (['download', 'upload', 'save', 'comment', 'view'].includes(type)) {
          activity.model = model._id;
        }
        
        // Add details for specific activity types
        if (type === 'comment') {
          activity.details = {
            comment: 'Great model, I love the details!'
          };
        } else if (type === 'login') {
          activity.ipAddress = '192.168.1.' + Math.floor(Math.random() * 255);
          activity.userAgent = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36';
        }
        
        activities.push(activity);
      }
    }
    
    // Insert activities
    await Activity.insertMany(activities);
    console.log(`Seeded ${activities.length} activities`);
    
    // Update user login history
    for (const user of users) {
      const loginActivities = activities.filter(a => a.user.toString() === user._id.toString() && a.type === 'login');
      
      if (loginActivities.length > 0) {
        const loginHistory = loginActivities.map(a => ({
          date: a.createdAt,
          ipAddress: a.ipAddress || '127.0.0.1',
          userAgent: a.userAgent || 'Unknown',
          successful: true
        }));
        
        await User.findByIdAndUpdate(user._id, { loginHistory });
      }
    }
    
    console.log('Updated user login history');
    
    console.log('Activity data seeding completed successfully');
  } catch (error) {
    console.error('Error seeding activity data:', error);
  } finally {
    mongoose.disconnect();
  }
};

// Run the seeder
generateActivityData();
