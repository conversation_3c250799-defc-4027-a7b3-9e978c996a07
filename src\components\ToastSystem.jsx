import React, { createContext, useContext, useState, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { FiCheck, FiX, FiAlertTriangle, FiInfo, FiHeart } from 'react-icons/fi';

const ToastContext = createContext();

export const useToast = () => {
  const context = useContext(ToastContext);
  if (!context) {
    throw new Error('useToast must be used within a ToastProvider');
  }
  return context;
};

const ToastProvider = ({ children }) => {
  const [toasts, setToasts] = useState([]);

  const addToast = useCallback((message, type = 'info', duration = 5000, options = {}) => {
    const id = Date.now() + Math.random();
    const toast = {
      id,
      message,
      type,
      duration,
      ...options
    };

    setToasts(prev => [...prev, toast]);

    if (duration > 0) {
      setTimeout(() => {
        removeToast(id);
      }, duration);
    }

    return id;
  }, []);

  const removeToast = useCallback((id) => {
    setToasts(prev => prev.filter(toast => toast.id !== id));
  }, []);

  const success = useCallback((message, options) => 
    addToast(message, 'success', 5000, options), [addToast]);
  
  const error = useCallback((message, options) => 
    addToast(message, 'error', 7000, options), [addToast]);
  
  const warning = useCallback((message, options) => 
    addToast(message, 'warning', 6000, options), [addToast]);
  
  const info = useCallback((message, options) => 
    addToast(message, 'info', 5000, options), [addToast]);

  const love = useCallback((message, options) => 
    addToast(message, 'love', 4000, options), [addToast]);

  const value = {
    toasts,
    addToast,
    removeToast,
    success,
    error,
    warning,
    info,
    love
  };

  return (
    <ToastContext.Provider value={value}>
      {children}
      <ToastContainer toasts={toasts} removeToast={removeToast} />
    </ToastContext.Provider>
  );
};

const ToastContainer = ({ toasts, removeToast }) => {
  return (
    <div className="fixed top-4 right-4 z-[9999] space-y-2 max-w-sm w-full">
      <AnimatePresence>
        {toasts.map((toast) => (
          <Toast key={toast.id} toast={toast} onRemove={removeToast} />
        ))}
      </AnimatePresence>
    </div>
  );
};

const Toast = ({ toast, onRemove }) => {
  const { id, message, type, title, action } = toast;

  const getToastConfig = () => {
    switch (type) {
      case 'success':
        return {
          icon: FiCheck,
          bgClass: 'bg-gradient-to-r from-green-500 to-emerald-600',
          iconBg: 'bg-white/20',
          textColor: 'text-white'
        };
      case 'error':
        return {
          icon: FiX,
          bgClass: 'bg-gradient-to-r from-red-500 to-pink-600',
          iconBg: 'bg-white/20',
          textColor: 'text-white'
        };
      case 'warning':
        return {
          icon: FiAlertTriangle,
          bgClass: 'bg-gradient-to-r from-yellow-500 to-orange-600',
          iconBg: 'bg-white/20',
          textColor: 'text-white'
        };
      case 'love':
        return {
          icon: FiHeart,
          bgClass: 'bg-gradient-to-r from-pink-500 to-red-500',
          iconBg: 'bg-white/20',
          textColor: 'text-white'
        };
      default:
        return {
          icon: FiInfo,
          bgClass: 'bg-gradient-to-r from-blue-500 to-purple-600',
          iconBg: 'bg-white/20',
          textColor: 'text-white'
        };
    }
  };

  const config = getToastConfig();
  const Icon = config.icon;

  return (
    <motion.div
      initial={{ opacity: 0, x: 300, scale: 0.8 }}
      animate={{ opacity: 1, x: 0, scale: 1 }}
      exit={{ opacity: 0, x: 300, scale: 0.8 }}
      transition={{ duration: 0.3, ease: "easeOut" }}
      className={`${config.bgClass} ${config.textColor} rounded-2xl shadow-professional-lg border border-white/20 overflow-hidden backdrop-blur-sm`}
    >
      <div className="p-4">
        <div className="flex items-start">
          <div className={`${config.iconBg} rounded-xl p-2 mr-3 flex-shrink-0`}>
            <Icon className="h-5 w-5" />
          </div>
          
          <div className="flex-1 min-w-0">
            {title && (
              <h4 className="font-bold text-sm mb-1">{title}</h4>
            )}
            <p className="text-sm leading-relaxed">{message}</p>
            
            {action && (
              <button
                onClick={action.onClick}
                className="mt-2 text-sm font-medium underline hover:no-underline transition-all"
              >
                {action.label}
              </button>
            )}
          </div>

          <button
            onClick={() => onRemove(id)}
            className="ml-2 p-1 hover:bg-white/20 rounded-lg transition-colors flex-shrink-0"
          >
            <FiX className="h-4 w-4" />
          </button>
        </div>
      </div>

      {/* Progress bar */}
      <motion.div
        className="h-1 bg-white/30"
        initial={{ scaleX: 1 }}
        animate={{ scaleX: 0 }}
        transition={{ duration: toast.duration / 1000, ease: "linear" }}
        style={{ originX: 0 }}
      />
    </motion.div>
  );
};

// Pre-built toast messages for common scenarios
export const showToastMessage = (message, type = 'info', options = {}) => {
  // This will be used with the hook
  console.log('Toast:', message, type);
};

// Common toast messages
export const ToastMessages = {
  // Success messages
  SUCCESS: {
    DOWNLOAD: 'Model đã được tải xuống thành công! 🎉',
    SAVE: 'Model đã được lưu vào danh sách yêu thích! ❤️',
    LOGIN: 'Đăng nhập thành công! Chào mừng bạn trở lại! 👋',
    REGISTER: 'Đăng ký thành công! Chào mừng bạn đến với 3DSKETCHUP.NET! 🎊',
    UPDATE: 'Cập nhật thông tin thành công! ✅',
    UPLOAD: 'Upload model thành công! 🚀'
  },
  
  // Error messages
  ERROR: {
    DOWNLOAD: 'Không thể tải xuống model. Vui lòng thử lại! ❌',
    LOGIN: 'Đăng nhập thất bại. Vui lòng kiểm tra thông tin! 🔒',
    NETWORK: 'Lỗi kết nối mạng. Vui lòng kiểm tra internet! 🌐',
    PERMISSION: 'Bạn không có quyền thực hiện hành động này! 🚫',
    FILE_SIZE: 'File quá lớn. Vui lòng chọn file nhỏ hơn! 📁'
  },
  
  // Warning messages
  WARNING: {
    UNSAVED: 'Bạn có thay đổi chưa được lưu! ⚠️',
    PREMIUM: 'Model này yêu cầu tài khoản Premium! 💎',
    LIMIT: 'Bạn đã đạt giới hạn tải xuống hôm nay! 📊',
    BROWSER: 'Trình duyệt của bạn có thể không hỗ trợ tính năng này! 🌐'
  },
  
  // Info messages
  INFO: {
    LOADING: 'Đang tải dữ liệu... Vui lòng đợi! ⏳',
    PROCESSING: 'Đang xử lý yêu cầu của bạn... 🔄',
    MAINTENANCE: 'Hệ thống đang bảo trì. Vui lòng thử lại sau! 🔧',
    UPDATE_AVAILABLE: 'Có phiên bản mới! Vui lòng cập nhật! 🆕'
  },
  
  // Love messages
  LOVE: {
    WELCOME: 'Chào mừng bạn đến với cộng đồng 3D tuyệt vời! 💖',
    MILESTONE: 'Chúc mừng! Bạn đã đạt được cột mốc quan trọng! 🏆',
    THANK_YOU: 'Cảm ơn bạn đã sử dụng dịch vụ của chúng tôi! 🙏',
    BIRTHDAY: 'Chúc mừng sinh nhật! Chúc bạn một ngày tuyệt vời! 🎂'
  }
};

export default ToastProvider;
