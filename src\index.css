@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700&family=JetBrains+Mono:wght@400;500&display=swap');
@import url('https://rsms.me/inter/inter.css'); /* Import Inter var */
@import './styles/animations.css';
@import './styles/components.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  /* Enhanced Primary color - Modern Blue */
  --primary-50: #eff6ff;
  --primary-100: #dbeafe;
  --primary-200: #bfdbfe;
  --primary-300: #93c5fd;
  --primary-400: #60a5fa;
  --primary-500: #3b82f6;
  --primary-600: #2563eb;
  --primary-700: #1d4ed8;
  --primary-800: #1e40af;
  --primary-900: #1e3a8a;
  --primary-950: #172554;

  /* Secondary color - Slate */
  --secondary-50: #f8fafc;
  --secondary-100: #f1f5f9;
  --secondary-200: #e2e8f0;
  --secondary-300: #cbd5e1;
  --secondary-400: #94a3b8;
  --secondary-500: #64748b;
  --secondary-600: #475569;
  --secondary-700: #334155;
  --secondary-800: #1e293b;
  --secondary-900: #0f172a;
  --secondary-950: #020617;

  /* Enhanced Accent color - Modern Teal */
  --accent-50: #f0fdfa;
  --accent-100: #ccfbf1;
  --accent-200: #99f6e4;
  --accent-300: #5eead4;
  --accent-400: #2dd4bf;
  --accent-500: #14b8a6;
  --accent-600: #0d9488;
  --accent-700: #0f766e;
  --accent-800: #115e59;
  --accent-900: #134e4a;
  --accent-950: #042f2e;

  /* New Gradient Colors */
  --gradient-primary: linear-gradient(135deg, var(--primary-600) 0%, var(--primary-700) 100%);
  --gradient-secondary: linear-gradient(135deg, var(--accent-500) 0%, var(--primary-600) 100%);
  --gradient-hero: linear-gradient(135deg, var(--primary-600) 0%, var(--primary-800) 50%, var(--accent-600) 100%);
  --gradient-card: linear-gradient(145deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);

  /* Enhanced Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  --shadow-glow: 0 0 20px rgba(59, 130, 246, 0.3);
  --shadow-glow-accent: 0 0 20px rgba(20, 184, 166, 0.3);

  /* Background colors */
  --background-light: #f9fafb;
  --background-dark: #0f172a;

  /* Text colors */
  --text-light: #1e293b;
  --text-dark: #f1f5f9;

  /* System properties */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

* {
  box-sizing: border-box;
}

html, body {
  margin: 0;
  padding: 0;
  width: 100%;
  overflow-x: hidden;
  position: relative;
}

/* Reset any conflicting styles */
html {
  scroll-padding-top: 120px;
}

/* Ensure smooth scrolling with header offset */
html {
  scroll-behavior: smooth;
}

body {
  min-width: 320px;
  min-height: 100vh;
  font-family: 'Inter', sans-serif;
  transition: background-color 0.3s ease, color 0.3s ease;
}

/* Ensure header doesn't cause horizontal scroll */
header {
  max-width: 100vw;
  overflow-x: hidden;
  will-change: transform;
  backface-visibility: hidden;
}

/* Ensure proper stacking context for header elements */
.header-dropdown {
  position: relative;
  z-index: 10001;
}

/* Prevent layout shift when header changes */
.header-offset {
  padding-top: 120px !important;
  margin-top: 0 !important;
}

@media (max-width: 768px) {
  .header-offset {
    padding-top: 130px !important;
  }

  body {
    padding-top: 130px !important;
  }

  main, .main, #root > div:first-child {
    margin-top: 130px !important;
  }

  /* Mobile header fix */
  header[class*="Header"],
  header[class*="header"],
  .header,
  [data-testid="header"],
  nav[role="banner"],
  header {
    min-height: 80px !important;
    height: auto !important;
  }
}

/* Professional Website Styling */
* {
  box-sizing: border-box;
}

/* CLEAN HEADER STYLES - ULTIMATE Z-INDEX */
header {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  width: 100% !important;
  height: 88px !important;
  z-index: 999999999 !important;
  background: rgba(255, 255, 255, 0.95) !important;
  backdrop-filter: blur(10px) !important;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1) !important;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1) !important;
  display: flex !important;
  align-items: center !important;
  visibility: visible !important;
  opacity: 1 !important;
  transform: translateZ(0) !important;
  will-change: transform !important;
  isolation: isolate !important;
}

/* Force header content to inherit high z-index */
header *,
header nav,
header div,
header button,
header a {
  z-index: inherit !important;
}

/* Lower z-index for other elements */
.modal, .overlay, .popup, .dropdown, .toast {
  z-index: 50 !important;
}

/* Specific z-index fixes for common overlays */
.fixed[class*="z-50"],
.fixed[class*="z-40"],
.fixed[class*="z-30"],
[style*="z-index: 50"],
[style*="z-index: 40"],
[style*="z-index: 30"] {
  z-index: 50 !important;
}

/* Header dropdowns should be higher than other elements but lower than header */
header .modal, header .overlay, header .popup, header .dropdown {
  z-index: 999999998 !important;
}

/* Force any fixed positioned element to be below header */
.fixed:not(header):not([data-header]) {
  z-index: 50 !important;
}

/* EMERGENCY Z-INDEX OVERRIDE - NUCLEAR OPTION */
* {
  z-index: auto !important;
}

/* Only header gets ultra high z-index */
header,
header *,
[data-header="true"],
[data-header="true"] * {
  z-index: 999999999 !important;
}

/* ULTIMATE HEADER PROTECTION - TOP-MOST LAYER */
header {
  z-index: 2147483647 !important; /* Maximum possible z-index */
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  width: 100vw !important;
  pointer-events: auto !important;
  visibility: visible !important;
  opacity: 1 !important;
  transform: translateZ(999999px) !important; /* Force to front layer */
  will-change: transform !important;
  isolation: isolate !important;
  contain: layout style paint !important;
  backface-visibility: hidden !important;
  perspective: 1000px !important;
}

/* Force all other elements to be below header */
body > *:not(header) {
  z-index: 1 !important;
  transform: translateZ(0) !important;
}

/* Specific overrides for common overlays */
.toast-container,
.notification,
.modal-overlay,
.popup-overlay,
[class*="toast"],
[class*="notification"],
[class*="modal"],
[class*="popup"] {
  z-index: 50 !important;
  transform: translateZ(0) !important;
}

/* ABSOLUTE HEADER SUPREMACY */
html {
  transform-style: preserve-3d !important;
}

body {
  transform-style: preserve-3d !important;
}

/* Force header to be in its own stacking context */
header {
  transform-style: preserve-3d !important;
  filter: none !important;
}

/* Ensure header content inherits top-most layer */
header *,
header nav,
header div,
header button,
header a,
header span,
header img {
  z-index: inherit !important;
  transform: translateZ(0) !important;
}

/* Override any potential conflicts */
* {
  transform-style: flat !important;
}

header, header * {
  transform-style: preserve-3d !important;
}

/* BROWSER EXTENSION OVERRIDE */
/* Force header above browser extensions and plugins */
header {
  z-index: 2147483647 !important;
  position: fixed !important;
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
  pointer-events: auto !important;
  user-select: auto !important;
  -webkit-user-select: auto !important;
  -moz-user-select: auto !important;
  -ms-user-select: auto !important;
}

/* Prevent any element from covering header */
*:not(header):not(header *) {
  z-index: 1 !important;
}

/* Force header to be rendered on top of everything */
header::before {
  content: '';
  position: absolute;
  top: -1px;
  left: -1px;
  right: -1px;
  bottom: -1px;
  z-index: -1;
  background: transparent;
  pointer-events: none;
}

/* FINAL HEADER SUPREMACY RULE */
/* This ensures header is ALWAYS on top, no matter what */
header[data-header="true"] {
  z-index: 2147483647 !important;
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  width: 100vw !important;
  transform: translateZ(999999px) !important;
  isolation: isolate !important;
  contain: layout style paint !important;
  will-change: transform !important;
  backface-visibility: hidden !important;
  perspective: 1000px !important;
  transform-style: preserve-3d !important;
  filter: none !important;
  clip-path: none !important;
  mask: none !important;
  mix-blend-mode: normal !important;
  opacity: 1 !important;
  visibility: visible !important;
  display: flex !important;
  pointer-events: auto !important;
}

/* NUCLEAR OPTION - FORCE HEADER ABOVE EVERYTHING */
/* This targets browser extensions and external overlays */
html::before,
body::before,
#root::before {
  content: none !important;
  display: none !important;
}

/* Remove any top margins/padding that might push header down */
html, body {
  margin: 0 !important;
  padding: 0 !important;
  position: relative !important;
}

/* Force header to be the first child and always visible */
header[data-header="true"] {
  order: -999999 !important;
  margin: 0 !important;
  padding: 0 !important;
  border: none !important;
  outline: none !important;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1) !important;
}

/* Override any external CSS that might affect header */
header[data-header="true"] * {
  all: revert !important;
  z-index: inherit !important;
}

/* Ensure no element can be positioned above header */
*:not(header):not(header *) {
  z-index: 1 !important;
  position: relative !important;
}

/* Force header background to be visible */
header[data-header="true"] {
  background: rgba(255, 255, 255, 0.98) !important;
  backdrop-filter: blur(10px) !important;
  -webkit-backdrop-filter: blur(10px) !important;
}

/* BROWSER EXTENSION KILLER - TARGET COMMON EXTENSION OVERLAYS */
/* This targets common browser extension elements that might cover the header */
div[id*="extension"],
div[class*="extension"],
div[id*="addon"],
div[class*="addon"],
div[id*="plugin"],
div[class*="plugin"],
div[id*="toolbar"],
div[class*="toolbar"],
div[id*="banner"],
div[class*="banner"],
iframe[id*="extension"],
iframe[class*="extension"] {
  z-index: 1 !important;
  position: relative !important;
  top: auto !important;
  left: auto !important;
  right: auto !important;
  bottom: auto !important;
}

/* Force any fixed positioned element to be below header */
*[style*="position: fixed"]:not(header):not(header *),
*[style*="position:fixed"]:not(header):not(header *) {
  z-index: 1 !important;
  top: 88px !important;
}

/* Override inline styles that might interfere */
*[style*="z-index"]:not(header):not(header *) {
  z-index: 1 !important;
}

/* Ensure header is always at the very top */
header[data-header="true"] {
  top: 0 !important;
  margin-top: 0 !important;
  padding-top: 0 !important;
  transform: translateY(0) translateZ(999999px) !important;
}

/* TARGET SPECIFIC DARK OVERLAY THAT'S COVERING HEADER */
/* This targets the dark banner/overlay visible in the screenshot */
div[style*="background-color: rgb(52, 58, 64)"],
div[style*="background-color: rgb(33, 37, 41)"],
div[style*="background-color: #343a40"],
div[style*="background-color: #212529"],
div[style*="background: rgb(52, 58, 64)"],
div[style*="background: rgb(33, 37, 41)"],
div[style*="background: #343a40"],
div[style*="background: #212529"],
.bg-dark,
.navbar-dark,
[class*="dark-banner"],
[class*="dark-overlay"] {
  z-index: 1 !important;
  position: relative !important;
  top: 88px !important;
  margin-top: 88px !important;
}

/* Force remove any top-positioned dark overlays */
*[style*="position: fixed"][style*="top: 0"]:not(header):not(header *) {
  top: 88px !important;
  z-index: 1 !important;
}

/* ULTIMATE HEADER VISIBILITY GUARANTEE */
header[data-header="true"] {
  background: rgba(255, 255, 255, 0.98) !important;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1) !important;
  min-height: 88px !important;
  height: auto !important;
}

/* Dark mode header */
.dark header {
  background: rgba(17, 24, 39, 0.95) !important;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
}

/* Header dropdown menus */
.header-dropdown {
  position: relative;
  z-index: 10000000 !important;
}

.header-dropdown > * {
  z-index: 10000000 !important;
}

/* CLEAN LAYOUT - REMOVE CONFLICTING STYLES */
/* Main content spacing */
.main-content {
  position: relative;
  z-index: 1;
  margin-top: 0; /* Remove margin to prevent double spacing */
  min-height: calc(100vh - 88px);
  padding-top: 88px; /* Use padding instead of margin */
}

/* Clean body - no extra padding */
body {
  padding-top: 0 !important;
  margin-top: 0 !important;
  position: relative !important;
}

/* Clean root element */
#root {
  padding-top: 0 !important;
  margin-top: 0 !important;
}

/* Clean app container */
#root > div {
  padding-top: 0 !important;
  margin-top: 0 !important;
}

/* Clean main content - only necessary margin */
main, .main {
  margin-top: 0 !important;
  position: relative;
  z-index: 1;
}

/* Professional gradients */
.gradient-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.gradient-secondary {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.gradient-success {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.gradient-hero {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 30%, #f093fb 60%, #667eea 100%);
  background-size: 400% 400%;
  animation: gradient-shift 8s ease infinite;
}

/* Glass morphism effects */
.glass-card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.dark .glass-card {
  background: rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Professional shadows */
.shadow-professional {
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1), 0 4px 10px rgba(0, 0, 0, 0.05);
}

.shadow-professional-lg {
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1), 0 8px 16px rgba(0, 0, 0, 0.05);
}

/* Smooth transitions */
.transition-professional {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Professional hover effects */
.hover-lift-professional {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.hover-lift-professional:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

/* Custom utility classes are now in components.css */

/* Custom animations */
.bg-pattern {
  background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
}

/* Line clamp utilities */
.line-clamp-1 {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Dark mode styles */
.dark body {
  background-color: var(--background-dark);
  color: var(--text-dark);
}

/* Scrollbar styling */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--secondary-100);
}

.dark ::-webkit-scrollbar-track {
  background: var(--secondary-800);
}

::-webkit-scrollbar-thumb {
  background: var(--secondary-300);
  border-radius: 4px;
}

.dark ::-webkit-scrollbar-thumb {
  background: var(--secondary-600);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--secondary-400);
}

.dark ::-webkit-scrollbar-thumb:hover {
  background: var(--secondary-500);
}
