import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { Link, useNavigate, useLocation } from 'react-router-dom';
import { Helmet } from 'react-helmet-async';
import { useAuth } from '../context/AuthContext';
import DarkModeToggle from './DarkModeToggle';
import { motion, AnimatePresence } from 'framer-motion';
import { FiMenu, FiX, FiUser, FiLogOut, FiLogIn, FiUserPlus, FiSearch, FiHeart, FiShoppingCart, FiDownload, FiUpload, FiCreditCard, FiGrid, FiZap, FiExternalLink, FiTool, FiCamera, FiSliders, FiBarChart } from 'react-icons/fi';
import { FaRegLightbulb, FaRegBuilding, FaTree, FaCouch } from 'react-icons/fa';

// Import UI components
import Button from './ui/Button';
import Input from './ui/Input';
import Badge from './ui/Badge';
import ImageSearch from './search/ImageSearch';
import AdvancedSearch from './search/AdvancedSearch';
import SearchSuggestions from './SearchSuggestions';
import ModelComparison from './ModelComparison';
import AdvancedSearchFilters from './AdvancedSearchFilters';

const Header = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isProfileMenuOpen, setIsProfileMenuOpen] = useState(false);
  const [isSearchOpen, setIsSearchOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [isScrolled, setIsScrolled] = useState(false);
  const [showImageSearch, setShowImageSearch] = useState(false);
  const [showAdvancedSearch, setShowAdvancedSearch] = useState(false);
  const [showAdvancedFilters, setShowAdvancedFilters] = useState(false);
  const [showComparison, setShowComparison] = useState(false);
  const [comparisonModels, setComparisonModels] = useState([]);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [isSearching, setIsSearching] = useState(false);
  const [searchError, setSearchError] = useState(null);
  const { currentUser, logout } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();

  // Categories with icons - fixed paths to match CategoryPage.jsx expectations and App.jsx routes
  const categories = useMemo(() => [
    { name: 'Interior Scenes', href: '/category/interior', icon: <FaRegLightbulb className="mr-2" /> },
    { name: 'Exterior Scenes', href: '/category/exterior', icon: <FaRegBuilding className="mr-2" /> },
    { name: 'Landscape/Garden', href: '/category/landscape', icon: <FaTree className="mr-2" /> },
    { name: 'Models/Objects', href: '/category/models', icon: <FaCouch className="mr-2" /> },
    { name: 'Virtual Showrooms', href: '/showrooms', icon: <FiGrid className="mr-2" /> },
    { name: 'Smart Collections', href: '/collections', icon: <FiZap className="mr-2" /> },
    { name: '3D Warehouse', href: '/warehouse', icon: <FiExternalLink className="mr-2" /> },
    { name: 'Plugins & Extensions', href: '/plugins', icon: <FiTool className="mr-2" /> },
  ], []);

  // Check if header is scrolled
  useEffect(() => {
    const handleScroll = () => {
      if (window.scrollY > 10) {
        setIsScrolled(true);
      } else {
        setIsScrolled(false);
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // Debug current user state (reduced logging)
  useEffect(() => {
    if (currentUser && !window.headerUserLogged) {
      console.log('Header component - User logged in:', currentUser.name);
      window.headerUserLogged = true;
    } else if (!currentUser && window.headerUserLogged) {
      console.log('Header component - User logged out');
      window.headerUserLogged = false;
    }
  }, [currentUser]);

  // Close menus when location changes
  useEffect(() => {
    setIsMenuOpen(false);
    setIsProfileMenuOpen(false);
    setIsSearchOpen(false);
    setShowImageSearch(false);
    setShowAdvancedSearch(false);
    setShowAdvancedFilters(false);
    setShowComparison(false);
    setShowSuggestions(false);
  }, [location]);

  // Close menus when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (!event.target.closest('.header-dropdown')) {
        setIsProfileMenuOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Force header to be visible and on top
  useEffect(() => {
    const forceHeaderVisibility = () => {
      const header = document.querySelector('header[data-header="true"]');
      if (header) {
        // Force header styles
        header.style.zIndex = '2147483647';
        header.style.position = 'fixed';
        header.style.top = '0';
        header.style.left = '0';
        header.style.right = '0';
        header.style.width = '100vw';
        header.style.transform = 'translateZ(999999px)';
        header.style.visibility = 'visible';
        header.style.opacity = '1';
        header.style.display = 'flex';
        header.style.pointerEvents = 'auto';

        // Remove any conflicting styles from body
        document.body.style.paddingTop = '0';
        document.body.style.marginTop = '0';
        document.documentElement.style.paddingTop = '0';
        document.documentElement.style.marginTop = '0';

        // Force header to be first child
        if (header.parentNode && header.parentNode.firstChild !== header) {
          header.parentNode.insertBefore(header, header.parentNode.firstChild);
        }
      }
    };

    // Run immediately
    forceHeaderVisibility();

    // Run on DOM changes
    const observer = new MutationObserver(forceHeaderVisibility);
    observer.observe(document.body, { childList: true, subtree: true });

    // Run periodically to combat browser extensions
    const interval = setInterval(forceHeaderVisibility, 1000);

    return () => {
      observer.disconnect();
      clearInterval(interval);
    };
  }, []);

  // Handle keyboard navigation
  useEffect(() => {
    const handleKeyDown = (event) => {
      if (event.key === 'Escape') {
        setIsMenuOpen(false);
        setIsProfileMenuOpen(false);
        setIsSearchOpen(false);
        setShowImageSearch(false);
        setShowAdvancedSearch(false);
        setShowAdvancedFilters(false);
        setShowComparison(false);
        setShowSuggestions(false);
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, []);

  const handleLogout = useCallback(() => {
    logout();
    navigate('/');
    setIsProfileMenuOpen(false);
  }, [logout, navigate]);

  const handleSearch = async (e) => {
    e.preventDefault();
    if (!searchQuery.trim()) return;

    setIsSearching(true);
    setSearchError(null);

    try {
      // Add a small delay to show loading state
      await new Promise(resolve => setTimeout(resolve, 300));

      navigate(`/search?q=${encodeURIComponent(searchQuery.trim())}`);
      setIsSearchOpen(false);
      setSearchQuery('');
    } catch (error) {
      console.error('Search error:', error);
      setSearchError('Search failed. Please try again.');
    } finally {
      setIsSearching(false);
    }
  };

  const handleAdvancedSearch = useCallback((searchParams) => {
    const queryParams = new URLSearchParams();

    Object.entries(searchParams).forEach(([key, value]) => {
      if (value && value !== '' && value !== 0) {
        queryParams.append(key, value);
      }
    });

    navigate(`/search?${queryParams.toString()}`);
    setShowAdvancedSearch(false);
  }, [navigate]);

  return (
    <>
      <Helmet>
        <title>3DSKETCHUP.NET - Premium 3D Models Platform</title>
        <meta name="description" content="Download premium SketchUp 3D models, scenes, and objects. Professional interior, exterior, landscape designs and architectural models." />
        <meta name="keywords" content="SketchUp, 3D models, interior design, exterior design, landscape, architecture, premium models" />
        <meta property="og:title" content="3DSKETCHUP.NET - Premium 3D Models Platform" />
        <meta property="og:description" content="Download premium SketchUp 3D models, scenes, and objects. Professional interior, exterior, landscape designs and architectural models." />
        <meta property="og:type" content="website" />
        <meta property="og:url" content="https://3dsketchup.net" />
        <meta property="og:image" content="https://3dsketchup.net/og-image.jpg" />
        <meta name="twitter:card" content="summary_large_image" />
        <meta name="twitter:title" content="3DSKETCHUP.NET - Premium 3D Models Platform" />
        <meta name="twitter:description" content="Download premium SketchUp 3D models, scenes, and objects. Professional interior, exterior, landscape designs and architectural models." />
        <meta name="twitter:image" content="https://3dsketchup.net/twitter-image.jpg" />
        <link rel="canonical" href="https://3dsketchup.net" />
        <script type="application/ld+json">
          {JSON.stringify({
            "@context": "https://schema.org",
            "@type": "WebSite",
            "name": "3DSKETCHUP.NET",
            "description": "Premium 3D Models Platform for SketchUp",
            "url": "https://3dsketchup.net",
            "potentialAction": {
              "@type": "SearchAction",
              "target": "https://3dsketchup.net/search?q={search_term_string}",
              "query-input": "required name=search_term_string"
            }
          })}
        </script>
      </Helmet>

      <header
      data-header="true"
      className={`header-professional fixed top-0 left-0 right-0 w-full transition-all duration-300 ${
        isScrolled
          ? 'bg-white/98 dark:bg-gray-900/98 backdrop-blur-xl shadow-2xl border-b border-gray-200/20 dark:border-gray-700/20 py-2'
          : 'bg-white/98 dark:bg-gray-900/98 backdrop-blur-xl py-3'
      }`}
      style={{
        margin: 0,
        padding: 0,
        minHeight: '88px',
        height: 'auto',
        boxShadow: isScrolled ? '0 10px 30px rgba(0,0,0,0.15)' : '0 5px 15px rgba(0,0,0,0.08)',
        zIndex: 2147483647, // Maximum possible z-index
        position: 'fixed',
        top: 0,
        left: 0,
        right: 0,
        width: '100vw',
        maxWidth: '100vw',
        transform: 'translateZ(999999px)', // Force to front layer
        willChange: 'transform',
        isolation: 'isolate', // Create new stacking context
        transformStyle: 'preserve-3d',
        backfaceVisibility: 'hidden',
        perspective: '1000px',
        contain: 'layout style paint'
      }}
    >
      <div className="w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-20 w-full">
          {/* Logo Section - Left */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5 }}
            className="flex items-center flex-shrink-0 relative z-[10002]"
          >
            <Link to="/" className="flex items-center space-x-4 group">
              {/* Ultra Professional Animated Logo */}
              <div className="relative w-16 h-16 perspective-1000 animate-perspective-rotate">
                {/* Main logo container with 3D effect */}
                <div className="relative w-full h-full bg-gradient-to-br from-blue-600 via-purple-600 via-indigo-600 to-cyan-500 rounded-2xl flex items-center justify-center shadow-2xl group-hover:animate-energy-pulse transition-all duration-500 group-hover:scale-110 overflow-hidden transform-gpu animate-holographic">

                  {/* Animated background layers */}
                  <div className="absolute inset-0 bg-gradient-to-r from-blue-400 via-purple-400 via-pink-400 to-cyan-400 opacity-0 group-hover:opacity-100 transition-opacity duration-700 animate-pulse"></div>

                  {/* Rotating outer ring */}
                  <div className="absolute inset-0 rounded-2xl border-4 border-transparent bg-gradient-to-r from-blue-300 via-purple-300 via-pink-300 to-cyan-300 opacity-0 group-hover:opacity-60 animate-rotate-slow"></div>

                  {/* Inner rotating ring */}
                  <div className="absolute inset-2 rounded-xl border-2 border-transparent bg-gradient-to-r from-yellow-300 via-orange-300 to-red-300 opacity-0 group-hover:opacity-40 animate-spin-slow" style={{animationDirection: 'reverse'}}></div>

                  {/* Shimmer overlay */}
                  <div className="absolute inset-0 rounded-2xl animate-shimmer opacity-40"></div>

                  {/* 3D Text with multiple effects */}
                  <div className="relative z-20 flex items-center justify-center">
                    <span className="text-white font-black text-2xl group-hover:text-yellow-200 transition-all duration-500 animate-bounce-subtle drop-shadow-2xl transform group-hover:scale-110 text-shadow-glow">
                      3D
                    </span>
                  </div>

                  {/* Enhanced floating particles system */}
                  <div className="absolute top-2 left-2 w-2 h-2 bg-white rounded-full opacity-70 animate-particle-float shadow-lg"></div>
                  <div className="absolute top-3 right-3 w-1.5 h-1.5 bg-yellow-300 rounded-full opacity-90 animate-particle-float shadow-md" style={{animationDelay: '0.3s'}}></div>
                  <div className="absolute bottom-3 left-3 w-1.5 h-1.5 bg-cyan-300 rounded-full opacity-80 animate-particle-float shadow-md" style={{animationDelay: '0.6s'}}></div>
                  <div className="absolute bottom-2 right-2 w-1 h-1 bg-purple-300 rounded-full opacity-85 animate-particle-float shadow-sm" style={{animationDelay: '0.9s'}}></div>
                  <div className="absolute top-1/2 left-1 w-1 h-1 bg-pink-300 rounded-full opacity-75 animate-particle-float shadow-sm" style={{animationDelay: '1.2s'}}></div>
                  <div className="absolute top-1/2 right-1 w-0.5 h-0.5 bg-orange-300 rounded-full opacity-90 animate-particle-float" style={{animationDelay: '1.5s'}}></div>

                  {/* Pulsing glow effect */}
                  <div className="absolute inset-0 rounded-2xl bg-gradient-to-r from-blue-400 via-purple-400 to-cyan-400 opacity-0 group-hover:opacity-30 transition-opacity duration-500 blur-lg animate-pulse-glow"></div>

                  {/* Corner accents */}
                  <div className="absolute top-0 left-0 w-4 h-4 bg-gradient-to-br from-yellow-400 to-orange-400 rounded-br-full opacity-60 animate-pulse"></div>
                  <div className="absolute top-0 right-0 w-4 h-4 bg-gradient-to-bl from-pink-400 to-purple-400 rounded-bl-full opacity-60 animate-pulse" style={{animationDelay: '0.5s'}}></div>
                  <div className="absolute bottom-0 left-0 w-4 h-4 bg-gradient-to-tr from-cyan-400 to-blue-400 rounded-tr-full opacity-60 animate-pulse" style={{animationDelay: '1s'}}></div>
                  <div className="absolute bottom-0 right-0 w-4 h-4 bg-gradient-to-tl from-green-400 to-teal-400 rounded-tr-full opacity-60 animate-pulse" style={{animationDelay: '1.5s'}}></div>

                  {/* Holographic overlay */}
                  <div className="absolute inset-0 rounded-2xl bg-gradient-to-r from-transparent via-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 animate-shimmer"></div>
                </div>
              </div>

              <div className="flex flex-col space-y-1">
                <span className="text-xl sm:text-2xl md:text-3xl font-black text-gray-900 dark:text-white font-heading group-hover:text-transparent group-hover:bg-gradient-to-r group-hover:from-blue-600 group-hover:via-purple-600 group-hover:to-cyan-600 group-hover:bg-clip-text transition-all duration-500 whitespace-nowrap leading-tight tracking-tight">
                  3DSKETCHUP.NET
                </span>
                <span className="hidden sm:block text-sm font-semibold text-gray-600 dark:text-gray-400 group-hover:text-transparent group-hover:bg-gradient-to-r group-hover:from-purple-500 group-hover:to-cyan-500 group-hover:bg-clip-text transition-all duration-500 tracking-wide">
                  Premium 3D Models Platform
                </span>
              </div>
            </Link>
          </motion.div>

          {/* Mobile menu button */}
          <div className="flex items-center justify-end gap-3 md:hidden">
            <button
              onClick={() => setIsSearchOpen(!isSearchOpen)}
              className="p-2 text-gray-700 hover:text-blue-600 dark:text-gray-300 dark:hover:text-blue-400 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg focus:outline-none transition-colors"
              aria-label="Search"
            >
              <FiSearch className="h-5 w-5" />
            </button>

            <DarkModeToggle />

            <button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="p-2 text-gray-700 hover:text-blue-600 dark:text-gray-300 dark:hover:text-blue-400 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg focus:outline-none transition-colors"
              aria-label="Toggle menu"
            >
              {isMenuOpen ? (
                <FiX className="h-6 w-6" />
              ) : (
                <FiMenu className="h-6 w-6" />
              )}
            </button>
          </div>

          {/* Desktop navigation - Center section */}
          <div className="hidden lg:flex items-center justify-center flex-1 mx-8">
            <nav className="flex items-center space-x-2">
              {categories.slice(0, 4).map((category) => (
                <Link
                  key={category.name}
                  to={category.href}
                  className="flex items-center space-x-2 px-3 py-2 text-gray-700 hover:text-blue-600 dark:text-gray-300 dark:hover:text-blue-400 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg transition-all duration-200 text-sm font-medium whitespace-nowrap group"
                >
                  <span className="text-lg group-hover:scale-110 transition-transform duration-200">{category.icon}</span>
                  <span className="hidden xl:inline">{category.name}</span>
                </Link>
              ))}
            </nav>
          </div>

          {/* Right section - Search, Dark Mode, User */}
          <div className="hidden md:flex items-center justify-end space-x-2">
            {/* Enhanced Search buttons */}
            <div className="flex items-center space-x-1">
              <motion.button
                onClick={() => setIsSearchOpen(!isSearchOpen)}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="p-2 glass-card border border-white/10 text-gray-700 hover:text-blue-600 dark:text-gray-300 dark:hover:text-blue-400 hover:bg-white/20 rounded-xl focus:outline-none transition-all duration-300 shadow-md"
                aria-label="Search"
                title="Text Search"
              >
                <FiSearch className="h-4 w-4" />
              </motion.button>
              <motion.button
                onClick={() => setShowImageSearch(true)}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="p-2 glass-card border border-white/10 text-gray-700 hover:text-purple-600 dark:text-gray-300 dark:hover:text-purple-400 hover:bg-white/20 rounded-xl focus:outline-none transition-all duration-300 shadow-md"
                aria-label="Image Search"
                title="Visual Search"
              >
                <FiCamera className="h-4 w-4" />
              </motion.button>
              {/* Advanced Filters Button - DISABLED */}
              {/* <motion.button
                onClick={() => setShowAdvancedFilters(true)}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="p-2 glass-card border border-white/10 text-gray-700 hover:text-indigo-600 dark:text-gray-300 dark:hover:text-indigo-400 hover:bg-white/20 rounded-xl focus:outline-none transition-all duration-300 shadow-md"
                aria-label="Advanced Filters"
                title="Advanced Filters"
              >
                <FiSliders className="h-4 w-4" />
              </motion.button> */}
              {comparisonModels.length > 0 && (
                <motion.button
                  onClick={() => setShowComparison(true)}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  className="relative p-2 glass-card border border-white/10 text-gray-700 hover:text-green-600 dark:text-gray-300 dark:hover:text-green-400 hover:bg-white/20 rounded-xl focus:outline-none transition-all duration-300 shadow-md"
                  aria-label="Compare Models"
                  title="Compare Models"
                >
                  <FiBarChart className="h-4 w-4" />
                  <span className="absolute -top-1 -right-1 w-5 h-5 bg-gradient-to-r from-red-500 to-pink-600 text-white text-xs rounded-full flex items-center justify-center font-bold">
                    {comparisonModels.length}
                  </span>
                </motion.button>
              )}
            </div>

            <DarkModeToggle />

            {/* User menu */}
            <div className="relative header-dropdown">
              {currentUser ? (
                <>
                  <button
                    onClick={() => setIsProfileMenuOpen(!isProfileMenuOpen)}
                    className="flex items-center text-gray-700 hover:text-blue-600 dark:text-gray-300 dark:hover:text-blue-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded-lg p-1"
                    aria-expanded={isProfileMenuOpen}
                    aria-haspopup="true"
                    aria-label="User menu"
                  >
                    <div className="w-8 h-8 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center text-blue-600 dark:text-blue-400 mr-2">
                      {currentUser?.name ? currentUser.name.charAt(0).toUpperCase() : 'U'}
                    </div>
                    <span className="hidden lg:inline font-medium whitespace-nowrap">{currentUser?.name || 'User'}</span>
                  </button>

                  <AnimatePresence>
                    {isProfileMenuOpen && (
                      <motion.div
                        initial={{ opacity: 0, y: -10 }}
                        animate={{ opacity: 1, y: 0 }}
                        exit={{ opacity: 0, y: -10 }}
                        transition={{ duration: 0.2 }}
                        className="absolute right-0 mt-2 w-56 bg-white dark:bg-gray-800 rounded-lg shadow-xl py-2 border border-gray-200 dark:border-gray-700"
                        style={{ zIndex: 10000001 }}
                      >
                        <div className="px-4 py-2 border-b border-gray-200 dark:border-gray-700">
                          <p className="text-sm font-medium text-gray-900 dark:text-white">{currentUser?.name || 'User'}</p>
                          <p className="text-xs text-gray-500 dark:text-gray-400 truncate">{currentUser?.email || 'No email available'}</p>
                        </div>

                        <Link
                          to="/profile"
                          className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-700"
                          onClick={() => setIsProfileMenuOpen(false)}
                        >
                          <FiUser className="h-4 w-4 mr-2" />
                          Your Profile
                        </Link>
                        <Link
                          to="/saved-models"
                          className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-700"
                          onClick={() => setIsProfileMenuOpen(false)}
                        >
                          <FiHeart className="h-4 w-4 mr-2" />
                          Saved Models
                        </Link>
                        <Link
                          to="/downloads"
                          className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-700"
                          onClick={() => setIsProfileMenuOpen(false)}
                        >
                          <FiDownload className="h-4 w-4 mr-2" />
                          Downloads
                        </Link>
                        <Link
                          to="/upload"
                          className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-700"
                          onClick={() => setIsProfileMenuOpen(false)}
                        >
                          <FiUpload className="h-4 w-4 mr-2" />
                          Upload Model
                        </Link>
                        <Link
                          to="/subscription"
                          className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-700"
                          onClick={() => setIsProfileMenuOpen(false)}
                        >
                          <FiCreditCard className="h-4 w-4 mr-2" />
                          Subscription
                        </Link>
                        {currentUser.role === 'admin' && (
                          <Link
                            to="/admin"
                            className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-700"
                            onClick={() => setIsProfileMenuOpen(false)}
                          >
                            <FiShoppingCart className="h-4 w-4 mr-2" />
                            Admin Dashboard
                          </Link>
                        )}
                        <div className="border-t border-gray-200 dark:border-gray-700 mt-1 pt-1">
                          <button
                            onClick={handleLogout}
                            className="flex w-full items-center px-4 py-2 text-sm text-red-600 hover:bg-red-50 dark:text-red-400 dark:hover:bg-red-900/20"
                          >
                            <FiLogOut className="h-4 w-4 mr-2" />
                            Sign out
                          </button>
                        </div>
                      </motion.div>
                    )}
                  </AnimatePresence>
                </>
              ) : (
                <div className="flex space-x-2 whitespace-nowrap">
                  <Button
                    to="/login"
                    variant="secondary"
                    size="sm"
                    leftIcon={<FiLogIn className="h-4 w-4" />}
                  >
                    Login
                  </Button>
                  <Button
                    to="/register"
                    variant="primary"
                    size="sm"
                    leftIcon={<FiUserPlus className="h-4 w-4" />}
                  >
                    Register
                  </Button>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Search overlay */}
        <AnimatePresence>
          {isSearchOpen && (
            <motion.div
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.2 }}
              className="absolute top-full left-0 right-0 bg-white dark:bg-gray-800 shadow-xl p-4 border-t border-gray-200 dark:border-gray-700"
              style={{ zIndex: 10000001 }}
            >
              <form onSubmit={handleSearch} className="flex gap-2">
                <Input
                  type="text"
                  value={searchQuery}
                  onChange={(e) => {
                    setSearchQuery(e.target.value);
                    setSearchError(null);
                  }}
                  placeholder="Search for models, scenes, objects..."
                  className="flex-1"
                  rightIcon={isSearching ?
                    <div className="animate-spin h-4 w-4 border-2 border-blue-600 border-t-transparent rounded-full" /> :
                    <FiSearch className="h-4 w-4 text-gray-400" />
                  }
                  autoFocus
                  disabled={isSearching}
                />
                <Button
                  type="submit"
                  variant="primary"
                  size="md"
                  disabled={isSearching || !searchQuery.trim()}
                >
                  {isSearching ? 'Searching...' : 'Search'}
                </Button>
                <Button
                  type="button"
                  variant="secondary"
                  size="md"
                  onClick={() => setIsSearchOpen(false)}
                  disabled={isSearching}
                >
                  Cancel
                </Button>
              </form>
              {searchError && (
                <div className="mt-2 text-sm text-red-600 dark:text-red-400">
                  {searchError}
                </div>
              )}
              <div className="mt-3 flex flex-wrap gap-2 items-center">
                <span className="text-xs text-gray-500 dark:text-gray-400">Popular:</span>
                <Badge
                  variant="primary"
                  pill
                  onClick={() => {
                    setSearchQuery('modern interior');
                    handleSearch({ preventDefault: () => {} });
                  }}
                  className="cursor-pointer transition-colors"
                >
                  Modern Interior
                </Badge>
                <Badge
                  variant="accent"
                  pill
                  onClick={() => {
                    setSearchQuery('kitchen');
                    handleSearch({ preventDefault: () => {} });
                  }}
                  className="cursor-pointer transition-colors"
                >
                  Kitchen
                </Badge>
                <Badge
                  variant="secondary"
                  pill
                  onClick={() => {
                    setSearchQuery('office');
                    handleSearch({ preventDefault: () => {} });
                  }}
                  className="cursor-pointer transition-colors"
                >
                  Office
                </Badge>
                <div className="flex space-x-2 ml-auto">
                  <button
                    onClick={() => setShowImageSearch(true)}
                    className="flex items-center space-x-1 px-3 py-1 bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400 rounded-full text-xs hover:bg-blue-200 dark:hover:bg-blue-800 transition-colors"
                  >
                    <FiCamera className="h-3 w-3" />
                    <span>Visual Search</span>
                  </button>
                  {/* Advanced Search Button - DISABLED */}
                  {/* <button
                    onClick={() => setShowAdvancedSearch(true)}
                    className="flex items-center space-x-1 px-3 py-1 bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 rounded-full text-xs hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
                  >
                    <FiSliders className="h-3 w-3" />
                    <span>Advanced</span>
                  </button> */}
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Mobile navigation */}
        <AnimatePresence>
          {isMenuOpen && (
            <motion.nav
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              transition={{ duration: 0.3 }}
              className="mt-4 md:hidden bg-white dark:bg-gray-800 rounded-lg shadow-xl p-4 border border-gray-200 dark:border-gray-700"
              style={{ zIndex: 10000001 }}
            >
              <div className="flex flex-col space-y-3">
                {categories.map((category) => (
                  <Link
                    key={category.name}
                    to={category.href}
                    className="flex items-center text-gray-700 hover:text-blue-600 dark:text-gray-300 dark:hover:text-blue-400 transition-colors py-2"
                    onClick={() => setIsMenuOpen(false)}
                  >
                    {category.icon}
                    {category.name}
                  </Link>
                ))}

                <div className="pt-4 mt-2 border-t border-gray-200 dark:border-gray-700">
                  {currentUser ? (
                    <>
                      <Link
                        to="/profile"
                        className="flex items-center py-2 text-gray-700 hover:text-blue-600 dark:text-gray-300 dark:hover:text-blue-400"
                        onClick={() => setIsMenuOpen(false)}
                      >
                        <FiUser className="h-5 w-5 mr-2" />
                        Your Profile
                      </Link>
                      <Link
                        to="/saved-models"
                        className="flex items-center py-2 text-gray-700 hover:text-blue-600 dark:text-gray-300 dark:hover:text-blue-400"
                        onClick={() => setIsMenuOpen(false)}
                      >
                        <FiHeart className="h-5 w-5 mr-2" />
                        Saved Models
                      </Link>
                      <Link
                        to="/downloads"
                        className="flex items-center py-2 text-gray-700 hover:text-blue-600 dark:text-gray-300 dark:hover:text-blue-400"
                        onClick={() => setIsMenuOpen(false)}
                      >
                        <FiDownload className="h-5 w-5 mr-2" />
                        Downloads
                      </Link>
                      <Link
                        to="/upload"
                        className="flex items-center py-2 text-gray-700 hover:text-blue-600 dark:text-gray-300 dark:hover:text-blue-400"
                        onClick={() => setIsMenuOpen(false)}
                      >
                        <FiUpload className="h-5 w-5 mr-2" />
                        Upload Model
                      </Link>
                      <Link
                        to="/subscription"
                        className="flex items-center py-2 text-gray-700 hover:text-blue-600 dark:text-gray-300 dark:hover:text-blue-400"
                        onClick={() => setIsMenuOpen(false)}
                      >
                        <FiCreditCard className="h-5 w-5 mr-2" />
                        Subscription
                      </Link>
                      {currentUser.role === 'admin' && (
                        <Link
                          to="/admin"
                          className="flex items-center py-2 text-gray-700 hover:text-blue-600 dark:text-gray-300 dark:hover:text-blue-400"
                          onClick={() => setIsMenuOpen(false)}
                        >
                          <FiShoppingCart className="h-5 w-5 mr-2" />
                          Admin Dashboard
                        </Link>
                      )}
                      <button
                        onClick={() => {
                          handleLogout();
                          setIsMenuOpen(false);
                        }}
                        className="flex items-center py-2 text-red-600 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300 w-full"
                      >
                        <FiLogOut className="h-5 w-5 mr-2" />
                        Sign out
                      </button>
                    </>
                  ) : (
                    <div className="flex flex-col space-y-3 pt-2">
                      <Button
                        to="/login"
                        variant="secondary"
                        fullWidth
                        leftIcon={<FiLogIn className="h-5 w-5" />}
                        onClick={() => setIsMenuOpen(false)}
                      >
                        Login
                      </Button>
                      <Button
                        to="/register"
                        variant="primary"
                        fullWidth
                        leftIcon={<FiUserPlus className="h-5 w-5" />}
                        onClick={() => setIsMenuOpen(false)}
                      >
                        Register
                      </Button>
                    </div>
                  )}
                </div>
              </div>
            </motion.nav>
          )}
        </AnimatePresence>
      </div>

      {/* Image Search Modal */}
      <ImageSearch
        isOpen={showImageSearch}
        onClose={() => setShowImageSearch(false)}
      />

      {/* Advanced Search Modal - DISABLED */}
      {/* <AdvancedSearch
        isOpen={showAdvancedSearch}
        onClose={() => setShowAdvancedSearch(false)}
        onSearch={handleAdvancedSearch}
      /> */}

      {/* Advanced Search Filters Modal - DISABLED */}
      {/* <AdvancedSearchFilters
        isVisible={showAdvancedFilters}
        onClose={() => setShowAdvancedFilters(false)}
        onApplyFilters={(filters) => {
          const queryParams = new URLSearchParams();
          Object.entries(filters).forEach(([key, value]) => {
            if (value && value !== '' && value !== 0 && value !== null) {
              if (typeof value === 'object' && !Array.isArray(value)) {
                Object.entries(value).forEach(([subKey, subValue]) => {
                  if (subValue && subValue !== '' && subValue !== 0) {
                    queryParams.append(`${key}_${subKey}`, subValue);
                  }
                });
              } else if (Array.isArray(value) && value.length > 0) {
                queryParams.append(key, value.join(','));
              } else {
                queryParams.append(key, value);
              }
            }
          });
          navigate(`/search?${queryParams.toString()}`);
        }}
      /> */}

      {/* Model Comparison Modal */}
      {showComparison && (
        <ModelComparison
          models={comparisonModels}
          onClose={() => setShowComparison(false)}
          onRemoveModel={(modelId) => {
            setComparisonModels(prev => prev.filter(model => model.id !== modelId));
          }}
        />
      )}

      {/* Search Suggestions */}
      <SearchSuggestions
        query={searchQuery}
        isVisible={showSuggestions && isSearchOpen}
        onSuggestionClick={(suggestion) => {
          setSearchQuery(suggestion);
          navigate(`/search?q=${encodeURIComponent(suggestion)}`);
          setIsSearchOpen(false);
          setShowSuggestions(false);
        }}
        onClose={() => setShowSuggestions(false)}
      />
    </header>
    </>
  );
};

export default Header;
