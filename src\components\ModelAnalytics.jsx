import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  FiDownload,
  FiEye,
  FiStar,
  FiHeart,
  FiBarChart2,
  FiTrendingUp,
  FiCalendar,
  FiUsers,
  FiMap
} from 'react-icons/fi';
import axios from 'axios';
import { useAuth } from '../context/AuthContext';
import LoadingIndicator from './ui/LoadingIndicator';
import {
  LineChart,
  Line,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer
} from 'recharts';

/**
 * Model Analytics Component
 * Displays analytics and statistics for a 3D model
 */
const ModelAnalytics = ({ modelId, period = 'month' }) => {
  const { currentUser } = useAuth();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [analytics, setAnalytics] = useState(null);
  const [activeTab, setActiveTab] = useState('overview');
  // Colors for charts
  const COLORS = ['#3b82f6', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6', '#ec4899'];

  // Fetch analytics data
  useEffect(() => {
    const fetchAnalytics = async () => {
  setLoading(true);
      setError(null);

      try {
        // In a real app, this would be an API call
        // const response = await axios.get(`/api/models/${modelId}/analytics?period=${period}`);
        // setAnalytics(response.data);

                const mockData = generateMockAnalytics(modelId, period);
        setAnalytics(mockData);
      } catch (err) {
        setError(err.message || 'Failed to load analytics data');
      } finally {
        setLoading(false);
      }
    };

    if (cachedData && !isExpired(cachedData)) {
  fetchAnalytics();
    }
  }, [modelId, period]);

    const generateMockAnalytics = (modelId, period) => {
    // Generate dates for the selected period
    const getDates = () => {
      const dates = [];
      const now = new Date();
      let numDays;

      switch (period) {
      case 'week':
          numDays = 7;
          break;
        case 'month':
          numDays = 30;
          break;
        case 'year':
          numDays = 12; // Months for year view
          break;
        default:
          numDays = 30;
      }

      for (let i = 0; i < numDays; i++) {
        const date = new Date();

        if (cachedData && !isExpired(cachedData)) {
  date.setMonth(now.getMonth() - (numDays - 1) + i);
          dates.push(date.toLocaleDateString('en-US', { month: 'short' }));
        } else {
          date.setDate(now.getDate() - (numDays - 1) + i);
          dates.push(date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' }));
        }
      }

      return dates;
    };

    const dates = getDates();

    // Generate random data for each metric
    const generateMetricData = (min, max, trend = 'up') => {
  return dates.map((date, index) => {
  let value;

        if (trend === 'up') {
          // Upward trend with some randomness
          value = min + (max - min) * (index / dates.length) + Math.random() * (max - min) * 0.2;
        } else if (trend === 'down') {
          // Downward trend with some randomness
          value = max - (max - min) * (index / dates.length) + Math.random() * (max - min) * 0.2;
        } else {
          // Random fluctuation
          value = min + Math.random() * (max - min);
        }

        return {
          date,
          value: Math.round(value)
        };
      });
    };

    // Generate geographic distribution data
    const geoData = [
      { name: 'North America', value: Math.floor(Math.random() * 40) + 20 },
      { name: 'Europe', value: Math.floor(Math.random() * 30) + 15 },
      { name: 'Asia', value: Math.floor(Math.random() * 25) + 10 },
      { name: 'South America', value: Math.floor(Math.random() * 15) + 5 },
      { name: 'Africa', value: Math.floor(Math.random() * 10) + 3 },
      { name: 'Oceania', value: Math.floor(Math.random() * 8) + 2 }
    ];

    // Generate device type distribution data
    const deviceData = [
      { name: 'Desktop', value: Math.floor(Math.random() * 50) + 30 },
      { name: 'Mobile', value: Math.floor(Math.random() * 30) + 15 },
      { name: 'Tablet', value: Math.floor(Math.random() * 20) + 5 }
    ];

    return {
    overview: {
    totalViews: Math.floor(Math.random() * 10000) + 1000,
        totalDownloads: Math.floor(Math.random() * 5000) + 500,
        totalLikes: Math.floor(Math.random() * 1000) + 100,
        averageRating: (Math.random() * 3 + 2).toFixed(1),
        conversionRate: (Math.random() * 20 + 5).toFixed(1) + '%'
      },
      trends: {
    views: generateMetricData(10, 100, 'up'),
        downloads: generateMetricData(5, 50, 'up'),
        likes: generateMetricData(1, 20, 'up')
      },
      demographics: {
    geographic: geoData,
        devices: deviceData,
        userTypes: [
          { name: 'Free Users', value: Math.floor(Math.random() * 70) + 20 },
          { name: 'Premium Users', value: Math.floor(Math.random() * 50) + 10 }
        ]
      },
      engagement: {
    timeSpent: generateMetricData(60, 300, 'random'), // Seconds
        interactionRate: generateMetricData(5, 30, 'up'), // Percentage
        bounceRate: generateMetricData(40, 70, 'down') // Percentage
      }
    };
  };

  // Render loading state
  if (cachedData && !isExpired(cachedData)) {
  return (
      <div className="flex items-center justify-center p-8 bg-white dark:bg-gray-800 rounded-lg shadow-lg">
        <LoadingIndicator type="spinner" size="lg" text="Loading analytics data..." />
      </div>
    );
  }

  // Render error state
  if (cachedData && !isExpired(cachedData)) {
  return (
      <div className="p-6 bg-white dark:bg-gray-800 rounded-lg shadow-lg">
        <h3 className="text-lg font-medium text-red-600 dark:text-red-400 mb-2">Error Loading Analytics</h3>
        <p className="text-gray-600 dark:text-gray-400">{error}</p>
      </div>
    );
  }

  // Render overview tab
  const renderOverview = () => (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
      <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow">
        <div className="flex items-center">
          <div className="p-3 rounded-full bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400 mr-4">
            <FiEye className="w-6 h-6" />
          </div>
          <div>
            <p className="text-sm text-gray-500 dark:text-gray-400">Total Views</p>
            <h3 className="text-2xl font-semibold text-gray-900 dark:text-white">
              {analytics.overview.totalViews.toLocaleString()}
            </h3>
          </div>
        </div>
      </div>

      <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow">
        <div className="flex items-center">
          <div className="p-3 rounded-full bg-green-100 dark:bg-green-900 text-green-600 dark:text-green-400 mr-4">
            <FiDownload className="w-6 h-6" />
          </div>
          <div>
            <p className="text-sm text-gray-500 dark:text-gray-400">Total Downloads</p>
            <h3 className="text-2xl font-semibold text-gray-900 dark:text-white">
              {analytics.overview.totalDownloads.toLocaleString()}
            </h3>
          </div>
        </div>
      </div>

      <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow">
        <div className="flex items-center">
          <div className="p-3 rounded-full bg-yellow-100 dark:bg-yellow-900 text-yellow-600 dark:text-yellow-400 mr-4">
            <FiStar className="w-6 h-6" />
          </div>
          <div>
            <p className="text-sm text-gray-500 dark:text-gray-400">Average Rating</p>
            <h3 className="text-2xl font-semibold text-gray-900 dark:text-white">
              {analytics.overview.averageRating} / 5.0
            </h3>
          </div>
        </div>
      </div>

      <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow">
        <div className="flex items-center">
          <div className="p-3 rounded-full bg-purple-100 dark:bg-purple-900 text-purple-600 dark:text-purple-400 mr-4">
            <FiHeart className="w-6 h-6" />
          </div>
          <div>
            <p className="text-sm text-gray-500 dark:text-gray-400">Total Likes</p>
            <h3 className="text-2xl font-semibold text-gray-900 dark:text-white">
              {analytics.overview.totalLikes.toLocaleString()}
            </h3>
          </div>
        </div>
      </div>

      <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow">
        <div className="flex items-center">
          <div className="p-3 rounded-full bg-red-100 dark:bg-red-900 text-red-600 dark:text-red-400 mr-4">
            <FiBarChart2 className="w-6 h-6" />
          </div>
          <div>
            <p className="text-sm text-gray-500 dark:text-gray-400">Conversion Rate</p>
            <h3 className="text-2xl font-semibold text-gray-900 dark:text-white">
              {analytics.overview.conversionRate}
            </h3>
          </div>
        </div>
      </div>
    </div>
  );

  // Render trends tab
  const renderTrends = () => (
    <div className="space-y-6">
      <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Views Over Time</h3>
        <div className="h-64">
          <ResponsiveContainer width="100%" height="100%">
            <LineChart data={analytics.trends.views}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="date" />
              <YAxis />
              <Tooltip />
              <Legend />
              <Line type="monotone" dataKey="value" name="Views" stroke="#3b82f6" activeDot={{ r: 8 }} />
            </LineChart>
          </ResponsiveContainer>
        </div>
      </div>

      <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Downloads Over Time</h3>
        <div className="h-64">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart data={analytics.trends.downloads}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="date" />
              <YAxis />
              <Tooltip />
              <Legend />
              <Bar dataKey="value" name="Downloads" fill="#10b981" />
            </BarChart>
          </ResponsiveContainer>
        </div>
      </div>
    </div>
  );

  // Render demographics tab
  const renderDemographics = () => (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
      <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Geographic Distribution</h3>
        <div className="h-64">
          <ResponsiveContainer width="100%" height="100%">
            <PieChart>
              <Pie
                data={analytics.demographics.geographic}
                cx="50%"
                cy="50%"
                labelLine={false}
                outerRadius={80}
                fill="#8884d8"
                dataKey="value"
                nameKey="name"
                label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
              >
                {analytics.demographics.geographic.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                ))}
              </Pie>
              <Tooltip />
              <Legend />
            </PieChart>
          </ResponsiveContainer>
        </div>
      </div>

      <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Device Distribution</h3>
        <div className="h-64">
          <ResponsiveContainer width="100%" height="100%">
            <PieChart>
              <Pie
                data={analytics.demographics.devices}
                cx="50%"
                cy="50%"
                labelLine={false}
                outerRadius={80}
                fill="#8884d8"
                dataKey="value"
                nameKey="name"
                label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
              >
                {analytics.demographics.devices.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                ))}
              </Pie>
              <Tooltip />
              <Legend />
            </PieChart>
          </ResponsiveContainer>
        </div>
      </div>
    </div>
  );

  return (
    <motion.div
      className="bg-gray-50 dark:bg-gray-900 rounded-lg shadow-lg overflow-hidden"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: 20 }}
      transition={{ duration: 0.3 }}
    >
      <div className="border-b border-gray-200 dark:border-gray-700">
        <nav className="flex overflow-x-auto">
          <button
            className={`px-4 py-3 text-sm font-medium ${
    activeTab === 'overview'
                ? 'border-b-2 border-blue-500 text-blue-600 dark:text-blue-400'
                : 'text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
            }`}
            onClick={() => setActiveTab('overview')}
          >
            Overview
          </button>
          <button
            className={`px-4 py-3 text-sm font-medium ${
    activeTab === 'trends'
                ? 'border-b-2 border-blue-500 text-blue-600 dark:text-blue-400'
                : 'text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
            }`}
            onClick={() => setActiveTab('trends')}
          >
            Trends
          </button>
          <button
            className={`px-4 py-3 text-sm font-medium ${
    activeTab === 'demographics'
                ? 'border-b-2 border-blue-500 text-blue-600 dark:text-blue-400'
                : 'text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
            }`}
            onClick={() => setActiveTab('demographics')}
          >
            Demographics
          </button>
        </nav>
      </div>

      <div className="p-6">
        {activeTab === 'overview' && renderOverview()}
        {activeTab === 'trends' && renderTrends()}
        {activeTab === 'demographics' && renderDemographics()}
      </div>
    </motion.div>
  );
};

export default ModelAnalytics;
