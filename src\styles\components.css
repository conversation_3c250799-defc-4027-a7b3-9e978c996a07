/* Enhanced <PERSON><PERSON> Styles */
.btn {
  @apply inline-flex items-center justify-center px-4 py-2 rounded-lg font-medium transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-offset-2 transform hover:scale-105 active:scale-95;
}

.btn-xs {
  @apply px-2 py-1 text-xs rounded;
}

.btn-sm {
  @apply px-3 py-1.5 text-sm rounded;
}

.btn-lg {
  @apply px-6 py-3 text-lg rounded-lg;
}

.btn-xl {
  @apply px-8 py-4 text-xl rounded-xl;
}

.btn-primary {
  @apply bg-gradient-to-r from-primary-600 to-primary-700 hover:from-primary-700 hover:to-primary-800 text-white focus:ring-primary-500 shadow-lg hover:shadow-xl;
}

.btn-secondary {
  @apply bg-white hover:bg-gray-50 text-gray-700 border border-gray-300 hover:border-gray-400 focus:ring-primary-500 shadow-md hover:shadow-lg dark:bg-gray-800 dark:hover:bg-gray-700 dark:text-white dark:border-gray-600 dark:hover:border-gray-500;
}

.btn-accent {
  @apply bg-gradient-to-r from-accent-500 to-accent-600 hover:from-accent-600 hover:to-accent-700 text-white focus:ring-accent-400 shadow-lg hover:shadow-xl;
}

.btn-gradient {
  @apply bg-gradient-to-r from-primary-600 via-purple-600 to-accent-600 hover:from-primary-700 hover:via-purple-700 hover:to-accent-700 text-white focus:ring-primary-500 shadow-lg hover:shadow-xl;
}

.btn-glass {
  @apply bg-white/10 backdrop-blur-md border border-white/20 text-white hover:bg-white/20 focus:ring-white/50 shadow-lg hover:shadow-xl;
}

.btn-success {
  @apply bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white focus:ring-green-400 shadow-lg hover:shadow-xl;
}

.btn-warning {
  @apply bg-gradient-to-r from-yellow-500 to-orange-500 hover:from-yellow-600 hover:to-orange-600 text-white focus:ring-yellow-400 shadow-lg hover:shadow-xl;
}

.btn-error {
  @apply bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white focus:ring-red-400 shadow-lg hover:shadow-xl;
}

.btn-outline {
  @apply border-2 bg-transparent;
}

.btn-outline-primary {
  @apply border-primary-500 text-primary-600 hover:bg-primary-50 hover:text-primary-700 dark:text-primary-400 dark:hover:bg-primary-900/20 dark:hover:text-primary-300;
}

.btn-outline-secondary {
  @apply border-secondary-300 text-secondary-700 hover:bg-secondary-50 hover:text-secondary-800 dark:border-secondary-600 dark:text-secondary-300 dark:hover:bg-secondary-800 dark:hover:text-secondary-200;
}

.btn-outline-accent {
  @apply border-accent-500 text-accent-600 hover:bg-accent-50 hover:text-accent-700 dark:text-accent-400 dark:hover:bg-accent-900/20 dark:hover:text-accent-300;
}

.btn-ghost {
  @apply bg-transparent hover:bg-gray-100 text-gray-700 dark:text-gray-300 dark:hover:bg-gray-800;
}

.btn-link {
  @apply p-0 bg-transparent text-primary-600 hover:text-primary-700 hover:underline dark:text-primary-400 dark:hover:text-primary-300;
}

.btn-disabled, .btn[disabled] {
  @apply opacity-50 cursor-not-allowed pointer-events-none;
}

/* Enhanced Card Styles */
.card {
  @apply bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden transition-all duration-300 hover:shadow-xl hover:-translate-y-1 border border-gray-100 dark:border-gray-700;
}

.card-glass {
  @apply bg-white/10 dark:bg-gray-800/10 backdrop-blur-md rounded-xl shadow-lg overflow-hidden transition-all duration-300 hover:shadow-xl hover:-translate-y-1 border border-white/20 dark:border-gray-700/20;
}

.card-gradient {
  @apply bg-gradient-to-br from-white to-gray-50 dark:from-gray-800 dark:to-gray-900 rounded-xl shadow-lg overflow-hidden transition-all duration-300 hover:shadow-xl hover:-translate-y-1 border border-gray-100 dark:border-gray-700;
}

.card-header {
  @apply px-6 py-4 border-b border-gray-200 dark:border-gray-700 bg-gray-50/50 dark:bg-gray-700/50;
}

.card-body {
  @apply p-6;
}

.card-footer {
  @apply px-6 py-4 border-t border-gray-200 dark:border-gray-700 bg-gray-50/50 dark:bg-gray-700/50;
}

/* Form Styles */
.input {
  @apply block w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-800 dark:border-gray-700 dark:text-white;
}

.input-sm {
  @apply px-2 py-1 text-sm rounded;
}

.input-lg {
  @apply px-4 py-3 text-lg rounded-lg;
}

.input-error {
  @apply border-error-500 focus:ring-error-500 focus:border-error-500;
}

.label {
  @apply block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1;
}

.form-group {
  @apply mb-4;
}

.form-error {
  @apply mt-1 text-sm text-error-600 dark:text-error-400;
}

.form-helper {
  @apply mt-1 text-sm text-gray-500 dark:text-gray-400;
}

/* Badge Styles */
.badge {
  @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
}

.badge-primary {
  @apply bg-primary-100 text-primary-800 dark:bg-primary-900 dark:text-primary-300;
}

.badge-secondary {
  @apply bg-secondary-100 text-secondary-800 dark:bg-secondary-900 dark:text-secondary-300;
}

.badge-accent {
  @apply bg-accent-100 text-accent-800 dark:bg-accent-900 dark:text-accent-300;
}

.badge-success {
  @apply bg-success-100 text-success-800 dark:bg-success-900 dark:text-success-300;
}

.badge-warning {
  @apply bg-warning-100 text-warning-800 dark:bg-warning-900 dark:text-warning-300;
}

.badge-error {
  @apply bg-error-100 text-error-800 dark:bg-error-900 dark:text-error-300;
}

/* Alert Styles */
.alert {
  @apply p-4 rounded-lg mb-4;
}

.alert-primary {
  @apply bg-primary-50 text-primary-800 dark:bg-primary-900/50 dark:text-primary-300;
}

.alert-secondary {
  @apply bg-secondary-50 text-secondary-800 dark:bg-secondary-900/50 dark:text-secondary-300;
}

.alert-accent {
  @apply bg-accent-50 text-accent-800 dark:bg-accent-900/50 dark:text-accent-300;
}

.alert-success {
  @apply bg-success-50 text-success-800 dark:bg-success-900/50 dark:text-success-300;
}

.alert-warning {
  @apply bg-warning-50 text-warning-800 dark:bg-warning-900/50 dark:text-warning-300;
}

.alert-error {
  @apply bg-error-50 text-error-800 dark:bg-error-900/50 dark:text-error-300;
}

/* Typography */
.heading-1 {
  @apply font-heading text-4xl sm:text-5xl font-bold text-gray-900 dark:text-white;
}

.heading-2 {
  @apply font-heading text-3xl sm:text-4xl font-bold text-gray-900 dark:text-white;
}

.heading-3 {
  @apply font-heading text-2xl sm:text-3xl font-bold text-gray-900 dark:text-white;
}

.heading-4 {
  @apply font-heading text-xl sm:text-2xl font-bold text-gray-900 dark:text-white;
}

.heading-5 {
  @apply font-heading text-lg sm:text-xl font-bold text-gray-900 dark:text-white;
}

.heading-6 {
  @apply font-heading text-base sm:text-lg font-bold text-gray-900 dark:text-white;
}

.text-body {
  @apply text-base text-gray-700 dark:text-gray-300;
}

.text-body-sm {
  @apply text-sm text-gray-700 dark:text-gray-300;
}

.text-body-lg {
  @apply text-lg text-gray-700 dark:text-gray-300;
}

/* Container with responsive padding */
.container-padded {
  @apply container mx-auto px-4 sm:px-6 lg:px-8;
}

/* Section spacing */
.section {
  @apply py-12 md:py-16 lg:py-20;
}

.section-sm {
  @apply py-8 md:py-10 lg:py-12;
}

/* Header spacing utilities */
.header-offset {
  @apply pt-20 md:pt-24;
}

.header-offset-sm {
  @apply pt-16 md:pt-20;
}

.section-lg {
  @apply py-16 md:py-20 lg:py-24;
}

/* Animations moved to animations.css */

/* Transitions */
.transition-fast {
  @apply transition-all duration-150 ease-in-out;
}

.transition-normal {
  @apply transition-all duration-300 ease-in-out;
}

.transition-slow {
  @apply transition-all duration-500 ease-in-out;
}

.transition-bounce {
  @apply transition-all duration-300 ease-bounce;
}

/* Hover effects */
.hover-lift {
  @apply transition-transform duration-300 hover:-translate-y-1;
}

.hover-scale {
  @apply transition-transform duration-300 hover:scale-105;
}

.hover-glow {
  @apply transition-shadow duration-300 hover:shadow-glow;
}

/* Focus styles */
.focus-ring {
  @apply focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 dark:focus:ring-offset-gray-900;
}

/* Dividers */
.divider {
  @apply h-px w-full bg-gray-200 dark:bg-gray-700 my-6;
}

.divider-vertical {
  @apply w-px h-full bg-gray-200 dark:bg-gray-700 mx-4;
}

/* New Enhanced Utilities */
.glass-effect {
  @apply bg-white/10 backdrop-blur-md border border-white/20;
}

.gradient-text {
  @apply bg-gradient-to-r from-primary-600 to-accent-600 bg-clip-text text-transparent;
}

.gradient-border {
  @apply bg-gradient-to-r from-primary-600 to-accent-600 p-0.5 rounded-lg;
}

.floating-animation {
  animation: float 3s ease-in-out infinite;
}

.pulse-glow {
  animation: pulse-glow 2s ease-in-out infinite alternate;
}

.shimmer {
  @apply relative overflow-hidden;
}

.shimmer::before {
  content: '';
  @apply absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent;
  animation: shimmer 2s infinite;
}

/* Hero Section Enhancements */
.hero-gradient {
  background: linear-gradient(135deg, var(--primary-600) 0%, var(--primary-800) 50%, var(--accent-600) 100%);
}

.hero-overlay {
  @apply absolute inset-0 bg-black/20;
}

/* Interactive Elements */
.interactive-card {
  @apply transition-all duration-300 hover:scale-105 hover:shadow-2xl cursor-pointer;
}

.interactive-button {
  @apply transition-all duration-200 hover:scale-105 active:scale-95 focus:scale-105;
}
