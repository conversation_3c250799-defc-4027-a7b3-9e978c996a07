#!/usr/bin/env node

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Get the src directory
const srcDir = path.join(__dirname, '..', 'src');

// Console log patterns to remove
const consolePatterns = [
  /console\.log\([^)]*\);?\s*\n?/g,
  /console\.warn\([^)]*\);?\s*\n?/g,
  /console\.error\([^)]*\);?\s*\n?/g,
  /console\.info\([^)]*\);?\s*\n?/g,
  /console\.debug\([^)]*\);?\s*\n?/g,
  // Multi-line console logs
  /console\.(log|warn|error|info|debug)\(\s*[^)]*\s*\);?\s*\n?/gm
];

// Files to exclude from console log removal
const excludeFiles = [
  'errorHandling.js',
  'logger.js',
  'debug.js'
];

// Function to process a file
function processFile(filePath) {
  const fileName = path.basename(filePath);
  
  // Skip excluded files
  if (excludeFiles.some(exclude => fileName.includes(exclude))) {
    return false;
  }

  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let originalContent = content;
    let changes = 0;

    // Remove console logs
    consolePatterns.forEach(pattern => {
      const matches = content.match(pattern);
      if (matches) {
        changes += matches.length;
        content = content.replace(pattern, '');
      }
    });

    // Clean up empty lines (max 2 consecutive empty lines)
    content = content.replace(/\n\s*\n\s*\n+/g, '\n\n');

    // Only write if there were changes
    if (content !== originalContent) {
      fs.writeFileSync(filePath, content, 'utf8');
      return changes;
    }

    return 0;
  } catch (error) {
    console.error(`Error processing ${filePath}:`, error.message);
    return 0;
  }
}

// Function to recursively process directory
function processDirectory(dirPath) {
  let totalChanges = 0;
  let filesProcessed = 0;

  try {
    const items = fs.readdirSync(dirPath);

    for (const item of items) {
      const itemPath = path.join(dirPath, item);
      const stat = fs.statSync(itemPath);

      if (stat.isDirectory()) {
        // Skip node_modules and other build directories
        if (!['node_modules', '.git', 'dist', 'build'].includes(item)) {
          const dirChanges = processDirectory(itemPath);
          totalChanges += dirChanges.changes;
          filesProcessed += dirChanges.files;
        }
      } else if (stat.isFile()) {
        // Process JS, JSX, TS, TSX files
        if (/\.(js|jsx|ts|tsx)$/.test(item)) {
          const changes = processFile(itemPath);
          if (changes > 0) {
            console.log(`✅ Removed ${changes} console logs from ${path.relative(srcDir, itemPath)}`);
            totalChanges += changes;
          }
          filesProcessed++;
        }
      }
    }
  } catch (error) {
    console.error(`Error processing directory ${dirPath}:`, error.message);
  }

  return { changes: totalChanges, files: filesProcessed };
}

// Main execution
console.log('🧹 Removing console logs from source files...\n');

const result = processDirectory(srcDir);

console.log('\n📊 Summary:');
console.log(`Files processed: ${result.files}`);
console.log(`Console logs removed: ${result.changes}`);

if (result.changes > 0) {
  console.log('\n🎉 Console logs cleanup completed!');
  console.log('💡 Your website should now run faster without debug logs.');
} else {
  console.log('\n✨ No console logs found to remove.');
}

// Also create a production config
const prodConfigContent = `// Production configuration
export const isProd = process.env.NODE_ENV === 'production';

// Disable console logs in production
if (isProd) {
  console.log = () => {};
  console.warn = () => {};
  console.info = () => {};
  console.debug = () => {};
  // Keep console.error for critical errors
}

export default {
  isProd,
  enableLogs: !isProd,
  enableDebug: !isProd
};`;

const configPath = path.join(srcDir, 'config', 'production.js');
const configDir = path.dirname(configPath);

// Create config directory if it doesn't exist
if (!fs.existsSync(configDir)) {
  fs.mkdirSync(configDir, { recursive: true });
}

fs.writeFileSync(configPath, prodConfigContent, 'utf8');
console.log(`\n📝 Created production config at ${path.relative(srcDir, configPath)}`);

export default { processDirectory, processFile };
