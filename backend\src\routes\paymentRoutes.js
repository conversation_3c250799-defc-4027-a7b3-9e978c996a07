import express from 'express';
import {
  createPaymentIntent,
  confirmPayment,
  getPaymentHistory,
  getPaymentDetails,
  createSubscription,
  cancelSubscription,
  updateSubscription,
  getSubscriptionDetails,
  handleStripeWebhook,
  getInvoices,
  getPaymentMethods,
  addPaymentMethod,
  removePaymentMethod,
  getCurrentSubscription,
  getPaymentApiStatus
} from '../controllers/paymentController.js';
import { protect, authorize } from '../middleware/auth.js';

const router = express.Router();

// Payment routes
router.route('/create-payment-intent')
  .post(protect, createPaymentIntent);

router.route('/confirm-payment')
  .post(protect, confirmPayment);

router.route('/history')
  .get(protect, getPaymentHistory);

// Subscription routes
router.route('/subscriptions')
  .post(protect, createSubscription);

// Current subscription - MUST be before /:id route
router.route('/subscriptions/user')
  .get(protect, getCurrentSubscription);

router.route('/subscriptions/:id')
  .get(protect, getSubscriptionDetails)
  .put(protect, updateSubscription)
  .delete(protect, cancelSubscription);

// Invoices routes
router.route('/user-invoices')
  .get(protect, getInvoices);

// Payment methods routes
router.route('/user-methods')
  .get(protect, getPaymentMethods)
  .post(protect, addPaymentMethod);

router.route('/user-methods/:id')
  .delete(protect, removePaymentMethod);

// Stripe webhook
router.route('/webhook')
  .post(handleStripeWebhook);

// Payment API status
router.route('/status')
  .get(getPaymentApiStatus);

// Generic payment details - MUST be last to avoid conflicts
router.route('/:id')
  .get(protect, getPaymentDetails);

export default router;
