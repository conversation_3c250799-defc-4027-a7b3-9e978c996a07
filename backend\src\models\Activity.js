import mongoose from 'mongoose';

const ActivitySchema = new mongoose.Schema({
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  type: {
    type: String,
    required: true,
    enum: [
      'download', 'upload', 'save', 'comment', 'like', 'view', 'purchase', 'login', 'register',
      'profile_update', 'password_update', 'subscription_update', 'subscription_create',
      'subscription_cancel', 'subscription_reactivate', 'security_update'
    ]
  },
  model: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Model'
  },
  review: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Review'
  },
  payment: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Payment'
  },
  subscription: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Subscription'
  },
  details: {
    type: Object
  },
  ipAddress: {
    type: String
  },
  userAgent: {
    type: String
  },
  createdAt: {
    type: Date,
    default: Date.now
  }
});

// Create index for faster queries
ActivitySchema.index({ user: 1, createdAt: -1 });
ActivitySchema.index({ type: 1, createdAt: -1 });

export default mongoose.model('Activity', ActivitySchema);
