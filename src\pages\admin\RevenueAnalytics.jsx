import React from 'react';
import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { toast } from 'react-hot-toast';
import {
  FiDollarSign, FiTrendingUp, FiTrendingDown, FiCreditCard,
  FiUsers, FiCalendar, FiBarChart, FiPieChart
} from 'react-icons/fi';
import { adminAPI } from '../../utils/api';

const RevenueAnalytics = () => {
  const [loading, setLoading] = useState(true);
  const [timeRange, setTimeRange] = useState('30d');
  const [analytics, setAnalytics] = useState({
    totalRevenue: 0,
    revenueGrowth: 0,
    monthlyRecurringRevenue: 0,
    averageRevenuePerUser: 0,
    revenueByPlan: [],
    revenueByMonth: [],
    topCustomers: [],
    conversionRate: 0,
    churnRate: 0
  });

  useEffect(() => {
    fetchAnalytics();
  }, [timeRange]);

  const fetchAnalytics = async () => {
    try {
      setLoading(true);

      // In a real app, you would fetch from payment/subscription endpoints
      // For now, we'll use mock data based on the payment data we created

      const mockAnalytics = {
        totalRevenue: 169.96,
        revenueGrowth: 15.3,
        monthlyRecurringRevenue: 89.97,
        averageRevenuePerUser: 42.49,
        revenueByPlan: [
          { plan: 'Basic', revenue: 9.99, users: 1, color: 'blue' },
          { plan: 'Premium', revenue: 59.98, users: 2, color: 'green' },
          { plan: 'Professional', revenue: 99.99, users: 1, color: 'purple' }
        ],
        revenueByMonth: generateMockRevenueData(timeRange),
        topCustomers: [
          { name: 'Designer Pro', email: '<EMAIL>', revenue: 99.99, plan: 'Professional' },
          { name: 'Admin User', revenue: 59.98, plan: 'Premium' },
          { name: 'John Smith', revenue: 9.99, plan: 'Basic' }
        ],
        conversionRate: 12.5,
        churnRate: 3.2
      };

      setAnalytics(mockAnalytics);
    } catch (error) {
      console.error('Error fetching revenue analytics:', error);
      toast.error('Failed to load revenue analytics');
    } finally {
      setLoading(false);
    }
  };

  const generateMockRevenueData = (range) => {
    const months = range === '7d' ? 1 : range === '30d' ? 6 : 12;
    const data = [];

    for (let i = months - 1; i >= 0; i--) {
      const date = new Date();
      date.setMonth(date.getMonth() - i);
      data.push({
        month: date.toLocaleDateString('en-US', { month: 'short', year: 'numeric' }),
        revenue: Math.floor(Math.random() * 500) + 100,
        subscriptions: Math.floor(Math.random() * 20) + 5
      });
    }

    return data;
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  const formatNumber = (num) => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M';
    } else if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-96">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Revenue Analytics</h1>
          <p className="text-gray-600 dark:text-gray-400">Track and analyze revenue performance</p>
        </div>
        <div className="flex items-center space-x-3">
          <select
            value={timeRange}
            onChange={(e) => setTimeRange(e.target.value)}
            className="px-3 py-2 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md text-gray-700 dark:text-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="7d">Last 7 days</option>
            <option value="30d">Last 30 days</option>
            <option value="90d">Last 90 days</option>
          </select>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {[
          {
            title: 'Total Revenue',
            value: formatCurrency(analytics.totalRevenue),
            change: analytics.revenueGrowth,
            icon: <FiDollarSign className="h-6 w-6" />,
            color: 'green'
          },
          {
            title: 'Monthly Recurring Revenue',
            value: formatCurrency(analytics.monthlyRecurringRevenue),
            change: 8.7,
            icon: <FiBarChart className="h-6 w-6" />,
            color: 'blue'
          },
          {
            title: 'Average Revenue Per User',
            value: formatCurrency(analytics.averageRevenuePerUser),
            change: 5.2,
            icon: <FiUsers className="h-6 w-6" />,
            color: 'purple'
          },
          {
            title: 'Conversion Rate',
            value: `${analytics.conversionRate}%`,
            change: 2.1,
            icon: <FiTrendingUp className="h-6 w-6" />,
            color: 'amber'
          }
        ].map((metric, index) => (
          <motion.div
            key={metric.title}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: index * 0.1 }}
            className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6"
          >
            <div className="flex justify-between items-start">
              <div>
                <p className="text-sm font-medium text-gray-500 dark:text-gray-400">{metric.title}</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white mt-1">{metric.value}</p>
              </div>
              <div className={`p-3 rounded-full bg-${metric.color}-100 dark:bg-${metric.color}-900/30 text-${metric.color}-600 dark:text-${metric.color}-400`}>
                {metric.icon}
              </div>
            </div>
            <div className="mt-4 flex items-center">
              {metric.change >= 0 ? (
                <FiTrendingUp className="text-green-500 mr-1" />
              ) : (
                <FiTrendingDown className="text-red-500 mr-1" />
              )}
              <span className={metric.change >= 0 ? 'text-green-500' : 'text-red-500'}>
                {Math.abs(metric.change)}%
              </span>
              <span className="text-gray-500 dark:text-gray-400 ml-1">vs last period</span>
            </div>
          </motion.div>
        ))}
      </div>

      {/* Charts Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Revenue Over Time */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.4 }}
          className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6"
        >
          <h2 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Revenue Over Time</h2>
          <div className="h-64 flex items-end space-x-2">
            {analytics.revenueByMonth.map((month, index) => (
              <div key={index} className="flex-1 flex flex-col items-center">
                <div
                  className="w-full bg-green-500 dark:bg-green-600 rounded-t-sm"
                  style={{
                    height: `${(month.revenue / Math.max(...analytics.revenueByMonth.map(m => m.revenue))) * 100}%`,
                    minHeight: '8px'
                  }}
                ></div>
                <div className="text-xs text-gray-500 dark:text-gray-400 mt-2 transform -rotate-45 origin-top-left">
                  {month.month}
                </div>
              </div>
            ))}
          </div>
        </motion.div>

        {/* Revenue by Plan */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.5 }}
          className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6"
        >
          <h2 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Revenue by Plan</h2>
          <div className="space-y-4">
            {analytics.revenueByPlan.map((plan, index) => {
              const percentage = (plan.revenue / analytics.totalRevenue) * 100;
              return (
                <div key={plan.plan} className="space-y-2">
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium text-gray-700 dark:text-gray-300">{plan.plan}</span>
                    <span className="text-sm text-gray-900 dark:text-white">{formatCurrency(plan.revenue)}</span>
                  </div>
                  <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                    <div
                      className={`bg-${plan.color}-500 dark:bg-${plan.color}-600 h-2 rounded-full`}
                      style={{ width: `${percentage}%` }}
                    ></div>
                  </div>
                  <div className="flex justify-between text-xs text-gray-500 dark:text-gray-400">
                    <span>{plan.users} users</span>
                    <span>{percentage.toFixed(1)}%</span>
                  </div>
                </div>
              );
            })}
          </div>
        </motion.div>
      </div>

      {/* Top Customers */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3, delay: 0.6 }}
        className="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden"
      >
        <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
          <h2 className="text-lg font-medium text-gray-900 dark:text-white">Top Customers</h2>
        </div>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead className="bg-gray-50 dark:bg-gray-700">
              <tr>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Customer
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Plan
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Revenue
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Status
                </th>
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
              {analytics.topCustomers.map((customer, index) => (
                <tr key={index} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="flex-shrink-0 h-10 w-10">
                        <div className="h-10 w-10 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center">
                          <span className="text-sm font-medium text-blue-600 dark:text-blue-400">
                            {customer.name.charAt(0)}
                          </span>
                        </div>
                      </div>
                      <div className="ml-4">
                        <div className="text-sm font-medium text-gray-900 dark:text-white">
                          {customer.name}
                        </div>
                        <div className="text-sm text-gray-500 dark:text-gray-400">
                          {customer.email}
                        </div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                      customer.plan === 'Professional' ? 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300' :
                      customer.plan === 'Premium' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300' :
                      'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300'
                    }`}>
                      {customer.plan}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                    {formatCurrency(customer.revenue)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300">
                      Active
                    </span>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </motion.div>

      {/* Additional Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.7 }}
          className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6"
        >
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Key Performance Indicators</h3>
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <span className="text-gray-600 dark:text-gray-400">Customer Lifetime Value</span>
              <span className="font-semibold text-gray-900 dark:text-white">{formatCurrency(127.47)}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-600 dark:text-gray-400">Churn Rate</span>
              <span className="font-semibold text-red-600 dark:text-red-400">{analytics.churnRate}%</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-600 dark:text-gray-400">Customer Acquisition Cost</span>
              <span className="font-semibold text-gray-900 dark:text-white">{formatCurrency(23.50)}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-600 dark:text-gray-400">Revenue Growth Rate</span>
              <span className="font-semibold text-green-600 dark:text-green-400">+{analytics.revenueGrowth}%</span>
            </div>
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: 0.8 }}
          className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6"
        >
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Revenue Forecast</h3>
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <span className="text-gray-600 dark:text-gray-400">Next Month Projection</span>
              <span className="font-semibold text-gray-900 dark:text-white">{formatCurrency(195.45)}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-600 dark:text-gray-400">Quarterly Target</span>
              <span className="font-semibold text-gray-900 dark:text-white">{formatCurrency(600.00)}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-600 dark:text-gray-400">Annual Target</span>
              <span className="font-semibold text-gray-900 dark:text-white">{formatCurrency(2400.00)}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-600 dark:text-gray-400">Target Achievement</span>
              <span className="font-semibold text-green-600 dark:text-green-400">7.1%</span>
            </div>
          </div>
        </motion.div>
      </div>
    </div>
  );
};

export default RevenueAnalytics;
