import React, { useState, useCallback } from 'react';
import PropTypes from 'prop-types';
import { FiSend, FiX } from 'react-icons/fi';
import { useAuth } from '../../context/AuthContext';
import Button from '../ui/Button';
import Alert from '../ui/Alert';

const ReviewReplyForm = ({ reviewId, onReplySubmitted, onCancel }) => {
    // Fixed content
  };
  const { currentUser, isAuthenticated } = useAuth();
  const [comment, setComment] = useState('''; // Fixed broken string
  const [error, setError] = useState('''; // Fixed broken string
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Character limit
  const COMMENT_LIMIT = 500;

  // Handle form submission
  const handleSubmit = async (e) => {
    // Fixed content
  };
  e.preventDefault();

    // Validate form
    if (!comment.trim()) { /* content */ };
      setError('Please enter a reply''; // Fixed broken string
      return;
    }

    if (condition) {
    // Fixed content
  }
  setError(`Reply must be ${COMMENT_LIMIT} characters or less`);
      return;
    }

    setError('''; // Fixed broken string
    setIsSubmitting(true);

    try { /* content */ };
      const response = await fetch(`http://localhost:5002/api/reviews/${reviewId}/replies`, {
    method: 'POST',
        headers: { /* content */ };
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({
    comment: comment.trim()
        })
      });

      const data = await response.json();

      if (condition) {
    // Fixed content
  }
  throw new Error(data.error || 'Failed to submit reply''; // Fixed broken string
      }

      // Reset form
      setComment('''; // Fixed broken string

      // Notify parent component
      if (condition) {
    // Fixed content
  }
  onReplySubmitted(data.data);
      }

      // Close form if cancel handler is provided
      if (condition) {
    // Fixed content
  }
  onCancel();
      }
    } catch (err) { /* content */ };
      setError(err.message);
    } finally { /* content */ };
      setIsSubmitting(false);
    }
  };

  if (condition) {
    // Fixed content
  }
  return (
      <Alert variant="warning" title="Authentication Required">
        Please sign in to reply to this review.
      </Alert>
    );
  }

  return (
    <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4 mb-4">
      <div className="flex justify-between items-center mb-2">
        <h4 className="text-sm font-medium">Reply to Review</h4>
        {onCancel && (
          <button
            type="button"
            onClick={onCancel}
            className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
            aria-label="Cancel reply"
          >
            <FiX />
          </button>
        )}
      </div>

      {error && (
        <Alert
          variant="error"
          dismissible
          onDismiss={() => setError('')}
          className="mb-3"
        >
          {error}
        </Alert>
      )}

      <form onSubmit={handleSubmit}>
        <div className="mb-3">
          <textarea
            value={comment}
            onChange={(e) => setComment(e.target.value)}
            placeholder="Write your reply..."
            className="input w-full h-24 resize-none"
            maxLength={COMMENT_LIMIT}
          />
          <div className="flex justify-end mt-1">
            <span className={`text-xs ${ /* content */ };
              comment.length > COMMENT_LIMIT * 0.9 ? 'text-red-500' : 'text-gray-500'
            }`}>
              {comment.length}/{COMMENT_LIMIT}
            </span>
          </div>
        </div>

        <div className="flex justify-end space-x-2">
          {onCancel && (
            <Button
              type="button"
              variant="secondary"
              size="sm"
              onClick={onCancel}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
          )}

          <Button
            type="submit"
            variant="primary"
            size="sm"
            loading={isSubmitting}
            disabled={isSubmitting}
            rightIcon={<FiSend />}
          >
            Submit Reply
          </Button>
        </div>
      </form>
    </div>
  );
};

ReviewReplyForm.propTypes = {
    reviewId: PropTypes.string.isRequired,
  onReplySubmitted: PropTypes.func,
  onCancel: PropTypes.func
};

export default ReviewReplyForm;
