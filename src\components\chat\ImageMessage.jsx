import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  FiImage, FiEye, FiDownload, FiExternalLink, FiX, 
  FiZoomIn, <PERSON>ZoomOut, FiRotateCw, FiMaximize2 
} from 'react-icons/fi';

const ImageMessage = ({ 
  message, 
  isUser = false, 
  onModelClick = null,
  language = 'vi' 
}) => {
  const [showImageModal, setShowImageModal] = useState(false);
  const [imageLoaded, setImageLoaded] = useState(false);
  const [imageError, setImageError] = useState(false);

  const isEnglish = language === 'en-US';

  // Handle image load
  const handleImageLoad = () => {
    setImageLoaded(true);
    setImageError(false);
  };

  // Handle image error
  const handleImageError = () => {
    setImageError(true);
    setImageLoaded(false);
  };

  // Handle model click
  const handleModelClick = (model) => {
    if (onModelClick) {
      onModelClick(model);
    }
  };

  return (
    <div className={`flex ${isUser ? 'justify-end' : 'justify-start'} mb-4`}>
      <div className={`max-w-2xl ${isUser ? 'order-2' : 'order-1'}`}>
        {/* Message Content */}
        <div className={`rounded-lg p-4 ${
          isUser 
            ? 'bg-blue-600 text-white ml-4' 
            : 'bg-white dark:bg-gray-800 text-gray-900 dark:text-white shadow-lg mr-4'
        }`}>
          {/* Image Display */}
          {message.imageUrl && (
            <div className="mb-3">
              <div className="relative group">
                {!imageLoaded && !imageError && (
                  <div className="w-full h-48 bg-gray-200 dark:bg-gray-700 rounded-lg flex items-center justify-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-400"></div>
                  </div>
                )}
                
                {imageError && (
                  <div className="w-full h-48 bg-gray-200 dark:bg-gray-700 rounded-lg flex items-center justify-center">
                    <div className="text-center text-gray-500">
                      <FiImage className="h-8 w-8 mx-auto mb-2" />
                      <p className="text-sm">
                        {isEnglish ? 'Failed to load image' : 'Không thể tải hình ảnh'}
                      </p>
                    </div>
                  </div>
                )}

                <img
                  src={message.imageUrl}
                  alt="Uploaded image"
                  className={`w-full max-w-md rounded-lg cursor-pointer transition-all duration-300 hover:shadow-lg ${
                    imageLoaded ? 'block' : 'hidden'
                  }`}
                  onLoad={handleImageLoad}
                  onError={handleImageError}
                  onClick={() => setShowImageModal(true)}
                />

                {/* Image Overlay */}
                {imageLoaded && (
                  <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-all duration-300 rounded-lg flex items-center justify-center opacity-0 group-hover:opacity-100">
                    <button
                      onClick={() => setShowImageModal(true)}
                      className="p-2 bg-white/90 text-gray-800 rounded-full hover:bg-white transition-colors"
                      title={isEnglish ? 'View full size' : 'Xem kích thước đầy đủ'}
                    >
                      <FiMaximize2 className="h-5 w-5" />
                    </button>
                  </div>
                )}
              </div>

              {/* Image Info */}
              {message.additionalData?.imageMetadata && (
                <div className="mt-2 text-xs opacity-75">
                  <p>
                    {message.additionalData.imageMetadata.dimensions.width}×{message.additionalData.imageMetadata.dimensions.height} • 
                    {isEnglish ? 'Compressed' : 'Đã nén'}: {Math.round(message.additionalData.imageMetadata.compressionRatio)}%
                  </p>
                </div>
              )}
            </div>
          )}

          {/* Text Content */}
          <div className="prose prose-sm max-w-none">
            <p className={`${isUser ? 'text-white' : 'text-gray-800 dark:text-gray-200'} leading-relaxed`}>
              {message.text}
            </p>
          </div>

          {/* Related Models */}
          {message.additionalData?.relatedModels && message.additionalData.relatedModels.length > 0 && (
            <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-600">
              <h4 className={`text-sm font-semibold mb-3 ${
                isUser ? 'text-white' : 'text-gray-900 dark:text-white'
              }`}>
                {isEnglish ? 'Related 3D Models' : 'Mô hình 3D liên quan'}
              </h4>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                {message.additionalData.relatedModels.slice(0, 4).map((model, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3, delay: index * 0.1 }}
                    className={`p-3 rounded-lg border cursor-pointer transition-all duration-200 hover:shadow-md ${
                      isUser 
                        ? 'border-blue-400 bg-blue-500/20 hover:bg-blue-500/30' 
                        : 'border-gray-200 dark:border-gray-600 bg-gray-50 dark:bg-gray-700 hover:bg-gray-100 dark:hover:bg-gray-600'
                    }`}
                    onClick={() => handleModelClick(model)}
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex-1 min-w-0">
                        <h5 className={`font-medium text-sm truncate ${
                          isUser ? 'text-white' : 'text-gray-900 dark:text-white'
                        }`}>
                          {model.title}
                        </h5>
                        <p className={`text-xs mt-1 opacity-75 ${
                          isUser ? 'text-blue-100' : 'text-gray-600 dark:text-gray-400'
                        }`}>
                          {model.category} • {model.downloads} {isEnglish ? 'downloads' : 'lượt tải'}
                        </p>
                      </div>
                      <FiExternalLink className={`h-4 w-4 ml-2 flex-shrink-0 ${
                        isUser ? 'text-blue-200' : 'text-gray-400'
                      }`} />
                    </div>
                  </motion.div>
                ))}
              </div>
            </div>
          )}

          {/* Analysis Data */}
          {message.additionalData?.analysis && (
            <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-600">
              <details className="group">
                <summary className={`cursor-pointer text-sm font-medium ${
                  isUser ? 'text-white' : 'text-gray-900 dark:text-white'
                } hover:opacity-75 transition-opacity`}>
                  {isEnglish ? 'View Analysis Details' : 'Xem chi tiết phân tích'}
                </summary>
                <div className="mt-2 space-y-2 text-xs">
                  {message.additionalData.analysis.elements?.length > 0 && (
                    <div>
                      <span className={`font-medium ${
                        isUser ? 'text-blue-100' : 'text-gray-700 dark:text-gray-300'
                      }`}>
                        {isEnglish ? 'Elements:' : 'Yếu tố:'}
                      </span>
                      <span className={`ml-2 ${
                        isUser ? 'text-blue-200' : 'text-gray-600 dark:text-gray-400'
                      }`}>
                        {message.additionalData.analysis.elements.join(', ')}
                      </span>
                    </div>
                  )}
                  {message.additionalData.analysis.objects?.length > 0 && (
                    <div>
                      <span className={`font-medium ${
                        isUser ? 'text-blue-100' : 'text-gray-700 dark:text-gray-300'
                      }`}>
                        {isEnglish ? 'Objects:' : 'Đối tượng:'}
                      </span>
                      <span className={`ml-2 ${
                        isUser ? 'text-blue-200' : 'text-gray-600 dark:text-gray-400'
                      }`}>
                        {message.additionalData.analysis.objects.join(', ')}
                      </span>
                    </div>
                  )}
                  {message.additionalData.analysis.style && (
                    <div>
                      <span className={`font-medium ${
                        isUser ? 'text-blue-100' : 'text-gray-700 dark:text-gray-300'
                      }`}>
                        {isEnglish ? 'Style:' : 'Phong cách:'}
                      </span>
                      <span className={`ml-2 ${
                        isUser ? 'text-blue-200' : 'text-gray-600 dark:text-gray-400'
                      }`}>
                        {message.additionalData.analysis.style}
                      </span>
                    </div>
                  )}
                </div>
              </details>
            </div>
          )}
        </div>

        {/* Timestamp */}
        <div className={`text-xs text-gray-500 mt-1 ${isUser ? 'text-right' : 'text-left'}`}>
          {new Date(message.timestamp).toLocaleTimeString()}
        </div>
      </div>

      {/* Image Modal */}
      <AnimatePresence>
        {showImageModal && message.imageUrl && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/80 backdrop-blur-sm z-[70] flex items-center justify-center p-4"
            onClick={() => setShowImageModal(false)}
          >
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.9 }}
              className="relative max-w-4xl max-h-[90vh] bg-white dark:bg-gray-800 rounded-lg overflow-hidden"
              onClick={(e) => e.stopPropagation()}
            >
              {/* Modal Header */}
              <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                  {isEnglish ? 'Image Preview' : 'Xem trước hình ảnh'}
                </h3>
                <button
                  onClick={() => setShowImageModal(false)}
                  className="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700"
                >
                  <FiX className="h-5 w-5" />
                </button>
              </div>

              {/* Modal Content */}
              <div className="p-4">
                <img
                  src={message.imageUrl}
                  alt="Full size preview"
                  className="w-full h-auto max-h-[70vh] object-contain rounded-lg"
                />
              </div>

              {/* Modal Footer */}
              <div className="flex items-center justify-between p-4 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-700">
                <div className="text-sm text-gray-600 dark:text-gray-400">
                  {message.additionalData?.imageMetadata && (
                    <span>
                      {message.additionalData.imageMetadata.dimensions.width}×{message.additionalData.imageMetadata.dimensions.height} • 
                      {(message.additionalData.imageMetadata.processedSize / 1024 / 1024).toFixed(2)} MB
                    </span>
                  )}
                </div>
                <div className="flex items-center space-x-2">
                  <a
                    href={message.imageUrl}
                    download
                    className="flex items-center px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm"
                  >
                    <FiDownload className="h-4 w-4 mr-2" />
                    {isEnglish ? 'Download' : 'Tải xuống'}
                  </a>
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default ImageMessage;
