import express from 'express';
import {
  getAvailableVersions,
  requestConversion,
  getConversionStatus,
  downloadConvertedVersion,
  getConversionStats
} from '../controllers/versionController.js';
import { protect, authorize } from '../middleware/auth.js';

const router = express.Router();

// Public routes
router.get('/:modelId/available', getAvailableVersions);
router.get('/:modelId/conversion/:conversionId', getConversionStatus);

// Protected routes (require authentication)
router.post('/:modelId/convert', protect, requestConversion);
router.get('/:modelId/download/:version', protect, downloadConvertedVersion);

// Admin routes
router.get('/stats', protect, authorize('admin'), getConversionStats);

export default router;
