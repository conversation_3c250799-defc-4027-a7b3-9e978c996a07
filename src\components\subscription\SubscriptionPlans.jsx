import React, { useState, useCallback } from 'react';
import { motion } from 'framer-motion';
import { FiCheck, FiX, FiDownload, FiUpload, FiDollarSign, FiCreditCard } from 'react-icons/fi';
import Button from '../ui/Button';
import Badge from '../ui/Badge';

const SubscriptionPlans = ({ onSelectPlan }) => {
  const [billingCycle, setBillingCycle] = useState('monthly');
  const [selectedPlan, setSelectedPlan] = useState(null);

  // Define subscription plans
  const plans = [
    {
    id: 'free',
      name: 'Free',
      description: 'Basic access to 3D models',
      price: {
    monthly: 0,
        yearly: 0
      },
      features: [
        { text: 'Access to free models', included: true },
        { text: '5 downloads per month', included: true },
        { text: 'Basic model viewer', included: true },
        { text: 'Community support', included: true },
        { text: 'Upload models', included: false },
        { text: 'Premium models access', included: false },
        { text: 'Priority support', included: false },
        { text: 'Commercial use license', included: false }
      ],
      popular: false,
      cta: 'Get Started'
    },
    {
    id: 'basic',
      name: 'Basic',
      description: 'Perfect for hobbyists and beginners',
      price: {
    monthly: 9.99,
        yearly: 99.99
      },
      features: [
        { text: 'Access to free models', included: true },
        { text: '25 downloads per month', included: true },
        { text: 'Advanced model viewer', included: true },
        { text: 'Community support', included: true },
        { text: 'Upload up to 10 models', included: true },
        { text: 'Limited premium models access', included: true },
        { text: 'Priority support', included: false },
        { text: 'Commercial use license', included: false }
      ],
      popular: true,
      cta: 'Subscribe Now'
    },
    {
    id: 'pro',
      name: 'Professional',
      description: 'For professionals and businesses',
      price: {
    monthly: 19.99,
        yearly: 199.99
      },
      features: [
        { text: 'Access to free models', included: true },
        { text: 'Unlimited downloads', included: true },
        { text: 'Advanced model viewer', included: true },
        { text: 'Priority support', included: true },
        { text: 'Unlimited model uploads', included: true },
        { text: 'Full premium models access', included: true },
        { text: 'API access', included: true },
        { text: 'Commercial use license', included: true }
      ],
      popular: false,
      cta: 'Go Pro'
    }
  ];

  // Calculate savings for yearly billing
  const calculateSavings = (plan) => {
  if (plan.price.monthly === 0) return 0;
    const monthlyCost = plan.price.monthly * 12;
    const yearlyCost = plan.price.yearly;
    return Math.round(((monthlyCost - yearlyCost) / monthlyCost) * 100);
  };

  // Handle plan selection
  const handleSelectPlan = (plan) => {
  setSelectedPlan(plan.id);
    if (cachedData && !isExpired(cachedData)) {
  onSelectPlan({
    planId: plan.id,
        billingCycle,
        price: plan.price[billingCycle]
      });
    }
  };

  return (
    <div className="py-8">
      {/* Billing toggle */}
      <div className="flex justify-center mb-12">
        <div className="bg-gray-100 dark:bg-gray-800 p-1 rounded-lg inline-flex">
          <button
            onClick={() => setBillingCycle('monthly')}
            className={`px-4 py-2 text-sm font-medium rounded-md transition-colors ${
    billingCycle === 'monthly'
                ? 'bg-white dark:bg-gray-700 shadow-sm text-gray-900 dark:text-white'
                : 'text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white'
            }`}
          >
            Monthly
          </button>
          <button
            onClick={() => setBillingCycle('yearly')}
            className={`px-4 py-2 text-sm font-medium rounded-md transition-colors ${
    billingCycle === 'yearly'
                ? 'bg-white dark:bg-gray-700 shadow-sm text-gray-900 dark:text-white'
                : 'text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white'
            }`}
          >
            Yearly
            <span className="ml-1 text-xs text-green-600 dark:text-green-400">
              Save up to 20%
            </span>
          </button>
        </div>
      </div>

      {/* Plans grid */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
        {plans.map((plan, index) => (
          <motion.div
            key={plan.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: index * 0.1 }}
            className={`relative bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden ${
              plan.popular ? 'ring-2 ring-primary-500 dark:ring-primary-400' : '
            }`}
          >
            {plan.popular && (
              <div className="absolute top-0 right-0 mt-4 mr-4">
                <Badge variant="primary" pill>
                  Most Popular
                </Badge>
              </div>
            )}

            <div className="p-6">
              <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-2">
                {plan.name}
              </h3>
              <p className="text-gray-600 dark:text-gray-400 mb-4">
                {plan.description}
              </p>

              <div className="mb-6">
                <div className="flex items-end">
                  <span className="text-3xl font-bold text-gray-900 dark:text-white">
                    ${plan.price[billingCycle].toFixed(2)}
                  </span>
                  {plan.price[billingCycle] > 0 && (
                    <span className="text-gray-600 dark:text-gray-400 ml-2">
                      /{billingCycle === 'monthly' ? 'month' : 'year'}
                    </span>
                  )}
                </div>

                {billingCycle === 'yearly' && plan.price.monthly > 0 && (
                  <div className="mt-1 text-sm text-green-600 dark:text-green-400">
                    Save {calculateSavings(plan)}% with annual billing
                  </div>
                )}
              </div>

              <Button
                variant={plan.popular ? 'primary' : 'secondary'}
                size="lg"
                fullWidth
                onClick={() => handleSelectPlan(plan)}
                leftIcon={plan.id === 'free' ? <FiDownload /> : <FiCreditCard />}
              >
                {plan.cta}
              </Button>
            </div>

            <div className="px-6 pb-6">
              <h4 className="font-medium text-gray-900 dark:text-white mb-4">
                Features include:
              </h4>
              <ul className="space-y-3">
                {plan.features.map((feature, i) => (
                  <li key={i} className="flex items-start">
                    {feature.included ? (
                      <FiCheck className="h-5 w-5 text-green-500 mt-0.5 mr-2 flex-shrink-0" />
                    ) : (
                      <FiX className="h-5 w-5 text-gray-400 mt-0.5 mr-2 flex-shrink-0" />
                    )}
                    <span className={feature.included ? 'text-gray-700 dark:text-gray-300' : 'text-gray-500 dark:text-gray-500'}>
                      {feature.text}
                    </span>
                  </li>
                ))}
              </ul>
            </div>
          </motion.div>
        ))}
      </div>
    </div>
  );
};

export default SubscriptionPlans;
