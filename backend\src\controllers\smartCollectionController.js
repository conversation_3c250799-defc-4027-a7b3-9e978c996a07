import SmartCollection from '../models/SmartCollection.js';
import Model from '../models/Model.js';
import asyncHandler from 'express-async-handler';

// @desc    Create new smart collection
// @route   POST /api/collections/smart
// @access  Private
export const createSmartCollection = asyncHandler(async (req, res) => {
  const collectionData = {
    ...req.body,
    owner: req.user.id
  };

  const collection = await SmartCollection.create(collectionData);

  res.status(201).json({
    success: true,
    data: collection
  });
});

// @desc    Get all smart collections
// @route   GET /api/collections/smart
// @access  Public
export const getSmartCollections = asyncHandler(async (req, res) => {
  const { 
    page = 1, 
    limit = 12, 
    type, 
    featured,
    search,
    owner 
  } = req.query;

  // Build query
  let query = {};
  
  // Public collections only for non-owners
  if (!owner || owner !== req.user?.id) {
    query.isPublic = true;
  }
  
  if (type) query.type = type;
  if (featured) query.featured = featured === 'true';
  if (owner) query.owner = owner;
  
  if (search) {
    query.$or = [
      { name: { $regex: search, $options: 'i' } },
      { description: { $regex: search, $options: 'i' } },
      { tags: { $in: [new RegExp(search, 'i')] } }
    ];
  }

  const collections = await SmartCollection.find(query)
    .populate('owner', 'name profileImage')
    .populate('models.model', 'title imageUrl category rating downloads')
    .sort({ featured: -1, 'analytics.totalViews': -1, createdAt: -1 })
    .limit(limit * 1)
    .skip((page - 1) * limit);

  const total = await SmartCollection.countDocuments(query);

  res.status(200).json({
    success: true,
    data: collections,
    pagination: {
      page: parseInt(page),
      limit: parseInt(limit),
      total,
      pages: Math.ceil(total / limit)
    }
  });
});

// @desc    Get single smart collection
// @route   GET /api/collections/smart/:id
// @access  Public
export const getSmartCollection = asyncHandler(async (req, res) => {
  const collection = await SmartCollection.findById(req.params.id)
    .populate('owner', 'name profileImage bio')
    .populate('models.model', 'title description imageUrl category format fileSize rating downloads isPremium')
    .populate('collaboration.contributors.user', 'name profileImage')
    .populate('collaboration.pendingSuggestions.suggestedBy', 'name profileImage')
    .populate('collaboration.pendingSuggestions.model', 'title imageUrl category');

  if (!collection) {
    return res.status(404).json({
      success: false,
      error: 'Collection not found'
    });
  }

  // Check access permissions
  if (!collection.isPublic) {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        error: 'Authentication required'
      });
    }

    const hasAccess = collection.owner.toString() === req.user.id ||
                     collection.collaboration.contributors.some(contrib => contrib.user.toString() === req.user.id);

    if (!hasAccess) {
      return res.status(403).json({
        success: false,
        error: 'Access denied'
      });
    }
  }

  // Increment view count
  collection.analytics.totalViews += 1;
  await collection.save();

  res.status(200).json({
    success: true,
    data: collection
  });
});

// @desc    Update smart collection
// @route   PUT /api/collections/smart/:id
// @access  Private
export const updateSmartCollection = asyncHandler(async (req, res) => {
  let collection = await SmartCollection.findById(req.params.id);

  if (!collection) {
    return res.status(404).json({
      success: false,
      error: 'Collection not found'
    });
  }

  // Check ownership
  if (collection.owner.toString() !== req.user.id && req.user.role !== 'admin') {
    return res.status(403).json({
      success: false,
      error: 'Not authorized to update this collection'
    });
  }

  collection = await SmartCollection.findByIdAndUpdate(
    req.params.id,
    req.body,
    { new: true, runValidators: true }
  ).populate('owner', 'name profileImage')
   .populate('models.model', 'title imageUrl category');

  res.status(200).json({
    success: true,
    data: collection
  });
});

// @desc    Delete smart collection
// @route   DELETE /api/collections/smart/:id
// @access  Private
export const deleteSmartCollection = asyncHandler(async (req, res) => {
  const collection = await SmartCollection.findById(req.params.id);

  if (!collection) {
    return res.status(404).json({
      success: false,
      error: 'Collection not found'
    });
  }

  // Check ownership
  if (collection.owner.toString() !== req.user.id && req.user.role !== 'admin') {
    return res.status(403).json({
      success: false,
      error: 'Not authorized to delete this collection'
    });
  }

  await collection.deleteOne();

  res.status(200).json({
    success: true,
    data: {}
  });
});

// @desc    Add model to smart collection
// @route   POST /api/collections/smart/:id/models
// @access  Private
export const addModelToCollection = asyncHandler(async (req, res) => {
  const { modelId } = req.body;

  const collection = await SmartCollection.findById(req.params.id);

  if (!collection) {
    return res.status(404).json({
      success: false,
      error: 'Collection not found'
    });
  }

  // Check permissions
  const hasPermission = collection.owner.toString() === req.user.id ||
                       collection.collaboration.contributors.some(contrib => 
                         contrib.user.toString() === req.user.id && 
                         contrib.role !== 'viewer'
                       );

  if (!hasPermission) {
    return res.status(403).json({
      success: false,
      error: 'Not authorized to edit this collection'
    });
  }

  // Check if model exists
  const model = await Model.findById(modelId);
  if (!model) {
    return res.status(404).json({
      success: false,
      error: 'Model not found'
    });
  }

  // Check if model already exists in collection
  const existingModel = collection.models.find(m => m.model.toString() === modelId);
  if (existingModel) {
    return res.status(400).json({
      success: false,
      error: 'Model already exists in this collection'
    });
  }

  // Add model to collection
  collection.models.push({
    model: modelId,
    addedBy: req.user.id,
    order: collection.models.length
  });

  await collection.save();

  // Populate the new model data
  await collection.populate('models.model', 'title imageUrl category');

  res.status(200).json({
    success: true,
    data: collection
  });
});

// @desc    Remove model from smart collection
// @route   DELETE /api/collections/smart/:id/models/:modelId
// @access  Private
export const removeModelFromCollection = asyncHandler(async (req, res) => {
  const collection = await SmartCollection.findById(req.params.id);

  if (!collection) {
    return res.status(404).json({
      success: false,
      error: 'Collection not found'
    });
  }

  // Check permissions
  const hasPermission = collection.owner.toString() === req.user.id ||
                       collection.collaboration.contributors.some(contrib => 
                         contrib.user.toString() === req.user.id && 
                         contrib.role !== 'viewer'
                       );

  if (!hasPermission) {
    return res.status(403).json({
      success: false,
      error: 'Not authorized to edit this collection'
    });
  }

  // Remove model from collection
  collection.models = collection.models.filter(
    m => m.model.toString() !== req.params.modelId
  );

  await collection.save();

  res.status(200).json({
    success: true,
    data: collection
  });
});

// @desc    Update smart collection criteria
// @route   PUT /api/collections/smart/:id/criteria
// @access  Private
export const updateSmartCriteria = asyncHandler(async (req, res) => {
  const collection = await SmartCollection.findById(req.params.id);

  if (!collection) {
    return res.status(404).json({
      success: false,
      error: 'Collection not found'
    });
  }

  // Check ownership
  if (collection.owner.toString() !== req.user.id) {
    return res.status(403).json({
      success: false,
      error: 'Not authorized to update criteria'
    });
  }

  collection.smartCriteria = {
    ...collection.smartCriteria,
    ...req.body,
    lastUpdated: new Date()
  };

  await collection.save();

  res.status(200).json({
    success: true,
    data: collection.smartCriteria
  });
});

// @desc    Get AI suggestions for collection
// @route   GET /api/collections/smart/:id/suggestions
// @access  Private
export const getAISuggestions = asyncHandler(async (req, res) => {
  const collection = await SmartCollection.findById(req.params.id);

  if (!collection) {
    return res.status(404).json({
      success: false,
      error: 'Collection not found'
    });
  }

  // Check permissions
  const hasPermission = collection.owner.toString() === req.user.id ||
                       collection.collaboration.contributors.some(contrib => 
                         contrib.user.toString() === req.user.id
                       );

  if (!hasPermission) {
    return res.status(403).json({
      success: false,
      error: 'Not authorized to view suggestions'
    });
  }

  res.status(200).json({
    success: true,
    data: collection.aiRecommendations.suggestions
  });
});

// @desc    Apply AI suggestion
// @route   POST /api/collections/smart/:id/suggestions/:suggestionId/apply
// @access  Private
export const applyAISuggestion = asyncHandler(async (req, res) => {
  const collection = await SmartCollection.findById(req.params.id);

  if (!collection) {
    return res.status(404).json({
      success: false,
      error: 'Collection not found'
    });
  }

  // Check ownership
  if (collection.owner.toString() !== req.user.id) {
    return res.status(403).json({
      success: false,
      error: 'Not authorized to apply suggestions'
    });
  }

  const suggestion = collection.aiRecommendations.suggestions.id(req.params.suggestionId);
  if (!suggestion) {
    return res.status(404).json({
      success: false,
      error: 'Suggestion not found'
    });
  }

  // Add model to collection
  collection.models.push({
    model: suggestion.model,
    addedBy: req.user.id,
    order: collection.models.length,
    note: `Added from AI suggestion: ${suggestion.reason}`
  });

  // Remove suggestion
  collection.aiRecommendations.suggestions.pull(suggestion._id);

  await collection.save();

  res.status(200).json({
    success: true,
    data: collection
  });
});

// @desc    Vote on collaborative suggestion
// @route   POST /api/collections/smart/:id/suggestions/:suggestionId/vote
// @access  Private
export const voteOnSuggestion = asyncHandler(async (req, res) => {
  const { vote } = req.body; // 'approve' or 'reject'
  
  const collection = await SmartCollection.findById(req.params.id);

  if (!collection) {
    return res.status(404).json({
      success: false,
      error: 'Collection not found'
    });
  }

  // Check if user is a contributor
  const isContributor = collection.collaboration.contributors.some(contrib => 
    contrib.user.toString() === req.user.id
  );

  if (!isContributor && collection.owner.toString() !== req.user.id) {
    return res.status(403).json({
      success: false,
      error: 'Not authorized to vote on suggestions'
    });
  }

  const suggestion = collection.collaboration.pendingSuggestions.id(req.params.suggestionId);
  if (!suggestion) {
    return res.status(404).json({
      success: false,
      error: 'Suggestion not found'
    });
  }

  // Remove existing vote from this user
  suggestion.votes = suggestion.votes.filter(v => v.user.toString() !== req.user.id);

  // Add new vote
  suggestion.votes.push({
    user: req.user.id,
    vote,
    votedAt: new Date()
  });

  await collection.save();

  res.status(200).json({
    success: true,
    data: suggestion
  });
});
