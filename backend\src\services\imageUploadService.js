import path from 'path';
import fs from 'fs/promises';
import sharp from 'sharp';
import { v4 as uuidv4 } from 'uuid';

class ImageUploadService {
  constructor() {
    this.uploadDir = 'uploads/models';
    this.maxFileSize = 10 * 1024 * 1024; // 10MB
    this.allowedMimeTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
    this.thumbnailSizes = {
      small: { width: 300, height: 200 },
      medium: { width: 600, height: 400 },
      large: { width: 1200, height: 800 }
    };
    
    this.ensureUploadDir();
  }

  /**
   * Ensure upload directory exists
   */
  async ensureUploadDir() {
    try {
      await fs.access(this.uploadDir);
    } catch {
      await fs.mkdir(this.uploadDir, { recursive: true });
    }
  }

  /**
   * Validate image file
   */
  validateImage(file) {
    const errors = [];

    // Check file size
    if (file.size > this.maxFileSize) {
      errors.push(`File size too large. Maximum size is ${this.maxFileSize / 1024 / 1024}MB`);
    }

    // Check mime type
    if (!this.allowedMimeTypes.includes(file.mimetype)) {
      errors.push(`Invalid file type. Allowed types: ${this.allowedMimeTypes.join(', ')}`);
    }

    // Check if file has data
    if (!file.data || file.data.length === 0) {
      errors.push('File data is empty');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Process and save image with multiple sizes
   */
  async processAndSaveImage(file, modelId) {
    try {
      // Validate image
      const validation = this.validateImage(file);
      if (!validation.isValid) {
        throw new Error(validation.errors.join(', '));
      }

      const fileId = uuidv4();
      const originalName = file.name;
      const extension = path.extname(originalName).toLowerCase();
      const baseName = `${modelId}_${fileId}`;

      const savedImages = {
        original: null,
        thumbnails: {}
      };

      // Save original image
      const originalFilename = `${baseName}_original${extension}`;
      const originalPath = path.join(this.uploadDir, originalFilename);
      
      await fs.writeFile(originalPath, file.data);
      
      savedImages.original = {
        filename: originalFilename,
        path: originalPath,
        url: `/uploads/models/${originalFilename}`,
        size: file.size,
        mimetype: file.mimetype,
        width: null,
        height: null
      };

      // Get original image dimensions
      const metadata = await sharp(file.data).metadata();
      savedImages.original.width = metadata.width;
      savedImages.original.height = metadata.height;

      // Generate thumbnails
      for (const [sizeName, dimensions] of Object.entries(this.thumbnailSizes)) {
        const thumbnailFilename = `${baseName}_${sizeName}${extension}`;
        const thumbnailPath = path.join(this.uploadDir, thumbnailFilename);

        await sharp(file.data)
          .resize(dimensions.width, dimensions.height, {
            fit: 'cover',
            position: 'center'
          })
          .jpeg({ quality: 85 })
          .toFile(thumbnailPath);

        savedImages.thumbnails[sizeName] = {
          filename: thumbnailFilename,
          path: thumbnailPath,
          url: `/uploads/models/${thumbnailFilename}`,
          width: dimensions.width,
          height: dimensions.height
        };
      }

      return {
        success: true,
        data: {
          id: fileId,
          originalName,
          ...savedImages,
          uploadedAt: new Date()
        }
      };

    } catch (error) {
      console.error('Image processing error:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Delete image and all its thumbnails
   */
  async deleteImage(imageData) {
    try {
      const filesToDelete = [
        imageData.original?.path,
        ...Object.values(imageData.thumbnails || {}).map(thumb => thumb.path)
      ].filter(Boolean);

      for (const filePath of filesToDelete) {
        try {
          await fs.unlink(filePath);
        } catch (error) {
          console.warn(`Failed to delete file ${filePath}:`, error.message);
        }
      }

      return { success: true };
    } catch (error) {
      console.error('Image deletion error:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Process multiple images
   */
  async processMultipleImages(files, modelId) {
    const results = [];
    const errors = [];

    for (let i = 0; i < files.length; i++) {
      const file = files[i];
      const result = await this.processAndSaveImage(file, modelId);
      
      if (result.success) {
        results.push(result.data);
      } else {
        errors.push({
          filename: file.name,
          error: result.error
        });
      }
    }

    return {
      success: errors.length === 0,
      data: results,
      errors
    };
  }

  /**
   * Get image info for database storage
   */
  formatImageForDatabase(imageData, isMain = false) {
    return {
      url: imageData.original.url,
      filename: imageData.originalName,
      size: imageData.original.size,
      mimetype: imageData.original.mimetype,
      isMain,
      thumbnails: imageData.thumbnails,
      uploadedAt: imageData.uploadedAt
    };
  }

  /**
   * Clean up orphaned images
   */
  async cleanupOrphanedImages(validImageIds) {
    try {
      const files = await fs.readdir(this.uploadDir);
      const orphanedFiles = files.filter(file => {
        const fileId = file.split('_')[1];
        return !validImageIds.includes(fileId);
      });

      for (const file of orphanedFiles) {
        await fs.unlink(path.join(this.uploadDir, file));
      }

      return {
        success: true,
        deletedCount: orphanedFiles.length
      };
    } catch (error) {
      console.error('Cleanup error:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }
}

export default new ImageUploadService();
