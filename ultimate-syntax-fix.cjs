const fs = require('fs');
const path = require('path');

// Ultimate syntax fix patterns
const syntaxFixes = [
  // Fix conditional statements with missing conditions
  { pattern: /if \(true\) \{/g, replacement: 'if (cachedData && !isExpired(cachedData)) {' },
  { pattern: /if \(true\) \{/g, replacement: 'if (query && query.trim()) {' },
  { pattern: /if \(true\) \{/g, replacement: 'if (filters.category) {' },
  { pattern: /if \(true\) \{/g, replacement: 'if (filters.subcategory) {' },
  { pattern: /if \(true\) \{/g, replacement: 'if (filters.format) {' },
  { pattern: /if \(true\) \{/g, replacement: 'if (filters.year) {' },
  { pattern: /if \(true\) \{/g, replacement: 'if (filters.isPremium !== undefined) {' },
  { pattern: /if \(true\) \{/g, replacement: 'if (value !== null && value !== undefined) {' },
  { pattern: /if \(true\) \{/g, replacement: 'if (file) {' },
  { pattern: /if \(true\) \{/g, replacement: 'if (currentUser) {' },
  { pattern: /if \(true\) \{/g, replacement: 'if (files.modelFile) {' },
  { pattern: /if \(true\) \{/g, replacement: 'if (!model) {' },
  { pattern: /if \(true\) \{/g, replacement: 'if (model.downloadUrl) {' },
  { pattern: /if \(true\) \{/g, replacement: 'if (localModel) {' },
  { pattern: /if \(true\) \{/g, replacement: 'if (cache.size >= maxSize) {' },
  { pattern: /if \(true\) \{/g, replacement: 'if (\'serviceWorker\' in navigator) {' },
  { pattern: /if \(true\) \{/g, replacement: 'if (document.readyState === \'loading\') {' },
  
  // Fix missing closing parentheses and semicolons
  { pattern: /console\.warn\(`Performance warning: \$\{label\} took \$\{duration\.toFixed\(2\)\}ms`\);/g, replacement: 'console.warn(`Performance warning: ${label} took ${duration.toFixed(2)}ms`);' },
  { pattern: /console\.error\('Failed to register service worker:', err/g, replacement: 'console.error(\'Failed to register service worker:\', err)' },
  { pattern: /console\.error\('MongoDB error:', mongoErr/g, replacement: 'console.error(\'MongoDB error:\', mongoErr)' },
  
  // Fix method calls with missing parameters
  { pattern: /method: 'GET',/g, replacement: 'method: \'GET\',' },
  { pattern: /method: 'PUT',/g, replacement: 'method: \'PUT\',' },
  { pattern: /method: 'POST',/g, replacement: 'method: \'POST\',' },
  { pattern: /method: 'DELETE',/g, replacement: 'method: \'DELETE\',' },
  
  // Fix object properties
  { pattern: /modelsCount: models\.length,/g, replacement: 'modelsCount: models.length,' },
  { pattern: /usersCount: 0,/g, replacement: 'usersCount: 0,' },
  { pattern: /downloadsCount: models\.reduce\(\(sum, model\) => sum \+ \(model\.downloads \|\| 0\), 0\),/g, replacement: 'downloadsCount: models.reduce((sum, model) => sum + (model.downloads || 0), 0),' },
  { pattern: /categoriesCount: categories\.length/g, replacement: 'categoriesCount: categories.length' },
  
  // Fix array and object destructuring
  { pattern: /downloads: \(model\.downloads \|\| 0\) \+ 1/g, replacement: 'downloads: (model.downloads || 0) + 1' },
  
  // Fix template literals and string concatenation
  { pattern: /\$\{model\.title\.replace\(\/\\s\+\/g, '_'\)\}\.\$\{model\.format\?\.toLowerCase\(\) \|\| 'model'\}/g, replacement: '${model.title.replace(/\\s+/g, \'_\')}.${model.format?.toLowerCase() || \'model\'}' },
  { pattern: /model_\$\{id\}\.\$\{model\.format\?\.toLowerCase\(\) \|\| 'model'\}/g, replacement: 'model_${id}.${model.format?.toLowerCase() || \'model\'}' },
  
  // Fix specific syntax errors found in files
  { pattern: /if \(!id \|\| typeof id !== 'string' \|\| id\.trim\(\) === ''\) \{/g, replacement: 'if (!id || typeof id !== \'string\' || id.trim() === \'\') {' },
  { pattern: /if \(cachedData && Date\.now\(\) - cachedData\.timestamp < 5 \* 60 \* 1000\) \{/g, replacement: 'if (cachedData && Date.now() - cachedData.timestamp < 5 * 60 * 1000) {' },
  { pattern: /if \(searchResults && searchResults\.length > 0\) \{/g, replacement: 'if (searchResults && searchResults.length > 0) {' },
  { pattern: /if \(query && query\.trim\(\)\) \{/g, replacement: 'if (query && query.trim()) {' },
  { pattern: /if \(filters\.category\) \{/g, replacement: 'if (filters.category) {' },
  { pattern: /if \(filters\.subcategory\) \{/g, replacement: 'if (filters.subcategory) {' },
  { pattern: /if \(filters\.format\) \{/g, replacement: 'if (filters.format) {' },
  { pattern: /if \(filters\.year\) \{/g, replacement: 'if (filters.year) {' },
  { pattern: /if \(filters\.isPremium !== undefined\) \{/g, replacement: 'if (filters.isPremium !== undefined) {' },
  
  // Fix return statements and expressions
  { pattern: /return null;/g, replacement: 'return null;' },
  { pattern: /return cachedData\.data;/g, replacement: 'return cachedData.data;' },
  { pattern: /return searchResults;/g, replacement: 'return searchResults;' },
  { pattern: /return apiResults;/g, replacement: 'return apiResults;' },
  { pattern: /return filteredModels;/g, replacement: 'return filteredModels;' },
  { pattern: /return uploadedModel;/g, replacement: 'return uploadedModel;' },
  { pattern: /return newModel;/g, replacement: 'return newModel;' },
  { pattern: /return \{ success: true, downloadUrl: model\.downloadUrl \};/g, replacement: 'return { success: true, downloadUrl: model.downloadUrl };' },
  { pattern: /return \{ success: true, downloadUrl: model\.modelUrl \};/g, replacement: 'return { success: true, downloadUrl: model.modelUrl };' },
  { pattern: /return \{ success: true, downloadUrl: response\.data\.downloadUrl \};/g, replacement: 'return { success: true, downloadUrl: response.data.downloadUrl };' },
  { pattern: /return stats;/g, replacement: 'return stats;' },
  { pattern: /return apiStats;/g, replacement: 'return apiStats;' },
  
  // Fix function calls and method chaining
  { pattern: /setLoading\(true\);/g, replacement: 'setLoading(true);' },
  { pattern: /setLoading\(false\);/g, replacement: 'setLoading(false);' },
  { pattern: /setError\(null\);/g, replacement: 'setError(null);' },
  { pattern: /setError\(err\.message \|\| 'Failed to fetch models'\);/g, replacement: 'setError(err.message || \'Failed to fetch models\');' },
  { pattern: /setError\(err\.message \|\| 'Failed to fetch model'\);/g, replacement: 'setError(err.message || \'Failed to fetch model\');' },
  { pattern: /setError\(err\.message \|\| 'Search failed'\);/g, replacement: 'setError(err.message || \'Search failed\');' },
  { pattern: /setError\(err\.message \|\| 'Upload failed'\);/g, replacement: 'setError(err.message || \'Upload failed\');' },
  { pattern: /setError\(err\.message \|\| 'Download failed'\);/g, replacement: 'setError(err.message || \'Download failed\');' },
  
  // Fix array operations
  { pattern: /setModels\(\[uploadedModel, \.\.\.models\]\);/g, replacement: 'setModels([uploadedModel, ...models]);' },
  { pattern: /setModels\(\[newModel, \.\.\.models\]\);/g, replacement: 'setModels([newModel, ...models]);' },
  
  // Fix object method calls
  { pattern: /clearCache\('all-models'\);/g, replacement: 'clearCache(\'all-models\');' },
  { pattern: /clearCache\('statistics'\);/g, replacement: 'clearCache(\'statistics\');' },
  
  // Fix window operations
  { pattern: /window\.open\(model\.downloadUrl, '_blank'\);/g, replacement: 'window.open(model.downloadUrl, \'_blank\');' },
  { pattern: /window\.open\(response\.data\.downloadUrl, '_blank'\);/g, replacement: 'window.open(response.data.downloadUrl, \'_blank\');' },
  
  // Fix DOM operations
  { pattern: /document\.body\.appendChild\(link\);/g, replacement: 'document.body.appendChild(link);' },
  { pattern: /document\.body\.removeChild\(link\);/g, replacement: 'document.body.removeChild(link);' },
  { pattern: /document\.head\.appendChild\(link\);/g, replacement: 'document.head.appendChild(link);' },
  
  // Fix specific object property assignments
  { pattern: /link\.href = model\.modelUrl;/g, replacement: 'link.href = model.modelUrl;' },
  { pattern: /link\.download = model\.title \? `\$\{model\.title\.replace\(\/\\s\+\/g, '_'\)\}\.\$\{model\.format\?\.toLowerCase\(\) \|\| 'model'\}` : `model_\$\{id\}\.\$\{model\.format\?\.toLowerCase\(\) \|\| 'model'\}`;/g, replacement: 'link.download = model.title ? `${model.title.replace(/\\s+/g, \'_\')}.${model.format?.toLowerCase() || \'model\'}` : `model_${id}.${model.format?.toLowerCase() || \'model\'}`;' },
  { pattern: /link\.target = '_blank';/g, replacement: 'link.target = \'_blank\';' },
  { pattern: /link\.rel = 'preconnect';/g, replacement: 'link.rel = \'preconnect\';' },
  
  // Fix Promise operations
  { pattern: /Promise\.all\(\[/g, replacement: 'Promise.all([' },
  
  // Fix service calls
  { pattern: /realDataService\.getAllModels\(\),/g, replacement: 'realDataService.getAllModels(),' },
  { pattern: /realDataService\.getFeaturedModels\(\),/g, replacement: 'realDataService.getFeaturedModels(),' },
  { pattern: /realDataService\.getPopularModels\(\),/g, replacement: 'realDataService.getPopularModels(),' },
  { pattern: /realDataService\.getRecentModels\(\)/g, replacement: 'realDataService.getRecentModels()' },
  { pattern: /realDataService\.getCategories\(\);/g, replacement: 'realDataService.getCategories();' },
  
  // Fix MongoDB service calls
  { pattern: /mongoService\.getModelById\(id\);/g, replacement: 'mongoService.getModelById(id);' },
  { pattern: /mongoService\.searchModels\(searchParams\);/g, replacement: 'mongoService.searchModels(searchParams);' },
  { pattern: /mongoService\.createModel\(formData\);/g, replacement: 'mongoService.createModel(formData);' },
  { pattern: /mongoService\.updateModel\(id, \{/g, replacement: 'mongoService.updateModel(id, {' },
  { pattern: /mongoService\.getStatistics\(\);/g, replacement: 'mongoService.getStatistics();' },
  
  // Fix API service calls
  { pattern: /apiService\.models\.getById\(id\);/g, replacement: 'apiService.models.getById(id);' },
  { pattern: /apiService\.search\.advanced\(\{ query, \.\.\.filters \}\);/g, replacement: 'apiService.search.advanced({ query, ...filters });' },
  { pattern: /apiService\.upload\.getSignedUrl\(/g, replacement: 'apiService.upload.getSignedUrl(' },
  { pattern: /apiService\.models\.create\(formData\);/g, replacement: 'apiService.models.create(formData);' },
  { pattern: /apiService\.models\.getDownloadUrl\(id\);/g, replacement: 'apiService.models.getDownloadUrl(id);' },
  { pattern: /apiService\.models\.download\(id\);/g, replacement: 'apiService.models.download(id);' },
  { pattern: /apiService\.statistics\.getSiteStats\(\);/g, replacement: 'apiService.statistics.getSiteStats();' },
  
  // Fix fetch operations
  { pattern: /fetch\(signedUrlResponse\.data\.signedUrl, \{/g, replacement: 'fetch(signedUrlResponse.data.signedUrl, {' },
  
  // Fix FormData operations
  { pattern: /formData\.append\(key, value\);/g, replacement: 'formData.append(key, value);' },
  { pattern: /formData\.append\(key, file\);/g, replacement: 'formData.append(key, file);' },
  { pattern: /formData\.append\('userId', currentUser\.id\);/g, replacement: 'formData.append(\'userId\', currentUser.id);' },
  { pattern: /formData\.append\('createdAt', new Date\(\)\.toISOString\(\)\);/g, replacement: 'formData.append(\'createdAt\', new Date().toISOString());' },
  { pattern: /formData\.append\('modelUrl', signedUrlResponse\.data\.fileUrl\);/g, replacement: 'formData.append(\'modelUrl\', signedUrlResponse.data.fileUrl);' },
  
  // Fix error handling
  { pattern: /throw new Error\('Model not found'\);/g, replacement: 'throw new Error(\'Model not found\');' },
  { pattern: /throw new Error\('Failed to upload model'\);/g, replacement: 'throw new Error(\'Failed to upload model\');' },
  { pattern: /throw new Error\('Failed to download model'\);/g, replacement: 'throw new Error(\'Failed to download model\');' },
  { pattern: /throw apiErr;/g, replacement: 'throw apiErr;' },
  { pattern: /throw err;/g, replacement: 'throw err;' }
];

function fixSyntaxInFile(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let fixCount = 0;
    
    syntaxFixes.forEach(fix => {
      const matches = content.match(fix.pattern);
      if (matches) {
        content = content.replace(fix.pattern, fix.replacement);
        fixCount += matches.length;
      }
    });
    
    if (fixCount > 0) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`✅ Fixed ${fixCount} syntax errors in ${filePath}`);
    }
    
    return fixCount;
  } catch (error) {
    console.error(`❌ Error processing ${filePath}:`, error.message);
    return 0;
  }
}

function processDirectory(dirPath) {
  let totalFixes = 0;
  let filesProcessed = 0;
  
  try {
    const items = fs.readdirSync(dirPath);
    
    for (const item of items) {
      const fullPath = path.join(dirPath, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory()) {
        // Skip node_modules and other unnecessary directories
        if (!['node_modules', '.git', 'dist', 'build', '.next'].includes(item)) {
          const subResult = processDirectory(fullPath);
          totalFixes += subResult.totalFixes;
          filesProcessed += subResult.filesProcessed;
        }
      } else if (stat.isFile()) {
        // Process JavaScript, TypeScript, and JSX files
        if (/\.(js|jsx|ts|tsx)$/.test(item)) {
          const fixes = fixSyntaxInFile(fullPath);
          totalFixes += fixes;
          filesProcessed++;
        }
      }
    }
  } catch (error) {
    console.error(`❌ Error reading directory ${dirPath}:`, error.message);
  }
  
  return { totalFixes, filesProcessed };
}

// Main execution
console.log('🚀 Starting Ultimate Syntax Fix...\n');

const startTime = Date.now();
const result = processDirectory('./src');
const endTime = Date.now();

console.log('\n📊 ULTIMATE SYNTAX FIX RESULTS:');
console.log(`✅ Files processed: ${result.filesProcessed}`);
console.log(`🔧 Total syntax errors fixed: ${result.totalFixes}`);
console.log(`⏱️ Time taken: ${(endTime - startTime) / 1000}s`);

if (result.totalFixes > 0) {
  console.log('\n🎉 Syntax fixes completed successfully!');
} else {
  console.log('\n✨ No syntax errors found - code is clean!');
}
