import <PERSON><PERSON>hecker from '../utils/linkChecker.js';
import WarehouseScraper from '../utils/warehouseScraper.js';
import Model from '../models/Model.js';
import path from 'path';
import fs from 'fs';

const linkChecker = new LinkChecker();
const warehouseScraper = new WarehouseScraper();

// @desc    Smart download with backup links
// @route   POST /api/download/smart
// @access  Public
export const smartDownload = async (req, res, next) => {
  try {
    const { backupLinks, filename, modelId } = req.body;

    if (!backupLinks || !Array.isArray(backupLinks) || backupLinks.length === 0) {
      return res.status(400).json({
        success: false,
        error: 'Backup links array is required'
      });
    }

    if (!filename) {
      return res.status(400).json({
        success: false,
        error: 'Filename is required'
      });
    }

    console.log(`🚀 Starting smart download for: ${filename}`);
    console.log(`📋 Backup links: ${backupLinks.length}`);

    // Perform smart download
    const result = await linkChecker.smartDownload(backupLinks, filename);

    // Update model download count if modelId provided
    if (modelId) {
      try {
        await Model.findByIdAndUpdate(modelId, {
          $inc: { downloads: 1 }
        });
      } catch (error) {
        console.error('Failed to update download count:', error);
      }
    }

    res.status(200).json({
      success: true,
      data: {
        message: 'File downloaded successfully',
        filename: result.filename,
        size: result.size,
        downloadUrl: result.localUrl,
        sourceUrl: result.sourceUrl,
        backupLinksChecked: result.backupLinksChecked
      }
    });

  } catch (error) {
    console.error('Smart download failed:', error);
    res.status(500).json({
      success: false,
      error: error.message || 'Download failed'
    });
  }
};

// @desc    Check link status
// @route   POST /api/download/check-links
// @access  Public
export const checkLinks = async (req, res, next) => {
  try {
    const { links } = req.body;

    if (!links || !Array.isArray(links)) {
      return res.status(400).json({
        success: false,
        error: 'Links array is required'
      });
    }

    console.log(`🔍 Checking ${links.length} links...`);

    const results = [];
    for (const link of links) {
      const isWorking = await linkChecker.checkLink(link);
      const fileInfo = isWorking ? await linkChecker.getFileInfo(link) : null;

      results.push({
        url: link,
        isWorking,
        fileInfo
      });
    }

    const workingCount = results.filter(r => r.isWorking).length;

    res.status(200).json({
      success: true,
      data: {
        totalLinks: links.length,
        workingLinks: workingCount,
        brokenLinks: links.length - workingCount,
        results
      }
    });

  } catch (error) {
    console.error('Link check failed:', error);
    res.status(500).json({
      success: false,
      error: error.message || 'Link check failed'
    });
  }
};

// @desc    Search models from our database (imported from 3D Warehouse)
// @route   GET /api/download/warehouse/search
// @access  Public
export const searchWarehouse = async (req, res, next) => {
  try {
    const { q: query, page = 1 } = req.query;

    if (!query) {
      return res.status(400).json({
        success: false,
        error: 'Search query is required'
      });
    }

    console.log(`🔍 Searching imported models: "${query}"`);

    // First, try to find existing models in our database
    const existingModels = await Model.find({
      $or: [
        { title: { $regex: query, $options: 'i' } },
        { description: { $regex: query, $options: 'i' } },
        { tags: { $in: [new RegExp(query, 'i')] } },
        { category: { $regex: query, $options: 'i' } }
      ],
      source: '3d-warehouse'
    }).limit(10).populate('createdBy', 'name');

    // If we have existing models, return them
    if (existingModels.length > 0) {
      console.log(`✅ Found ${existingModels.length} existing models`);
      return res.status(200).json({
        success: true,
        data: {
          query,
          page: parseInt(page),
          count: existingModels.length,
          models: existingModels,
          imported: false,
          message: `Found ${existingModels.length} models in library`
        }
      });
    }

    // If no existing models, import new ones
    console.log(`📥 No existing models found, importing new ones...`);
    const importedModels = await warehouseScraper.searchAndImportModels(query, parseInt(page));

    res.status(200).json({
      success: true,
      data: {
        query,
        page: parseInt(page),
        count: importedModels.length,
        models: importedModels,
        imported: true,
        message: `Successfully imported ${importedModels.length} new models`
      }
    });

  } catch (error) {
    console.error('Warehouse search failed:', error);
    res.status(500).json({
      success: false,
      error: error.message || 'Search failed'
    });
  }
};

// @desc    Get trending models from our database (imported from 3D Warehouse)
// @route   GET /api/download/warehouse/trending
// @access  Public
export const getTrendingWarehouse = async (req, res, next) => {
  try {
    const { category } = req.query;

    console.log(`🔥 Getting trending models from database${category ? ` in ${category}` : ''}`);

    // Get trending models from our database
    let query = { source: '3d-warehouse' };
    if (category && category !== 'all') {
      query.category = { $regex: category, $options: 'i' };
    }

    const models = await Model.find(query)
      .sort({ downloads: -1, rating: -1, createdAt: -1 })
      .limit(12)
      .populate('createdBy', 'name');

    // If we don't have enough models, import some
    if (models.length < 6) {
      console.log(`📥 Not enough models in database, importing more...`);
      const importedModels = await warehouseScraper.bulkImportTrendingModels(12);

      // Get updated models from database
      const updatedModels = await Model.find(query)
        .sort({ downloads: -1, rating: -1, createdAt: -1 })
        .limit(12)
        .populate('createdBy', 'name');

      return res.status(200).json({
        success: true,
        data: {
          category: category || 'all',
          count: updatedModels.length,
          models: updatedModels,
          imported: importedModels.length
        }
      });
    }

    res.status(200).json({
      success: true,
      data: {
        category: category || 'all',
        count: models.length,
        models,
        imported: 0
      }
    });

  } catch (error) {
    console.error('Failed to get trending models:', error);
    res.status(500).json({
      success: false,
      error: error.message || 'Failed to get trending models'
    });
  }
};

// @desc    Get model details from 3D Warehouse
// @route   GET /api/download/warehouse/model
// @access  Public
export const getWarehouseModelDetails = async (req, res, next) => {
  try {
    const { url } = req.query;

    if (!url) {
      return res.status(400).json({
        success: false,
        error: 'Model URL is required'
      });
    }

    console.log(`📋 Getting model details: ${url}`);

    const details = await warehouseScraper.getModelDetails(url);

    if (!details) {
      return res.status(404).json({
        success: false,
        error: 'Model not found or failed to parse'
      });
    }

    res.status(200).json({
      success: true,
      data: details
    });

  } catch (error) {
    console.error('Failed to get model details:', error);
    res.status(500).json({
      success: false,
      error: error.message || 'Failed to get model details'
    });
  }
};

// @desc    Download from 3D Warehouse
// @route   POST /api/download/warehouse/download
// @access  Public
export const downloadFromWarehouse = async (req, res, next) => {
  try {
    const { modelUrl, filename } = req.body;

    if (!modelUrl) {
      return res.status(400).json({
        success: false,
        error: 'Model URL is required'
      });
    }

    console.log(`⬇️ Downloading from 3D Warehouse: ${modelUrl}`);

    // Get direct download URL
    const downloadUrl = await warehouseScraper.getDirectDownloadUrl(modelUrl);

    if (!downloadUrl) {
      return res.status(404).json({
        success: false,
        error: 'Download URL not found'
      });
    }

    // Generate filename if not provided
    const finalFilename = filename || `warehouse_model_${Date.now()}.skp`;

    // Download through proxy
    const result = await linkChecker.proxyDownload(downloadUrl, finalFilename);

    res.status(200).json({
      success: true,
      data: {
        message: 'Model downloaded from 3D Warehouse',
        filename: result.filename,
        size: result.size,
        downloadUrl: result.localUrl,
        sourceUrl: downloadUrl,
        warehouseUrl: modelUrl
      }
    });

  } catch (error) {
    console.error('Warehouse download failed:', error);
    res.status(500).json({
      success: false,
      error: error.message || 'Download failed'
    });
  }
};

// @desc    Serve downloaded file
// @route   GET /api/download/file/:filename
// @access  Public
export const serveFile = async (req, res, next) => {
  try {
    const { filename } = req.params;
    const filePath = path.join(linkChecker.downloadDir, filename);

    if (!fs.existsSync(filePath)) {
      return res.status(404).json({
        success: false,
        error: 'File not found'
      });
    }

    const stats = fs.statSync(filePath);

    res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
    res.setHeader('Content-Length', stats.size);
    res.setHeader('Content-Type', 'application/octet-stream');

    const fileStream = fs.createReadStream(filePath);
    fileStream.pipe(res);

  } catch (error) {
    console.error('File serve failed:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to serve file'
    });
  }
};

// @desc    Bulk import trending models from 3D Warehouse
// @route   POST /api/download/warehouse/bulk-import
// @access  Private/Admin
export const bulkImportWarehouse = async (req, res, next) => {
  try {
    const { limit = 20 } = req.body;

    console.log(`🚀 Starting bulk import of ${limit} models...`);

    const importedModels = await warehouseScraper.bulkImportTrendingModels(limit);

    res.status(200).json({
      success: true,
      data: {
        message: `Successfully imported ${importedModels.length} models`,
        count: importedModels.length,
        models: importedModels
      }
    });

  } catch (error) {
    console.error('Bulk import failed:', error);
    res.status(500).json({
      success: false,
      error: error.message || 'Bulk import failed'
    });
  }
};

// @desc    Get SketchUp plugins data
// @route   GET /api/download/plugins
// @access  Public
export const getPluginsData = async (req, res, next) => {
  try {
    const pluginsData = await warehouseScraper.importSketchUpPlugins();

    res.status(200).json({
      success: true,
      data: {
        count: pluginsData.length,
        plugins: pluginsData
      }
    });

  } catch (error) {
    console.error('Failed to get plugins data:', error);
    res.status(500).json({
      success: false,
      error: error.message || 'Failed to get plugins data'
    });
  }
};

// @desc    Cleanup old files
// @route   POST /api/download/cleanup
// @access  Private/Admin
export const cleanupFiles = async (req, res, next) => {
  try {
    await linkChecker.cleanupOldFiles();

    res.status(200).json({
      success: true,
      data: {
        message: 'Cleanup completed'
      }
    });

  } catch (error) {
    console.error('Cleanup failed:', error);
    res.status(500).json({
      success: false,
      error: 'Cleanup failed'
    });
  }
};
