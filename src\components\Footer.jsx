import React from 'react';
import { Link } from 'react-router-dom';
import { FiFacebook, FiTwitter, FiInstagram, FiYoutube, FiMail, FiPhone, FiMapPin } from 'react-icons/fi';
import { motion } from 'framer-motion';
import Newsletter from './Newsletter';

const Footer = () => {
  const currentYear = new Date().getFullYear();

  const footerLinks = {
    company: [
      { name: 'About Us', href: '/about' },
      { name: 'Contact', href: '/contact' },
      { name: 'Careers', href: '/careers' },
      { name: 'Privacy Policy', href: '/privacy' },
      { name: 'Terms of Service', href: '/terms' },
    ],
    categories: [
      { name: 'Interior Scenes', href: '/category/interior' },
      { name: 'Exterior Scenes', href: '/category/exterior' },
      { name: 'Landscape/Garden', href: '/category/landscape' },
      { name: 'Models/Objects', href: '/category/models' },
      { name: 'All Categories', href: '/categories' },
    ],
    support: [
      { name: 'Help Center', href: '/help' },
      { name: 'FAQ', href: '/faq' },
      { name: 'Tutorials', href: '/tutorials' },
      { name: 'Pricing', href: '/pricing' },
      { name: 'Report an Issue', href: '/report' },
    ],
  };

  const socialLinks = [
    { name: 'Facebook', icon: <FiFacebook />, href: 'https://facebook.com' },
    { name: 'Twitter', icon: <FiTwitter />, href: 'https://twitter.com' },
    { name: 'Instagram', icon: <FiInstagram />, href: 'https://instagram.com' },
    { name: 'YouTube', icon: <FiYoutube />, href: 'https://youtube.com' },
  ];

  return (
    <footer className="relative bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 text-white pt-20 pb-8 overflow-hidden">
      {/* Animated Background */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute top-10 left-10 w-40 h-40 bg-blue-400 rounded-full blur-3xl animate-float"></div>
        <div className="absolute bottom-10 right-10 w-32 h-32 bg-purple-400 rounded-full blur-2xl animate-float" style={{animationDelay: '2s'}}></div>
        <div className="absolute top-1/2 left-1/3 w-24 h-24 bg-pink-400 rounded-full blur-xl animate-float" style={{animationDelay: '4s'}}></div>
      </div>

      {/* Grid Pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="w-full h-full" style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.4'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
          backgroundSize: '60px 60px'
        }}></div>
      </div>

      <div className="container mx-auto px-4 relative z-10">
        {/* Newsletter Section */}
        <div className="max-w-4xl mx-auto mb-20">
          <Newsletter />
        </div>

        {/* Main Footer Content */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-8 mb-12">
          {/* Brand Column */}
          <div className="lg:col-span-2">
            <Link to="/" className="inline-block group">
              <div className="flex items-center space-x-3 mb-6">
                {/* Enhanced Logo */}
                <div className="relative w-14 h-14 bg-gradient-to-br from-blue-500 via-purple-500 to-indigo-600 rounded-2xl flex items-center justify-center shadow-xl group-hover:scale-110 transition-transform duration-300">
                  <span className="text-white font-black text-xl">3D</span>
                  <div className="absolute inset-0 rounded-2xl bg-gradient-to-r from-blue-400 to-purple-400 opacity-0 group-hover:opacity-20 transition-opacity duration-300 blur-sm"></div>
                </div>
                <div>
                  <h2 className="text-3xl font-black bg-gradient-to-r from-white via-blue-200 to-purple-200 bg-clip-text text-transparent font-heading">
                    3DSKETCHUP.NET
                  </h2>
                  <p className="text-blue-200 text-sm font-medium">Premium 3D Platform</p>
                </div>
              </div>
            </Link>
            <p className="text-gray-300 mb-8 max-w-md text-lg leading-relaxed">
              Nền tảng 3D models chất lượng cao hàng đầu. Trao quyền cho các nhà thiết kế và kiến trúc sư trên toàn thế giới với nội dung chuyên nghiệp.
            </p>
            <div className="flex space-x-3">
              {socialLinks.map((link, index) => {
                const colors = [
                  'from-blue-600 to-blue-700 hover:from-blue-500 hover:to-blue-600',
                  'from-sky-500 to-sky-600 hover:from-sky-400 hover:to-sky-500',
                  'from-pink-500 to-purple-600 hover:from-pink-400 hover:to-purple-500',
                  'from-red-600 to-red-700 hover:from-red-500 hover:to-red-600'
                ];
                return (
                  <a
                    key={link.name}
                    href={link.href}
                    target="_blank"
                    rel="noopener noreferrer"
                    className={`p-3 bg-gradient-to-r ${colors[index]} rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-110 group`}
                    aria-label={link.name}
                  >
                    <span className="text-white group-hover:scale-110 transition-transform block">
                      {link.icon}
                    </span>
                  </a>
                );
              })}
            </div>
          </div>

          {/* Links Columns */}
          <div className="glass-card p-6 rounded-2xl border border-white/10">
            <h3 className="text-xl font-bold mb-6 text-white flex items-center">
              <span className="w-2 h-2 bg-blue-400 rounded-full mr-3"></span>
              Company
            </h3>
            <ul className="space-y-4">
              {footerLinks.company.map((link) => (
                <li key={link.name}>
                  <Link
                    to={link.href}
                    className="text-gray-300 hover:text-white hover:translate-x-2 transition-all duration-300 flex items-center group"
                  >
                    <span className="w-1 h-1 bg-blue-400 rounded-full mr-3 opacity-0 group-hover:opacity-100 transition-opacity"></span>
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          <div className="glass-card p-6 rounded-2xl border border-white/10">
            <h3 className="text-xl font-bold mb-6 text-white flex items-center">
              <span className="w-2 h-2 bg-purple-400 rounded-full mr-3"></span>
              Categories
            </h3>
            <ul className="space-y-4">
              {footerLinks.categories.map((link) => (
                <li key={link.name}>
                  <Link
                    to={link.href}
                    className="text-gray-300 hover:text-white hover:translate-x-2 transition-all duration-300 flex items-center group"
                  >
                    <span className="w-1 h-1 bg-purple-400 rounded-full mr-3 opacity-0 group-hover:opacity-100 transition-opacity"></span>
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          <div className="glass-card p-6 rounded-2xl border border-white/10">
            <h3 className="text-xl font-bold mb-6 text-white flex items-center">
              <span className="w-2 h-2 bg-green-400 rounded-full mr-3"></span>
              Support
            </h3>
            <ul className="space-y-4">
              {footerLinks.support.map((link) => (
                <li key={link.name}>
                  <Link
                    to={link.href}
                    className="text-gray-300 hover:text-white hover:translate-x-2 transition-all duration-300 flex items-center group"
                  >
                    <span className="w-1 h-1 bg-green-400 rounded-full mr-3 opacity-0 group-hover:opacity-100 transition-opacity"></span>
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>

            <div className="mt-8 p-6 glass-card rounded-2xl border border-white/10">
              <h3 className="text-xl font-bold mb-6 text-white flex items-center">
                <span className="w-2 h-2 bg-yellow-400 rounded-full mr-3"></span>
                Contact
              </h3>
              <ul className="space-y-4">
                <li className="flex items-center group">
                  <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-500 rounded-xl flex items-center justify-center mr-4 group-hover:scale-110 transition-transform">
                    <FiMail className="text-white" />
                  </div>
                  <a href="mailto:<EMAIL>" className="text-gray-300 hover:text-white transition-colors">
                    <EMAIL>
                  </a>
                </li>
                <li className="flex items-center group">
                  <div className="w-10 h-10 bg-gradient-to-r from-green-500 to-teal-500 rounded-xl flex items-center justify-center mr-4 group-hover:scale-110 transition-transform">
                    <FiPhone className="text-white" />
                  </div>
                  <a href="tel:+1234567890" className="text-gray-300 hover:text-white transition-colors">
                    +1 (234) 567-890
                  </a>
                </li>
              </ul>
            </div>
          </div>
        </div>

        {/* Bottom Bar */}
        <div className="border-t border-white/20 pt-8 mt-12">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="text-center md:text-left mb-4 md:mb-0">
              <p className="text-gray-300 text-lg font-medium">
                &copy; {currentYear} <span className="bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent font-bold">3DSKETCHUP.NET</span>. All rights reserved.
              </p>
              <p className="text-gray-400 text-sm mt-1">
                Crafted with ❤️ for the 3D community
              </p>
            </div>
            <div className="flex flex-wrap justify-center gap-4 text-sm">
              <Link to="/privacy" className="text-gray-400 hover:text-white transition-colors px-3 py-1 rounded-lg hover:bg-white/10">
                Privacy Policy
              </Link>
              <Link to="/terms" className="text-gray-400 hover:text-white transition-colors px-3 py-1 rounded-lg hover:bg-white/10">
                Terms of Service
              </Link>
              <Link to="/cookies" className="text-gray-400 hover:text-white transition-colors px-3 py-1 rounded-lg hover:bg-white/10">
                Cookie Policy
              </Link>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
