import mongoose from 'mongoose';

const AIAnalysisSchema = new mongoose.Schema({
  model: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Model',
    required: true
  },
  analysisType: {
    type: String,
    enum: ['geometry', 'optimization', 'quality', 'compatibility', 'performance'],
    required: true
  },
  status: {
    type: String,
    enum: ['pending', 'processing', 'completed', 'failed'],
    default: 'pending'
  },
  results: {
    score: {
      type: Number,
      min: 0,
      max: 100
    },
    issues: [{
      type: {
        type: String,
        enum: ['error', 'warning', 'info', 'suggestion']
      },
      category: {
        type: String,
        enum: ['geometry', 'topology', 'materials', 'textures', 'performance', 'compatibility']
      },
      description: String,
      severity: {
        type: String,
        enum: ['critical', 'high', 'medium', 'low']
      },
      suggestion: String,
      autoFixAvailable: {
        type: Boolean,
        default: false
      }
    }],
    optimizations: [{
      type: String,
      description: String,
      impact: String,
      difficulty: {
        type: String,
        enum: ['easy', 'medium', 'hard']
      }
    }],
    compatibility: [{
      software: String,
      version: String,
      compatible: Boolean,
      notes: String
    }],
    recommendations: [{
      category: String,
      suggestion: String,
      reason: String,
      priority: {
        type: String,
        enum: ['high', 'medium', 'low']
      }
    }]
  },
  aiModel: {
    type: String,
    default: 'gemini-pro'
  },
  processingTime: {
    type: Number // in milliseconds
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

// Index for efficient queries
AIAnalysisSchema.index({ model: 1, analysisType: 1 });
AIAnalysisSchema.index({ status: 1 });
AIAnalysisSchema.index({ createdAt: -1 });

// Update timestamps pre-save
AIAnalysisSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  next();
});

// Static method to get latest analysis for a model
AIAnalysisSchema.statics.getLatestAnalysis = async function(modelId, analysisType) {
  return await this.findOne({
    model: modelId,
    analysisType: analysisType,
    status: 'completed'
  }).sort({ createdAt: -1 });
};

// Static method to get model health score
AIAnalysisSchema.statics.getModelHealthScore = async function(modelId) {
  const analyses = await this.find({
    model: modelId,
    status: 'completed'
  }).sort({ createdAt: -1 }).limit(5);

  if (analyses.length === 0) return null;

  const totalScore = analyses.reduce((sum, analysis) => sum + (analysis.results.score || 0), 0);
  return Math.round(totalScore / analyses.length);
};

export default mongoose.model('AIAnalysis', AIAnalysisSchema);
