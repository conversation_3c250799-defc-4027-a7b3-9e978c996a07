import fs from 'fs';
import path from 'path';
import axios from 'axios';
import { promisify } from 'util';
import { pipeline } from 'stream';
import { createWriteStream } from 'fs';
import { fileTypeFromBuffer } from 'file-type';

// Promisify pipeline for async/await usage
const streamPipeline = promisify(pipeline);

/**
 * Download a file from a URL and save it to the specified path
 * 
 * @param {string} url - URL of the file to download
 * @param {string} outputPath - Path where the file should be saved
 * @param {Object} options - Additional options
 * @param {Function} options.onProgress - Callback for download progress
 * @param {number} options.timeout - Timeout in milliseconds
 * @returns {Promise<Object>} Object containing file information
 */
export const downloadFile = async (url, outputPath, options = {}) => {
  const { onProgress, timeout = 30000 } = options;
  
  try {
    // Create directory if it doesn't exist
    const dir = path.dirname(outputPath);
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }
    
    // Download file with progress tracking
    const response = await axios({
      method: 'GET',
      url,
      responseType: 'stream',
      timeout,
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
      }
    });
    
    // Get content length for progress calculation
    const contentLength = parseInt(response.headers['content-length'], 10);
    let downloadedBytes = 0;
    
    // Create write stream
    const writer = createWriteStream(outputPath);
    
    // Track download progress
    if (onProgress && !isNaN(contentLength)) {
      response.data.on('data', (chunk) => {
        downloadedBytes += chunk.length;
        const progress = Math.round((downloadedBytes / contentLength) * 100);
        onProgress(progress, downloadedBytes, contentLength);
      });
    }
    
    // Pipe response to file
    await streamPipeline(response.data, writer);
    
    // Get file size
    const stats = fs.statSync(outputPath);
    const fileSize = stats.size;
    
    // Determine file type
    const fileBuffer = fs.readFileSync(outputPath, { length: 4100 });
    const fileType = await fileTypeFromBuffer(fileBuffer);
    
    return {
      success: true,
      filePath: outputPath,
      fileSize,
      mimeType: fileType?.mime || response.headers['content-type'],
      extension: fileType?.ext || path.extname(outputPath).slice(1),
      originalUrl: url
    };
  } catch (error) {
    // Clean up partial file if download failed
    if (fs.existsSync(outputPath)) {
      fs.unlinkSync(outputPath);
    }
    
    throw {
      success: false,
      error: error.message,
      code: error.code,
      originalUrl: url
    };
  }
};

/**
 * Validate a URL and check if it's accessible
 * 
 * @param {string} url - URL to validate
 * @param {Object} options - Additional options
 * @param {number} options.timeout - Timeout in milliseconds
 * @param {string[]} options.allowedMimeTypes - List of allowed MIME types
 * @returns {Promise<Object>} Validation result
 */
export const validateUrl = async (url, options = {}) => {
  const { timeout = 5000, allowedMimeTypes = [] } = options;
  
  try {
    // Check if URL is valid
    new URL(url);
    
    // Send HEAD request to check if URL is accessible
    const response = await axios({
      method: 'HEAD',
      url,
      timeout,
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
      },
      maxRedirects: 5
    });
    
    // Check if response is successful
    if (response.status < 200 || response.status >= 300) {
      return {
        valid: false,
        error: `Invalid response status: ${response.status}`
      };
    }
    
    // Get content type and size
    const contentType = response.headers['content-type'];
    const contentLength = parseInt(response.headers['content-length'], 10);
    
    // Check if content type is allowed
    if (allowedMimeTypes.length > 0 && !allowedMimeTypes.some(type => contentType?.includes(type))) {
      return {
        valid: false,
        error: `Invalid content type: ${contentType}`,
        contentType
      };
    }
    
    return {
      valid: true,
      contentType,
      contentLength: isNaN(contentLength) ? null : contentLength,
      url
    };
  } catch (error) {
    return {
      valid: false,
      error: error.message,
      code: error.code,
      url
    };
  }
};

/**
 * Get file extension from URL or content type
 * 
 * @param {string} url - URL of the file
 * @param {string} contentType - Content type of the file
 * @returns {string} File extension
 */
export const getFileExtension = (url, contentType) => {
  // Try to get extension from URL
  const urlExtension = path.extname(new URL(url).pathname).slice(1).toLowerCase();
  
  if (urlExtension) {
    return urlExtension;
  }
  
  // Map content types to extensions
  const contentTypeMap = {
    'application/x-zip-compressed': 'zip',
    'application/zip': 'zip',
    'application/octet-stream': 'bin',
    'model/gltf-binary': 'glb',
    'model/gltf+json': 'gltf',
    'application/vnd.ms-pki.stl': 'stl',
    'application/sla': 'stl',
    'application/x-tgif': 'obj',
    'image/jpeg': 'jpg',
    'image/png': 'png',
    'image/webp': 'webp'
  };
  
  return contentTypeMap[contentType] || 'bin';
};

export default {
  downloadFile,
  validateUrl,
  getFileExtension
};
