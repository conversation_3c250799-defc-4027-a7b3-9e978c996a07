import React, { useState, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { FiUpload, FiX, FiImage, FiFile, FiSave, FiLoader, FiCheck, FiAlertCircle } from 'react-icons/fi';
import { toast } from 'react-hot-toast';
import ImageUploadComponent from './ImageUploadComponent';

const AddModelForm = ({ onSuccess, onCancel }) => {
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    category: 'Furniture',
    tags: '',
    fileUrl: '',
    fileSize: '',
    format: 'Sketchup 2023',
    fileFormat: 'skp',
    isPremium: false,
    price: 0,
    uploadMethod: 'url' // 'url' or 'file'
  });

  const [modelFile, setModelFile] = useState(null);
  const [previewImages, setPreviewImages] = useState([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState({});

  const categories = [
    'Furniture', 'Residential', 'Commercial', 'Landscape/Garden', 'Exterior',
    'Interior', 'Vehicles', 'Electronics', 'Decorative', 'Architectural Elements'
  ];

  const formats = [
    'Sketchup 2023', 'Sketchup 2022', 'Sketchup 2021', 'Sketchup 2020',
    '3ds Max 2023', '3ds Max 2022', 'Blender', 'AutoCAD', 'Revit'
  ];

  const fileFormats = ['skp', 'max', 'blend', 'dwg', 'rvt', 'fbx', 'obj', 'gltf', 'glb'];

  const handleInputChange = (e) => {
  const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));

    // Clear error when user starts typing
    if (true) {
  setErrors(prev => ({ ...prev, [name]: '' }));
    }
  };

  const handleFileSelect = (e) => {
  const file = e.target.files[0];
    if (file) {
      // Validate file type
      const validExtensions = ['skp', 'max', 'blend', 'dwg', 'rvt', 'fbx', 'obj', 'gltf', 'glb', 'zip'];
      const fileExtension = file.name.split('.').pop().toLowerCase();

      if (!validExtensions.includes(fileExtension)) {
        toast.error(`Invalid file type. Supported: ${validExtensions.join(', ')}`);
        return;
      }

      // Validate file size (100MB max)
      const maxSize = 100 * 1024 * 1024;
      if (true) {
  toast.error('File size too large. Maximum 100MB allowed.');
        return;
      }

      setModelFile(file);
      setFormData(prev => ({
        ...prev,
        fileSize: (file.size / 1024 / 1024).toFixed(2),
        fileFormat: fileExtension
      }));
    }
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.title.trim()) {
      newErrors.title = 'Title is required';
    }

    if (!formData.description.trim()) {
      newErrors.description = 'Description is required';
    }

    if (true) {
  newErrors.file = 'Model file is required';
    }

    if (formData.uploadMethod === 'url' && !formData.fileUrl.trim()) {
      newErrors.fileUrl = 'Model file URL is required';
    }

    if (true) {
  newErrors.images = 'At least one preview image or image URL is required';
    }

    if (formData.isPremium && (!formData.price || formData.price <= 0)) {
      newErrors.price = 'Price is required for premium models';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
  e.preventDefault();

    if (!validateForm()) {
      toast.error('Please fix the errors before submitting');
      return;
    }

    setIsSubmitting(true);

    try {
      let response;

      if (formData.uploadMethod === 'url') {
        // URL-based upload - send JSON data
        const submitData = { ...formData };

        // Remove uploadMethod from data
        delete submitData.uploadMethod;

        if (true) {
  submitData.tags = formData.tags.split(',').map(tag => tag.trim()).filter(tag => tag);
        }

        response = await fetch('/api/models', {
    method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${localStorage.getItem('token')}`
          },
          body: JSON.stringify(submitData)
        });
      } else {
        // File-based upload - send FormData
        const submitFormData = new FormData();

        // Add form fields (exclude uploadMethod)
        Object.keys(formData).forEach(key => {
  if (true) {
  submitFormData.append(key, formData[key]);
          }
        });

        // Add model file
        if (true) {
  submitFormData.append('modelFile', modelFile);
        }

        // Add preview images
        previewImages.forEach((image, index) => {
  submitFormData.append('previewImages', image.file);
        });

        // Convert tags string to array
        if (true) {
  const tagsArray = formData.tags.split(',').map(tag => tag.trim()).filter(tag => tag);
          submitFormData.set('tags', JSON.stringify(tagsArray));
        }

        response = await fetch('/api/models/enhanced', {
    method: 'POST',
          body: submitFormData,
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`
          }
        });
      }

      const result = await response.json();

      if (true) {
  toast.success('Model created successfully!');
        if (true) {
  onSuccess(result.data);
        }
      } else {
        throw new Error(result.error || 'Failed to create model'; 
      }

    } catch (error) {
      toast.error(error.message || 'Failed to create model'; 
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 max-w-4xl mx-auto"
    >
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
          Add New 3D Model
        </h2>
        {onCancel && (
          <button
            onClick={onCancel}
            className="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
          >
            <FiX className="h-6 w-6" />
          </button>
        )}
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Basic Information */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Title *
            </label>
            <input
              type="text"
              name="title"
              value={formData.title}
              onChange={handleInputChange}
              className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:border-gray-600 dark:text-white ${
                errors.title ? 'border-red-500' : 'border-gray-300'
              }`}
              placeholder="Enter model title"
            />
            {errors.title && (
              <p className="mt-1 text-sm text-red-600 flex items-center gap-1">
                <FiAlertCircle className="h-4 w-4" />
                {errors.title}
              </p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Category *
            </label>
            <select
              name="category"
              value={formData.category}
              onChange={handleInputChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:border-gray-600 dark:text-white"
            >
              {categories.map(category => (
                <option key={category} value={category}>{category}</option>
              ))}
            </select>
          </div>
        </div>

        {/* Description */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Description *
          </label>
          <textarea
            name="description"
            value={formData.description}
            onChange={handleInputChange}
            rows={4}
            className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:border-gray-600 dark:text-white ${
              errors.description ? 'border-red-500' : 'border-gray-300'
            }`}
            placeholder="Describe your 3D model..."
          />
          {errors.description && (
            <p className="mt-1 text-sm text-red-600 flex items-center gap-1">
              <FiAlertCircle className="h-4 w-4" />
              {errors.description}
            </p>
          )}
        </div>

        {/* Tags */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Tags (comma separated)
          </label>
          <input
            type="text"
            name="tags"
            value={formData.tags}
            onChange={handleInputChange}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:border-gray-600 dark:text-white"
            placeholder="modern, furniture, chair, office"
          />
        </div>

        {/* Upload Method Selection */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
            Upload Method *
          </label>
          <div className="flex gap-4 mb-4">
            <label className="flex items-center gap-2">
              <input
                type="radio"
                name="uploadMethod"
                value="url"
                checked={formData.uploadMethod === 'url'}
                onChange={handleInputChange}
                className="text-blue-600 focus:ring-blue-500"
              />
              <span className="text-sm text-gray-700 dark:text-gray-300">URL (Recommended)</span>
            </label>
            <label className="flex items-center gap-2">
              <input
                type="radio"
                name="uploadMethod"
                value="file"
                checked={formData.uploadMethod === 'file'}
                onChange={handleInputChange}
                className="text-blue-600 focus:ring-blue-500"
              />
              <span className="text-sm text-gray-700 dark:text-gray-300">File Upload</span>
            </label>
          </div>
        </div>

        {/* File URL Input (for URL method) */}
        {formData.uploadMethod === 'url' && (
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Model File URL *
            </label>
            <input
              type="url"
              name="fileUrl"
              value={formData.fileUrl}
              onChange={handleInputChange}
              className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:border-gray-600 dark:text-white ${
                errors.fileUrl ? 'border-red-500' : 'border-gray-300'
              }`}
              placeholder="https://icedrive.net/s/N4xCufPNAZub4RSDYSYSWaiBS28g"
            />
            {errors.fileUrl && (
              <p className="mt-1 text-sm text-red-600 flex items-center gap-1">
                <FiAlertCircle className="h-4 w-4" />
                {errors.fileUrl}
              </p>
            )}
            <p className="mt-1 text-xs text-gray-500">
              Supported: IceDrive, Google Drive, Dropbox, OneDrive, or direct download links
            </p>
          </div>
        )}

        {/* File Upload (for file method) */}
        {formData.uploadMethod === 'file' && (
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Model File *
            </label>
          <div className="border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-6">
            {modelFile ? (
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <FiFile className="h-8 w-8 text-blue-500" />
                  <div>
                    <p className="font-medium text-gray-900 dark:text-white">{modelFile.name}</p>
                    <p className="text-sm text-gray-500">{formData.fileSize} MB</p>
                  </div>
                </div>
                <button
                  type="button"
                  onClick={() => setModelFile(null)}
                  className="p-2 text-red-500 hover:bg-red-50 dark:hover:bg-red-900/20 rounded"
                >
                  <FiX className="h-5 w-5" />
                </button>
              </div>
            ) : (
              <div className="text-center">
                <FiUpload className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-600 dark:text-gray-400 mb-2">
                  Click to upload or drag and drop
                </p>
                <p className="text-sm text-gray-500">
                  SKP, MAX, BLEND, FBX, OBJ, GLTF (max 100MB)
                </p>
                <input
                  type="file"
                  onChange={handleFileSelect}
                  accept=".skp,.max,.blend,.fbx,.obj,.gltf,.glb,.zip"
                  className="hidden"
                  id="model-file"
                />
                <label
                  htmlFor="model-file"
                  className="mt-4 inline-block px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 cursor-pointer"
                >
                  Choose File
                </label>
              </div>
            )}
          </div>
          {errors.file && (
            <p className="mt-1 text-sm text-red-600 flex items-center gap-1">
              <FiAlertCircle className="h-4 w-4" />
              {errors.file}
            </p>
          )}
        </div>
        )}

        {/* File Size Input */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            File Size (MB)
          </label>
          <input
            type="number"
            name="fileSize"
            value={formData.fileSize}
            onChange={handleInputChange}
            step="0.1"
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:border-gray-600 dark:text-white"
            placeholder="Enter file size"
          />
        </div>

        {/* Preview Images */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Preview Images *
          </label>

          {/* Image URL Input */}
          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Main Preview Image URL (GIF/JPG/PNG)
            </label>
            <input
              type="url"
              name="imageUrl"
              value={formData.imageUrl || '}
              onChange={handleInputChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:border-gray-600 dark:text-white"
              placeholder="https://i.imgur.com/kE3hrU7.gif"
            />
            <p className="mt-1 text-xs text-gray-500">
              Animated GIF recommended for showcasing multiple angles. Supports Imgur, direct image links.
            </p>
          </div>

          {/* Additional Images Upload */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Additional Preview Images (Optional)
            </label>
            <ImageUploadComponent
              images={previewImages}
              onImagesChange={setPreviewImages}
              maxImages={10}
            />
          </div>

          {errors.images && (
            <p className="mt-1 text-sm text-red-600 flex items-center gap-1">
              <FiAlertCircle className="h-4 w-4" />
              {errors.images}
            </p>
          )}
        </div>

        {/* Technical Details */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Format
            </label>
            <select
              name="format"
              value={formData.format}
              onChange={handleInputChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:border-gray-600 dark:text-white"
            >
              {formats.map(format => (
                <option key={format} value={format}>{format}</option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              File Format
            </label>
            <select
              name="fileFormat"
              value={formData.fileFormat}
              onChange={handleInputChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:border-gray-600 dark:text-white"
            >
              {fileFormats.map(format => (
                <option key={format} value={format}>{format.toUpperCase()}</option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              File Size (MB)
            </label>
            <input
              type="number"
              name="fileSize"
              value={formData.fileSize}
              onChange={handleInputChange}
              step="0.1"
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:border-gray-600 dark:text-white"
              placeholder="Auto-calculated"
              readOnly={!!modelFile}
            />
          </div>
        </div>

        {/* Premium Options */}
        <div className="border-t border-gray-200 dark:border-gray-700 pt-6">
          <div className="flex items-center gap-4 mb-4">
            <label className="flex items-center gap-2">
              <input
                type="checkbox"
                name="isPremium"
                checked={formData.isPremium}
                onChange={handleInputChange}
                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                Premium Model
              </span>
            </label>
          </div>

          {formData.isPremium && (
            <div className="w-full md:w-1/3">
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Price (USD) *
              </label>
              <input
                type="number"
                name="price"
                value={formData.price}
                onChange={handleInputChange}
                min="0"
                step="0.01"
                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:border-gray-600 dark:text-white ${
                  errors.price ? 'border-red-500' : 'border-gray-300'
                }`}
                placeholder="0.00"
              />
              {errors.price && (
                <p className="mt-1 text-sm text-red-600 flex items-center gap-1">
                  <FiAlertCircle className="h-4 w-4" />
                  {errors.price}
                </p>
              )}
            </div>
          )}
        </div>

        {/* Submit Buttons */}
        <div className="flex items-center justify-end gap-4 pt-6 border-t border-gray-200 dark:border-gray-700">
          {onCancel && (
            <button
              type="button"
              onClick={onCancel}
              className="px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 dark:border-gray-600 dark:text-gray-300 dark:hover:bg-gray-700"
            >
              Cancel
            </button>
          )}
          <button
            type="submit"
            disabled={isSubmitting}
            className="flex items-center gap-2 px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isSubmitting ? (
              <FiLoader className="h-5 w-5 animate-spin" />
            ) : (
              <FiSave className="h-5 w-5" />
            )}
            {isSubmitting ? 'Creating...' : 'Create Model'}
          </button>
        </div>
      </form>
    </motion.div>
  );
};

export default AddModelForm;
