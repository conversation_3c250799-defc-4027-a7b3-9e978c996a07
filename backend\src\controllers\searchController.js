import Model from '../models/Model.js';
import Category from '../models/Category.js';

// @desc    Search models
// @route   GET /api/search?q=query
// @access  Public
export const searchModels = async (req, res, next) => {
  try {
    const { q, category, format, year, isPremium, page = 1, limit = 12 } = req.query;

    // Build search query
    let searchQuery = {};

    // Text search
    if (q) {
      searchQuery.$or = [
        { title: { $regex: q, $options: 'i' } },
        { description: { $regex: q, $options: 'i' } },
        { tags: { $in: [new RegExp(q, 'i')] } },
        { category: { $regex: q, $options: 'i' } },
        { subcategory: { $regex: q, $options: 'i' } }
      ];
    }

    // Category filter
    if (category) {
      searchQuery.category = category;
    }

    // Format filter
    if (format) {
      searchQuery.format = { $regex: format, $options: 'i' };
    }

    // Year filter
    if (year) {
      searchQuery.year = year;
    }

    // Premium filter
    if (isPremium !== undefined) {
      searchQuery.isPremium = isPremium === 'true';
    }

    // Pagination
    const pageNum = parseInt(page, 10) || 1;
    const limitNum = parseInt(limit, 10) || 12;
    const startIndex = (pageNum - 1) * limitNum;

    // Execute search
    const models = await Model.find(searchQuery)
      .sort('-createdAt')
      .limit(limitNum)
      .skip(startIndex)
      .populate('createdBy', 'name');

    // Get total count
    const total = await Model.countDocuments(searchQuery);

    // Pagination info
    const pagination = {
      current: pageNum,
      total: Math.ceil(total / limitNum),
      hasNext: pageNum < Math.ceil(total / limitNum),
      hasPrev: pageNum > 1
    };

    res.status(200).json({
      success: true,
      count: models.length,
      total,
      pagination,
      data: models
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Advanced search models
// @route   GET /api/search/advanced
// @access  Public
export const advancedSearch = async (req, res, next) => {
  try {
    const {
      query,
      category,
      subcategory,
      format,
      year,
      isPremium,
      minRating,
      maxRating,
      minDownloads,
      maxDownloads,
      sortBy = 'createdAt',
      sortOrder = 'desc',
      page = 1,
      limit = 12
    } = req.query;

    // Build search query
    let searchQuery = {};

    // Text search
    if (query) {
      searchQuery.$or = [
        { title: { $regex: query, $options: 'i' } },
        { description: { $regex: query, $options: 'i' } },
        { tags: { $in: [new RegExp(query, 'i')] } },
        { category: { $regex: query, $options: 'i' } },
        { subcategory: { $regex: query, $options: 'i' } }
      ];
    }

    // Category filter
    if (category) {
      searchQuery.category = category;
    }

    // Subcategory filter
    if (subcategory) {
      searchQuery.subcategory = subcategory;
    }

    // Format filter
    if (format) {
      searchQuery.format = { $regex: format, $options: 'i' };
    }

    // Year filter
    if (year) {
      searchQuery.year = year;
    }

    // Premium filter
    if (isPremium !== undefined) {
      searchQuery.isPremium = isPremium === 'true';
    }

    // Rating filter
    if (minRating || maxRating) {
      searchQuery.rating = {};
      if (minRating) searchQuery.rating.$gte = parseFloat(minRating);
      if (maxRating) searchQuery.rating.$lte = parseFloat(maxRating);
    }

    // Downloads filter
    if (minDownloads || maxDownloads) {
      searchQuery.downloads = {};
      if (minDownloads) searchQuery.downloads.$gte = parseInt(minDownloads);
      if (maxDownloads) searchQuery.downloads.$lte = parseInt(maxDownloads);
    }

    // Pagination
    const pageNum = parseInt(page, 10) || 1;
    const limitNum = parseInt(limit, 10) || 12;
    const startIndex = (pageNum - 1) * limitNum;

    // Sort options
    const sortOptions = {};
    sortOptions[sortBy] = sortOrder === 'desc' ? -1 : 1;

    // Execute search
    const models = await Model.find(searchQuery)
      .sort(sortOptions)
      .limit(limitNum)
      .skip(startIndex)
      .populate('createdBy', 'name');

    // Get total count
    const total = await Model.countDocuments(searchQuery);

    // Pagination info
    const pagination = {
      current: pageNum,
      total: Math.ceil(total / limitNum),
      hasNext: pageNum < Math.ceil(total / limitNum),
      hasPrev: pageNum > 1
    };

    res.status(200).json({
      success: true,
      count: models.length,
      total,
      pagination,
      data: models
    });
  } catch (error) {
    next(error);
  }
};
