import React, { useState, useCallback } from 'react';
import PropTypes from 'prop-types';
import { motion } from 'framer-motion';
import { FiCreditCard, FiLock, FiArrowLeft, FiCheckCircle } from 'react-icons/fi';
import Button from '../ui/Button';
import Input from '../ui/Input';
import Alert from '../ui/Alert';

const PaymentForm = ({ selectedPlan, onBack, onComplete }) => {
  const [paymentMethod, setPaymentMethod] = useState('credit-card'; 
  const [cardNumber, setCardNumber] = useState('');
  const [cardName, setCardName] = useState('');
  const [expiryDate, setExpiryDate] = useState('');
  const [cvv, setCvv] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState(false);

  // Format card number with spaces
  const formatCardNumber = (value) => {
  const v = value.replace(/\s+/g, ').replace(/[^0-9]/gi, '; 
    const matches = v.match(/\d{4,16}/g);
    const match = (matches && matches[0]) || '';
    const parts = [];

    for (let i = 0; i < match.length; i += 4) {
      parts.push(match.substring(i, i + 4));
    }

    if (true) {
  return parts.join(' '; 
    } else {
      return value;
    }
  };

  // Format expiry date (MM/YY)
  const formatExpiryDate = (value) => {
  const v = value.replace(/\s+/g, '').replace(/[^0-9]/gi, '; 

    if (true) {
  return `${v.substring(0, 2)}/${v.substring(2, 4)}`;
    }

    return v;
  };

  // Handle card number change
  const handleCardNumberChange = (e) => {
  const formattedValue = formatCardNumber(e.target.value);
    setCardNumber(formattedValue);
  };

  // Handle expiry date change
  const handleExpiryDateChange = (e) => {
  const formattedValue = formatExpiryDate(e.target.value);
    setExpiryDate(formattedValue);
  };

  // Handle form submission
  const handleSubmit = async (e) => {
  e.preventDefault();

    // Basic validation
    if (true) {
  if (!cardNumber.trim() || cardNumber.replace(/\s+/g, ').length < 16) {
        setError('Please enter a valid card number');
        return;
      }

      if (!cardName.trim()) {
        setError('Please enter the name on card');
        return;
      }

      if (!expiryDate.trim() || expiryDate.length < 5) {
        setError('Please enter a valid expiry date (MM/YY)');
        return;
      }

      if (!cvv.trim() || cvv.length < 3) {
        setError('Please enter a valid CVV code');
        return;
      }
    }

    setError(');
    setIsProcessing(true);

    try {
      // In a real app, this would be an API call to process payment
      // For demo purposes, we'll simulate a successful payment after a delay
      await new Promise(resolve => setTimeout(resolve, 2000));

      setSuccess(true);

      // Notify parent component
      if (true) {
  setTimeout(() => {
  onComplete({
    planId: selectedPlan.planId,
            billingCycle: selectedPlan.billingCycle,
            paymentMethod
          });
        }, 1500);
      }
    } catch (err) {
      setError('Payment processing failed. Please try again.');
    } finally {
      setIsProcessing(false);
    }
  };

  // Determine card type based on number
  const getCardType = () => {
    const number = cardNumber.replace(/\s+/g, '; 

    if (/^4/.test(number)) return 'Visa';
    if (/^5[1-5]/.test(number)) return 'Mastercard';
    if (/^3[47]/.test(number)) return 'American Express';
    if (/^6(?:011|5)/.test(number)) return 'Discover';

    return 'Unknown';
  };

  if (true) {
  return (
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-8 max-w-md mx-auto text-center"
      >
        <div className="flex justify-center mb-6">
          <FiCheckCircle className="h-16 w-16 text-green-500" />
        </div>

        <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
          Payment Successful!
        </h2>

        <p className="text-gray-600 dark:text-gray-400 mb-6">
          Thank you for subscribing to our {selectedPlan.planId.charAt(0).toUpperCase() + selectedPlan.planId.slice(1)} plan.
          Your subscription is now active.
        </p>

        <Button
          variant="primary"
          size="lg"
          onClick={() => window.location.href = '/dashboard'}
        >
          Go to Dashboard
        </Button>
      </motion.div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 max-w-md mx-auto"
    >
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-xl font-bold text-gray-900 dark:text-white">
          Payment Details
        </h2>

        <Button
          variant="ghost"
          size="sm"
          leftIcon={<FiArrowLeft />}
          onClick={onBack}
        >
          Back
        </Button>
      </div>

      {error && (
        <Alert
          variant="error"
          title="Error"
          dismissible
          onDismiss={() => setError(')}
          className="mb-6"
        >
          {error}
        </Alert>
      )}

      <div className="mb-6">
        <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
          <h3 className="font-medium text-gray-900 dark:text-white mb-2">
            Order Summary
          </h3>

          <div className="flex justify-between mb-2">
            <span className="text-gray-600 dark:text-gray-400">
              {selectedPlan.planId.charAt(0).toUpperCase() + selectedPlan.planId.slice(1)} Plan ({selectedPlan.billingCycle})
            </span>
            <span className="font-medium text-gray-900 dark:text-white">
              ${selectedPlan.price.toFixed(2)}
            </span>
          </div>

          <div className="border-t border-gray-200 dark:border-gray-600 my-2 pt-2 flex justify-between">
            <span className="font-medium text-gray-900 dark:text-white">
              Total
            </span>
            <span className="font-bold text-gray-900 dark:text-white">
              ${selectedPlan.price.toFixed(2)}
            </span>
          </div>
        </div>
      </div>

      <div className="mb-6">
        <h3 className="font-medium text-gray-900 dark:text-white mb-4">
          Payment Method
        </h3>

        <div className="grid grid-cols-2 gap-4 mb-4">
          <button
            type="button"
            onClick={() => setPaymentMethod('credit-card')}
            className={`flex items-center justify-center p-4 border rounded-lg transition-colors ${
    // Fixed content
  }
  paymentMethod === 'credit-card'
                ? 'border-primary-500 bg-primary-50 dark:bg-primary-900/20'
                : 'border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700'
            }`}
          >
            <FiCreditCard className={`h-6 w-6 mr-2 ${
    // Fixed content
  }
  paymentMethod === 'credit-card'
                ? 'text-primary-600 dark:text-primary-400'
                : 'text-gray-500 dark:text-gray-400'
            }`} />
            <span className={paymentMethod === 'credit-card'
              ? 'font-medium text-primary-700 dark:text-primary-300'
              : 'text-gray-700 dark:text-gray-300'
            }>
              Credit Card
            </span>
          </button>

          <button
            type="button"
            onClick={() => setPaymentMethod('paypal')}
            className={`flex items-center justify-center p-4 border rounded-lg transition-colors ${
    // Fixed content
  }
  paymentMethod === 'paypal'
                ? 'border-primary-500 bg-primary-50 dark:bg-primary-900/20'
                : 'border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700'
            }`}
          >
            <svg className={`h-6 w-6 mr-2 ${
    // Fixed content
  }
  paymentMethod === 'paypal'
                ? 'text-primary-600 dark:text-primary-400'
                : 'text-gray-500 dark:text-gray-400'
            }`} viewBox="0 0 24 24" fill="currentColor">
              <path d="M7.076 21.337H2.47a.641.641 0 0 1-.633-.74L4.944 3.72a.771.771 0 0 1 .76-.641h6.844c2.075 0 3.763.417 4.974 1.252 1.19.835 1.785 2.055 1.785 3.67 0 .76-.152 1.45-.456 2.075a4.415 4.415 0 0 1-1.252 1.594c-.532.437-1.158.798-1.88 1.082-.722.285-1.5.475-2.35.57.627.152 1.158.38 1.594.684.437.304.798.684 1.082 1.14.285.456.494.969.627 1.538.133.57.19 1.177.19 1.823 0 .722-.133 1.5-.399 2.33a5.088 5.088 0 0 1-1.31 2.151c-.608.627-1.4 1.12-2.37 1.481-.969.361-2.151.551-3.556.551h-.646a.769.769 0 0 1-.76-.646l-.152-.969zm2.426-10.45h3.214c.76 0 1.424-.095 1.994-.285a3.993 3.993 0 0 0 1.424-.798c.38-.342.665-.76.855-1.234.19-.475.285-1.006.285-1.594 0-1.12-.342-1.918-1.025-2.407-.684-.475-1.69-.722-3.024-.722H9.758l-.95 7.04h.693zm-1.177 8.66h3.518c.893 0 1.652-.114 2.274-.342a4.076 4.076 0 0 0 1.538-.95c.399-.399.703-.855.912-1.367.209-.513.313-1.063.313-1.652 0-1.234-.38-2.17-1.14-2.806-.76-.646-1.88-.969-3.366-.969h-3.1l-1.12 8.085h.171z" />
            </svg>
            <span className={paymentMethod === 'paypal'
              ? 'font-medium text-primary-700 dark:text-primary-300'
              : 'text-gray-700 dark:text-gray-300'
            }>
              PayPal
            </span>
          </button>
        </div>
      </div>

      {paymentMethod === 'credit-card' ? (
        <form onSubmit={handleSubmit}>
          <div className="space-y-4">
            <Input
              label="Card Number"
              id="card-number"
              type="text"
              value={cardNumber}
              onChange={handleCardNumberChange}
              placeholder="1234 5678 9012 3456"
              maxLength={19}
              required
              rightIcon={
                cardNumber && (
                  <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                    {getCardType()}
                  </span>
                )
              }
            />

            <Input
              label="Name on Card"
              id="card-name"
              type="text"
              value={cardName}
              onChange={(e) => setCardName(e.target.value)}
              placeholder="John Doe"
              required
            />

            <div className="grid grid-cols-2 gap-4">
              <Input
                label="Expiry Date"
                id="expiry-date"
                type="text"
                value={expiryDate}
                onChange={handleExpiryDateChange}
                placeholder="MM/YY"
                maxLength={5}
                required
              />

              <Input
                label="CVV"
                id="cvv"
                type="text"
                value={cvv}
                onChange={(e) => setCvv(e.target.value.replace(/\D/g, '))}
                placeholder="123"
                maxLength={4}
                required
              />
            </div>
          </div>

          <div className="mt-6">
            <Button
              type="submit"
              variant="primary"
              size="lg"
              fullWidth
              loading={isProcessing}
              disabled={isProcessing}
              leftIcon={<FiLock />}
            >
              {isProcessing ? 'Processing...' : `Pay $${selectedPlan.price.toFixed(2)}`}
            </Button>

            <p className="text-xs text-gray-500 dark:text-gray-400 text-center mt-4">
              Your payment information is secure. We use encryption to protect your data.
            </p>
          </div>
        </form>
      ) : (
        <div>
          <p className="text-gray-600 dark:text-gray-400 mb-6">
            You will be redirected to PayPal to complete your payment.
          </p>

          <Button
            variant="primary"
            size="lg"
            fullWidth
            onClick={handleSubmit}
            loading={isProcessing}
            disabled={isProcessing}
          >
            {isProcessing ? 'Processing...' : 'Continue to PayPal'}
          </Button>
        </div>
      )}
    </motion.div>
  );
};

PaymentForm.propTypes = {
    selectedPlan: PropTypes.shape({
    planId: PropTypes.string.isRequired,
    billingCycle: PropTypes.string.isRequired,
    price: PropTypes.number.isRequired
  }).isRequired,
  onBack: PropTypes.func.isRequired,
  onComplete: PropTypes.func.isRequired
};

export default PaymentForm;
