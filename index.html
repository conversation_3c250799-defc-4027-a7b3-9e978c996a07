<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <link rel="apple-touch-icon" href="/apple-touch-icon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="description" content="3DSKETCHUP.NET - Premium 3D models for architectural visualization and design. Download high-quality 3D models, scenes, and assets." />
    <meta name="keywords" content="3D models, SketchUp, 3ds Max, architectural visualization, interior design, exterior design, landscape design, 3D assets, Blender, FBX, OBJ" />
    <meta name="author" content="3DSKETCHUP.NET" />
    <meta name="theme-color" content="#0ea5e9" />
    <meta name="robots" content="index, follow" />
    <link rel="canonical" href="https://3dsketchup.net/" />
    <title>3DSKETCHUP.NET - Premium 3D Models Repository</title>

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://3dsketchup.net/" />
    <meta property="og:title" content="3DSKETCHUP.NET - Premium 3D Models Repository" />
    <meta property="og:description" content="Download high-quality 3D models, scenes, and assets for architectural visualization and design." />
    <meta property="og:image" content="https://3dsketchup.net/og-image.jpg" />
    <meta property="og:image:width" content="1200" />
    <meta property="og:image:height" content="630" />
    <meta property="og:locale" content="en_US" />
    <meta property="og:site_name" content="3DSKETCHUP.NET" />

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image" />
    <meta property="twitter:url" content="https://3dsketchup.net/" />
    <meta property="twitter:title" content="3DSKETCHUP.NET - Premium 3D Models Repository" />
    <meta property="twitter:description" content="Download high-quality 3D models, scenes, and assets for architectural visualization and design." />
    <meta property="twitter:image" content="https://3dsketchup.net/og-image.jpg" />

    <!-- Structured Data - Organization -->
    <script type="application/ld+json">
      {
        "@context": "https://schema.org",
        "@type": "Organization",
        "name": "3DSKETCHUP.NET",
        "url": "https://3dsketchup.net",
        "logo": "https://3dsketchup.net/logo.png",
        "description": "Premium 3D models repository for architectural visualization and design.",
        "sameAs": [
          "https://facebook.com/3dsketchup",
          "https://twitter.com/3dsketchup",
          "https://instagram.com/3dsketchup"
        ]
      }
    </script>

    <!-- Structured Data - WebSite -->
    <script type="application/ld+json">
      {
        "@context": "https://schema.org",
        "@type": "WebSite",
        "name": "3DSKETCHUP.NET",
        "url": "https://3dsketchup.net",
        "potentialAction": {
          "@type": "SearchAction",
          "target": "https://3dsketchup.net/search?q={search_term_string}",
          "query-input": "required name=search_term_string"
        }
      }
    </script>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700&family=JetBrains+Mono:wght@400;500&display=swap" rel="stylesheet">

    <!-- HMR Recovery Script removed to avoid WebSocket issues -->

    <!-- Fallback styles to prevent FOUC (Flash of Unstyled Content) -->
    <style>
      .app-loading {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 100vh;
        width: 100vw;
        background-color: #f9fafb;
        position: fixed;
        top: 0;
        left: 0;
        z-index: 9999;
        transition: opacity 0.3s ease-out;
      }
      .app-loading.loaded {
        opacity: 0;
        pointer-events: none;
      }
      .app-loading-spinner {
        width: 50px;
        height: 50px;
        border: 3px solid rgba(14, 165, 233, 0.2);
        border-radius: 50%;
        border-top-color: #0ea5e9;
        animation: spin 1s ease-in-out infinite;
      }
      @keyframes spin {
        to { transform: rotate(360deg); }
      }
      .app-loading-text {
        margin-top: 20px;
        font-family: 'Inter', sans-serif;
        color: #64748b;
      }
      @media (prefers-color-scheme: dark) {
        .app-loading {
          background-color: #1e293b;
        }
        .app-loading-text {
          color: #94a3b8;
        }
      }
    </style>
  </head>
  <body class="font-sans antialiased">
    <!-- Fallback loading indicator -->
    <div id="app-loading" class="app-loading">
      <div class="app-loading-spinner"></div>
      <div class="app-loading-text">Loading 3DSKETCHUP.NET...</div>
    </div>

    <div id="root"></div>



    <!-- Preload React to ensure single instance -->
    <script>
      window.React = window.React || {};
      window.ReactDOM = window.ReactDOM || {};

      // Hide loading indicator when app is loaded
      window.addEventListener('load', function() {
        setTimeout(function() {
          const loadingEl = document.getElementById('app-loading');
          if (loadingEl) {
            loadingEl.classList.add('loaded');
            setTimeout(function() {
              loadingEl.style.display = 'none';
            }, 300);
          }
        }, 500);
      });
    </script>

    <!-- Simple fallback for loading issues -->
    <script>
      // Set a timeout to detect if the app fails to load
      const appLoadTimeout = setTimeout(function() {
        const rootEl = document.getElementById('root');
        // If the root element is empty after 5 seconds, show a message
        if (rootEl && (!rootEl.children || rootEl.children.length === 0)) {
          rootEl.innerHTML = `
            <div style="padding: 20px; text-align: center; font-family: 'Inter', sans-serif;">
              <h1 style="color: #3b82f6; font-size: 24px; margin-bottom: 16px;">3DSKETCHUP.NET</h1>
              <p style="color: #64748b; margin-bottom: 20px;">The application is taking longer than expected to load.</p>
              <button onclick="window.location.reload()" style="background: #3b82f6; color: white; border: none; padding: 10px 20px; border-radius: 4px; cursor: pointer;">
                Refresh Page
              </button>
            </div>
          `;
        }
      }, 5000);

      // Clear the timeout if the app loads successfully
      window.addEventListener('DOMContentLoaded', function() {
        if (document.getElementById('root').children.length > 0) {
          clearTimeout(appLoadTimeout);
        }
      });
    </script>

    <script type="module" src="/src/main.jsx"></script>
  </body>
</html>
