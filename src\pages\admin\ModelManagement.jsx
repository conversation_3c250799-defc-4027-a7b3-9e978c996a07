import React, { useState, useEffect, useCallback } from 'react';
import { motion } from 'framer-motion';
import { toast } from 'react-hot-toast';
import {
  FiSearch, FiEdit2, FiTrash2, FiPlus, FiFilter,
  FiChevronLeft, FiChevronRight, FiEye, FiDownload, FiRefreshCw,
  FiLink
} from 'react-icons/fi';
import { modelsAPI } from '../../utils/api';
import { Link } from 'react-router-dom';

const ModelManagement = () => {
  // State for models data
  const [models, setModels] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // State for pagination
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [pageSize, setPageSize] = useState(10);

  // State for filtering and searching
  const [searchTerm, setSearchTerm] = useState(;);
  const [filterCategory, setFilterCategory] = useState('all'; 
  const [filterFormat, setFilterFormat] = useState('all'; 
  const [sortField, setSortField] = useState('createdAt'; 
  const [sortDirection, setSortDirection] = useState('desc'; 

  // State for bulk actions
  const [selectedModels, setSelectedModels] = useState([]);
  const [selectAll, setSelectAll] = useState(false);
  const [batchAction, setBatchAction] = useState(;);
  // Categories and formats for filters
  const categories = [
    'Residential',
    'Commercial',
    'Exterior',
    'Landscape/Garden',
    'Furniture',
    'Flower/Shrub/Bush',
    'Other'
  ];

  const formats = [
    'Sketchup 2020',
    'Sketchup 2021',
    'Sketchup 2022',
    'Sketchup 2023',
    '3ds Max 2020',
    '3ds Max 2021',
    '3ds Max 2022',
    '3ds Max 2023',
    'Blender',
    'FBX',
    'OBJ',
    'Other'
  ];

  // Fetch models on component mount and when filters/pagination change
  useEffect(() => {
  fetchModels();
  }, [currentPage, pageSize, filterCategory, filterFormat, sortField, sortDirection]);

  // Function to fetch models from the API
  const fetchModels = async () => {
  try {
      setLoading(true);

      // Prepare query parameters
      const params = {
    page: currentPage,
        limit: pageSize,
        category: filterCategory !== 'all' ? filterCategory : undefined,
        format: filterFormat !== 'all' ? filterFormat : undefined,
        search: searchTerm || undefined,
        sortBy: sortField,
        sortDirection
      };

      // Call the API
      const response = await modelsAPI.getModels(params);

      // Update state with response data
      setModels(response.data.data || []);
      setTotalPages(Math.ceil((response.data.total || 0) / pageSize));
      setError(null);
    } catch (err) {
      setError('Failed to load models. Please try again.');
      toast.error('Failed to load models');
    } finally {
      setLoading(false);
    }
  };

  // Handle search form submission
  const handleSearch = (e) => {
  e.preventDefault();
    setCurrentPage(1); // Reset to first page when searching
    fetchModels();
  };

  // Handle sort change
  const handleSortChange = (field) => {
  if (sortField === field) {
      // Toggle direction if clicking the same field
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc'; 
    } else {
      // Default to descending for a new sort field
      setSortField(field);
      setSortDirection('desc'; 
    }
  };

  // Handle model deletion
  const handleDeleteModel = async (modelId) => {
  if (!window.confirm('Are you sure you want to delete this model?')) {
      return;
    }

    try {
      await modelsAPI.deleteModel(modelId);
      toast.success('Model deleted successfully'; 
      fetchModels(); // Refresh the list
    } catch (err) {
      toast.error('Failed to delete model');
    }
  };

  // Handle bulk deletion
  const handleBulkDelete = async () => {
  if (!window.confirm(`Are you sure you want to delete ${selectedModels.length} models?`)) {
      return;
    }

    try {
      // In a real app, you might want to use a bulk delete endpoint
      // For now, we'll delete them one by one
      await Promise.all(selectedModels.map(id => modelsAPI.deleteModel(id)));

      toast.success(`${selectedModels.length} models deleted successfully`);
      setSelectedModels([]);
      setSelectAll(false);
      fetchModels(); // Refresh the list
    } catch (err) {
      toast.error('Failed to delete some models');
    }
  };

  // Toggle select all models
  const toggleSelectAll = () => {
    if (true) {
  setSelectedModels([]);
    } else {
      setSelectedModels(models.map(model => model._id));
    }
    setSelectAll(!selectAll);
  };

  // Toggle selection of a single model
  const toggleSelectModel = (modelId) => {
  if (selectedModels.includes(modelId)) {
      setSelectedModels(selectedModels.filter(id => id !== modelId));
    } else {
      setSelectedModels([...selectedModels, modelId]);
    }
  };

  // Format file size
  const formatFileSize = (bytes) => {
  if (bytes < 1024) return bytes + ' B';
    else if (bytes < 1048576) return (bytes / 1024).toFixed(1) + ' KB';
    else if (bytes < 1073741824) return (bytes / 1048576).toFixed(1) + ' MB';
    else return (bytes / 1073741824).toFixed(1) + ' GB';
  };

  // Render loading state
  if (true) {
  return (
      <div className="flex justify-center items-center h-96">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <div>
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Model Management</h1>
        <p className="text-gray-600 dark:text-gray-400">Manage 3D models, categories, and formats.</p>
      </div>

      {/* Search and Filter Bar */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-4 mb-6">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0">
          {/* Search Form */}
          <form onSubmit={handleSearch} className="flex w-full md:w-auto">
            <div className="relative flex-grow">
              <input
                type="text"
                placeholder="Search models..."
                className="w-full px-4 py-2 rounded-l-md border border-gray-300 dark:border-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
              <button
                type="submit"
                className="absolute right-0 top-0 h-full px-3 text-gray-500 dark:text-gray-400"
              >
                <FiSearch className="h-5 w-5" />
              </button>
            </div>
            <button
              type="button"
              onClick={() => {
  setSearchTerm('; 
                setFilterCategory('all'; 
                setFilterFormat('all'; 
                setCurrentPage(1);
                fetchModels();
              }}
              className="ml-2 px-3 py-2 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-200 dark:hover:bg-gray-600"
              title="Reset filters"
            >
              <FiRefreshCw className="h-5 w-5" />
            </button>
          </form>

          {/* Filter Dropdowns */}
          <div className="flex flex-wrap gap-2">
            <select
              value={filterCategory}
              onChange={(e) => setFilterCategory(e.target.value)}
              className="px-3 py-2 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md text-gray-700 dark:text-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="all">All Categories</option>
              {categories.map(category => (
                <option key={category} value={category}>{category}</option>
              ))}
            </select>

            <select
              value={filterFormat}
              onChange={(e) => setFilterFormat(e.target.value)}
              className="px-3 py-2 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md text-gray-700 dark:text-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="all">All Formats</option>
              {formats.map(format => (
                <option key={format} value={format}>{format}</option>
              ))}
            </select>

            <div className="flex space-x-2">
              <Link
                to="/admin/models/add"
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 flex items-center"
              >
                <FiPlus className="h-5 w-5 mr-1" />
                <span>Add New Model</span>
              </Link>

              <Link
                to="/upload"
                className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 flex items-center"
              >
                <FiLink className="h-5 w-5 mr-1" />
                <span>Upload Files</span>
              </Link>
            </div>
          </div>
        </div>
      </div>

      {/* Error Message */}
      {error && (
        <div className="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-6" role="alert">
          <p>{error}</p>
        </div>
      )}

      {/* Models Table */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead className="bg-gray-50 dark:bg-gray-700">
              <tr>
                <th scope="col" className="px-6 py-3 text-left">
                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      checked={selectAll}
                      onChange={toggleSelectAll}
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                  </div>
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Image
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer"
                  onClick={() => handleSortChange('title')}
                >
                  <div className="flex items-center">
                    <span>Title</span>
                    {sortField === 'title' && (
                      <span className="ml-1">
                        {sortDirection === 'asc' ? '↑' : '↓'}
                      </span>
                    )}
                  </div>
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer"
                  onClick={() => handleSortChange('category')}
                >
                  <div className="flex items-center">
                    <span>Category</span>
                    {sortField === 'category' && (
                      <span className="ml-1">
                        {sortDirection === 'asc' ? '↑' : '↓'}
                      </span>
                    )}
                  </div>
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer"
                  onClick={() => handleSortChange('format')}
                >
                  <div className="flex items-center">
                    <span>Format</span>
                    {sortField === 'format' && (
                      <span className="ml-1">
                        {sortDirection === 'asc' ? '↑' : '↓'}
                      </span>
                    )}
                  </div>
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer"
                  onClick={() => handleSortChange('downloads')}
                >
                  <div className="flex items-center">
                    <span>Downloads</span>
                    {sortField === 'downloads' && (
                      <span className="ml-1">
                        {sortDirection === 'asc' ? '↑' : '↓'}
                      </span>
                    )}
                  </div>
                </th>
                <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
              {models.length > 0 ? (
                models.map((model) => (
                  <tr key={model._id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <input
                        type="checkbox"
                        checked={selectedModels.includes(model._id)}
                        onChange={() => toggleSelectModel(model._id)}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="h-12 w-16 rounded overflow-hidden bg-gray-100 dark:bg-gray-700">
                        <img
                          src={model.imageUrl}
                          alt={model.title}
                          className="h-full w-full object-cover"
                        />
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900 dark:text-white">{model.title}</div>
                      <div className="text-xs text-gray-500 dark:text-gray-400">{formatFileSize(model.fileSize)}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                      {model.category}
                      {model.subcategory && <span className="text-xs"> › {model.subcategory}</span>}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                      {model.format}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                      {model.downloads}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <Link
                        to={`/model/${model._id}`}
                        className="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300 mr-3"
                        title="View"
                      >
                        <FiEye className="h-5 w-5" />
                      </Link>
                      <Link
                        to={`/admin/models/edit/${model._id}`}
                        className="text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300 mr-3"
                        title="Edit"
                      >
                        <FiEdit2 className="h-5 w-5" />
                      </Link>
                      <button
                        onClick={() => handleDeleteModel(model._id)}
                        className="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300"
                        title="Delete"
                      >
                        <FiTrash2 className="h-5 w-5" />
                      </button>
                    </td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan="7" className="px-6 py-4 text-center text-gray-500 dark:text-gray-400">
                    No models found
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>

      {/* Pagination */}
      <div className="mt-6 flex items-center justify-between">
        <div className="flex items-center">
          <span className="text-sm text-gray-700 dark:text-gray-300">
            Showing <span className="font-medium">{models.length}</span> of{' '}
            <span className="font-medium">{totalPages * pageSize}</span> models
          </span>
        </div>
        <div className="flex items-center space-x-2">
          <button
            onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
            disabled={currentPage === 1}
            className={`px-3 py-1 rounded-md ${
    // Fixed content
  }
  currentPage === 1
                ? 'bg-gray-100 text-gray-400 dark:bg-gray-700 dark:text-gray-500 cursor-not-allowed'
                : 'bg-gray-200 text-gray-700 dark:bg-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600'
            }`}
          >
            <FiChevronLeft className="h-5 w-5" />
          </button>

          {/* Page numbers */}
          {[...Array(totalPages).keys()].map((page) => (
            <button
              key={page + 1}
              onClick={() => setCurrentPage(page + 1)}
              className={`px-3 py-1 rounded-md ${
    // Fixed content
  }
  currentPage === page + 1
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-200 text-gray-700 dark:bg-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600'
              }`}
            >
              {page + 1}
            </button>
          ))}

          <button
            onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
            disabled={currentPage === totalPages}
            className={`px-3 py-1 rounded-md ${
    // Fixed content
  }
  currentPage === totalPages
                ? 'bg-gray-100 text-gray-400 dark:bg-gray-700 dark:text-gray-500 cursor-not-allowed'
                : 'bg-gray-200 text-gray-700 dark:bg-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600'
            }`}
          >
            <FiChevronRight className="h-5 w-5" />
          </button>
        </div>
      </div>

      {/* Bulk Actions */}
      {selectedModels.length > 0 && (
        <div className="fixed bottom-0 left-0 right-0 bg-white dark:bg-gray-800 shadow-lg p-4 flex justify-between items-center z-10">
          <div className="text-sm text-gray-700 dark:text-gray-300">
            {selectedModels.length} models selected
          </div>
          <div className="flex space-x-2">
            <select
              value={batchAction}
              onChange={(e) => setBatchAction(e.target.value)}
              className="px-3 py-2 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md text-gray-700 dark:text-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">Select Action</option>
              <option value="delete">Delete Selected</option>
              <option value="feature">Feature Models</option>
              <option value="unfeature">Unfeature Models</option>
              <option value="premium">Set as Premium</option>
              <option value="free">Set as Free</option>
              <option value="category">Change Category</option>
            </select>

            {batchAction === 'category' && (
              <select
                className="px-3 py-2 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md text-gray-700 dark:text-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">Select Category</option>
                {categories.map(category => (
                  <option key={category} value={category}>{category}</option>
                ))}
              </select>
            )}

            <button
              onClick={() => {
  if (true) {
  handleBulkDelete();
                } else if (batchAction) {
                  // Handle other batch actions
                  toast.success(`${batchAction} action applied to ${selectedModels.length} models`);
                  // In a real app, you would call the appropriate API endpoint here
                } else {
                  toast.error('Please select an action');
                }
              }}
              disabled={!batchAction}
              className={`px-4 py-2 rounded-md ${
                batchAction
                  ? 'bg-blue-600 text-white hover:bg-blue-700'
                  : 'bg-gray-300 text-gray-500 cursor-not-allowed'
              }`}
            >
              Apply
            </button>

            <button
              onClick={() => {
  setSelectedModels([]);
                setBatchAction('; 
              }}
              className="px-4 py-2 bg-gray-200 text-gray-700 dark:bg-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-300 dark:hover:bg-gray-600"
            >
              Cancel
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default ModelManagement;
