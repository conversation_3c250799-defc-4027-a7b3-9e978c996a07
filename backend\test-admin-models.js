import fetch from 'node-fetch';

const testAdminModels = async () => {
  try {
    console.log('Testing Admin Models Endpoint...');

    // Login first
    const loginResponse = await fetch('http://localhost:5002/api/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'admin123456'
      })
    });

    const loginData = await loginResponse.json();
    const token = loginData.token;

    // Test admin models endpoint
    const modelsResponse = await fetch('http://localhost:5002/api/admin/models', {
      headers: { 'Authorization': `Bearer ${token}` }
    });

    console.log('Response status:', modelsResponse.status);
    
    if (modelsResponse.ok) {
      const modelsData = await modelsResponse.json();
      console.log('✅ Success! Found', modelsData.data?.length || 0, 'models');
      
      if (modelsData.data && modelsData.data.length > 0) {
        console.log('First model:', {
          id: modelsData.data[0]._id,
          title: modelsData.data[0].title,
          category: modelsData.data[0].category,
          status: modelsData.data[0].status || 'approved'
        });
      }
    } else {
      const errorText = await modelsResponse.text();
      console.log('❌ Error:', errorText);
    }

  } catch (error) {
    console.error('Error:', error);
  }
};

testAdminModels();
