import Extension from '../models/Extension.js';
import ExtensionsScraper from '../utils/extensionsScraper.js';
import ImageProcessor from '../utils/imageProcessor.js';
import VisualSimilarityService from '../utils/visualSimilarityService.js';
import RealDataScraper from '../utils/realDataScraper.js';
import ImageSearchService from '../services/imageSearchService.js';

const extensionsScraper = new ExtensionsScraper();
const imageProcessor = new ImageProcessor();
const visualSimilarityService = new VisualSimilarityService();
const realDataScraper = new RealDataScraper();
const imageSearchService = new ImageSearchService();

// @desc    Get all extensions with filtering and pagination
// @route   GET /api/extensions
// @access  Public
export const getExtensions = async (req, res, next) => {
  try {
    const {
      category,
      priceFilter,
      priceRange,
      search,
      sortBy = 'downloads',
      page = 1,
      limit = 20,
      minRating
    } = req.query;

    let query = { status: 'active' };

    // Apply filters
    if (category && category !== 'all') {
      query.category = category;
    }

    // Legacy price filter support
    if (priceFilter === 'free') {
      query.price = 'Free';
    } else if (priceFilter === 'paid') {
      query.price = { $ne: 'Free' };
    }

    // Advanced price range filter
    if (priceRange && priceRange !== 'all') {
      switch (priceRange) {
        case 'free':
          query.price = 'Free';
          break;
        case 'under50':
          query.$and = [
            { price: { $ne: 'Free' } },
            { price: { $regex: /^\$([0-4]?\d|[0-4]\d\.\d{2})$/ } }
          ];
          break;
        case '50to100':
          query.$and = [
            { price: { $ne: 'Free' } },
            { price: { $regex: /^\$([5-9]\d|100)(\.\d{2})?$/ } }
          ];
          break;
        case 'over100':
          query.$and = [
            { price: { $ne: 'Free' } },
            { price: { $regex: /^\$(1[0-9]\d|[2-9]\d\d|\d{4,})(\.\d{2})?$/ } }
          ];
          break;
      }
    }

    // Rating filter
    if (minRating) {
      query.rating = { $gte: parseFloat(minRating) };
    }

    if (search) {
      query.$or = [
        { name: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } },
        { tags: { $in: [new RegExp(search, 'i')] } },
        { developer: { $regex: search, $options: 'i' } },
        { features: { $in: [new RegExp(search, 'i')] } }
      ];
    }

    // Sort options
    const sortOptions = {};
    switch (sortBy) {
      case 'rating':
        sortOptions.rating = -1;
        break;
      case 'name':
        sortOptions.name = 1;
        break;
      case 'newest':
        sortOptions.createdAt = -1;
        break;
      case 'updated':
        sortOptions.lastUpdated = -1;
        break;
      case 'price':
        sortOptions.price = 1;
        break;
      default:
        sortOptions.downloads = -1;
    }

    const extensions = await Extension.find(query)
      .sort(sortOptions)
      .limit(parseInt(limit))
      .skip((parseInt(page) - 1) * parseInt(limit))
      .populate('createdBy', 'name');

    const total = await Extension.countDocuments(query);

    res.status(200).json({
      success: true,
      data: {
        extensions,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / parseInt(limit))
        }
      }
    });

  } catch (error) {
    console.error('Get extensions failed:', error);
    res.status(500).json({
      success: false,
      error: error.message || 'Failed to get extensions'
    });
  }
};

// @desc    Get extension by ID
// @route   GET /api/extensions/:id
// @access  Public
export const getExtension = async (req, res, next) => {
  try {
    const extension = await Extension.findById(req.params.id)
      .populate('createdBy', 'name')
      .populate('reviews.user', 'name');

    if (!extension) {
      return res.status(404).json({
        success: false,
        error: 'Extension not found'
      });
    }

    // Increment views
    extension.views += 1;
    await extension.save();

    res.status(200).json({
      success: true,
      data: extension
    });

  } catch (error) {
    console.error('Get extension failed:', error);
    res.status(500).json({
      success: false,
      error: error.message || 'Failed to get extension'
    });
  }
};

// @desc    Get extension categories
// @route   GET /api/extensions/categories
// @access  Public
export const getCategories = async (req, res, next) => {
  try {
    const categories = await extensionsScraper.getCategories();

    // Get actual counts from database
    const categoriesWithCounts = await Promise.all(
      categories.map(async (category) => {
        const count = await Extension.countDocuments({
          category: category.name,
          status: 'active'
        });
        return {
          ...category,
          count
        };
      })
    );

    res.status(200).json({
      success: true,
      data: {
        categories: categoriesWithCounts
      }
    });

  } catch (error) {
    console.error('Get categories failed:', error);
    res.status(500).json({
      success: false,
      error: error.message || 'Failed to get categories'
    });
  }
};

// @desc    Get featured extensions
// @route   GET /api/extensions/featured
// @access  Public
export const getFeaturedExtensions = async (req, res, next) => {
  try {
    const { limit = 6 } = req.query;

    const extensions = await Extension.getFeatured(parseInt(limit));

    res.status(200).json({
      success: true,
      data: {
        extensions,
        count: extensions.length
      }
    });

  } catch (error) {
    console.error('Get featured extensions failed:', error);
    res.status(500).json({
      success: false,
      error: error.message || 'Failed to get featured extensions'
    });
  }
};

// @desc    Search and import extensions from SketchUp Extensions
// @route   GET /api/extensions/search-import
// @access  Public
export const searchAndImportExtensions = async (req, res, next) => {
  try {
    const { q: query } = req.query;

    if (!query) {
      return res.status(400).json({
        success: false,
        error: 'Search query is required'
      });
    }

    console.log(`🔍 Searching and importing extensions: "${query}"`);

    // First, search existing extensions in database
    const existingExtensions = await Extension.find({
      $or: [
        { name: { $regex: query, $options: 'i' } },
        { description: { $regex: query, $options: 'i' } },
        { tags: { $in: [new RegExp(query, 'i')] } },
        { category: { $regex: query, $options: 'i' } }
      ],
      status: 'active'
    }).limit(10).populate('createdBy', 'name');

    // If we have existing extensions, return them
    if (existingExtensions.length > 0) {
      return res.status(200).json({
        success: true,
        data: {
          query,
          count: existingExtensions.length,
          extensions: existingExtensions,
          imported: false,
          message: `Found ${existingExtensions.length} extensions in library`
        }
      });
    }

    // If no existing extensions, import new ones
    const scrapedExtensions = await extensionsScraper.searchExtensions(query);
    const importedExtensions = [];

    for (const extensionData of scrapedExtensions) {
      try {
        const importedExtension = await extensionsScraper.importExtensionToDatabase(extensionData);
        if (importedExtension) {
          importedExtensions.push(importedExtension);
        }
      } catch (error) {
        console.error(`Failed to import extension: ${extensionData.name}`, error.message);
      }
    }

    res.status(200).json({
      success: true,
      data: {
        query,
        count: importedExtensions.length,
        extensions: importedExtensions,
        imported: true,
        message: `Successfully imported ${importedExtensions.length} new extensions`
      }
    });

  } catch (error) {
    console.error('Search and import failed:', error);
    res.status(500).json({
      success: false,
      error: error.message || 'Search and import failed'
    });
  }
};

// @desc    Bulk import all extensions from SketchUp Extensions
// @route   POST /api/extensions/bulk-import
// @access  Private/Admin
export const bulkImportExtensions = async (req, res, next) => {
  try {
    const { maxPerCategory = 5 } = req.body;

    console.log(`🚀 Starting bulk import of SketchUp extensions...`);
    console.log(`📊 Max per category: ${maxPerCategory}`);

    const allExtensions = await extensionsScraper.bulkScrapeAllExtensions(maxPerCategory);
    console.log(`📦 Scraped ${allExtensions.length} extensions`);

    const importedExtensions = [];

    for (const extensionData of allExtensions) {
      try {
        console.log(`📥 Importing: ${extensionData.name}`);
        const importedExtension = await extensionsScraper.importExtensionToDatabase(extensionData);
        if (importedExtension) {
          importedExtensions.push(importedExtension);
          console.log(`✅ Successfully imported: ${importedExtension.name}`);
        }
      } catch (error) {
        console.error(`❌ Failed to import extension: ${extensionData.name}`, error.message);
      }
    }

    console.log(`🎉 Import completed! Total: ${importedExtensions.length}`);

    res.status(200).json({
      success: true,
      data: {
        message: `Successfully imported ${importedExtensions.length} extensions`,
        count: importedExtensions.length,
        extensions: importedExtensions.slice(0, 10) // Return first 10 for preview
      }
    });

  } catch (error) {
    console.error('❌ Bulk import failed:', error);
    res.status(500).json({
      success: false,
      error: error.message || 'Bulk import failed'
    });
  }
};

// @desc    Download extension
// @route   POST /api/extensions/:id/download
// @access  Public
export const downloadExtension = async (req, res, next) => {
  try {
    const extension = await Extension.findById(req.params.id);

    if (!extension) {
      return res.status(404).json({
        success: false,
        error: 'Extension not found'
      });
    }

    // Increment download count
    await extension.incrementDownloads();

    res.status(200).json({
      success: true,
      data: {
        downloadUrl: extension.downloadUrl,
        backupLinks: extension.backupLinks,
        filename: `${extension.name.replace(/\s+/g, '_')}.rbz`,
        fileSize: extension.fileSize
      }
    });

  } catch (error) {
    console.error('Download extension failed:', error);
    res.status(500).json({
      success: false,
      error: error.message || 'Download failed'
    });
  }
};

// @desc    Add extension review
// @route   POST /api/extensions/:id/review
// @access  Private
export const addExtensionReview = async (req, res, next) => {
  try {
    const { rating, comment } = req.body;
    const extension = await Extension.findById(req.params.id);

    if (!extension) {
      return res.status(404).json({
        success: false,
        error: 'Extension not found'
      });
    }

    await extension.addReview(req.user.id, rating, comment);

    res.status(200).json({
      success: true,
      data: {
        message: 'Review added successfully'
      }
    });

  } catch (error) {
    console.error('Add review failed:', error);
    res.status(500).json({
      success: false,
      error: error.message || 'Failed to add review'
    });
  }
};

// @desc    Get extension statistics
// @route   GET /api/extensions/stats
// @access  Public
export const getExtensionStats = async (req, res, next) => {
  try {
    const totalExtensions = await Extension.countDocuments({ status: 'active' });
    const freeExtensions = await Extension.countDocuments({ status: 'active', price: 'Free' });
    const paidExtensions = await Extension.countDocuments({ status: 'active', price: { $ne: 'Free' } });

    const totalDownloads = await Extension.aggregate([
      { $match: { status: 'active' } },
      { $group: { _id: null, total: { $sum: '$downloads' } } }
    ]);

    const topCategories = await Extension.aggregate([
      { $match: { status: 'active' } },
      { $group: { _id: '$category', count: { $sum: 1 } } },
      { $sort: { count: -1 } },
      { $limit: 5 }
    ]);

    res.status(200).json({
      success: true,
      data: {
        totalExtensions,
        freeExtensions,
        paidExtensions,
        totalDownloads: totalDownloads[0]?.total || 0,
        topCategories
      }
    });

  } catch (error) {
    console.error('Get stats failed:', error);
    res.status(500).json({
      success: false,
      error: error.message || 'Failed to get statistics'
    });
  }
};

// @desc    Visual search for 3D models using image
// @route   POST /api/extensions/visual-search
// @access  Public
export const visualSearchModels = async (req, res, next) => {
  try {
    console.log('🔍 Visual model search request received');

    // Check if image file exists
    if (!req.files || !req.files.image) {
      return res.status(400).json({
        success: false,
        error: 'No image file provided'
      });
    }

    const imageFile = req.files.image;
    const { category = '', language = 'vi' } = req.body;

    console.log(`🔍 Processing image for visual model search: ${imageFile.name}`);

    // Validate image
    const validation = imageProcessor.validateImage(imageFile);
    if (!validation.isValid) {
      return res.status(400).json({
        success: false,
        error: validation.errors.join(', ')
      });
    }

    // Get image data - handle both buffer and temp file
    let imageData;
    if (imageFile.data && imageFile.data.length > 0) {
      imageData = imageFile.data;
    } else if (imageFile.tempFilePath) {
      const fs = await import('fs');
      imageData = await fs.promises.readFile(imageFile.tempFilePath);
    } else {
      throw new Error('No image data available');
    }

    console.log('🔍 Image data length:', imageData.length);

    // Use ImageSearchService for visual model search
    const result = await imageSearchService.analyzeImageForVisualModelSearch(imageData, category, language);

    if (!result.success) {
      return res.status(500).json({
        success: false,
        error: 'Failed to analyze image with AI',
        details: result.error
      });
    }

    console.log('✅ Visual model search completed successfully');
    console.log(`   - Total models found: ${result.data.totalFound}`);
    console.log(`   - Exact matches: ${result.data.matchLevels.exact.length}`);
    console.log(`   - Similar matches: ${result.data.matchLevels.similar.length}`);
    console.log(`   - Related matches: ${result.data.matchLevels.related.length}`);

    res.status(200).json({
      success: true,
      data: result.data
    });

  } catch (error) {
    console.error('❌ Visual model search error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to perform visual model search',
      details: error.message
    });
  }
};

// @desc    Hybrid search for extensions using text and image
// @route   POST /api/extensions/hybrid-search
// @access  Public
export const hybridSearchExtensions = async (req, res, next) => {
  try {
    console.log('🔍 Hybrid extension search request received');

    const { query = '', category = '' } = req.body;
    let imageAnalysis = null;
    let visualExtensions = [];

    // Process image if provided
    if (req.files && req.files.image) {
      const imageFile = req.files.image;
      console.log(`🔍 Processing image for hybrid search: ${imageFile.name}`);

      // Validate and process image
      const validation = imageProcessor.validateImage(imageFile);
      if (!validation.isValid) {
        return res.status(400).json({
          success: false,
          error: validation.errors.join(', ')
        });
      }

      const processedImage = await imageProcessor.processImage(imageFile.data, {
        width: 512,
        height: 512,
        quality: 80,
        format: 'jpeg'
      });

      // Analyze image
      const analysisPrompt = "Analyze this image and identify UI elements, interface components, tools, features, and functionality that might be related to SketchUp extensions.";
      imageAnalysis = await imageProcessor.analyzeImageWithGemini(
        processedImage.buffer,
        { prompt: analysisPrompt, language: 'en' }
      );

      // Get visual matches
      visualExtensions = await findExtensionsFromImageAnalysis(imageAnalysis.analysis, category);
    }

    // Perform text search
    let textExtensions = [];
    if (query.trim()) {
      const textQuery = {
        $or: [
          { name: { $regex: query, $options: 'i' } },
          { description: { $regex: query, $options: 'i' } },
          { tags: { $in: [new RegExp(query, 'i')] } },
          { features: { $in: [new RegExp(query, 'i')] } }
        ],
        status: 'active'
      };

      if (category) {
        textQuery.category = category;
      }

      textExtensions = await Extension.find(textQuery)
        .populate('createdBy', 'name')
        .sort({ downloads: -1, rating: -1 })
        .limit(20)
        .lean();
    }

    // Combine and score results
    const combinedResults = combineSearchResults(textExtensions, visualExtensions, imageAnalysis?.analysis);

    console.log('✅ Hybrid extension search completed successfully');

    res.status(200).json({
      success: true,
      data: {
        extensions: combinedResults.slice(0, 20),
        textScore: textExtensions.length,
        visualScore: visualExtensions.length,
        combinedScore: combinedResults.length,
        analysis: imageAnalysis?.analysis,
        searchType: 'hybrid'
      }
    });

  } catch (error) {
    console.error('❌ Hybrid extension search error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to perform hybrid search'
    });
  }
};

/**
 * Find extensions based on image analysis
 */
async function findExtensionsFromImageAnalysis(analysis, category = '') {
  try {
    const searchTerms = [
      ...analysis.elements || [],
      ...analysis.objects || [],
      ...analysis.tags || [],
      analysis.style || ''
    ].filter(term => term && term.length > 0);

    if (searchTerms.length === 0) {
      console.log('⚠️ No search terms extracted from image analysis');
      return [];
    }

    console.log('🔍 Searching extensions with terms:', searchTerms);

    // First try to find extensions in database
    let extensions = await realDataScraper.searchExtensions(searchTerms, category, 20);

    // If no extensions found in database, try to scrape fresh data
    if (extensions.length === 0) {
      console.log('📡 No extensions found in database, scraping fresh data...');

      // Update database with fresh scraped data
      await realDataScraper.updateExtensionsDatabase();

      // Try searching again
      extensions = await realDataScraper.searchExtensions(searchTerms, category, 20);
    }

    console.log(`✅ Found ${extensions.length} matching extensions`);
    return extensions;

  } catch (error) {
    console.error('Error finding extensions from image analysis:', error);
    return [];
  }
}

/**
 * Calculate visual similarity score between image analysis and extension
 */
function calculateVisualSimilarity(analysis, extension) {
  let score = 0;
  let maxScore = 0;

  // Check for matching elements
  if (analysis.elements && analysis.elements.length > 0) {
    maxScore += 30;
    const matchingElements = analysis.elements.filter(element =>
      extension.name.toLowerCase().includes(element.toLowerCase()) ||
      extension.description.toLowerCase().includes(element.toLowerCase()) ||
      (extension.tags && extension.tags.some(tag => tag.toLowerCase().includes(element.toLowerCase())))
    );
    score += (matchingElements.length / analysis.elements.length) * 30;
  }

  // Check for matching objects
  if (analysis.objects && analysis.objects.length > 0) {
    maxScore += 25;
    const matchingObjects = analysis.objects.filter(object =>
      extension.name.toLowerCase().includes(object.toLowerCase()) ||
      extension.description.toLowerCase().includes(object.toLowerCase()) ||
      (extension.tags && extension.tags.some(tag => tag.toLowerCase().includes(object.toLowerCase())))
    );
    score += (matchingObjects.length / analysis.objects.length) * 25;
  }

  // Check for matching style
  if (analysis.style) {
    maxScore += 20;
    if (extension.name.toLowerCase().includes(analysis.style.toLowerCase()) ||
        extension.description.toLowerCase().includes(analysis.style.toLowerCase()) ||
        (extension.tags && extension.tags.some(tag => tag.toLowerCase().includes(analysis.style.toLowerCase())))) {
      score += 20;
    }
  }

  // Check for matching categories
  if (analysis.categories && analysis.categories.length > 0) {
    maxScore += 15;
    const matchingCategories = analysis.categories.filter(category =>
      extension.category.toLowerCase().includes(category.toLowerCase())
    );
    score += (matchingCategories.length / analysis.categories.length) * 15;
  }

  // Check for matching tags
  if (analysis.tags && analysis.tags.length > 0) {
    maxScore += 10;
    const matchingTags = analysis.tags.filter(tag =>
      extension.tags && extension.tags.some(extTag => extTag.toLowerCase().includes(tag.toLowerCase()))
    );
    score += (matchingTags.length / analysis.tags.length) * 10;
  }

  // Return normalized score (0-1)
  return maxScore > 0 ? Math.min(score / maxScore, 1) : 0;
}

/**
 * Combine text and visual search results
 */
function combineSearchResults(textExtensions, visualExtensions, imageAnalysis) {
  const combinedMap = new Map();

  // Add text search results with higher weight
  textExtensions.forEach(extension => {
    combinedMap.set(extension._id.toString(), {
      ...extension,
      textScore: 1.0,
      visualScore: 0,
      combinedScore: 0.7 // Higher weight for text matches
    });
  });

  // Add visual search results
  visualExtensions.forEach(extension => {
    const id = extension._id.toString();
    const visualSimilarity = calculateVisualSimilarity(imageAnalysis, extension);

    if (combinedMap.has(id)) {
      // Extension found in both searches - boost score
      const existing = combinedMap.get(id);
      existing.visualScore = visualSimilarity;
      existing.combinedScore = (existing.textScore * 0.6) + (visualSimilarity * 0.4) + 0.2; // Bonus for appearing in both
    } else {
      // Extension only found in visual search
      combinedMap.set(id, {
        ...extension,
        textScore: 0,
        visualScore: visualSimilarity,
        combinedScore: visualSimilarity * 0.5 // Lower weight for visual-only matches
      });
    }
  });

  // Convert to array and sort by combined score
  return Array.from(combinedMap.values())
    .sort((a, b) => b.combinedScore - a.combinedScore);
}