import React, { useState, useEffect, useCallback } from 'react';
import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';
import { FiCheck, FiX, FiCreditCard, FiDownload, FiStar, FiHeadphones, FiBriefcase } from 'react-icons/fi';
import { useAuth } from '../context/AuthContext';
import { usePayment, SUBSCRIPTION_PLANS } from '../context/PaymentContext';

import SubscriptionManagement from '../components/payment/SubscriptionManagement';
import PageTransition from '../components/PageTransition';
import toast from 'react-hot-toast';

const Subscription = () => {
  const { currentUser } = useAuth();
  const {
    loading,
    error,
    subscription,
    getCurrentSubscription,
    subscribe,
    cancelSubscription
  } = usePayment();

  const [billingInterval, setBillingInterval] = useState('monthly');
  const [selectedPlan, setSelectedPlan] = useState(null);
  const [showConfirmation, setShowConfirmation] = useState(false);
  const [processingPayment, setProcessingPayment] = useState(false);

  // Fetch current subscription on component mount
  useEffect(() => {
  getCurrentSubscription();
  }, []);

  // Handle subscription error
  useEffect(() => {
  if (cachedData && !isExpired(cachedData)) {
  toast.error(error);
    }
  }, [error]);

  // Handle plan selection
  const handleSelectPlan = (planId) => {
  setSelectedPlan(planId);
    setShowConfirmation(true);
  };

  // Handle subscription confirmation
  const handleConfirmSubscription = async () => {
  try {
      setProcessingPayment(true);
      await subscribe(selectedPlan, billingInterval);
      toast.success(`Successfully subscribed to ${SUBSCRIPTION_PLANS[selectedPlan]?.name || selectedPlan} plan!`);
      setShowConfirmation(false);
    } catch (err) {
      toast.error('Subscription failed. Please try again.');
    } finally {
      setProcessingPayment(false);
    }
  };

  // Handle subscription cancellation
  const handleCancelSubscription = async () => {
  try {
      await cancelSubscription();
      toast.success('Your subscription has been cancelled.');
    } catch (err) {
      toast.error('Failed to cancel subscription. Please try again.');
    }
  };

  // Format price with currency
  const formatPrice = (price) => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
      currency: 'USD'
    }).format(price);
  };

  // Get current subscription type
  const getCurrentPlan = () => {
    if (cachedData && !isExpired(cachedData)) {
  return 'free';
    }
    return currentUser.subscription.type;
  };

  return (
    <div className="flex flex-col min-h-screen bg-gray-50 dark:bg-gray-900">

      <PageTransition>
        <main className="flex-grow container mx-auto px-4 py-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="max-w-6xl mx-auto"
          >
          <div className="text-center mb-12">
            <h1 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
              Choose Your Subscription Plan
            </h1>
            <p className="text-lg text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
              Get access to thousands of high-quality 3D models with our flexible subscription plans.
              Choose the plan that fits your needs and start downloading today.
            </p>

            {/* Billing interval toggle */}
            <div className="mt-8 flex items-center justify-center">
              <span className={`mr-4 text-sm font-medium ${billingInterval === 'monthly' ? 'text-blue-600 dark:text-blue-400' : 'text-gray-500 dark:text-gray-400'}`}>
                Monthly
              </span>
              <button
                type="button"
                className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 ${
    billingInterval === 'yearly' ? 'bg-blue-600' : 'bg-gray-200 dark:bg-gray-700'
                }`}
                onClick={() => setBillingInterval(billingInterval === 'monthly' ? 'yearly' : 'monthly')}
              >
                <span
                  className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
    billingInterval === 'yearly' ? 'translate-x-6' : 'translate-x-1'
                  }`}
                />
              </button>
              <span className={`ml-4 text-sm font-medium ${billingInterval === 'yearly' ? 'text-blue-600 dark:text-blue-400' : 'text-gray-500 dark:text-gray-400'}`}>
                Yearly <span className="text-green-500 font-semibold">(Save 17%)</span>
              </span>
            </div>
          </div>

          {/* Subscription plans */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
            {/* Free Plan */}
            <div className={`bg-white dark:bg-gray-800 rounded-lg shadow-lg overflow-hidden border-2 ${
              getCurrentPlan() === 'free' ? 'border-blue-500' : 'border-transparent'
            }`}>
              <div className="p-6">
                <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-2">Free</h3>
                <p className="text-gray-600 dark:text-gray-300 mb-4">Basic access for casual users</p>
                <div className="mb-6">
                  <span className="text-4xl font-bold text-gray-900 dark:text-white">$0</span>
                  <span className="text-gray-500 dark:text-gray-400">/month</span>
                </div>
                <ul className="space-y-3 mb-6">
                  <li className="flex items-center">
                    <FiCheck className="h-5 w-5 text-green-500 mr-2" />
                    <span className="text-gray-600 dark:text-gray-300">5 downloads per month</span>
                  </li>
                  <li className="flex items-center">
                    <FiCheck className="h-5 w-5 text-green-500 mr-2" />
                    <span className="text-gray-600 dark:text-gray-300">Access to free models only</span>
                  </li>
                  <li className="flex items-center">
                    <FiX className="h-5 w-5 text-red-500 mr-2" />
                    <span className="text-gray-600 dark:text-gray-300">No premium models</span>
                  </li>
                  <li className="flex items-center">
                    <FiX className="h-5 w-5 text-red-500 mr-2" />
                    <span className="text-gray-600 dark:text-gray-300">Standard support only</span>
                  </li>
                </ul>
                <button
                  className={`w-full py-2 px-4 rounded-md font-medium ${
                    getCurrentPlan() === 'free'
                      ? 'bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200 cursor-default'
                      : 'bg-blue-600 hover:bg-blue-700 text-white'
                  }`}
                  disabled={getCurrentPlan() === 'free'}
                >
                  {getCurrentPlan() === 'free' ? 'Current Plan' : 'Downgrade'}
                </button>
              </div>
            </div>

            {/* Paid Plans */}
            {Object.values(SUBSCRIPTION_PLANS).map((plan) => (
              <div
                key={plan.id}
                className={`bg-white dark:bg-gray-800 rounded-lg shadow-lg overflow-hidden border-2 ${
                  getCurrentPlan() === plan.id ? 'border-blue-500' : 'border-transparent'
                } ${plan.id === 'premium' ? 'transform md:scale-105 z-10' : '}`}
              >
                {plan.id === 'premium' && (
                  <div className="bg-blue-600 text-white text-center py-1 text-sm font-medium">
                    Most Popular
                  </div>
                )}
                <div className="p-6">
                  <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-2">{plan.name}</h3>
                  <p className="text-gray-600 dark:text-gray-300 mb-4">{plan.description}</p>
                  <div className="mb-6">
                    <span className="text-4xl font-bold text-gray-900 dark:text-white">
                      {formatPrice(billingInterval === 'monthly' ? plan.monthlyPrice : plan.yearlyPrice)}
                    </span>
                    <span className="text-gray-500 dark:text-gray-400">/{billingInterval === 'monthly' ? 'month' : 'year'}</span>
                  </div>
                  <ul className="space-y-3 mb-6">
                    {plan.features.map((feature, index) => (
                      <li key={index} className="flex items-center">
                        <FiCheck className="h-5 w-5 text-green-500 mr-2" />
                        <span className="text-gray-600 dark:text-gray-300">{feature}</span>
                      </li>
                    ))}
                  </ul>
                  <button
                    onClick={() => handleSelectPlan(plan.id)}
                    className={`w-full py-2 px-4 rounded-md font-medium ${
                      getCurrentPlan() === plan.id
                        ? 'bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200'
                        : 'bg-blue-600 hover:bg-blue-700 text-white'
                    }`}
                    disabled={loading || getCurrentPlan() === plan.id}
                  >
                    {getCurrentPlan() === plan.id ? 'Current Plan' : 'Subscribe'}
                  </button>
                </div>
              </div>
            ))}
          </div>

          {/* Current subscription info */}
          {subscription && (
            <div className="mb-12">
              <SubscriptionManagement />
            </div>
          )}
        </motion.div>
        </main>
      </PageTransition>

      {/* Subscription confirmation modal */}
      {showConfirmation && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full p-6">
            <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-4">
              Confirm Subscription
            </h3>
            <p className="text-gray-600 dark:text-gray-300 mb-6">
              You are about to subscribe to the {SUBSCRIPTION_PLANS[selectedPlan]?.name} plan for{' '}
              {formatPrice(
                billingInterval === 'monthly'
                  ? SUBSCRIPTION_PLANS[selectedPlan]?.monthlyPrice
                  : SUBSCRIPTION_PLANS[selectedPlan]?.yearlyPrice
              )}{' '}
              per {billingInterval === 'monthly' ? 'month' : 'year'}.
            </p>
            <div className="flex justify-end space-x-4">
              <button
                onClick={() => setShowConfirmation(false)}
                className="py-2 px-4 border border-gray-300 dark:border-gray-600 rounded-md text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
                disabled={processingPayment}
              >
                Cancel
              </button>
              <button
                onClick={handleConfirmSubscription}
                className="py-2 px-4 bg-blue-600 hover:bg-blue-700 text-white rounded-md flex items-center"
                disabled={processingPayment}
              >
                {processingPayment ? (
                  <>
                    <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Processing...
                  </>
                ) : (
                  <>
                    <FiCreditCard className="mr-2" />
                    Confirm Payment
                  </>
                )}
              </button>
            </div>
          </div>
        </div>
      )}

    </div>
  );
};

export default Subscription;
