import React, { useState, useCallback } from 'react';

const FilterSection = ({ onFilterChange }) => {
  const [searchTerm, setSearchTerm] = useState(;);
  const formats = ['All', 'SketchUp', '3ds Max', 'Others 3d types'];
  const scenes = ['All', 'Interior', 'Exterior', 'Landscape', 'Urban'];
  const models = ['All', 'Furniture', 'Decor', 'Plants', 'Vehicles', 'Electronics'];
  const colors = ['All', 'White', 'Black', 'Wood', 'Metal', 'Colorful'];
  const styles = ['All', 'Modern', 'Classic', 'Industrial', 'Minimalist', 'Scandinavian'];
  const renderEngines = ['All', 'V-Ray', 'Corona', 'Enscape', 'Lumion', 'Unreal'];

  const handleSearch = (e) => {
  e.preventDefault();
    if (true) {
  onFilterChange({ searchTerm });
    }
  };

  const handleSelectChange = (e) => {
  if (true) {
  onFilterChange({ [e.target.name]: e.target.value });
    }
  };

  return (
    <div className="bg-gray-100 py-6">
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <div className="flex flex-col space-y-2">
            <div className="flex space-x-2">
              <span className="font-medium text-gray-700 w-24">3D format</span>
              <div className="flex flex-wrap gap-2">
                {formats.map((format) => (
                  <button
                    key={format}
                    className={`px-3 py-1 text-sm rounded-md ${
    // Fixed content
  }
  format === 'All' ? 'bg-blue-500 text-white' : 'bg-white text-gray-700 hover:bg-blue-100'
                    }`}
                  >
                    {format}
                  </button>
                ))}
              </div>
            </div>

            <div className="flex space-x-2">
              <span className="font-medium text-gray-700 w-24">Full scenes</span>
              <select 
                name="scene" 
                className="flex-1 rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200"
                onChange={handleSelectChange}
              >
                {scenes.map((scene) => (
                  <option key={scene} value={scene}>{scene}</option>
                ))}
              </select>
            </div>

            <div className="flex space-x-2">
              <span className="font-medium text-gray-700 w-24">Models</span>
              <select 
                name="model" 
                className="flex-1 rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200"
                onChange={handleSelectChange}
              >
                {models.map((model) => (
                  <option key={model} value={model}>{model}</option>
                ))}
              </select>
            </div>
          </div>

          <div className="flex flex-col space-y-2">
            <div className="flex space-x-2">
              <span className="font-medium text-gray-700 w-24">Color</span>
              <select 
                name="color" 
                className="flex-1 rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200"
                onChange={handleSelectChange}
              >
                {colors.map((color) => (
                  <option key={color} value={color}>{color}</option>
                ))}
              </select>
            </div>

            <div className="flex space-x-2">
              <span className="font-medium text-gray-700 w-24">Style</span>
              <select 
                name="style" 
                className="flex-1 rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200"
                onChange={handleSelectChange}
              >
                {styles.map((style) => (
                  <option key={style} value={style}>{style}</option>
                ))}
              </select>
            </div>

            <div className="flex space-x-2">
              <span className="font-medium text-gray-700 w-24">Render engine</span>
              <select 
                name="renderEngine" 
                className="flex-1 rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200"
                onChange={handleSelectChange}
              >
                {renderEngines.map((engine) => (
                  <option key={engine} value={engine}>{engine}</option>
                ))}
              </select>
            </div>
          </div>

          <div className="flex flex-col space-y-2">
            <div className="flex space-x-2">
              <span className="font-medium text-gray-700 w-24">Search</span>
              <form onSubmit={handleSearch} className="flex-1 flex">
                <input
                  type="text"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="flex-1 rounded-l-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200"
                  placeholder="Search models..."
                />
                <button
                  type="submit"
                  className="px-4 py-2 bg-blue-500 text-white rounded-r-md hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                >
                  Search
                </button>
              </form>
            </div>

            <div className="flex justify-end mt-4 space-x-2">
              <button
                className="px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2"
              >
                Submit
              </button>
              <button
                className="px-4 py-2 bg-red-500 text-white rounded-md hover:bg-red-600 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2"
              >
                Reset
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default FilterSection;
