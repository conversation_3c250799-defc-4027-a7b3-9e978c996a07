/**
 * Error handling utilities for the application
 */
import toast from 'react-hot-toast';

/**
 * Custom error class for API errors
 */
export class ApiError extends Error {
  constructor(message, status, data = null) {
    super(message);
    this.name = 'ApiError';
    this.status = status;
    this.data = data;
  }
}

/**
 * Custom error class for validation errors
 */
export class ValidationError extends Error {
  constructor(message, errors = {}) {
    super(message);
    this.name = 'ValidationError';
    this.errors = errors;
  }
}

/**
 * Custom error class for authentication errors
 */
export class AuthError extends Error {
  constructor(message) {
    super(message);
    this.name = 'AuthError';
  }
}

/**
 * Handle API errors consistently
 * @param {Error} error - The error object
 * @param {boolean} showToast - Whether to show a toast notification
 * @returns {Object} Formatted error object
 */
export const handleApiError = (error, showToast = true) => {
  console.error('API Error:', error);

  let errorMessage = 'An unexpected error occurred. Please try again.';
  let errorStatus = 500;
  let errorData = null;

  if (error instanceof ApiError) {
    errorMessage = error.message;
    errorStatus = error.status;
    errorData = error.data;
  } else if (error.response) {
    // Axios error with response
    errorStatus = error.response.status;
    errorMessage = error.response.data?.message || errorMessage;
    errorData = error.response.data;

    // Handle specific status codes
    if (errorStatus === 401) {
      errorMessage = 'Your session has expired. Please log in again.';
      // Redirect to login page or refresh token
    } else if (errorStatus === 403) {
      errorMessage = 'You do not have permission to perform this action.';
    } else if (errorStatus === 404) {
      errorMessage = 'The requested resource was not found.';
    } else if (errorStatus === 422) {
      errorMessage = 'Validation failed. Please check your input.';
    }
  } else if (error.request) {
    // Request was made but no response received
    errorMessage = 'No response from server. Please check your internet connection.';
    errorStatus = 0;
  }

  if (showToast) {
    toast.error(errorMessage);
  }

  return {
    message: errorMessage,
    status: errorStatus,
    data: errorData
  };
};

/**
 * Handle validation errors
 * @param {Object|Error} errors - Validation errors object or error
 * @param {boolean} showToast - Whether to show a toast notification
 * @returns {Object} Formatted validation errors
 */
export const handleValidationErrors = (errors, showToast = true) => {
  let validationErrors = {};
  let errorMessage = 'Please check the form for errors.';

  if (errors instanceof ValidationError) {
    validationErrors = errors.errors;
    errorMessage = errors.message;
  } else if (errors.response && errors.response.status === 422) {
    // Laravel/Symfony style validation errors
    validationErrors = errors.response.data.errors || {};
    errorMessage = errors.response.data.message || errorMessage;
  } else if (typeof errors === 'object' && errors !== null) {
    validationErrors = errors;
  }

  if (showToast) {
    toast.error(errorMessage);
  }

  return {
    message: errorMessage,
    errors: validationErrors
  };
};

/**
 * Global error handler for uncaught exceptions
 */
export const setupGlobalErrorHandler = () => {
  // Save original console.error
  const originalConsoleError = console.error;

  // Override console.error to provide more helpful information for React errors
  console.error = function(...args) {
    // Call original console.error
    originalConsoleError.apply(console, args);

    // Check for common React errors
    const errorString = args.join(' ');

    if (errorString.includes('Invalid hook call')) {
      console.warn('REACT HOOK ERROR DETECTED: This is likely caused by multiple React instances.');
      console.warn('Possible solutions:');
      console.warn('1. Make sure you\'re importing React from the singleton utility');
      console.warn('2. Check for duplicate React in node_modules (npm ls react)');
      console.warn('3. Check for conflicting React versions');
    }
  };

  // Handle uncaught exceptions
  window.addEventListener('error', (event) => {
    console.error('Global error:', event.error);

    // Don't show toast for network errors as they're handled elsewhere
    if (event.error && event.error.name !== 'NetworkError') {
      toast.error('An unexpected error occurred. Our team has been notified.');
    }

    // Log additional debugging information
    logErrorDetails(event.error);

    // Here you could send the error to a monitoring service like Sentry
    // Sentry.captureException(event.error);

    // Prevent the error from bubbling up
    event.preventDefault();
  });

  window.addEventListener('unhandledrejection', (event) => {
    console.error('Unhandled promise rejection:', event.reason);

    // Don't show toast for network errors as they're handled elsewhere
    if (event.reason && event.reason.name !== 'NetworkError') {
      toast.error('An unexpected error occurred. Our team has been notified.');
    }

    // Log additional debugging information
    logErrorDetails(event.reason);

    // Here you could send the error to a monitoring service like Sentry
    // Sentry.captureException(event.reason);

    // Prevent the error from bubbling up
    event.preventDefault();
  });

  // Add special handling for WebSocket errors
  const originalWebSocket = window.WebSocket;
  window.WebSocket = function(url, protocols) {
    const socket = new originalWebSocket(url, protocols);

    socket.addEventListener('error', function(event) {
      console.error('WebSocket error:', event);

      // Only show toast for HMR WebSocket errors in development
      if (url.includes('localhost') && url.includes('ws')) {
        toast.error('WebSocket connection failed. Hot reloading may not work.');
      }
    });

    return socket;
  };

  // Restore original WebSocket constructor properties
  Object.defineProperties(window.WebSocket, Object.getOwnPropertyDescriptors(originalWebSocket));
};

/**
 * Log detailed error information for debugging
 */
function logErrorDetails(error) {
  console.group('Error Details');

  // Log error message and stack trace
  console.error('Error:', error?.message || error);
  console.error('Stack trace:', error?.stack);

  // Log browser information
  console.log('Browser Information:', {
    userAgent: navigator.userAgent,
    language: navigator.language,
    platform: navigator.platform,
    screenWidth: window.screen.width,
    screenHeight: window.screen.height,
    viewportWidth: window.innerWidth,
    viewportHeight: window.innerHeight
  });

  // Log React version if available
  if (window.React) {
    console.log('React version:', window.React.version);
  }

  // Log memory usage if available
  if (window.performance && window.performance.memory) {
    console.log('Memory usage:', {
      jsHeapSizeLimit: formatBytes(window.performance.memory.jsHeapSizeLimit),
      totalJSHeapSize: formatBytes(window.performance.memory.totalJSHeapSize),
      usedJSHeapSize: formatBytes(window.performance.memory.usedJSHeapSize)
    });
  }

  console.groupEnd();
}

/**
 * Format bytes to a human-readable string
 */
function formatBytes(bytes, decimals = 2) {
  if (bytes === 0) return '0 Bytes';

  const k = 1024;
  const dm = decimals < 0 ? 0 : decimals;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];

  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
}

export default {
  ApiError,
  ValidationError,
  AuthError,
  handleApiError,
  handleValidationErrors,
  setupGlobalErrorHandler
};
