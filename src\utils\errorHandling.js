/**
 * Enhanced Error handling utilities for 3DSKETCHUP.NET
 * Provides consistent error handling across the application with real-time monitoring
 */

import toast from 'react-hot-toast';

/**
 * Custom error class for API errors
 */
export class ApiError extends Error {
  constructor(message, status, data = null, endpoint = null) {
    super(message);
    this.name = 'ApiError';
    this.status = status;
    this.data = data;
    this.endpoint = endpoint;
    this.timestamp = new Date().toISOString();
  }

  toJSON() {
    return {
      name: this.name,
      message: this.message,
      status: this.status,
      data: this.data,
      endpoint: this.endpoint,
      timestamp: this.timestamp,
      stack: this.stack
    };
  }
}

/**
 * Custom error class for validation errors
 */
export class ValidationError extends Error {
  constructor(message, errors = {}, field = null) {
    super(message);
    this.name = 'ValidationError';
    this.errors = errors;
    this.field = field;
    this.timestamp = new Date().toISOString();
  }

  toJSON() {
    return {
      name: this.name,
      message: this.message,
      errors: this.errors,
      field: this.field,
      timestamp: this.timestamp,
      stack: this.stack
    };
  }
}

/**
 * Custom error class for authentication errors
 */
export class AuthError extends Error {
  constructor(message, code = null) {
    super(message);
    this.name = 'AuthError';
    this.code = code;
    this.timestamp = new Date().toISOString();
  }

  toJSON() {
    return {
      name: this.name,
      message: this.message,
      code: this.code,
      timestamp: this.timestamp,
      stack: this.stack
    };
  }
}

/**
 * Custom error class for network errors
 */
export class NetworkError extends Error {
  constructor(message, url = null, method = null) {
    super(message);
    this.name = 'NetworkError';
    this.url = url;
    this.method = method;
    this.timestamp = new Date().toISOString();
  }

  toJSON() {
    return {
      name: this.name,
      message: this.message,
      url: this.url,
      method: this.method,
      timestamp: this.timestamp,
      stack: this.stack
    };
  }
}

/**
 * Error tracking and analytics
 */
class ErrorTracker {
  constructor() {
    this.errors = [];
    this.maxErrors = 100; // Keep last 100 errors
    this.errorCounts = new Map();
  }

  track(error) {
    const errorData = {
      ...error,
      id: Date.now() + Math.random(),
      userAgent: navigator.userAgent,
      url: window.location.href,
      timestamp: new Date().toISOString()
    };

    this.errors.unshift(errorData);
    if (this.errors.length > this.maxErrors) {
      this.errors.pop();
    }

    // Count error types
    const errorType = error.name || 'Unknown';
    this.errorCounts.set(errorType, (this.errorCounts.get(errorType) || 0) + 1);

    return errorData;
  }

  getErrors() {
    return this.errors;
  }

  getErrorCounts() {
    return Object.fromEntries(this.errorCounts);
  }

  clearErrors() {
    this.errors = [];
    this.errorCounts.clear();
  }
}

// Global error tracker instance
const errorTracker = new ErrorTracker();

/**
 * Enhanced API error handler with detailed logging and user feedback
 */
export const handleApiError = (error, showToast = true, context = {}) => {
  let errorMessage = 'An unexpected error occurred. Please try again.';
  let errorStatus = 500;
  let errorData = null;
  let endpoint = null;

  // Extract error information
  if (error instanceof ApiError) {
    errorMessage = error.message;
    errorStatus = error.status;
    errorData = error.data;
    endpoint = error.endpoint;
  } else if (error.response) {
    // Axios error with response
    errorStatus = error.response.status;
    errorMessage = error.response.data?.message || error.response.data?.error || errorMessage;
    errorData = error.response.data;
    endpoint = error.config?.url;

    // Handle specific status codes with better messages
    switch (errorStatus) {
      case 400:
        errorMessage = 'Invalid request. Please check your input and try again.';
        break;
      case 401:
        errorMessage = 'Your session has expired. Please log in again.';
        break;
      case 403:
        errorMessage = 'You do not have permission to perform this action.';
        break;
      case 404:
        errorMessage = 'The requested resource was not found.';
        break;
      case 422:
        errorMessage = 'Validation failed. Please check your input.';
        break;
      case 429:
        errorMessage = 'Too many requests. Please wait a moment and try again.';
        break;
      case 500:
        errorMessage = 'Server error. Our team has been notified.';
        break;
      case 502:
      case 503:
      case 504:
        errorMessage = 'Service temporarily unavailable. Please try again later.';
        break;
    }
  } else if (error.request) {
    // Request was made but no response received
    errorMessage = 'No response from server. Please check your internet connection.';
    errorStatus = 0;
    endpoint = error.config?.url;
  } else if (error instanceof NetworkError) {
    errorMessage = error.message;
    endpoint = error.url;
  }

  // Create comprehensive error object
  const errorInfo = {
    message: errorMessage,
    status: errorStatus,
    data: errorData,
    endpoint,
    context,
    originalError: error.message,
    timestamp: new Date().toISOString()
  };

  // Track error
  errorTracker.track(errorInfo);

  // Show user-friendly toast notification
  if (showToast) {
    if (errorStatus >= 500) {
      toast.error(errorMessage, {
        duration: 6000,
        icon: '🚨'
      });
    } else if (errorStatus === 401) {
      toast.error(errorMessage, {
        duration: 5000,
        icon: '🔒'
      });
    } else {
      toast.error(errorMessage, {
        duration: 4000,
        icon: '⚠️'
      });
    }
  }

  return errorInfo;
};

/**
 * Enhanced validation error handler
 */
export const handleValidationErrors = (errors, showToast = true, context = {}) => {
  let validationErrors = {};
  let errorMessage = 'Please check the form for errors.';

  if (errors instanceof ValidationError) {
    validationErrors = errors.errors;
    errorMessage = errors.message;
  } else if (errors.response && errors.response.status === 422) {
    // Laravel/Symfony style validation errors
    validationErrors = errors.response.data.errors || {};
    errorMessage = errors.response.data.message || errorMessage;
  } else if (typeof errors === 'object') {
    validationErrors = errors;
  }

  const errorInfo = {
    message: errorMessage,
    errors: validationErrors,
    context,
    timestamp: new Date().toISOString()
  };

  // Track validation error
  errorTracker.track(errorInfo);

  // Show toast notification with field-specific errors
  if (showToast) {
    const fieldErrors = Object.entries(validationErrors);
    if (fieldErrors.length > 0) {
      const firstError = fieldErrors[0];
      const fieldName = firstError[0];
      const fieldError = Array.isArray(firstError[1]) ? firstError[1][0] : firstError[1];
      
      toast.error(`${fieldName}: ${fieldError}`, {
        duration: 5000,
        icon: '📝'
      });
    } else {
      toast.error(errorMessage, {
        duration: 4000,
        icon: '⚠️'
      });
    }
  }

  return errorInfo;
};

/**
 * Enhanced global error handler with comprehensive monitoring
 */
export const setupGlobalErrorHandler = () => {
  // Save original console methods
  const originalConsoleError = console.error;
  const originalConsoleWarn = console.warn;

  // Enhanced console.error with React error detection
  console.error = function(...args) {
    originalConsoleError.apply(console, args);

    const errorString = args.join(' ');

    // Detect and provide helpful messages for common React errors
    if (errorString.includes('Invalid hook call')) {
      console.warn('🔧 React Hook Error: Make sure you are calling hooks only inside React components or custom hooks.');
      toast.error('Component error detected. Please refresh the page.', {
        duration: 5000,
        icon: '🔧'
      });
    } else if (errorString.includes('Cannot read property') || errorString.includes('Cannot read properties')) {
      console.warn('🔧 Property Access Error: Check for undefined objects before accessing properties.');
    } else if (errorString.includes('Failed to fetch')) {
      console.warn('🌐 Network Error: Check your internet connection and API server status.');
      toast.error('Network connection issue. Please check your internet.', {
        duration: 5000,
        icon: '🌐'
      });
    }
  };

  // Handle uncaught exceptions
  window.addEventListener('error', (event) => {
    const error = event.error;
    
    const errorInfo = {
      message: error?.message || 'Unknown error',
      filename: event.filename,
      lineno: event.lineno,
      colno: event.colno,
      stack: error?.stack,
      type: 'uncaught-exception',
      timestamp: new Date().toISOString()
    };

    errorTracker.track(errorInfo);

    // Don't show toast for network errors as they're handled elsewhere
    if (!error?.message?.includes('Network Error') && !error?.message?.includes('Failed to fetch')) {
      toast.error('An unexpected error occurred. Our team has been notified.', {
        duration: 4000,
        icon: '🚨'
      });
    }

    // Log detailed error information
    logErrorDetails(error);
  });

  // Handle unhandled promise rejections
  window.addEventListener('unhandledrejection', (event) => {
    const error = event.reason;
    
    const errorInfo = {
      message: error?.message || 'Promise rejection',
      stack: error?.stack,
      type: 'unhandled-promise-rejection',
      timestamp: new Date().toISOString()
    };

    errorTracker.track(errorInfo);

    // Don't show toast for network errors
    if (!error?.message?.includes('Network Error') && !error?.message?.includes('Failed to fetch')) {
      toast.error('An unexpected error occurred. Our team has been notified.', {
        duration: 4000,
        icon: '🚨'
      });
    }

    logErrorDetails(error);
    event.preventDefault();
  });

  // Enhanced WebSocket error handling
  const originalWebSocket = window.WebSocket;
  if (originalWebSocket) {
    window.WebSocket = function(url, protocols) {
      const socket = new originalWebSocket(url, protocols);

      socket.addEventListener('error', function(event) {
        const errorInfo = {
          message: 'WebSocket connection error',
          url,
          type: 'websocket-error',
          timestamp: new Date().toISOString()
        };

        errorTracker.track(errorInfo);

        // Only show toast for non-development WebSocket errors
        if (!url.includes('localhost') || !url.includes('ws')) {
          toast.error('Real-time connection failed. Some features may not work.', {
            duration: 5000,
            icon: '🔌'
          });
        }
      });

      return socket;
    };

    // Restore original WebSocket constructor properties
    Object.defineProperties(window.WebSocket, Object.getOwnPropertyDescriptors(originalWebSocket));
  }

  // Expose error tracker for debugging
  window.errorTracker = errorTracker;
};

/**
 * Log detailed error information for debugging
 */
function logErrorDetails(error) {
  console.group('🔍 Error Details');

  // Log error message and stack trace
  console.error('Error:', error?.message || error);
  console.error('Stack trace:', error?.stack);

  // Log browser and environment information
  console.log('Browser Information:', {
    userAgent: navigator.userAgent,
    url: window.location.href,
    timestamp: new Date().toISOString(),
    viewport: `${window.innerWidth}x${window.innerHeight}`,
    language: navigator.language,
    online: navigator.onLine
  });

  // Log React version if available
  if (window.React) {
    console.log('React version:', window.React.version || 'Unknown');
  }

  // Log memory usage if available
  if (window.performance && window.performance.memory) {
    console.log('Memory usage:', {
      totalJSHeapSize: formatBytes(window.performance.memory.totalJSHeapSize),
      usedJSHeapSize: formatBytes(window.performance.memory.usedJSHeapSize),
      jsHeapSizeLimit: formatBytes(window.performance.memory.jsHeapSizeLimit)
    });
  }

  // Log performance metrics
  if (window.performance && window.performance.timing) {
    const timing = window.performance.timing;
    console.log('Performance metrics:', {
      pageLoadTime: timing.loadEventEnd - timing.navigationStart,
      domContentLoaded: timing.domContentLoadedEventEnd - timing.navigationStart,
      firstPaint: window.performance.getEntriesByType('paint')[0]?.startTime || 'N/A'
    });
  }

  console.groupEnd();
}

/**
 * Format bytes to a human-readable string
 */
function formatBytes(bytes, decimals = 2) {
  if (bytes === 0) return '0 Bytes';

  const k = 1024;
  const dm = decimals < 0 ? 0 : decimals;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];

  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
}

/**
 * Get error statistics for monitoring
 */
export const getErrorStats = () => {
  return {
    totalErrors: errorTracker.getErrors().length,
    errorCounts: errorTracker.getErrorCounts(),
    recentErrors: errorTracker.getErrors().slice(0, 10),
    lastError: errorTracker.getErrors()[0] || null
  };
};

/**
 * Clear error history
 */
export const clearErrorHistory = () => {
  errorTracker.clearErrors();
  toast.success('Error history cleared', {
    duration: 2000,
    icon: '🧹'
  });
};

export default {
  ApiError,
  ValidationError,
  AuthError,
  NetworkError,
  handleApiError,
  handleValidationErrors,
  setupGlobalErrorHandler,
  getErrorStats,
  clearErrorHistory
};
