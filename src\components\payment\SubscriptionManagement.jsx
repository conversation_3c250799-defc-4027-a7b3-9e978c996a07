import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  FiCreditCard,
  FiDownload,
  FiCalendar,
  FiAlertCircle,
  FiCheckCircle,
  FiX,
  FiEdit2,
  FiTrash2,
  FiPlus,
  FiRefreshCw,
  FiHelpCircle
} from 'react-icons/fi';
import { usePayment, SUBSCRIPTION_PLANS } from '../../context/PaymentContext';
import { useAuth } from '../../context/AuthContext';
import Button from '../ui/Button';
import Alert from '../ui/Alert';
import Modal from '../ui/Modal';
import toast from 'react-hot-toast';

const SubscriptionManagement = () => {
  const { currentUser } = useAuth();
  const {
    subscription,
    subscriptionStatus,
    paymentHistory,
    invoices,
    paymentMethods,
    remainingDownloads,
    getPaymentHistory,
    getInvoices,
    getPaymentMethods,
    cancelSubscription,
    addPaymentMethod,
    removePaymentMethod,
    loading,
    error
  } = usePayment();

  const [showCancelModal, setShowCancelModal] = useState(false);
  const [showAddCardModal, setShowAddCardModal] = useState(false);
  const [cancelReason, setCancelReason] = useState('');
  const [otherReason, setOtherReason] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [newCard, setNewCard] = useState({
    cardNumber: '',
    cardName: '',
    expMonth: '',
    expYear: '',
    cvc: '',
    isDefault: false
  });

  // Fetch payment data on component mount
  useEffect(() => {
    getPaymentHistory();
    getInvoices();
    getPaymentMethods();
  }, []);

  // Format date
  const formatDate = (dateString) => {
    const options = { year: 'numeric', month: 'long', day: 'numeric' };
    return new Date(dateString).toLocaleDateString(undefined, options);
  };

  // Format price with currency
  const formatPrice = (price) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(price);
  };

  // Format expiration date
  const formatExpiryDate = (month, year) => {
    if (!month || !year) return 'N/A';
    return `${month.toString().padStart(2, '0')}/${year.toString().slice(-2)}`;
  };

  // Format card number (show only last 4 digits)
  const formatCardNumber = (number) => {
    if (!number) return 'N/A';
    return `•••• ${number.slice(-4)}`;
  };

  // Get card brand icon
  const getCardBrandIcon = (brand) => {
    // In a real app, you would use different icons for different card brands
    return <FiCreditCard />;
  };

  // Handle cancel subscription
  const handleCancelSubscription = async () => {
    if (!cancelReason) {
      toast.error('Please select a reason for cancellation');
      return;
    }

    setIsSubmitting(true);

    try {
      const reason = cancelReason === 'other' ? otherReason : cancelReason;
      await cancelSubscription(reason);

      setShowCancelModal(false);
      toast.success('Your subscription has been canceled');
    } catch (error) {
      toast.error('Failed to cancel subscription: ' + error.message);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle add payment method
  const handleAddPaymentMethod = async (e) => {
    e.preventDefault();

    // Validate form
    if (!newCard.cardNumber || !newCard.cardName || !newCard.expMonth || !newCard.expYear || !newCard.cvc) {
      toast.error('Please fill in all card details');
      return;
    }

    setIsSubmitting(true);

    try {
      // In a real app, you would use Stripe Elements for secure card input
      // This is just a simplified example
      await addPaymentMethod({
        cardNumber: newCard.cardNumber,
        cardName: newCard.cardName,
        expMonth: newCard.expMonth,
        expYear: newCard.expYear,
        cvc: newCard.cvc,
        isDefault: newCard.isDefault
      });

      // Reset form and close modal
      setNewCard({
        cardNumber: '',
        cardName: '',
        expMonth: '',
        expYear: '',
        cvc: '',
        isDefault: false
      });
      setShowAddCardModal(false);
    } catch (error) {
      toast.error('Failed to add payment method: ' + error.message);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle remove payment method
  const handleRemovePaymentMethod = async (paymentMethodId) => {
    if (!confirm('Are you sure you want to remove this payment method?')) {
      return;
    }

    try {
      await removePaymentMethod(paymentMethodId);
    } catch (error) {
      toast.error('Failed to remove payment method: ' + error.message);
    }
  };

  // Get days remaining in subscription
  const getDaysRemaining = () => {
    if (!subscription || !subscription.endDate) return 0;

    const endDate = new Date(subscription.endDate);
    const today = new Date();
    const diffTime = endDate - today;
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    return diffDays > 0 ? diffDays : 0;
  };

  // Get subscription status badge color
  const getStatusColor = (status) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300';
      case 'cancelled':
        return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-300';
      case 'past_due':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-300';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300';
    }
  };

  return (
    <div className="space-y-8">
      {/* Current Subscription */}
      {subscription && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden"
        >
          <div className="p-6">
            <h2 className="text-xl font-bold text-gray-900 dark:text-white mb-4">Current Subscription</h2>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <div className="flex items-center mb-4">
                  <div className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(subscription.status)}`}>
                    {subscription.status.charAt(0).toUpperCase() + subscription.status.slice(1)}
                  </div>
                </div>

                <p className="text-gray-600 dark:text-gray-300 mb-2">
                  <span className="font-medium">Plan:</span>{' '}
                  {SUBSCRIPTION_PLANS[subscription.plan]?.name || 'Free'}
                </p>

                <p className="text-gray-600 dark:text-gray-300 mb-2">
                  <span className="font-medium">Price:</span>{' '}
                  {formatPrice(subscription.price)} / {subscription.interval}
                </p>

                <p className="text-gray-600 dark:text-gray-300 mb-2">
                  <span className="font-medium">Started:</span>{' '}
                  {subscription.startDate ? formatDate(subscription.startDate) : 'N/A'}
                </p>

                <p className="text-gray-600 dark:text-gray-300 mb-2">
                  <span className="font-medium">Renews:</span>{' '}
                  {subscription.endDate ? formatDate(subscription.endDate) : 'N/A'}
                </p>

                {subscription.status === 'active' && (
                  <p className="text-gray-600 dark:text-gray-300 mb-2">
                    <span className="font-medium">Days remaining:</span>{' '}
                    {getDaysRemaining()}
                  </p>
                )}
              </div>

              <div>
                <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 mb-4">
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">Usage</h3>

                  <div className="flex items-center mb-2">
                    <FiDownload className="text-blue-500 mr-2" />
                    <span className="text-gray-600 dark:text-gray-300">
                      Downloads: {currentUser?.downloadCredits || 0} / {SUBSCRIPTION_PLANS[subscription.plan]?.downloadLimit || 0} remaining
                    </span>
                  </div>

                  <div className="w-full bg-gray-200 dark:bg-gray-600 rounded-full h-2.5 mb-4">
                    <div
                      className="bg-blue-600 h-2.5 rounded-full"
                      style={{
                        width: `${Math.min(100, ((currentUser?.downloadCredits || 0) / (SUBSCRIPTION_PLANS[subscription.plan]?.downloadLimit || 1)) * 100)}%`
                      }}
                    ></div>
                  </div>
                </div>

                {subscription.status === 'active' && subscription.plan !== 'free' && (
                  <button
                    onClick={() => setShowCancelModal(true)}
                    className="px-4 py-2 border border-red-500 text-red-500 hover:bg-red-500 hover:text-white rounded-md transition-colors"
                    disabled={loading}
                  >
                    Cancel Subscription
                  </button>
                )}
              </div>
            </div>
          </div>
        </motion.div>
      )}

      {/* Payment History */}
      {paymentHistory && paymentHistory.length > 0 && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.1 }}
          className="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden"
        >
          <div className="p-6">
            <h2 className="text-xl font-bold text-gray-900 dark:text-white mb-4">Payment History</h2>

            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                <thead className="bg-gray-50 dark:bg-gray-700">
                  <tr>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      Date
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      Description
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      Amount
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      Status
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                  {paymentHistory.map((payment) => (
                    <tr key={payment.id}>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                        {formatDate(payment.date)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                        {payment.description}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                        {formatPrice(payment.amount)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                          payment.status === 'completed'
                            ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300'
                            : 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-300'
                        }`}>
                          {payment.status}
                        </span>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </motion.div>
      )}

      {/* Cancellation Modal */}
      {showCancelModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full p-6"
          >
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-xl font-bold text-gray-900 dark:text-white">
                Cancel Subscription
              </h3>
              <button
                onClick={() => setShowCancelModal(false)}
                className="text-gray-400 hover:text-gray-500 dark:hover:text-gray-300"
              >
                <FiX className="h-5 w-5" />
              </button>
            </div>

            <p className="text-gray-600 dark:text-gray-300 mb-6">
              We're sorry to see you go. Your subscription will remain active until the end of your current billing period on <strong>{subscription?.endDate ? formatDate(subscription.endDate) : 'N/A'}</strong>.
            </p>

            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Reason for cancellation
              </label>
              <select
                value={cancelReason}
                onChange={(e) => setCancelReason(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
              >
                <option value="">Select a reason</option>
                <option value="too_expensive">Too expensive</option>
                <option value="not_using">Not using the service enough</option>
                <option value="missing_features">Missing features</option>
                <option value="switching">Switching to another service</option>
                <option value="other">Other</option>
              </select>
            </div>

            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Additional feedback (optional)
              </label>
              <textarea
                value={cancelFeedback}
                onChange={(e) => setCancelFeedback(e.target.value)}
                rows="3"
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                placeholder="Please tell us how we could improve..."
              ></textarea>
            </div>

            <div className="flex justify-end space-x-4">
              <button
                onClick={() => setShowCancelModal(false)}
                className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700"
                disabled={processingCancel}
              >
                Keep Subscription
              </button>
              <button
                onClick={handleCancelSubscription}
                className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-70 disabled:cursor-not-allowed"
                disabled={processingCancel}
              >
                {processingCancel ? (
                  <>
                    <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white inline-block" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Processing...
                  </>
                ) : (
                  'Confirm Cancellation'
                )}
              </button>
            </div>
          </motion.div>
        </div>
      )}
    </div>
  );
};

export default SubscriptionManagement;
