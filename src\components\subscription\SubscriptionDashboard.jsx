import React, { useState } from 'react';
import PropTypes from 'prop-types';
import { motion } from 'framer-motion';
import {
  FiCreditCard,
  FiDownload,
  FiUpload,
  FiCalendar,
  FiRefreshCw,
  FiAlertTriangle,
  FiEdit,
  FiTrash2,
  FiPlus
} from 'react-icons/fi';
import Button from '../ui/Button';
import Badge from '../ui/Badge';
import Alert from '../ui/Alert';

const SubscriptionDashboard = ({ subscription, onUpgrade, onCancel, onUpdatePayment }) => {
  const [showCancelConfirm, setShowCancelConfirm] = useState(false);

  // Format date
  const formatDate = (dateString) => {
    const options = { year: 'numeric', month: 'long', day: 'numeric' };
    return new Date(dateString).toLocaleDateString(undefined, options);
  };

  // Get plan details
  const getPlanDetails = (planId) => {
    switch (planId) {
      case 'free':
        return {
          name: 'Free',
          color: 'secondary',
          limits: {
            downloads: 5,
            uploads: 0
          }
        };
      case 'basic':
        return {
          name: 'Basic',
          color: 'primary',
          limits: {
            downloads: 25,
            uploads: 10
          }
        };
      case 'pro':
        return {
          name: 'Professional',
          color: 'accent',
          limits: {
            downloads: 'Unlimited',
            uploads: 'Unlimited'
          }
        };
      default:
        return {
          name: 'Unknown',
          color: 'secondary',
          limits: {
            downloads: 0,
            uploads: 0
          }
        };
    }
  };

  const planDetails = getPlanDetails(subscription.planId);

  // Calculate days remaining in billing cycle
  const getDaysRemaining = () => {
    const nextBillingDate = new Date(subscription.nextBillingDate);
    const today = new Date();
    const diffTime = nextBillingDate - today;
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays > 0 ? diffDays : 0;
  };

  // Handle cancel subscription
  const handleCancelSubscription = () => {
    if (onCancel) {
      onCancel();
    }
    setShowCancelConfirm(false);
  };

  return (
    <div className="space-y-6">
      {/* Subscription Overview */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6"
      >
        <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
          <div>
            <h2 className="text-xl font-bold text-gray-900 dark:text-white mb-1">
              Current Subscription
            </h2>
            <p className="text-gray-600 dark:text-gray-400">
              Manage your subscription and payment details
            </p>
          </div>

          {subscription.planId !== 'pro' && (
            <Button
              variant="primary"
              size="md"
              onClick={onUpgrade}
              className="mt-4 md:mt-0"
            >
              Upgrade Plan
            </Button>
          )}
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
            <div className="flex items-center mb-2">
              <Badge variant={planDetails.color} className="mr-2">
                {planDetails.name}
              </Badge>
              <span className="text-sm text-gray-600 dark:text-gray-400">
                {subscription.billingCycle === 'monthly' ? 'Monthly' : 'Annual'} Plan
              </span>
            </div>

            {subscription.planId !== 'free' && (
              <div className="mt-2 text-sm text-gray-600 dark:text-gray-400">
                <div className="flex items-center">
                  <FiCalendar className="mr-2 h-4 w-4" />
                  <span>
                    Next billing: {formatDate(subscription.nextBillingDate)}
                  </span>
                </div>
                <div className="mt-1">
                  {getDaysRemaining()} days remaining in current cycle
                </div>
              </div>
            )}
          </div>

          <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
            <h3 className="font-medium text-gray-900 dark:text-white mb-2">
              Downloads
            </h3>
            <div className="flex items-center">
              <FiDownload className="mr-2 h-5 w-5 text-primary-600 dark:text-primary-400" />
              <div>
                <div className="font-medium text-gray-900 dark:text-white">
                  {subscription.usage.downloads} / {planDetails.limits.downloads}
                </div>
                <div className="text-sm text-gray-600 dark:text-gray-400">
                  {subscription.billingCycle === 'monthly' ? 'This month' : 'This year'}
                </div>
              </div>
            </div>
          </div>

          <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
            <h3 className="font-medium text-gray-900 dark:text-white mb-2">
              Uploads
            </h3>
            <div className="flex items-center">
              <FiUpload className="mr-2 h-5 w-5 text-accent-600 dark:text-accent-400" />
              <div>
                <div className="font-medium text-gray-900 dark:text-white">
                  {subscription.usage.uploads} / {planDetails.limits.uploads}
                </div>
                <div className="text-sm text-gray-600 dark:text-gray-400">
                  {subscription.billingCycle === 'monthly' ? 'This month' : 'This year'}
                </div>
              </div>
            </div>
          </div>
        </div>
      </motion.div>

      {/* Payment Methods */}
      {subscription.planId !== 'free' && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6"
        >
          <h2 className="text-xl font-bold text-gray-900 dark:text-white mb-4">
            Payment Methods
          </h2>

          <div className="space-y-4">
            {subscription.paymentMethods.map((method, index) => (
              <div
                key={index}
                className="flex items-center justify-between p-4 border border-gray-200 dark:border-gray-700 rounded-lg"
              >
                <div className="flex items-center">
                  {method.type === 'card' ? (
                    <div className="bg-gray-100 dark:bg-gray-700 p-2 rounded mr-4">
                      <FiCreditCard className="h-6 w-6 text-gray-600 dark:text-gray-400" />
                    </div>
                  ) : (
                    <div className="bg-gray-100 dark:bg-gray-700 p-2 rounded mr-4">
                      <svg className="h-6 w-6 text-gray-600 dark:text-gray-400" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M7.076 21.337H2.47a.641.641 0 0 1-.633-.74L4.944 3.72a.771.771 0 0 1 .76-.641h6.844c2.075 0 3.763.417 4.974 1.252 1.19.835 1.785 2.055 1.785 3.67 0 .76-.152 1.45-.456 2.075a4.415 4.415 0 0 1-1.252 1.594c-.532.437-1.158.798-1.88 1.082-.722.285-1.5.475-2.35.57.627.152 1.158.38 1.594.684.437.304.798.684 1.082 1.14.285.456.494.969.627 1.538.133.57.19 1.177.19 1.823 0 .722-.133 1.5-.399 2.33a5.088 5.088 0 0 1-1.31 2.151c-.608.627-1.4 1.12-2.37 1.481-.969.361-2.151.551-3.556.551h-.646a.769.769 0 0 1-.76-.646l-.152-.969zm2.426-10.45h3.214c.76 0 1.424-.095 1.994-.285a3.993 3.993 0 0 0 1.424-.798c.38-.342.665-.76.855-1.234.19-.475.285-1.006.285-1.594 0-1.12-.342-1.918-1.025-2.407-.684-.475-1.69-.722-3.024-.722H9.758l-.95 7.04h.693zm-1.177 8.66h3.518c.893 0 1.652-.114 2.274-.342a4.076 4.076 0 0 0 1.538-.95c.399-.399.703-.855.912-1.367.209-.513.313-1.063.313-1.652 0-1.234-.38-2.17-1.14-2.806-.76-.646-1.88-.969-3.366-.969h-3.1l-1.12 8.085h.171z" />
                      </svg>
                    </div>
                  )}

                  <div>
                    <div className="font-medium text-gray-900 dark:text-white">
                      {method.type === 'card'
                        ? `${method.brand} •••• ${method.last4}`
                        : 'PayPal Account'
                      }
                    </div>

                    {method.type === 'card' && (
                      <div className="text-sm text-gray-600 dark:text-gray-400">
                        Expires {method.expMonth}/{method.expYear}
                      </div>
                    )}

                    {method.isDefault && (
                      <Badge variant="success" size="sm" className="mt-1">
                        Default
                      </Badge>
                    )}
                  </div>
                </div>

                <div className="flex space-x-2">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => onUpdatePayment(method.id)}
                    title="Edit payment method"
                  >
                    <FiEdit className="h-4 w-4" />
                  </Button>

                  {!method.isDefault && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => {/* Handle delete */}}
                      title="Remove payment method"
                    >
                      <FiTrash2 className="h-4 w-4" />
                    </Button>
                  )}
                </div>
              </div>
            ))}

            <Button
              variant="secondary"
              size="md"
              leftIcon={<FiPlus />}
              onClick={() => {/* Handle add payment method */}}
            >
              Add Payment Method
            </Button>
          </div>
        </motion.div>
      )}

      {/* Billing History */}
      {subscription.planId !== 'free' && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6"
        >
          <h2 className="text-xl font-bold text-gray-900 dark:text-white mb-4">
            Billing History
          </h2>

          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
              <thead>
                <tr>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    Date
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    Description
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    Amount
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                    Receipt
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
                {subscription.billingHistory.map((item, index) => (
                  <tr key={index}>
                    <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-700 dark:text-gray-300">
                      {formatDate(item.date)}
                    </td>
                    <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-700 dark:text-gray-300">
                      {item.description}
                    </td>
                    <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-700 dark:text-gray-300">
                      ${item.amount.toFixed(2)}
                    </td>
                    <td className="px-4 py-4 whitespace-nowrap text-sm">
                      <Badge
                        variant={item.status === 'paid' ? 'success' : item.status === 'pending' ? 'warning' : 'error'}
                        size="sm"
                      >
                        {item.status.charAt(0).toUpperCase() + item.status.slice(1)}
                      </Badge>
                    </td>
                    <td className="px-4 py-4 whitespace-nowrap text-sm text-right">
                      <Button
                        variant="link"
                        size="sm"
                        onClick={() => window.open(item.receiptUrl, '_blank')}
                      >
                        Download
                      </Button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </motion.div>
      )}

      {/* Cancel Subscription */}
      {subscription.planId !== 'free' && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6"
        >
          <h2 className="text-xl font-bold text-gray-900 dark:text-white mb-4">
            Cancel Subscription
          </h2>

          {showCancelConfirm ? (
            <div>
              <Alert
                variant="warning"
                title="Are you sure you want to cancel?"
                icon
                className="mb-4"
              >
                <p>
                  Your subscription will remain active until the end of your current billing period on {formatDate(subscription.nextBillingDate)}.
                  After that, your account will be downgraded to the Free plan.
                </p>
              </Alert>

              <div className="flex space-x-4">
                <Button
                  variant="error"
                  size="md"
                  onClick={handleCancelSubscription}
                >
                  Yes, Cancel Subscription
                </Button>

                <Button
                  variant="secondary"
                  size="md"
                  onClick={() => setShowCancelConfirm(false)}
                >
                  No, Keep Subscription
                </Button>
              </div>
            </div>
          ) : (
            <div>
              <p className="text-gray-600 dark:text-gray-400 mb-4">
                You can cancel your subscription at any time. Your subscription will remain active until the end of your current billing period.
              </p>

              <Button
                variant="secondary"
                size="md"
                leftIcon={<FiAlertTriangle />}
                onClick={() => setShowCancelConfirm(true)}
              >
                Cancel Subscription
              </Button>
            </div>
          )}
        </motion.div>
      )}
    </div>
  );
};

SubscriptionDashboard.propTypes = {
  subscription: PropTypes.shape({
    planId: PropTypes.string.isRequired,
    billingCycle: PropTypes.string.isRequired,
    nextBillingDate: PropTypes.string,
    usage: PropTypes.shape({
      downloads: PropTypes.number.isRequired,
      uploads: PropTypes.number.isRequired
    }).isRequired,
    paymentMethods: PropTypes.arrayOf(
      PropTypes.shape({
        id: PropTypes.string.isRequired,
        type: PropTypes.string.isRequired,
        isDefault: PropTypes.bool.isRequired,
        brand: PropTypes.string,
        last4: PropTypes.string,
        expMonth: PropTypes.string,
        expYear: PropTypes.string
      })
    ),
    billingHistory: PropTypes.arrayOf(
      PropTypes.shape({
        date: PropTypes.string.isRequired,
        description: PropTypes.string.isRequired,
        amount: PropTypes.number.isRequired,
        status: PropTypes.string.isRequired,
        receiptUrl: PropTypes.string.isRequired
      })
    )
  }).isRequired,
  onUpgrade: PropTypes.func.isRequired,
  onCancel: PropTypes.func.isRequired,
  onUpdatePayment: PropTypes.func.isRequired
};

export default SubscriptionDashboard;
