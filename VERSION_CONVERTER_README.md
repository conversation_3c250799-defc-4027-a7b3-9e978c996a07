# SketchUp Version Converter Tool

## Overview

The SketchUp Version Converter Tool allows users to convert SketchUp models from higher versions to lower versions for compatibility with older SketchUp software. This feature is integrated into the 3DSKETCHUP.NET website and provides a seamless user experience for version downgrade conversions.

## Features

### Core Functionality
- **Version Detection**: Automatically detects the current SketchUp version of models
- **Downgrade Conversion**: Converts models from higher to lower SketchUp versions
- **Progress Tracking**: Real-time conversion progress with status updates
- **Smart Caching**: Stores converted versions to avoid re-conversion
- **Download Management**: Integrates with existing smart download system

### Supported Versions
- SketchUp 2024
- SketchUp 2023
- SketchUp 2022
- SketchUp 2021
- SketchUp 2020

### User Interface
- **Convert Version Button**: Appears only for SketchUp models
- **Version Selection**: Interactive UI for choosing target version
- **Progress Indicators**: Visual progress bars and status messages
- **Download Options**: Direct download of converted versions

## Technical Implementation

### Backend Components

#### 1. Database Schema Extensions
- **Model.js**: Added version conversion fields
  - `sketchupVersion`: Current SketchUp version
  - `availableVersions`: Array of converted versions
  - `conversionHistory`: Conversion request tracking

#### 2. Version Conversion Service
- **versionConverter.js**: Core conversion logic
  - Version compatibility checking
  - File conversion processing
  - Cleanup and maintenance utilities

#### 3. API Endpoints
- **versionController.js**: RESTful API for version operations
  - `GET /api/version/:modelId/available` - Get available versions
  - `POST /api/version/:modelId/convert` - Request conversion
  - `GET /api/version/:modelId/conversion/:conversionId` - Check status
  - `GET /api/version/:modelId/download/:version` - Download converted version
  - `GET /api/version/stats` - Conversion statistics (admin)

### Frontend Components

#### 1. Version Converter Component
- **VersionConverter.jsx**: Main UI component
  - Version selection interface
  - Progress tracking
  - Download management
  - Error handling

#### 2. Model Detail Integration
- **ModelDetail.jsx**: Enhanced with version converter
  - Conditional display for SketchUp models
  - Modal integration
  - User authentication checks

## API Usage

### Get Available Versions
```javascript
GET /api/version/:modelId/available

Response:
{
  "success": true,
  "data": {
    "currentVersion": "2023",
    "availableTargetVersions": ["2022", "2021", "2020"],
    "convertedVersions": [],
    "canConvert": true
  }
}
```

### Request Conversion
```javascript
POST /api/version/:modelId/convert
{
  "targetVersion": "2022"
}

Response:
{
  "success": true,
  "data": {
    "conversionId": "...",
    "status": "pending",
    "estimatedTime": "2-5 minutes"
  }
}
```

### Check Conversion Status
```javascript
GET /api/version/:modelId/conversion/:conversionId

Response:
{
  "success": true,
  "data": {
    "status": "completed",
    "fileUrl": "/api/download/file/converted_model.skp",
    "fileSize": 1024000
  }
}
```

## User Workflow

1. **Model Access**: User visits a SketchUp model detail page
2. **Version Check**: System displays current version and conversion options
3. **Version Selection**: User clicks "Convert Version" button
4. **Target Selection**: User selects desired target version
5. **Conversion Request**: System starts conversion process
6. **Progress Tracking**: User sees real-time conversion progress
7. **Download**: User downloads converted model when ready

## Configuration

### Environment Variables
- `MONGODB_URI`: Database connection string
- `NODE_ENV`: Environment mode (development/production)

### File Storage
- Converted files stored in `backend/downloads/conversions/`
- Temporary files in `backend/temp/`
- Automatic cleanup of old files

## Security & Authentication

- **User Authentication**: Required for conversion requests
- **Rate Limiting**: Prevents abuse of conversion service
- **File Validation**: Ensures only valid SketchUp files are processed
- **Access Control**: Users can only access their own conversions

## Performance Considerations

- **Asynchronous Processing**: Conversions run in background
- **Caching**: Converted versions stored for reuse
- **Cleanup**: Automatic removal of old conversion files
- **Progress Updates**: Real-time status via polling

## Error Handling

- **Invalid Models**: Graceful handling of non-SketchUp files
- **Conversion Failures**: Detailed error messages and retry options
- **Network Issues**: Robust error recovery and user feedback
- **Authentication**: Proper login redirects for unauthorized users

## Future Enhancements

- **Real SketchUp API Integration**: Replace simulation with actual SketchUp conversion
- **Batch Conversion**: Convert multiple models simultaneously
- **Version Upgrade**: Support for upgrading to newer versions
- **Format Conversion**: Extend to other 3D file formats
- **Cloud Processing**: Offload conversion to cloud services

## Installation & Setup

1. **Backend Setup**:
   ```bash
   cd backend
   npm install
   npm start
   ```

2. **Frontend Setup**:
   ```bash
   npm install
   npm run dev
   ```

3. **Database**: Ensure MongoDB is running and accessible

4. **Testing**: Use the provided test scripts to verify functionality

## Support

For issues or questions regarding the Version Converter Tool, please refer to the main project documentation or contact the development team.
