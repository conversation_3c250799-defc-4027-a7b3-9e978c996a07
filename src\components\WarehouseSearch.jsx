import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { toast } from 'react-hot-toast';
import {
  FiSearch, FiExternalLink, FiDownload, FiLoader,
  FiTrendingUp, FiEye, FiHeart, FiUser
} from 'react-icons/fi';
import axios from 'axios';

const WarehouseSearch = ({ onClose, onDownload }) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState([]);
  const [trendingModels, setTrendingModels] = useState([]);
  const [isSearching, setIsSearching] = useState(false);
  const [isLoadingTrending, setIsLoadingTrending] = useState(false);
  const [activeTab, setActiveTab] = useState('search');

  useEffect(() => {
    if (activeTab === 'trending') {
      loadTrendingModels();
    }
  }, [activeTab]);

  const searchWarehouse = async () => {
    if (!searchQuery.trim()) {
      toast.error('Please enter a search query');
      return;
    }

    setIsSearching(true);
    try {
      const response = await axios.get('/api/download/warehouse/search', {
        params: { q: searchQuery }
      });

      if (response.data.success) {
        setSearchResults(response.data.data.models);
        if (response.data.data.imported) {
          toast.success(`Imported ${response.data.data.count} models to your library!`);
        } else {
          toast.success(`Found ${response.data.data.count} models`);
        }
      }
    } catch (error) {
      console.error('Warehouse search failed:', error);
      toast.error('Search failed. Please try again.');
    } finally {
      setIsSearching(false);
    }
  };

  const loadTrendingModels = async () => {
    setIsLoadingTrending(true);
    try {
      const response = await axios.get('/api/download/warehouse/trending');

      if (response.data.success) {
        setTrendingModels(response.data.data.models);
      }
    } catch (error) {
      console.error('Failed to load trending models:', error);
      toast.error('Failed to load trending models');
    } finally {
      setIsLoadingTrending(false);
    }
  };

  const downloadModel = async (model) => {
    try {
      // Since models are now imported to our database, use our smart download
      const response = await axios.post('/api/download/smart', {
        backupLinks: model.backupLinks || [model.fileUrl],
        filename: `${model.title.replace(/[^a-zA-Z0-9]/g, '_')}.skp`,
        modelId: model._id || model.id
      });

      if (response.data.success) {
        // Create download link
        const downloadUrl = `${window.location.origin}/api/download/file/${response.data.data.filename}`;

        // Trigger download
        const link = document.createElement('a');
        link.href = downloadUrl;
        link.download = response.data.data.filename;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        toast.success('Downloaded successfully!');
        onDownload?.(model);
      }
    } catch (error) {
      console.error('Download failed:', error);
      toast.error(error.response?.data?.error || 'Download failed');
    }
  };

  const ModelCard = ({ model }) => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="bg-white dark:bg-gray-700 rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow"
    >
      {model.thumbnail && (
        <img
          src={model.thumbnail}
          alt={model.title}
          className="w-full h-48 object-cover"
          onError={(e) => {
            e.target.src = '/images/placeholder.jpg';
          }}
        />
      )}

      <div className="p-4">
        <h3 className="font-medium text-gray-900 dark:text-white mb-2 line-clamp-2">
          {model.title}
        </h3>

        {model.author && (
          <div className="flex items-center text-sm text-gray-600 dark:text-gray-400 mb-2">
            <FiUser className="h-4 w-4 mr-1" />
            <span>{model.author}</span>
          </div>
        )}

        {model.description && (
          <p className="text-sm text-gray-600 dark:text-gray-400 mb-3 line-clamp-2">
            {model.description}
          </p>
        )}

        <div className="flex justify-between items-center">
          <div className="flex items-center space-x-4 text-xs text-gray-500 dark:text-gray-400">
            {model.stats?.views && (
              <div className="flex items-center">
                <FiEye className="h-3 w-3 mr-1" />
                <span>{model.stats.views}</span>
              </div>
            )}
            {model.stats?.likes && (
              <div className="flex items-center">
                <FiHeart className="h-3 w-3 mr-1" />
                <span>{model.stats.likes}</span>
              </div>
            )}
          </div>

          <div className="flex space-x-2">
            <button
              onClick={() => {
                // Navigate to model detail page instead of external link
                window.location.href = `/model/${model._id || model.id}`;
              }}
              className="p-2 text-gray-600 hover:text-blue-600 transition-colors"
              title="View Details"
            >
              <FiEye className="h-4 w-4" />
            </button>

            <button
              onClick={() => downloadModel(model)}
              className="p-2 text-gray-600 hover:text-green-600 transition-colors"
              title="Download"
            >
              <FiDownload className="h-4 w-4" />
            </button>
          </div>
        </div>
      </div>
    </motion.div>
  );

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
      onClick={(e) => e.target === e.currentTarget && onClose?.()}
    >
      <motion.div
        initial={{ scale: 0.9, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        exit={{ scale: 0.9, opacity: 0 }}
        className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-6xl w-full max-h-[90vh] overflow-hidden"
      >
        {/* Header */}
        <div className="flex justify-between items-center p-6 border-b border-gray-200 dark:border-gray-700">
          <div>
            <h2 className="text-xl font-bold text-gray-900 dark:text-white">3D Warehouse Integration</h2>
            <p className="text-gray-600 dark:text-gray-400">Search and download from Google 3D Warehouse</p>
          </div>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-full transition-colors"
          >
            ×
          </button>
        </div>

        {/* Tabs */}
        <div className="flex border-b border-gray-200 dark:border-gray-700">
          <button
            onClick={() => setActiveTab('search')}
            className={`px-6 py-3 font-medium text-sm ${
              activeTab === 'search'
                ? 'text-blue-600 border-b-2 border-blue-600'
                : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'
            }`}
          >
            <FiSearch className="inline h-4 w-4 mr-2" />
            Search
          </button>
          <button
            onClick={() => setActiveTab('trending')}
            className={`px-6 py-3 font-medium text-sm ${
              activeTab === 'trending'
                ? 'text-blue-600 border-b-2 border-blue-600'
                : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'
            }`}
          >
            <FiTrendingUp className="inline h-4 w-4 mr-2" />
            Trending
          </button>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[calc(90vh-200px)]">
          {activeTab === 'search' && (
            <div className="space-y-6">
              {/* Search Bar */}
              <div className="flex space-x-3">
                <div className="flex-1 relative">
                  <input
                    type="text"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    onKeyPress={(e) => e.key === 'Enter' && searchWarehouse()}
                    placeholder="Search 3D models on Google 3D Warehouse..."
                    className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
                <button
                  onClick={searchWarehouse}
                  disabled={isSearching}
                  className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 transition-colors flex items-center space-x-2"
                >
                  {isSearching ? (
                    <FiLoader className="h-5 w-5 animate-spin" />
                  ) : (
                    <FiSearch className="h-5 w-5" />
                  )}
                  <span>{isSearching ? 'Searching...' : 'Search'}</span>
                </button>
              </div>

              {/* Search Results */}
              {searchResults.length > 0 && (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {searchResults.map((model, index) => (
                    <ModelCard key={index} model={model} />
                  ))}
                </div>
              )}

              {searchResults.length === 0 && searchQuery && !isSearching && (
                <div className="text-center py-12">
                  <FiSearch className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-600 dark:text-gray-400">No results found. Try a different search term.</p>
                </div>
              )}
            </div>
          )}

          {activeTab === 'trending' && (
            <div className="space-y-6">
              {isLoadingTrending ? (
                <div className="text-center py-12">
                  <FiLoader className="h-8 w-8 text-blue-600 mx-auto mb-4 animate-spin" />
                  <p className="text-gray-600 dark:text-gray-400">Loading trending models...</p>
                </div>
              ) : trendingModels.length > 0 ? (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {trendingModels.map((model, index) => (
                    <ModelCard key={index} model={model} />
                  ))}
                </div>
              ) : (
                <div className="text-center py-12">
                  <FiTrendingUp className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-600 dark:text-gray-400">No trending models available.</p>
                </div>
              )}
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="p-4 bg-gray-50 dark:bg-gray-700 border-t border-gray-200 dark:border-gray-600">
          <p className="text-xs text-gray-600 dark:text-gray-400 text-center">
            Models are sourced from Google 3D Warehouse. All downloads are proxied through our servers for better performance.
          </p>
        </div>
      </motion.div>
    </motion.div>
  );
};

export default WarehouseSearch;
