import mongoose from 'mongoose';
import dotenv from 'dotenv';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

// Load environment variables
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
dotenv.config({ path: join(__dirname, '../../.env') });

// Import models
import User from '../src/models/User.js';

// Connect to MongoDB
const connectDB = async () => {
  try {
    console.log('Connecting to MongoDB...');
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('MongoDB Connected for recreating users...');
  } catch (error) {
    console.error('Error connecting to MongoDB:', error);
    process.exit(1);
  }
};

// Recreate users with proper password hashing
const recreateUsers = async () => {
  try {
    console.log('Starting to recreate users...');

    // Delete existing users
    console.log('Deleting existing users...');
    await User.deleteMany({});
    console.log('✅ Existing users deleted');

    // Create admin user
    console.log('Creating admin user...');
    const adminUser = await User.create({
      name: 'Admin User',
      email: '<EMAIL>',
      password: 'admin123',
      role: 'admin',
      bio: 'System Administrator'
    });
    console.log('✅ Admin user created successfully!');
    console.log('Admin ID:', adminUser._id);
    console.log('Admin email:', adminUser.email);

    // Create test user
    console.log('Creating test user...');
    const testUser = await User.create({
      name: 'Test User',
      email: '<EMAIL>',
      password: 'test123',
      role: 'user',
      bio: 'Test user for authentication'
    });
    console.log('✅ Test user created successfully!');
    console.log('Test user ID:', testUser._id);
    console.log('Test user email:', testUser.email);

    // Create designer user
    console.log('Creating designer user...');
    const designerUser = await User.create({
      name: 'John Designer',
      email: '<EMAIL>',
      password: 'designer123',
      role: 'user',
      bio: 'Professional 3D Designer'
    });
    console.log('✅ Designer user created successfully!');
    console.log('Designer ID:', designerUser._id);
    console.log('Designer email:', designerUser.email);

    // Test password matching for all users
    console.log('\nTesting password matching...');
    
    const adminPasswordMatch = await adminUser.matchPassword('admin123');
    console.log('Admin password match:', adminPasswordMatch);
    
    const testPasswordMatch = await testUser.matchPassword('test123');
    console.log('Test user password match:', testPasswordMatch);
    
    const designerPasswordMatch = await designerUser.matchPassword('designer123');
    console.log('Designer password match:', designerPasswordMatch);

    // Test JWT token generation
    console.log('\nTesting JWT token generation...');
    const adminToken = adminUser.getSignedJwtToken();
    const testToken = testUser.getSignedJwtToken();
    const designerToken = designerUser.getSignedJwtToken();
    
    console.log('Admin token generated:', adminToken ? 'Yes' : 'No');
    console.log('Test token generated:', testToken ? 'Yes' : 'No');
    console.log('Designer token generated:', designerToken ? 'Yes' : 'No');

    console.log('\n🎉 Users recreated successfully!');
    console.log('\nLogin credentials:');
    console.log('1. Admin: <EMAIL> / admin123');
    console.log('2. Test User: <EMAIL> / test123');
    console.log('3. Designer: <EMAIL> / designer123');

    return { adminUser, testUser, designerUser };

  } catch (error) {
    console.error('❌ Error recreating users:', error);
    throw error;
  }
};

// Run the script
const main = async () => {
  try {
    await connectDB();
    await recreateUsers();
  } catch (error) {
    console.error('❌ Script failed:', error);
  } finally {
    mongoose.connection.close();
    console.log('Database connection closed.');
  }
};

// Execute
main();
