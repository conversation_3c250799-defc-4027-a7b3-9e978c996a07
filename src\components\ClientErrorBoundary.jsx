// Import React directly to ensure Component is available
import React, { Component } from 'react';
import { FiAlertTriangle, FiRefreshCw } from 'react-icons/fi';
import toast from 'react-hot-toast';

/**
 * A specialized error boundary for client components
 * This helps catch and handle errors in client components that might otherwise crash the app
 */
class ClientErrorBoundary extends Component {
  constructor(props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null
    };
  }

  static getDerivedStateFromError(error) {
    // Update state so the next render will show the fallback UI
    return { hasError: true, error };
  }

  componentDidCatch(error, errorInfo) {
    // Log the error
    console.error('ClientErrorBoundary caught an error:', error, errorInfo);

    // Update error state
    this.setState({ errorInfo });

    // Show toast notification
    toast.error('An error occurred in a client component. Trying to recover...');

    // Attempt to recover after a short delay
    setTimeout(() => {
      this.handleReset();
    }, 3000);
  }

  handleReset = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null
    });
  };

  render() {
    const { hasError, error } = this.state;
    const { children, fallback } = this.props;

    if (hasError) {
      // Use custom fallback if provided
      if (fallback) {
        return fallback(error, this.handleReset);
      }

      // Default fallback UI
      return (
        <div className="p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
          <div className="flex items-center">
            <FiAlertTriangle className="h-5 w-5 text-red-500 mr-2" />
            <h3 className="text-sm font-medium text-red-800 dark:text-red-200">
              Component Error
            </h3>
          </div>
          <div className="mt-2 text-sm text-red-700 dark:text-red-300">
            <p>This component encountered an error and couldn't be displayed.</p>
          </div>
          <div className="mt-3">
            <button
              onClick={this.handleReset}
              className="inline-flex items-center px-2 py-1 border border-transparent text-xs font-medium rounded-md shadow-sm text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
            >
              <FiRefreshCw className="mr-1" />
              Try Again
            </button>
          </div>
        </div>
      );
    }

    // If there's no error, render children normally
    return children;
  }
}

export default ClientErrorBoundary;
