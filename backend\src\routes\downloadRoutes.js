import express from 'express';
import {
  smartDownload,
  checkLinks,
  searchWarehouse,
  getTrendingWarehouse,
  getWarehouseModelDetails,
  downloadFromWarehouse,
  bulkImportWarehouse,
  getPluginsData,
  serveFile,
  cleanupFiles
} from '../controllers/downloadController.js';
import { protect, authorize } from '../middleware/auth.js';

const router = express.Router();

// Smart download with backup links
router.post('/smart', smartDownload);

// Check link status
router.post('/check-links', checkLinks);

// 3D Warehouse integration
router.get('/warehouse/search', searchWarehouse);
router.get('/warehouse/trending', getTrendingWarehouse);
router.get('/warehouse/model', getWarehouseModelDetails);
router.post('/warehouse/download', downloadFromWarehouse);
router.post('/warehouse/bulk-import', protect, authorize('admin'), bulkImportWarehouse);

// SketchUp plugins data
router.get('/plugins', getPluginsData);

// File serving
router.get('/file/:filename', serveFile);

// Admin routes
router.post('/cleanup', protect, authorize('admin'), cleanupFiles);

export default router;
