import { GoogleGenerativeAI } from '@google/generative-ai';
import Model from '../models/Model.js';
import Extension from '../models/Extension.js';
import ImageProcessor from '../utils/imageProcessor.js';

class ImageSearchService {
  constructor() {
    this.genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY);
    this.imageProcessor = new ImageProcessor();
  }

  /**
   * Analyze image and find related 3D models
   */
  async analyzeImageForModels(imageBuffer, language = 'vi') {
    try {
      console.log('🔍 Starting image analysis for 3D models...');

      // Process image
      const processedImage = await this.imageProcessor.processImage(imageBuffer, {
        width: 512,
        height: 512,
        quality: 80,
        format: 'jpeg'
      });

      // Analyze with Gemini Vision using enhanced prompts
      const analysis = await this.analyzeImageWithGemini(processedImage.buffer, {
        context: 'model_search',
        language,
        prompt: language === 'vi'
          ? `<PERSON>ân tích hình ảnh này một cách chi tiết và toàn diện để xác định:

1. **<PERSON>ếu tố kiến trúc**: c<PERSON><PERSON>, c<PERSON><PERSON> sổ, tường, trần, sàn, mái, cột, dầm, cầu thang, ban công, mặt tiền, hiên, sân thượng
2. **Nội thất và đồ đạc**: ghế, bàn, sofa, giường, bàn làm việc, tủ, kệ, đèn, tủ quần áo, tủ ngăn kéo, bàn đầu giường, gương
3. **Vật trang trí**: tranh ảnh, cây cối, rèm cửa, thảm, đèn chùm, phụ kiện trang trí
4. **Thành phần cấu trúc**: móng, khung, hỗ trợ, khớp nối
5. **Màu sắc**: màu chính (đỏ, xanh, vàng), màu trung tính (trắng, đen, xám, nâu), màu vật liệu
6. **Vật liệu**: gỗ, đá, gạch, kim loại, thủy tinh, nhựa, vải, da, bê tông
7. **Phong cách kiến trúc**: hiện đại, cổ điển, tối giản, công nghiệp, v.v.
8. **Phong cách nội thất**: mid-century, farmhouse, bohemian, eclectic, v.v.
9. **Loại phòng**: phòng khách, phòng ngủ, bếp, phòng tắm, v.v.
10. **Loại công trình**: nhà ở, chung cư, biệt thự, văn phòng, v.v.

Hãy mô tả chi tiết từng yếu tố bạn nhận diện được và giải thích tại sao chúng quan trọng cho việc tìm kiếm mô hình 3D phù hợp.`
          : `Analyze this image comprehensively and identify:

1. **Architectural Elements**: doors, windows, walls, ceilings, floors, roofs, columns, beams, stairs, balconies, facades, porches, terraces
2. **Furniture & Objects**: chairs, tables, sofas, beds, desks, cabinets, shelves, lamps, wardrobes, dressers, nightstands, mirrors
3. **Decorative Items**: artwork, plants, curtains, rugs, lighting fixtures, accessories
4. **Structural Components**: foundations, frameworks, supports, joints
5. **Colors**: primary colors (red, blue, yellow), neutral tones (white, black, gray, brown), material-specific colors
6. **Materials**: wood, stone, brick, metal, glass, plastic, fabric, leather, concrete
7. **Architectural Style**: modern, contemporary, traditional, minimalist, industrial, etc.
8. **Interior Design Style**: mid-century modern, farmhouse, bohemian, eclectic, etc.
9. **Room Type**: living room, bedroom, kitchen, bathroom, etc.
10. **Building Type**: house, apartment, villa, office, commercial, etc.

Please describe each element you identify in detail and explain why they are important for finding suitable 3D models.`
      });

      if (!analysis.success) {
        throw new Error('Failed to analyze image: ' + analysis.error);
      }

      // Find related models from database
      const relatedModels = await this.findRelatedModels(analysis.data);

      // Generate contextual response
      const response = await this.generateModelResponse(analysis.data, relatedModels, language);

      return {
        success: true,
        data: {
          analysis: analysis.data,
          relatedModels: relatedModels.slice(0, 6),
          response,
          metadata: {
            processedImageSize: processedImage.buffer.length,
            totalModelsFound: relatedModels.length
          }
        }
      };

    } catch (error) {
      console.error('❌ Image analysis for models failed:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Analyze image and find related models with similarity scoring
   */
  async analyzeImageForVisualModelSearch(imageBuffer, category = '', language = 'vi') {
    try {
      console.log('🔍 Starting visual model search analysis...');

      // Process image
      const processedImage = await this.imageProcessor.processImage(imageBuffer, {
        width: 512,
        height: 512,
        quality: 80,
        format: 'jpeg'
      });

      // Analyze with Gemini Vision for comprehensive visual model search
      const analysis = await this.analyzeImageWithGemini(processedImage.buffer, {
        context: 'visual_model_search',
        language,
        prompt: language === 'vi'
          ? `Thực hiện phân tích hình ảnh toàn diện để tìm kiếm mô hình 3D tương tự. Hãy xác định chi tiết:

**PHÂN TÍCH KIẾN TRÚC & THIẾT KẾ:**
- Yếu tố kiến trúc: cửa ra vào, cửa sổ, tường, mái, cột, dầm, cầu thang, ban công
- Phong cách kiến trúc: hiện đại, cổ điển, tối giản, công nghiệp, địa trung hải, scandinavian
- Loại công trình: nhà ở, chung cư, biệt thự, văn phòng, thương mại

**PHÂN TÍCH NỘI THẤT:**
- Đồ nội thất: ghế, bàn, sofa, giường, tủ, kệ, đèn
- Phong cách nội thất: mid-century, farmhouse, bohemian, eclectic
- Loại phòng: phòng khách, phòng ngủ, bếp, phòng tắm

**PHÂN TÍCH VẬT LIỆU & MÀU SẮC:**
- Vật liệu tự nhiên: gỗ, đá, gạch, đá cẩm thạch
- Vật liệu nhân tạo: kim loại, thủy tinh, nhựa, bê tông
- Màu sắc chính: đỏ, xanh, vàng, xanh lá
- Màu trung tính: trắng, đen, xám, nâu, be

**ĐÁNH GIÁ TƯƠNG ĐỒNG:**
Dựa trên phân tích, hãy đề xuất các loại mô hình 3D nào sẽ phù hợp nhất và giải thích lý do tại sao chúng tương tự với hình ảnh này.`
          : `Perform comprehensive image analysis for visual 3D model search. Please identify in detail:

**ARCHITECTURAL & DESIGN ANALYSIS:**
- Architectural elements: doors, windows, walls, roofs, columns, beams, stairs, balconies
- Architectural style: modern, contemporary, traditional, minimalist, industrial, mediterranean, scandinavian
- Building type: residential, apartment, villa, office, commercial

**INTERIOR ANALYSIS:**
- Furniture items: chairs, tables, sofas, beds, cabinets, shelves, lamps
- Interior design style: mid-century modern, farmhouse, bohemian, eclectic
- Room type: living room, bedroom, kitchen, bathroom

**MATERIAL & COLOR ANALYSIS:**
- Natural materials: wood, stone, brick, marble, granite
- Manufactured materials: metal, glass, plastic, concrete
- Primary colors: red, blue, yellow, green
- Neutral tones: white, black, gray, brown, beige

**SIMILARITY ASSESSMENT:**
Based on your analysis, suggest what types of 3D models would be most suitable and explain why they are similar to this image.`
      });

      if (!analysis.success) {
        throw new Error('Failed to analyze image: ' + analysis.error);
      }

      // Find related models with tiered matching
      const modelMatches = await this.findVisuallyRelatedModels(analysis.data, category);

      // Calculate similarity scores and organize by match level
      const organizedResults = this.organizeModelsByMatchLevel(modelMatches, analysis.data);

      return {
        success: true,
        data: {
          analysis: analysis.data,
          models: organizedResults.allModels,
          matchLevels: {
            exact: organizedResults.exactMatches,
            similar: organizedResults.similarMatches,
            related: organizedResults.relatedMatches
          },
          totalFound: organizedResults.allModels.length,
          searchType: 'visual_model_search',
          metadata: {
            processedImageSize: processedImage.buffer.length,
            analysisTimestamp: new Date().toISOString()
          }
        }
      };

    } catch (error) {
      console.error('❌ Visual model search failed:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Analyze image with Gemini Vision API
   */
  async analyzeImageWithGemini(imageBuffer, options = {}) {
    try {
      const { prompt, language = 'en', context = 'general' } = options;

      // Convert to base64
      const base64Image = imageBuffer.toString('base64');

      // Get model
      const model = this.genAI.getGenerativeModel({ model: "gemini-1.5-flash" });

      // Prepare image part
      const imagePart = {
        inlineData: {
          data: base64Image,
          mimeType: "image/jpeg"
        }
      };

      // Generate content
      const result = await model.generateContent([prompt, imagePart]);
      const response = await result.response;
      const analysisText = response.text();

      console.log('✅ Gemini Vision analysis completed');

      // Parse analysis
      const parsedAnalysis = this.parseAnalysis(analysisText, context);

      return {
        success: true,
        data: {
          description: analysisText,
          ...parsedAnalysis,
          metadata: {
            model: "gemini-1.5-flash",
            timestamp: new Date().toISOString(),
            language,
            context
          }
        }
      };

    } catch (error) {
      console.error('❌ Gemini Vision API error:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Parse analysis text into structured data with comprehensive vocabulary
   */
  parseAnalysis(text, context) {
    const lowerText = text.toLowerCase();

    const analysis = {
      elements: [],
      objects: [],
      furniture: [],
      decorativeItems: [],
      structuralComponents: [],
      style: 'modern',
      architecturalStyle: '',
      interiorStyle: '',
      colors: [],
      primaryColors: [],
      neutralTones: [],
      materialColors: [],
      categories: [],
      tags: [],
      materials: [],
      naturalMaterials: [],
      manufacturedMaterials: [],
      designFeatures: [],
      roomType: '',
      buildingType: '',
      confidence: 0
    };

    // Comprehensive Architectural Elements
    const architecturalElements = [
      'door', 'doors', 'window', 'windows', 'wall', 'walls', 'ceiling', 'ceilings',
      'floor', 'floors', 'roof', 'roofs', 'column', 'columns', 'beam', 'beams',
      'balcony', 'balconies', 'staircase', 'stairs', 'stair', 'facade', 'facades',
      'entrance', 'entrances', 'porch', 'porches', 'terrace', 'terraces',
      'foundation', 'foundations', 'framework', 'frameworks', 'support', 'supports',
      'joint', 'joints', 'arch', 'arches', 'pillar', 'pillars', 'railing', 'railings'
    ];

    // Comprehensive Furniture Elements
    const furnitureElements = [
      'chair', 'chairs', 'table', 'tables', 'sofa', 'sofas', 'couch', 'couches',
      'bed', 'beds', 'desk', 'desks', 'cabinet', 'cabinets', 'shelf', 'shelves',
      'lamp', 'lamps', 'wardrobe', 'wardrobes', 'dresser', 'dressers',
      'nightstand', 'nightstands', 'bookshelf', 'bookshelves', 'mirror', 'mirrors',
      'armchair', 'armchairs', 'ottoman', 'ottomans', 'bench', 'benches',
      'stool', 'stools', 'sideboard', 'sideboards', 'credenza', 'credenzas',
      'coffee table', 'dining table', 'side table', 'end table', 'console table'
    ];

    // Decorative Items
    const decorativeItems = [
      'artwork', 'art', 'painting', 'paintings', 'sculpture', 'sculptures',
      'plant', 'plants', 'flower', 'flowers', 'vase', 'vases',
      'curtain', 'curtains', 'drape', 'drapes', 'blind', 'blinds',
      'rug', 'rugs', 'carpet', 'carpets', 'mat', 'mats',
      'lighting fixture', 'light fixture', 'chandelier', 'chandeliers',
      'pendant light', 'sconce', 'sconces', 'accessory', 'accessories',
      'cushion', 'cushions', 'pillow', 'pillows', 'throw', 'throws',
      'ornament', 'ornaments', 'decoration', 'decorations'
    ];

    // Structural Components
    const structuralComponents = [
      'foundation', 'foundations', 'framework', 'frameworks', 'frame', 'frames',
      'support', 'supports', 'joint', 'joints', 'connection', 'connections',
      'beam', 'beams', 'joist', 'joists', 'truss', 'trusses',
      'bracket', 'brackets', 'anchor', 'anchors', 'fastener', 'fasteners'
    ];

    // Extract architectural elements
    architecturalElements.forEach(element => {
      if (lowerText.includes(element)) {
        analysis.elements.push(element);
      }
    });

    // Extract furniture
    furnitureElements.forEach(element => {
      if (lowerText.includes(element)) {
        analysis.furniture.push(element);
        analysis.objects.push(element); // Keep backward compatibility
      }
    });

    // Extract decorative items
    decorativeItems.forEach(item => {
      if (lowerText.includes(item)) {
        analysis.decorativeItems.push(item);
        analysis.objects.push(item);
      }
    });

    // Extract structural components
    structuralComponents.forEach(component => {
      if (lowerText.includes(component)) {
        analysis.structuralComponents.push(component);
        analysis.elements.push(component);
      }
    });

    // Enhanced Color Detection
    // Primary Colors
    const primaryColors = [
      'red', 'blue', 'yellow', 'green', 'orange', 'purple', 'violet', 'indigo'
    ];
    primaryColors.forEach(color => {
      if (lowerText.includes(color)) {
        analysis.primaryColors.push(color);
        analysis.colors.push(color);
      }
    });

    // Neutral Tones
    const neutralTones = [
      'white', 'black', 'gray', 'grey', 'brown', 'beige', 'cream', 'tan',
      'ivory', 'off-white', 'charcoal', 'slate', 'taupe', 'khaki'
    ];
    neutralTones.forEach(color => {
      if (lowerText.includes(color)) {
        analysis.neutralTones.push(color);
        analysis.colors.push(color);
      }
    });

    // Material-specific Colors
    const materialColors = [
      'wood tone', 'wooden', 'oak', 'pine', 'mahogany', 'walnut', 'cherry',
      'metal finish', 'metallic', 'bronze', 'copper', 'brass', 'chrome', 'silver',
      'stone color', 'granite', 'marble', 'limestone', 'sandstone'
    ];
    materialColors.forEach(color => {
      if (lowerText.includes(color)) {
        analysis.materialColors.push(color);
        analysis.colors.push(color);
      }
    });

    // Enhanced Material Detection
    // Natural Materials
    const naturalMaterials = [
      'wood', 'wooden', 'timber', 'oak', 'pine', 'mahogany', 'walnut', 'cherry',
      'stone', 'granite', 'marble', 'limestone', 'sandstone', 'slate',
      'brick', 'clay', 'ceramic', 'tile', 'bamboo', 'rattan', 'wicker'
    ];
    naturalMaterials.forEach(material => {
      if (lowerText.includes(material)) {
        analysis.naturalMaterials.push(material);
        analysis.materials.push(material);
      }
    });

    // Manufactured Materials
    const manufacturedMaterials = [
      'metal', 'steel', 'aluminum', 'iron', 'brass', 'copper', 'bronze',
      'glass', 'tempered glass', 'frosted glass', 'plastic', 'acrylic',
      'fabric', 'textile', 'leather', 'vinyl', 'laminate', 'composite',
      'concrete', 'cement', 'plaster', 'drywall', 'fiberglass'
    ];
    manufacturedMaterials.forEach(material => {
      if (lowerText.includes(material)) {
        analysis.manufacturedMaterials.push(material);
        analysis.materials.push(material);
      }
    });

    // Enhanced Style Recognition
    // Architectural Styles
    const architecturalStyles = [
      'modern', 'contemporary', 'traditional', 'minimalist', 'industrial',
      'victorian', 'colonial', 'mediterranean', 'rustic', 'scandinavian',
      'art deco', 'bauhaus', 'gothic', 'baroque', 'neoclassical',
      'craftsman', 'prairie', 'tudor', 'georgian', 'federal', 'ranch',
      'cape cod', 'bungalow', 'cottage', 'farmhouse', 'loft'
    ];
    architecturalStyles.forEach(style => {
      if (lowerText.includes(style)) {
        analysis.style = style;
        analysis.architecturalStyle = style;
      }
    });

    // Interior Design Styles
    const interiorStyles = [
      'mid-century modern', 'farmhouse', 'bohemian', 'boho', 'eclectic',
      'transitional', 'shabby chic', 'vintage', 'retro', 'art nouveau',
      'french country', 'coastal', 'nautical', 'tropical', 'zen',
      'asian', 'moroccan', 'southwestern', 'lodge', 'cabin'
    ];
    interiorStyles.forEach(style => {
      if (lowerText.includes(style)) {
        analysis.interiorStyle = style;
        if (!analysis.style || analysis.style === 'modern') {
          analysis.style = style;
        }
      }
    });

    // Room Type Detection
    const roomTypes = [
      'living room', 'bedroom', 'kitchen', 'bathroom', 'dining room',
      'office', 'study', 'library', 'family room', 'den', 'basement',
      'attic', 'garage', 'laundry room', 'pantry', 'closet', 'hallway',
      'foyer', 'entryway', 'mudroom', 'sunroom', 'conservatory'
    ];
    roomTypes.forEach(room => {
      if (lowerText.includes(room)) {
        analysis.roomType = room;
      }
    });

    // Building Type Detection
    const buildingTypes = [
      'house', 'home', 'apartment', 'condo', 'townhouse', 'villa',
      'mansion', 'cottage', 'cabin', 'bungalow', 'duplex', 'studio',
      'loft', 'penthouse', 'office building', 'commercial', 'retail',
      'restaurant', 'hotel', 'hospital', 'school', 'warehouse'
    ];
    buildingTypes.forEach(building => {
      if (lowerText.includes(building)) {
        analysis.buildingType = building;
      }
    });

    // Enhanced Category Determination
    if (context === 'visual_model_search') {
      // Furniture category
      if (analysis.furniture.length > 0 || analysis.objects.some(obj =>
        ['chair', 'table', 'sofa', 'bed', 'desk', 'cabinet', 'shelf'].includes(obj))) {
        analysis.categories.push('Furniture');
      }

      // Residential category
      if (analysis.elements.some(elem =>
        ['door', 'window', 'wall', 'roof', 'staircase', 'balcony'].includes(elem)) ||
        analysis.roomType || analysis.buildingType === 'house') {
        analysis.categories.push('Residential');
      }

      // Commercial category
      if (analysis.buildingType && ['office building', 'commercial', 'retail', 'restaurant', 'hotel'].includes(analysis.buildingType)) {
        analysis.categories.push('Commercial');
      }

      // Landscape/Garden category
      if (lowerText.includes('landscape') || lowerText.includes('garden') ||
          analysis.decorativeItems.some(item => ['plant', 'flower'].includes(item))) {
        analysis.categories.push('Landscape/Garden');
      }

      // Exterior category
      if (lowerText.includes('exterior') || analysis.elements.some(elem =>
        ['facade', 'entrance', 'porch', 'terrace', 'balcony'].includes(elem))) {
        analysis.categories.push('Exterior');
      }
    } else {
      // Default categories for chatbot context
      if (analysis.furniture.length > 0 || analysis.objects.some(obj =>
        ['chair', 'table', 'sofa', 'bed'].includes(obj))) {
        analysis.categories.push('furniture');
      }
      if (analysis.elements.some(elem => ['door', 'window', 'wall'].includes(elem))) {
        analysis.categories.push('architecture');
      }
      if (analysis.decorativeItems.length > 0) {
        analysis.categories.push('decoration');
      }
    }

    // Calculate Confidence Score
    let confidenceScore = 0;
    confidenceScore += analysis.elements.length * 5;
    confidenceScore += analysis.furniture.length * 8;
    confidenceScore += analysis.decorativeItems.length * 3;
    confidenceScore += analysis.structuralComponents.length * 4;
    confidenceScore += analysis.colors.length * 2;
    confidenceScore += analysis.materials.length * 6;
    confidenceScore += analysis.categories.length * 10;
    confidenceScore += analysis.roomType ? 15 : 0;
    confidenceScore += analysis.buildingType ? 12 : 0;
    confidenceScore += analysis.architecturalStyle ? 10 : 0;
    confidenceScore += analysis.interiorStyle ? 8 : 0;

    analysis.confidence = Math.min(confidenceScore / 100, 1.0); // Normalize to 0-1

    // Generate comprehensive tags with deduplication
    const allTags = [
      ...analysis.elements,
      ...analysis.furniture,
      ...analysis.decorativeItems,
      ...analysis.structuralComponents,
      ...analysis.objects,
      analysis.style,
      analysis.architecturalStyle,
      analysis.interiorStyle,
      analysis.roomType,
      analysis.buildingType,
      ...analysis.colors,
      ...analysis.primaryColors,
      ...analysis.neutralTones,
      ...analysis.materialColors,
      ...analysis.materials,
      ...analysis.naturalMaterials,
      ...analysis.manufacturedMaterials,
      ...analysis.categories
    ].filter(tag => tag && tag.length > 0);

    // Remove duplicates and sort
    analysis.tags = [...new Set(allTags)].sort();

    // Add design features for backward compatibility
    analysis.designFeatures = [
      ...analysis.elements,
      ...analysis.furniture,
      ...analysis.decorativeItems
    ].slice(0, 10); // Limit to top 10

    return analysis;
  }

  /**
   * Find related 3D models based on analysis
   */
  async findRelatedModels(analysis) {
    try {
      const searchTerms = [
        ...analysis.elements || [],
        ...analysis.objects || [],
        ...analysis.categories || [],
        analysis.style || ''
      ].filter(term => term && term.length > 0);

      if (searchTerms.length === 0) {
        return [];
      }

      const searchQuery = {
        $or: [
          { title: { $regex: searchTerms.join('|'), $options: 'i' } },
          { description: { $regex: searchTerms.join('|'), $options: 'i' } },
          { tags: { $in: searchTerms.map(term => new RegExp(term, 'i')) } },
          { category: { $regex: searchTerms.join('|'), $options: 'i' } }
        ],
        status: 'active'
      };

      const models = await Model.find(searchQuery)
        .populate('createdBy', 'name')
        .populate('category')
        .sort({ downloads: -1, rating: -1 })
        .limit(10)
        .lean();

      return models;

    } catch (error) {
      console.error('Error finding related models:', error);
      return [];
    }
  }

  /**
   * Find visually related models with advanced matching
   */
  async findVisuallyRelatedModels(analysis, category = '') {
    try {
      const searchTerms = [
        ...analysis.elements || [],
        ...analysis.objects || [],
        ...analysis.designFeatures || [],
        ...analysis.materials || [],
        ...analysis.tags || [],
        analysis.style || '',
        analysis.architecturalStyle || ''
      ].filter(term => term && term.length > 0);

      if (searchTerms.length === 0) {
        return [];
      }

      // Create comprehensive search query
      const searchQuery = {
        $or: [
          { title: { $regex: searchTerms.join('|'), $options: 'i' } },
          { description: { $regex: searchTerms.join('|'), $options: 'i' } },
          { tags: { $in: searchTerms.map(term => new RegExp(term, 'i')) } },
          { category: { $in: analysis.categories || [] } }
        ]
      };

      // Add category filter if specified
      if (category) {
        searchQuery.category = { $regex: category, $options: 'i' };
      }

      const models = await Model.find(searchQuery)
        .sort({ downloads: -1, rating: -1 })
        .limit(30)
        .lean();

      // Calculate similarity scores for each model
      const modelsWithScores = models.map(model => ({
        ...model,
        similarityScore: this.calculateModelSimilarity(model, analysis),
        matchReasons: this.getMatchReasons(model, analysis)
      }));

      // Sort by similarity score
      modelsWithScores.sort((a, b) => b.similarityScore - a.similarityScore);

      return modelsWithScores;

    } catch (error) {
      console.error('Error finding visually related models:', error);
      return [];
    }
  }

  /**
   * Calculate enhanced similarity score between model and analysis
   */
  calculateModelSimilarity(model, analysis) {
    let score = 0;
    const weights = {
      category: 25,
      furniture: 20,
      architectural: 18,
      decorative: 10,
      structural: 12,
      materials: 15,
      colors: 8,
      style: 20,
      roomType: 15,
      buildingType: 12,
      tags: 10,
      popularity: 5
    };

    const modelText = `${model.title} ${model.description}`.toLowerCase();
    const modelTags = model.tags || [];

    // 1. Category Match (Highest Priority)
    if (analysis.categories.includes(model.category)) {
      score += weights.category;
    }

    // 2. Furniture Matches
    const furnitureMatches = analysis.furniture.filter(item =>
      modelText.includes(item.toLowerCase()) ||
      modelTags.some(tag => tag.toLowerCase().includes(item.toLowerCase()))
    );
    score += Math.min(furnitureMatches.length * 4, weights.furniture);

    // 3. Architectural Element Matches
    const architecturalMatches = analysis.elements.filter(element =>
      modelText.includes(element.toLowerCase()) ||
      modelTags.some(tag => tag.toLowerCase().includes(element.toLowerCase()))
    );
    score += Math.min(architecturalMatches.length * 3, weights.architectural);

    // 4. Decorative Item Matches
    const decorativeMatches = analysis.decorativeItems.filter(item =>
      modelText.includes(item.toLowerCase()) ||
      modelTags.some(tag => tag.toLowerCase().includes(item.toLowerCase()))
    );
    score += Math.min(decorativeMatches.length * 2, weights.decorative);

    // 5. Structural Component Matches
    const structuralMatches = analysis.structuralComponents.filter(component =>
      modelText.includes(component.toLowerCase()) ||
      modelTags.some(tag => tag.toLowerCase().includes(component.toLowerCase()))
    );
    score += Math.min(structuralMatches.length * 3, weights.structural);

    // 6. Material Matches (Enhanced)
    const naturalMatches = analysis.naturalMaterials.filter(material =>
      modelText.includes(material.toLowerCase()) ||
      modelTags.some(tag => tag.toLowerCase().includes(material.toLowerCase()))
    );
    const manufacturedMatches = analysis.manufacturedMaterials.filter(material =>
      modelText.includes(material.toLowerCase()) ||
      modelTags.some(tag => tag.toLowerCase().includes(material.toLowerCase()))
    );
    score += Math.min((naturalMatches.length + manufacturedMatches.length) * 2, weights.materials);

    // 7. Color Matches
    const colorMatches = analysis.colors.filter(color =>
      modelText.includes(color.toLowerCase()) ||
      modelTags.some(tag => tag.toLowerCase().includes(color.toLowerCase()))
    );
    score += Math.min(colorMatches.length * 1, weights.colors);

    // 8. Style Matches (Enhanced)
    let styleScore = 0;
    if (analysis.architecturalStyle && modelText.includes(analysis.architecturalStyle.toLowerCase())) {
      styleScore += 12;
    }
    if (analysis.interiorStyle && modelText.includes(analysis.interiorStyle.toLowerCase())) {
      styleScore += 8;
    }
    if (analysis.style && modelText.includes(analysis.style.toLowerCase())) {
      styleScore += 10;
    }
    score += Math.min(styleScore, weights.style);

    // 9. Room Type Match
    if (analysis.roomType && modelText.includes(analysis.roomType.toLowerCase())) {
      score += weights.roomType;
    }

    // 10. Building Type Match
    if (analysis.buildingType && modelText.includes(analysis.buildingType.toLowerCase())) {
      score += weights.buildingType;
    }

    // 11. Tag Matches (Comprehensive)
    const tagMatches = analysis.tags.filter(tag =>
      modelTags.some(modelTag =>
        modelTag.toLowerCase().includes(tag.toLowerCase()) ||
        tag.toLowerCase().includes(modelTag.toLowerCase()) ||
        this.calculateStringSimilarity(tag.toLowerCase(), modelTag.toLowerCase()) > 0.7
      )
    );
    score += Math.min(tagMatches.length * 1, weights.tags);

    // 12. Popularity Boost (Quality Indicator)
    if (model.downloads > 1000) score += 2;
    if (model.downloads > 5000) score += 1;
    if (model.rating > 4.0) score += 2;
    score = Math.min(score, 100 + weights.popularity);

    // 13. Confidence Boost
    score *= (0.7 + (analysis.confidence * 0.3)); // Boost based on analysis confidence

    return Math.min(score / 100, 1.0); // Normalize to 0-1
  }

  /**
   * Calculate string similarity using Levenshtein distance
   */
  calculateStringSimilarity(str1, str2) {
    const matrix = [];
    const len1 = str1.length;
    const len2 = str2.length;

    if (len1 === 0) return len2 === 0 ? 1 : 0;
    if (len2 === 0) return 0;

    // Initialize matrix
    for (let i = 0; i <= len1; i++) {
      matrix[i] = [i];
    }
    for (let j = 0; j <= len2; j++) {
      matrix[0][j] = j;
    }

    // Fill matrix
    for (let i = 1; i <= len1; i++) {
      for (let j = 1; j <= len2; j++) {
        const cost = str1[i - 1] === str2[j - 1] ? 0 : 1;
        matrix[i][j] = Math.min(
          matrix[i - 1][j] + 1,
          matrix[i][j - 1] + 1,
          matrix[i - 1][j - 1] + cost
        );
      }
    }

    const maxLen = Math.max(len1, len2);
    return (maxLen - matrix[len1][len2]) / maxLen;
  }

  /**
   * Get comprehensive match reasons for a model
   */
  getMatchReasons(model, analysis) {
    const reasons = [];
    const modelText = `${model.title} ${model.description}`.toLowerCase();
    const modelTags = model.tags || [];

    // Category match
    if (analysis.categories.includes(model.category)) {
      reasons.push(`Category Match: ${model.category}`);
    }

    // Furniture matches
    const furnitureMatches = analysis.furniture.filter(item =>
      modelText.includes(item.toLowerCase()) ||
      modelTags.some(tag => tag.toLowerCase().includes(item.toLowerCase()))
    );
    if (furnitureMatches.length > 0) {
      reasons.push(`Furniture: ${furnitureMatches.slice(0, 3).join(', ')}${furnitureMatches.length > 3 ? ` +${furnitureMatches.length - 3} more` : ''}`);
    }

    // Architectural element matches
    const elementMatches = analysis.elements.filter(elem =>
      modelText.includes(elem.toLowerCase()) ||
      modelTags.some(tag => tag.toLowerCase().includes(elem.toLowerCase()))
    );
    if (elementMatches.length > 0) {
      reasons.push(`Architecture: ${elementMatches.slice(0, 3).join(', ')}${elementMatches.length > 3 ? ` +${elementMatches.length - 3} more` : ''}`);
    }

    // Decorative item matches
    const decorativeMatches = analysis.decorativeItems.filter(item =>
      modelText.includes(item.toLowerCase()) ||
      modelTags.some(tag => tag.toLowerCase().includes(item.toLowerCase()))
    );
    if (decorativeMatches.length > 0) {
      reasons.push(`Decorative: ${decorativeMatches.slice(0, 2).join(', ')}${decorativeMatches.length > 2 ? ` +${decorativeMatches.length - 2} more` : ''}`);
    }

    // Style matches
    const styleMatches = [];
    if (analysis.architecturalStyle && modelText.includes(analysis.architecturalStyle.toLowerCase())) {
      styleMatches.push(analysis.architecturalStyle);
    }
    if (analysis.interiorStyle && modelText.includes(analysis.interiorStyle.toLowerCase())) {
      styleMatches.push(analysis.interiorStyle);
    }
    if (analysis.style && modelText.includes(analysis.style.toLowerCase()) && !styleMatches.includes(analysis.style)) {
      styleMatches.push(analysis.style);
    }
    if (styleMatches.length > 0) {
      reasons.push(`Style: ${styleMatches.join(', ')}`);
    }

    // Material matches
    const naturalMatches = analysis.naturalMaterials.filter(material =>
      modelText.includes(material.toLowerCase()) ||
      modelTags.some(tag => tag.toLowerCase().includes(material.toLowerCase()))
    );
    const manufacturedMatches = analysis.manufacturedMaterials.filter(material =>
      modelText.includes(material.toLowerCase()) ||
      modelTags.some(tag => tag.toLowerCase().includes(material.toLowerCase()))
    );
    const allMaterialMatches = [...naturalMatches, ...manufacturedMatches];
    if (allMaterialMatches.length > 0) {
      reasons.push(`Materials: ${allMaterialMatches.slice(0, 3).join(', ')}${allMaterialMatches.length > 3 ? ` +${allMaterialMatches.length - 3} more` : ''}`);
    }

    // Color matches
    const colorMatches = analysis.colors.filter(color =>
      modelText.includes(color.toLowerCase()) ||
      modelTags.some(tag => tag.toLowerCase().includes(color.toLowerCase()))
    );
    if (colorMatches.length > 0) {
      reasons.push(`Colors: ${colorMatches.slice(0, 3).join(', ')}${colorMatches.length > 3 ? ` +${colorMatches.length - 3} more` : ''}`);
    }

    // Room type match
    if (analysis.roomType && modelText.includes(analysis.roomType.toLowerCase())) {
      reasons.push(`Room Type: ${analysis.roomType}`);
    }

    // Building type match
    if (analysis.buildingType && modelText.includes(analysis.buildingType.toLowerCase())) {
      reasons.push(`Building Type: ${analysis.buildingType}`);
    }

    // High-quality model indicator
    if (model.downloads > 1000 && model.rating > 4.0) {
      reasons.push(`Popular & Highly Rated (${model.downloads} downloads, ${model.rating}★)`);
    } else if (model.downloads > 1000) {
      reasons.push(`Popular Model (${model.downloads} downloads)`);
    } else if (model.rating > 4.0) {
      reasons.push(`Highly Rated (${model.rating}★)`);
    }

    return reasons.slice(0, 5); // Limit to top 5 reasons
  }

  /**
   * Organize models by match level
   */
  organizeModelsByMatchLevel(models, analysis) {
    const exactMatches = models.filter(model => model.similarityScore >= 0.8);
    const similarMatches = models.filter(model =>
      model.similarityScore >= 0.5 && model.similarityScore < 0.8
    );
    const relatedMatches = models.filter(model =>
      model.similarityScore >= 0.2 && model.similarityScore < 0.5
    );

    return {
      exactMatches: exactMatches.slice(0, 5),
      similarMatches: similarMatches.slice(0, 8),
      relatedMatches: relatedMatches.slice(0, 10),
      allModels: models.slice(0, 20)
    };
  }

  /**
   * Generate contextual response for models
   */
  async generateModelResponse(analysis, models, language = 'vi') {
    try {
      const isVietnamese = language === 'vi';

      if (models.length === 0) {
        return isVietnamese
          ? "Tôi có thể thấy hình ảnh bạn đã chia sẻ! Mặc dù không tìm thấy mô hình phù hợp chính xác, tôi có thể giúp bạn tìm kiếm các yếu tố cụ thể. Bạn quan tâm đến khía cạnh nào nhất trong hình ảnh này?"
          : "I can see the image you've shared! While I couldn't find exact matching models, I can help you search for specific elements. What aspect of this image interests you most?";
      }

      const modelTitles = models.slice(0, 3).map(model => model.title).join(', ');
      const elements = analysis.elements?.slice(0, 3).join(', ') || '';
      const objects = analysis.objects?.slice(0, 3).join(', ') || '';

      if (isVietnamese) {
        return `Hình ảnh tuyệt vời! Tôi có thể thấy ${elements ? `các yếu tố như ${elements}` : 'các yếu tố thiết kế thú vị'}${objects ? ` và các đối tượng như ${objects}` : ''}. Dựa trên phân tích, tôi đã tìm thấy ${models.length} mô hình 3D liên quan, bao gồm "${modelTitles}". Những mô hình này có thể giúp bạn tái tạo thiết kế tương tự. Bạn có muốn xem chi tiết về mô hình nào không?`;
      } else {
        return `Great image! I can see ${elements ? `elements like ${elements}` : 'interesting design elements'}${objects ? ` and objects such as ${objects}` : ''}. Based on the analysis, I found ${models.length} related 3D models, including "${modelTitles}". These models could help you recreate similar designs. Would you like to see details about any specific model?`;
      }

    } catch (error) {
      console.error('Error generating model response:', error);
      const isVietnamese = language === 'vi';
      return isVietnamese
        ? "Tôi có thể thấy hình ảnh của bạn! Hãy để tôi giúp bạn tìm các mô hình 3D liên quan."
        : "I can see your image! Let me help you find related 3D models.";
    }
  }
}

export default ImageSearchService;
