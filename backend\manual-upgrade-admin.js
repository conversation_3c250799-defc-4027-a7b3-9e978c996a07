import fetch from 'node-fetch';

const manualUpgradeAdmin = async () => {
  try {
    console.log('🔧 Manual Admin Role Upgrade Process...');
    console.log('');

    // Step 1: Login to get user token
    console.log('1. Logging in as admin user...');
    const loginResponse = await fetch('http://localhost:5002/api/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'admin123456'
      })
    });

    if (!loginResponse.ok) {
      console.log('❌ Login failed. Please check credentials.');
      return;
    }

    const loginData = await loginResponse.json();
    console.log('✅ Login successful!');
    console.log('   User ID:', loginData.user.id);
    console.log('   Current Role:', loginData.user.role);
    console.log('   Token:', loginData.token ? 'Generated' : 'Missing');

    // Step 2: Get user profile to confirm current status
    console.log('');
    console.log('2. Getting current user profile...');
    const profileResponse = await fetch('http://localhost:5002/api/auth/profile', {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${loginData.token}`
      }
    });

    if (profileResponse.ok) {
      const profileData = await profileResponse.json();
      console.log('✅ Profile retrieved:');
      console.log('   Name:', profileData.user.name);
      console.log('   Email:', profileData.user.email);
      console.log('   Role:', profileData.user.role);
      console.log('   Subscription:', profileData.user.subscription?.type || 'None');
      console.log('   Download Credits:', profileData.user.downloadCredits || 0);
    }

    // Step 3: Create a simple endpoint to upgrade role
    console.log('');
    console.log('3. Creating temporary upgrade endpoint...');
    
    // Since we can't directly update the role via API without admin permissions,
    // we'll create a simple database update script
    console.log('');
    console.log('🎯 ADMIN DASHBOARD ACCESS INSTRUCTIONS:');
    console.log('');
    console.log('Current Status:');
    console.log('✅ User account created: <EMAIL>');
    console.log('✅ Login working: password admin123456');
    console.log('❌ Role: user (needs upgrade to admin)');
    console.log('');
    console.log('To access Admin Dashboard:');
    console.log('1. Role needs to be upgraded from "user" to "admin"');
    console.log('2. Admin Dashboard URL: http://localhost:5173/admin');
    console.log('3. Current access: Blocked (requires admin role)');
    console.log('');
    console.log('🔧 UPGRADE OPTIONS:');
    console.log('');
    console.log('Option 1 - Manual Database Update:');
    console.log('- Use MongoDB Compass or shell');
    console.log('- Find user with email: <EMAIL>');
    console.log('- Update role field from "user" to "admin"');
    console.log('- Add subscription: { type: "professional", status: "active" }');
    console.log('- Set downloadCredits: 1000');
    console.log('');
    console.log('Option 2 - Backend Script (Recommended):');
    console.log('- I will create a direct database update script');
    console.log('- Run script to upgrade role automatically');
    console.log('- No authentication required');
    console.log('');
    console.log('🌐 ADMIN DASHBOARD FEATURES:');
    console.log('Once role is upgraded, you will have access to:');
    console.log('- Dashboard Overview: /admin');
    console.log('- User Management: /admin/users');
    console.log('- Model Management: /admin/models');
    console.log('- Analytics: /admin/analytics');
    console.log('- Settings: /admin/settings');
    console.log('');
    console.log('📊 Dashboard includes:');
    console.log('- Real-time statistics from MongoDB');
    console.log('- User growth charts');
    console.log('- Download analytics');
    console.log('- Recent users and models');
    console.log('- System alerts');
    console.log('- Revenue tracking');

  } catch (error) {
    console.error('Error in manual upgrade process:', error);
  }
};

manualUpgradeAdmin();
