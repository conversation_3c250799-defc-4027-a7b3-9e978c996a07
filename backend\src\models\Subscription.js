import mongoose from 'mongoose';

const SubscriptionSchema = new mongoose.Schema({
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  plan: {
    type: String,
    required: [true, 'Please add a subscription plan'],
    enum: ['basic', 'premium', 'professional']
  },
  status: {
    type: String,
    required: [true, 'Please add a status'],
    enum: ['active', 'cancelled', 'expired', 'past_due'],
    default: 'active'
  },
  startDate: {
    type: Date,
    required: [true, 'Please add a start date'],
    default: Date.now
  },
  endDate: {
    type: Date,
    required: [true, 'Please add an end date']
  },
  renewalDate: {
    type: Date
  },
  autoRenew: {
    type: Boolean,
    default: true
  },
  price: {
    type: Number,
    required: [true, 'Please add a price']
  },
  currency: {
    type: String,
    default: 'USD'
  },
  interval: {
    type: String,
    enum: ['monthly', 'yearly'],
    default: 'monthly'
  },
  subscriptionId: {
    type: String
  },
  customerId: {
    type: String
  },
  cancelledAt: {
    type: Date
  },
  cancelReason: {
    type: String
  },
  features: {
    downloadLimit: {
      type: Number,
      required: true
    },
    accessToPremiumModels: {
      type: Boolean,
      default: false
    },
    prioritySupport: {
      type: Boolean,
      default: false
    },
    commercialUse: {
      type: Boolean,
      default: false
    }
  },
  paymentHistory: [{
    paymentId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Payment'
    },
    date: {
      type: Date,
      default: Date.now
    },
    amount: Number,
    status: {
      type: String,
      enum: ['successful', 'failed', 'pending', 'refunded']
    }
  }],
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

// Update timestamps pre-save
SubscriptionSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  next();
});

// Check if subscription is active
SubscriptionSchema.methods.isActive = function() {
  const now = new Date();
  return this.status === 'active' && this.endDate > now;
};

// Check if subscription is about to expire (within 7 days)
SubscriptionSchema.methods.isAboutToExpire = function() {
  const now = new Date();
  const sevenDaysFromNow = new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000);
  return this.status === 'active' && this.endDate <= sevenDaysFromNow && this.endDate > now;
};

export default mongoose.model('Subscription', SubscriptionSchema);
