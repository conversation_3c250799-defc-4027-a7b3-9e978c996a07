import mongoose from 'mongoose';
import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';

const UserSchema = new mongoose.Schema({
  name: {
    type: String,
    required: [true, 'Please add a name'],
    trim: true,
    maxlength: [50, 'Name cannot be more than 50 characters']
  },
  email: {
    type: String,
    required: [true, 'Please add an email'],
    unique: true,
    match: [
      /^\w+([\.-]?\w+)*@\w+([\.-]?\w+)*(\.\w{2,3})+$/,
      'Please add a valid email'
    ]
  },
  password: {
    type: String,
    required: [true, 'Please add a password'],
    minlength: [6, 'Password must be at least 6 characters'],
    select: false
  },
  role: {
    type: String,
    enum: ['user', 'admin'],
    default: 'user'
  },
  profileImage: {
    type: String,
    default: 'default-profile.jpg'
  },
  bio: {
    type: String,
    maxlength: [500, 'Bio cannot be more than 500 characters']
  },
  website: {
    type: String,
    match: [
      /https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)/,
      'Please use a valid URL with HTTP or HTTPS'
    ]
  },
  location: {
    type: String,
    maxlength: [100, 'Location cannot be more than 100 characters']
  },
  savedModels: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Model'
  }],
  downloadHistory: [{
    model: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Model'
    },
    downloadedAt: {
      type: Date,
      default: Date.now
    }
  }],
  subscription: {
    type: {
      type: String,
      enum: ['free', 'basic', 'premium', 'professional'],
      default: 'free'
    },
    startDate: {
      type: Date
    },
    endDate: {
      type: Date
    },
    status: {
      type: String,
      enum: ['active', 'expired', 'cancelled'],
      default: 'active'
    },
    stripeCustomerId: {
      type: String
    },
    stripeSubscriptionId: {
      type: String
    }
  },
  downloadCredits: {
    type: Number,
    default: 5 // Free users get 5 downloads per month
  },
  lastCreditReset: {
    type: Date,
    default: Date.now
  },
  resetPasswordToken: String,
  resetPasswordExpire: Date,
  twoFactorEnabled: {
    type: Boolean,
    default: false
  },
  twoFactorSecret: {
    type: String,
    select: false
  },
  lastPasswordChange: {
    type: Date,
    default: Date.now
  },
  loginHistory: [{
    date: {
      type: Date,
      default: Date.now
    },
    ipAddress: String,
    userAgent: String,
    successful: {
      type: Boolean,
      default: true
    }
  }],
  phone: {
    type: String
  },
  jobTitle: {
    type: String
  },
  company: {
    type: String
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

// Encrypt password using bcrypt
UserSchema.pre('save', async function(next) {
  if (!this.isModified('password')) {
    return next();
  }

  const salt = await bcrypt.genSalt(10);
  this.password = await bcrypt.hash(this.password, salt);
  next();
});

// Sign JWT and return
UserSchema.methods.getSignedJwtToken = function() {
  return jwt.sign(
    { id: this._id, name: this.name, email: this.email, role: this.role },
    process.env.JWT_SECRET,
    {
      expiresIn: process.env.JWT_EXPIRE
    }
  );
};

// Match user entered password to hashed password in database
UserSchema.methods.matchPassword = async function(enteredPassword) {
  return await bcrypt.compare(enteredPassword, this.password);
};

// Reset download credits if a month has passed
UserSchema.methods.resetCreditsIfNeeded = function() {
  const now = new Date();
  const lastReset = new Date(this.lastCreditReset);

  // Check if a month has passed since last reset
  if (now.getMonth() !== lastReset.getMonth() || now.getFullYear() !== lastReset.getFullYear()) {
    // Reset credits based on subscription type
    switch (this.subscription.type) {
      case 'free':
        this.downloadCredits = 5;
        break;
      case 'basic':
        this.downloadCredits = 20;
        break;
      case 'premium':
        this.downloadCredits = 50;
        break;
      case 'professional':
        this.downloadCredits = 100;
        break;
      default:
        this.downloadCredits = 5;
    }

    this.lastCreditReset = now;
    return true;
  }

  return false;
};

// Check if user has enough download credits
UserSchema.methods.hasDownloadCredits = function() {
  this.resetCreditsIfNeeded();
  return this.downloadCredits > 0 || this.subscription.type === 'professional';
};

// Decrement download credits
UserSchema.methods.useDownloadCredit = function() {
  if (this.subscription.type !== 'professional' && this.downloadCredits > 0) {
    this.downloadCredits -= 1;
  }
};

// Update timestamps pre-save
UserSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  next();
});

export default mongoose.model('User', UserSchema);
