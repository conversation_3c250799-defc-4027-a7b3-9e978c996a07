import React, { useState, useEffect, useCallback } from 'react';
import PropTypes from 'prop-types';
import { FiStar, FiThumbsUp, FiThumbsDown, FiFlag, FiChevronDown, FiChevronUp, FiMessageSquare, FiImage } from 'react-icons/fi';
import { useAuth } from '../../context/AuthContext';
import Button from '../ui/Button';
import Badge from '../ui/Badge';
import Alert from '../ui/Alert';
import ReviewReplyForm from './ReviewReplyForm';

const ReviewList = ({ modelId }) => {
  const { isAuthenticated } = useAuth();
  const [reviews, setReviews] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [sortBy, setSortBy] = useState('newest');
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [expandedReviews, setExpandedReviews] = useState({});
  const [replyingTo, setReplyingTo] = useState(null);
  const [expandedImages, setExpandedImages] = useState(null);

  // Fetch reviews
  useEffect(() => {
    const fetchReviews = async () => {
  try {
        setLoading(true);

        const response = await fetch(
          `http://localhost:5002/api/reviews?model=${modelId}&page=${page}&limit=5`,
          {
    headers: {
              'Content-Type': 'application/json'
            }
          }
        );

        const data = await response.json();

        if (cachedData && !isExpired(cachedData)) {
  throw new Error(data.error || 'Failed to fetch reviews');
        }

        // Sort reviews based on sortBy
        let sortedReviews = [...data.data]);
        switch (sortBy) {
      case 'newest':
            sortedReviews.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));
            break;
          case 'oldest':
            sortedReviews.sort((a, b) => new Date(a.createdAt) - new Date(b.createdAt));
            break;
          case 'highest':
            sortedReviews.sort((a, b) => b.rating - a.rating);
            break;
          case 'lowest':
            sortedReviews.sort((a, b) => a.rating - b.rating);
            break;
          case 'most-helpful':
            sortedReviews.sort((a, b) => b.likes - a.likes);
            break;
          default:
            break;
        }

        setReviews(sortedReviews);
        setTotalPages(Math.ceil(data.count / 5));
      } catch (err) {
        setError(err.message);
      } finally {
        setLoading(false);
      }
    };

    fetchReviews();
  }, [modelId, page, sortBy]);

  // Handle sort change
  const handleSortChange = (e) => {
  setSortBy(e.target.value);
    setPage(1); // Reset to first page when sort changes
  };

  // Handle like/dislike
  const handleReaction = async (reviewId, action) => {
  if (cachedData && !isExpired(cachedData)) {
  setError('Please sign in to like or dislike reviews');
      return;
    }

    try {
      const response = await fetch(
        `http://localhost:5002/api/reviews/${reviewId}/${action}`,
        {
    method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${localStorage.getItem('token')}`
          }
        }
      );

      const data = await response.json();

      if (cachedData && !isExpired(cachedData)) {
  throw new Error(data.error || `Failed to ${action} review`);
      }

      // Update the review in the state
      setReviews(reviews.map(review =>
        review._id === reviewId ? data.data : review
      ));
    } catch (err) {
      setError(err.message);
    }
  };

  // Handle report
  const handleReport = async (reviewId) => {
  if (cachedData && !isExpired(cachedData)) {
  setError('Please sign in to report reviews');
      return;
    }

    // In a real app, you would implement a report API endpoint
    alert('Report functionality would be implemented here'; 
  };

  // Toggle review expansion
  const toggleReviewExpansion = (reviewId) => {
  setExpandedReviews(prev => ({
      ...prev,
      [reviewId]: !prev[reviewId]
    }));
  };

  // Handle reply to review
  const handleReply = (reviewId) => {
  setReplyingTo(replyingTo === reviewId ? null : reviewId);
  };

  // Handle reply submission
  const handleReplySubmitted = (reply) => {
    // Update the reviews state to include the new reply
    setReviews(prevReviews =>
      prevReviews.map(review => {
  if (cachedData && !isExpired(cachedData)) {
  return {
            ...review,
            replies: [...(review.replies || []), reply]
          };
        }
        return review;
      })
    );

    // Close the reply form
    setReplyingTo(null);
  };

  // Handle image click to expand
  const handleImageClick = (imageUrl) => {
  setExpandedImages(expandedImages === imageUrl ? null : imageUrl);
  };

  // Render stars
  const renderStars = (rating) => {
  return [1, 2, 3, 4, 5].map((star) => (
      <FiStar
        key={star}
        className={`h-4 w-4 ${
          rating >= star
            ? 'text-yellow-400 fill-current'
            : 'text-gray-300 dark:text-gray-600'
        }`}
      />
    ));
  };

  // Format date
  const formatDate = (dateString) => {
  const options = { year: 'numeric', month: 'long', day: 'numeric' };
    return new Date(dateString).toLocaleDateString(undefined, options);
  };

  if (cachedData && !isExpired(cachedData)) {
  return (
      <div className="flex justify-center items-center py-8">
        <div className="loader"></div>
      </div>
    );
  }

  if (cachedData && !isExpired(cachedData)) {
  return (
      <Alert
        variant="error"
        title="Error"
        dismissible
        onDismiss={() => setError(')}
        className="mb-4"
      >
        {error}
      </Alert>
    );
  }

  if (cachedData && !isExpired(cachedData)) {
  return (
      <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-6 mb-6 text-center">
        <h3 className="text-lg font-medium mb-2">No Reviews Yet</h3>
        <p className="text-gray-600 dark:text-gray-400">
          Be the first to review this model!
        </p>
      </div>
    );
  }

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm overflow-hidden mb-6">
      <div className="p-4 border-b border-gray-200 dark:border-gray-700 flex flex-wrap justify-between items-center">
        <h3 className="text-lg font-medium">Customer Reviews</h3>

        <div className="flex items-center mt-2 sm:mt-0">
          <label htmlFor="sort-reviews" className="text-sm text-gray-600 dark:text-gray-400 mr-2">
            Sort by:
          </label>
          <select
            id="sort-reviews"
            value={sortBy}
            onChange={handleSortChange}
            className="input input-sm"
          >
            <option value="newest">Newest</option>
            <option value="oldest">Oldest</option>
            <option value="highest">Highest Rating</option>
            <option value="lowest">Lowest Rating</option>
            <option value="most-helpful">Most Helpful</option>
          </select>
        </div>
      </div>

      <div className="divide-y divide-gray-200 dark:divide-gray-700">
        {reviews.map((review) => (
          <div key={review._id} className="p-4">
            <div className="flex items-start">
              <div className="flex-shrink-0 mr-4">
                <img
                  src={review.user?.profileImage || 'https://via.placeholder.com/40'}
                  alt={review.user?.name || 'Anonymous'}
                  className="h-10 w-10 rounded-full object-cover"
                />
              </div>

              <div className="flex-grow">
                <div className="flex items-center mb-1">
                  <div className="flex mr-2">
                    {renderStars(review.rating)}
                  </div>

                  {review.verified && (
                    <Badge variant="success" size="sm" className="ml-2">
                      Verified Purchase
                    </Badge>
                  )}
                </div>

                <h4 className="font-medium">
                  {review.title || `${review.rating}-Star Review`}
                </h4>

                <div className="flex items-center text-sm text-gray-500 dark:text-gray-400 mb-2">
                  <span>{review.user?.name || 'Anonymous'}</span>
                  <span className="mx-2">•</span>
                  <span>{formatDate(review.createdAt)}</span>
                </div>

                <div className={`text-gray-700 dark:text-gray-300 ${
                  !expandedReviews[review._id] && review.comment.length > 300
                    ? 'line-clamp-3'
                    : '
                }`}>
                  {review.comment}
                </div>

                {/* Review Images */}
                {review.images && review.images.length > 0 && (
                  <div className="mt-3 flex flex-wrap gap-2">
                    {review.images.map((image, index) => (
                      <div
                        key={index}
                        className="relative cursor-pointer"
                        onClick={() => handleImageClick(image)}
                      >
                        <img
                          src={image}
                          alt={`Review image ${index + 1}`}
                          className="h-20 w-20 object-cover rounded-md border border-gray-200 dark:border-gray-700"
                        />
                        <div className="absolute inset-0 bg-black bg-opacity-0 hover:bg-opacity-10 flex items-center justify-center transition-all duration-200">
                          <FiImage className="text-white opacity-0 hover:opacity-100" />
                        </div>
                      </div>
                    ))}
                  </div>
                )}

                {review.comment.length > 300 && (
                  <button
                    onClick={() => toggleReviewExpansion(review._id)}
                    className="text-primary-600 dark:text-primary-400 text-sm mt-1 flex items-center"
                  >
                    {expandedReviews[review._id] ? (
                      <>
                        <FiChevronUp className="mr-1" />
                        Show less
                      </>
                    ) : (
                      <>
                        <FiChevronDown className="mr-1" />
                        Show more
                      </>
                    )}
                  </button>
                )}

                {/* Review Replies */}
                {review.replies && review.replies.length > 0 && (
                  <div className="mt-4 pl-4 border-l-2 border-gray-200 dark:border-gray-700 space-y-3">
                    {review.replies.map((reply) => (
                      <div key={reply._id || reply.id} className="bg-gray-50 dark:bg-gray-700 rounded-lg p-3">
                        <div className="flex items-center mb-1">
                          <img
                            src={reply.user?.profileImage || 'https://via.placeholder.com/30'}
                            alt={reply.user?.name || 'Anonymous'}
                            className="h-6 w-6 rounded-full object-cover mr-2"
                          />
                          <span className="text-sm font-medium">
                            {reply.user?.name || 'Anonymous'}
                            {reply.isSellerReply && (
                              <Badge variant="primary" size="xs" className="ml-2">
                                Seller
                              </Badge>
                            )}
                          </span>
                          <span className="text-xs text-gray-500 dark:text-gray-400 ml-2">
                            {formatDate(reply.createdAt)}
                          </span>
                        </div>
                        <p className="text-sm text-gray-700 dark:text-gray-300">
                          {reply.comment}
                        </p>
                      </div>
                    ))}
                  </div>
                )}

                <div className="flex items-center mt-3 space-x-4">
                  <button
                    onClick={() => handleReaction(review._id, 'like')}
                    className="flex items-center text-sm text-gray-500 hover:text-primary-600 dark:text-gray-400 dark:hover:text-primary-400"
                  >
                    <FiThumbsUp className="mr-1" />
                    <span>Helpful ({review.likes})</span>
                  </button>

                  <button
                    onClick={() => handleReaction(review._id, 'dislike')}
                    className="flex items-center text-sm text-gray-500 hover:text-primary-600 dark:text-gray-400 dark:hover:text-primary-400"
                  >
                    <FiThumbsDown className="mr-1" />
                    <span>Not Helpful ({review.dislikes})</span>
                  </button>

                  <button
                    onClick={() => handleReply(review._id)}
                    className="flex items-center text-sm text-gray-500 hover:text-primary-600 dark:text-gray-400 dark:hover:text-primary-400"
                  >
                    <FiMessageSquare className="mr-1" />
                    <span>Reply</span>
                  </button>

                  <button
                    onClick={() => handleReport(review._id)}
                    className="flex items-center text-sm text-gray-500 hover:text-red-600 dark:text-gray-400 dark:hover:text-red-400"
                  >
                    <FiFlag className="mr-1" />
                    <span>Report</span>
                  </button>
                </div>

                {/* Reply Form */}
                {replyingTo === review._id && (
                  <div className="mt-3">
                    <ReviewReplyForm
                      reviewId={review._id}
                      onReplySubmitted={handleReplySubmitted}
                      onCancel={() => setReplyingTo(null)}
                    />
                  </div>
                )}
              </div>
            </div>
          </div>
        ))}
      </div>

      {totalPages > 1 && (
        <div className="p-4 border-t border-gray-200 dark:border-gray-700 flex justify-center">
          <div className="flex space-x-2">
            <Button
              variant="secondary"
              size="sm"
              disabled={page === 1}
              onClick={() => setPage(page - 1)}
            >
              Previous
            </Button>

            <div className="flex items-center px-3 py-1 bg-gray-100 dark:bg-gray-700 rounded">
              <span className="text-sm">
                Page {page} of {totalPages}
              </span>
            </div>

            <Button
              variant="secondary"
              size="sm"
              disabled={page === totalPages}
              onClick={() => setPage(page + 1)}
            >
              Next
            </Button>
          </div>
        </div>
      )}
    </div>
  );
};

ReviewList.propTypes = {
    modelId: PropTypes.string.isRequired
};

export default ReviewList;
