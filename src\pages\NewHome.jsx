import React, { useState, useEffect, lazy, Suspense } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import Slider from 'react-slick';
import { FiSearch, FiDownload, FiUsers, FiDatabase, FiLoader, FiCamera, FiSliders } from 'react-icons/fi';
import { FaRegLightbulb, FaRegBuilding, FaTree, FaCouch } from 'react-icons/fa';
import PageTransition from '../components/PageTransition';
import NewFeaturesAnnouncement, { FeatureHighlights } from '../components/NewFeaturesAnnouncement';
import ImageSearch from '../components/search/ImageSearch';
import AdvancedSearch from '../components/search/AdvancedSearch';
import ModelPreviewCarousel from '../components/ModelPreviewCarousel';
import FloatingModelPreviews, { FloatingModelPreviewsAdvanced } from '../components/FloatingModelPreviews';
import ModelMasonryGallery from '../components/ModelMasonryGallery';
import AnimatedModelCounter from '../components/AnimatedModelCounter';
import RecentModels from '../components/RecentModels';
import PopularModels from '../components/PopularModels';
import StatisticsDisplay from '../components/StatisticsDisplay';
import Newsletter from '../components/Newsletter';
import DevTestPanel from '../components/DevTestPanel';

import { statsAPI, modelsAPI } from '../utils/api';
import mongoService from '../services/mongoService';
import realDataService from '../services/realDataService';

const NewHome = () => {
  const navigate = useNavigate();
  const [featuredModels, setFeaturedModels] = useState([]);
  const [stats, setStats] = useState({
    models: 0,
    downloads: 0,
    users: 0,
    categories: 0
  });
  const [searchQuery, setSearchQuery] = useState('');
  const [showImageSearch, setShowImageSearch] = useState(false);
  const [showAdvancedSearch, setShowAdvancedSearch] = useState(false);

  // Handle search
  const handleSearch = (e) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      navigate(`/search?q=${encodeURIComponent(searchQuery)}`);
    }
  };

  // Handle advanced search
  const handleAdvancedSearch = (searchParams) => {
    const queryParams = new URLSearchParams();

    Object.entries(searchParams).forEach(([key, value]) => {
      if (value && value !== '' && value !== 0) {
        queryParams.append(key, value);
      }
    });

    navigate(`/search?${queryParams.toString()}`);
    setShowAdvancedSearch(false);
  };

  useEffect(() => {
    // Load data using realDataService
    const loadData = async () => {
      try {
        console.log('🔄 Loading homepage data using realDataService...');

        // Fetch featured models and stats in parallel
        const [featuredModelsData, statsData] = await Promise.all([
          realDataService.getFeaturedModels(),
          realDataService.getStats()
        ]);

        console.log('✅ Homepage data loaded successfully:', {
          featuredModels: featuredModelsData.length,
          stats: statsData
        });

        // Set featured models
        setFeaturedModels(featuredModelsData);

        // Set stats
        setStats(statsData);

      } catch (error) {
        console.error('❌ Error loading homepage data:', error);
        setFeaturedModels([]);
        setStats({
          models: 0,
          downloads: 0,
          users: 0,
          categories: 0
        });
      }
    };

    loadData();
  }, []);

  // Slider settings
  const sliderSettings = {
    dots: true,
    infinite: true,
    speed: 500,
    slidesToShow: 1,
    slidesToScroll: 1,
    autoplay: true,
    autoplaySpeed: 5000,
    arrows: false
  };

  // Category cards
  const categories = [
    {
      name: 'Interior Scenes',
      icon: <FaRegLightbulb className="text-4xl mb-4 text-blue-500" />,
      description: 'High-quality interior scenes for architectural visualization',
      link: '/category/interior'
    },
    {
      name: 'Exterior Scenes',
      icon: <FaRegBuilding className="text-4xl mb-4 text-blue-500" />,
      description: 'Realistic exterior scenes for your architectural projects',
      link: '/category/exterior'
    },
    {
      name: 'Landscape/Garden',
      icon: <FaTree className="text-4xl mb-4 text-blue-500" />,
      description: 'Beautiful landscape and garden models for outdoor design',
      link: '/category/landscape'
    },
    {
      name: 'Furniture & Objects',
      icon: <FaCouch className="text-4xl mb-4 text-blue-500" />,
      description: 'Detailed furniture and object models for interior design',
      link: '/category/models'
    }
  ];

  // Loading component for Suspense
  const LoadingSpinner = () => (
    <div className="flex items-center justify-center min-h-screen">
      <FiLoader className="w-10 h-10 text-blue-500 animate-spin" />
    </div>
  );

  return (
    <div className="flex flex-col min-h-screen bg-gradient-to-br from-gray-50 via-blue-50 to-purple-50 dark:from-gray-900 dark:via-blue-900 dark:to-purple-900">
      <PageTransition>

      {/* Ultra Professional Hero Section */}
      <section className="hero-enhanced relative overflow-hidden min-h-screen flex items-center pt-20">
        {/* Enhanced Animated Background with better performance */}
        <div className="absolute inset-0 bg-gradient-to-br from-blue-600 via-purple-600 via-indigo-600 to-cyan-500 opacity-95"></div>

        {/* Floating Model Previews */}
        <FloatingModelPreviewsAdvanced
          count={15}
          layers={3}
          opacity={0.12}
        />

        {/* Additional Floating Elements */}
        <div className="absolute inset-0 opacity-15">
          <div className="absolute top-20 left-10 w-32 h-32 bg-white/30 rounded-full animate-float blur-xl"></div>
          <div className="absolute top-40 right-20 w-48 h-48 bg-purple-400/40 rounded-full animate-float blur-2xl" style={{animationDelay: '1s'}}></div>
          <div className="absolute bottom-20 left-1/4 w-40 h-40 bg-blue-400/35 rounded-full animate-float blur-xl" style={{animationDelay: '2s'}}></div>
          <div className="absolute top-1/2 right-1/3 w-24 h-24 bg-pink-400/30 rounded-full animate-float blur-lg" style={{animationDelay: '3s'}}></div>
        </div>
        <div className="container mx-auto px-4 py-20 md:py-32 relative z-10">
          <div className="flex flex-col lg:flex-row items-center gap-12">
            <div className="lg:w-1/2 mb-10 lg:mb-0">
              <motion.div
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, ease: "easeOut" }}
                className="mb-6"
              >
                <span className="inline-block px-6 py-3 bg-gradient-to-r from-white/20 to-blue-200/20 backdrop-blur-sm rounded-full text-white/95 text-sm font-bold mb-6 border border-white/40 shadow-lg hover:scale-105 transition-transform cursor-default">
                  🚀 Premium 3D Platform - AI Powered
                </span>
              </motion.div>

              <motion.h1
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, ease: "easeOut", delay: 0.1 }}
                className="heading-enhanced text-5xl md:text-6xl lg:text-7xl font-black mb-8 font-heading leading-tight text-white"
              >
                <span className="block">Professional</span>
                <span className="block bg-gradient-to-r from-yellow-200 via-pink-200 to-cyan-200 bg-clip-text text-transparent animate-gradient-shift">
                  3D Models
                </span>
                <span className="block">Platform</span>
              </motion.h1>

              <motion.p
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.3 }}
                className="text-enhanced text-xl md:text-2xl mb-10 text-white/90 leading-relaxed font-medium"
              >
                Khám phá bộ sưu tập 3D models chất lượng cao, scenes và assets cho kiến trúc và thiết kế chuyên nghiệp với AI-powered search
              </motion.p>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.5 }}
                className="flex flex-col sm:flex-row gap-4 mb-8"
              >
                <Link
                  to="/models"
                  className="btn-enhanced btn-secondary-enhanced group relative px-10 py-5 bg-gradient-to-r from-white to-blue-50 text-gray-900 hover:from-blue-50 hover:to-white rounded-2xl font-black text-lg shadow-2xl hover:shadow-white/30 transition-all duration-500 hover:scale-110 overflow-hidden border-2 border-white/50"
                >
                  <span className="relative z-10 flex items-center justify-center">
                    <span className="mr-3 text-xl">🎯</span>
                    Khám Phá Models
                    <svg className="ml-3 w-6 h-6 group-hover:translate-x-2 group-hover:scale-110 transition-all duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={3} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                    </svg>
                  </span>
                  <div className="absolute inset-0 bg-gradient-to-r from-blue-400/20 to-purple-400/20 opacity-0 group-hover:opacity-100 transition-all duration-500"></div>
                  <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-1000"></div>
                </Link>

                <Link
                  to="/register"
                  className="btn-enhanced btn-primary-enhanced group relative px-10 py-5 glass-card text-white rounded-2xl font-black text-lg hover:bg-white/30 transition-all duration-500 hover:scale-110 border-2 border-white/50 overflow-hidden"
                >
                  <span className="relative z-10 flex items-center justify-center">
                    <span className="mr-3 text-xl animate-pulse">✨</span>
                    Đăng Ký Miễn Phí
                    <svg className="ml-3 w-6 h-6 group-hover:rotate-12 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                    </svg>
                  </span>
                  <div className="absolute inset-0 bg-gradient-to-r from-pink-400/20 to-purple-400/20 opacity-0 group-hover:opacity-100 transition-all duration-500"></div>
                  <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-1000"></div>
                </Link>
              </motion.div>

              {/* Stats Preview */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.7 }}
                className="flex items-center gap-8 text-white/80"
              >
                <div className="text-center">
                  <div className="text-2xl font-bold text-white">{stats.models.toLocaleString()}+</div>
                  <div className="text-sm">Models</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-white">{stats.downloads.toLocaleString()}+</div>
                  <div className="text-sm">Downloads</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-white">{stats.users.toLocaleString()}+</div>
                  <div className="text-sm">Users</div>
                </div>
              </motion.div>
            </div>
            <div className="lg:w-1/2">
              <motion.div
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.7, delay: 0.3 }}
                className="relative"
              >
                {/* Professional Model Showcase */}
                <div className="relative glass-card p-6 rounded-3xl shadow-professional-lg overflow-hidden">
                  {/* Decorative elements */}
                  <div className="absolute top-4 right-4 w-20 h-20 bg-gradient-to-br from-yellow-400 to-orange-400 rounded-full opacity-20 animate-pulse"></div>
                  <div className="absolute bottom-4 left-4 w-16 h-16 bg-gradient-to-br from-pink-400 to-purple-400 rounded-full opacity-20 animate-pulse" style={{animationDelay: '1s'}}></div>

                  <Slider {...sliderSettings}>
                    {featuredModels.map((model, index) => (
                      <div key={model._id || model.id || `featured-model-${index}`} className="outline-none">
                        <div className="relative group">
                          <img
                            src={model.imageUrl}
                            alt={model.title}
                            className="w-full h-80 md:h-96 object-cover rounded-2xl shadow-lg group-hover:scale-105 transition-transform duration-500"
                          />

                          {/* Overlay with info */}
                          <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-transparent to-transparent rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                            <div className="absolute bottom-6 left-6 right-6">
                              <h3 className="text-white text-xl font-bold mb-2">{model.title}</h3>
                              <p className="text-blue-200 text-sm mb-3">{model.category}</p>
                              <div className="flex items-center gap-2">
                                <span className="px-3 py-1 bg-white/20 backdrop-blur-sm rounded-full text-white text-xs">
                                  Premium
                                </span>
                                <span className="px-3 py-1 bg-blue-500/20 backdrop-blur-sm rounded-full text-blue-200 text-xs">
                                  High Quality
                                </span>
                              </div>
                            </div>
                          </div>

                          {/* Play button overlay */}
                          <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                            <div className="w-16 h-16 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center border border-white/30">
                              <svg className="w-6 h-6 text-white ml-1" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M8 5v14l11-7z"/>
                              </svg>
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </Slider>
                </div>

                {/* Floating badges */}
                <div className="absolute -top-4 -left-4 bg-gradient-to-r from-blue-500 to-purple-500 text-white px-6 py-3 rounded-2xl shadow-xl transform -rotate-6 animate-bounce-subtle">
                  <span className="font-bold text-sm">🏆 Premium Quality</span>
                </div>

                <div className="absolute -bottom-4 -right-4 bg-gradient-to-r from-pink-500 to-orange-500 text-white px-6 py-3 rounded-2xl shadow-xl transform rotate-6 animate-bounce-subtle" style={{animationDelay: '1s'}}>
                  <span className="font-bold text-sm">⚡ Fast Download</span>
                </div>
              </motion.div>
            </div>
          </div>
        </div>

        {/* Wave divider */}
        <div className="absolute bottom-0 left-0 right-0">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1440 320" className="w-full h-auto">
            <path fill="#f9fafb" className="dark:fill-gray-900" fillOpacity="1" d="M0,96L48,112C96,128,192,160,288,160C384,160,480,128,576,112C672,96,768,96,864,112C960,128,1056,160,1152,160C1248,160,1344,128,1392,112L1440,96L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z"></path>
          </svg>
        </div>
      </section>

      {/* Ultra Professional Search Section */}
      <section className="py-20 bg-gradient-to-br from-white via-blue-50 to-purple-50 dark:from-gray-900 dark:via-blue-900 dark:to-purple-900 relative overflow-hidden">
        {/* Background decorations */}
        <div className="absolute inset-0 opacity-5">
          <div className="absolute top-10 left-10 w-40 h-40 bg-blue-400 rounded-full blur-3xl animate-float"></div>
          <div className="absolute bottom-10 right-10 w-32 h-32 bg-purple-400 rounded-full blur-2xl animate-float" style={{animationDelay: '2s'}}></div>
        </div>

        <div className="container mx-auto px-4 relative z-10">
          <div className="max-w-5xl mx-auto">
            {/* Section Header */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              className="text-center mb-12"
            >
              <span className="inline-block px-4 py-2 bg-gradient-to-r from-blue-500 to-purple-500 text-white rounded-full text-sm font-medium mb-4">
                🔍 Tìm Kiếm Thông Minh
              </span>
              <h2 className="text-4xl md:text-5xl font-black text-gray-900 dark:text-white mb-4">
                Tìm <span className="bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 bg-clip-text text-transparent animate-gradient-shift">3D Model</span> Hoàn Hảo
              </h2>
              <p className="text-xl text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
                Sử dụng AI-powered search để tìm kiếm models, scenes và objects chính xác nhất
              </p>
            </motion.div>

            {/* Professional Search Card */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              className="search-enhanced glass-card-enhanced p-8 md:p-10 rounded-3xl shadow-professional-lg border border-white/20"
            >
              {/* Main Search Form */}
              <form onSubmit={handleSearch} className="flex flex-col lg:flex-row gap-4 mb-8">
                <div className="flex-1 relative group">
                  <FiSearch className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 group-focus-within:text-blue-500 transition-colors z-10" />
                  <input
                    type="text"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    placeholder="Tìm kiếm models, scenes, objects..."
                    className="search-input-enhanced w-full pl-12 pr-4 py-4 bg-white/50 dark:bg-gray-800/50 backdrop-blur-sm border border-gray-200/50 dark:border-gray-600/50 rounded-2xl focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:text-white text-lg font-medium transition-all duration-300 hover:bg-white/70 dark:hover:bg-gray-800/70"
                  />
                  <div className="absolute inset-0 rounded-2xl bg-gradient-to-r from-blue-500/10 to-purple-500/10 opacity-0 group-focus-within:opacity-100 transition-opacity pointer-events-none"></div>
                </div>
                <button
                  type="submit"
                  className="group relative px-8 py-4 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white rounded-2xl font-bold text-lg shadow-xl hover:shadow-2xl transition-all duration-300 hover:scale-105 overflow-hidden"
                >
                  <span className="relative z-10 flex items-center justify-center">
                    <FiSearch className="mr-2 group-hover:scale-110 transition-transform" />
                    Tìm Kiếm
                  </span>
                  <div className="absolute inset-0 bg-gradient-to-r from-purple-600 to-pink-600 opacity-0 group-hover:opacity-100 transition-opacity"></div>
                </button>
              </form>

              {/* Search Tools */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
                <button
                  onClick={() => setShowImageSearch(true)}
                  className="group relative p-6 glass-card rounded-2xl border border-purple-200/50 dark:border-purple-700/50 hover:border-purple-400/50 transition-all duration-300 hover:scale-105"
                >
                  <div className="flex items-center space-x-4">
                    <div className="w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-500 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform">
                      <FiCamera className="h-6 w-6 text-white" />
                    </div>
                    <div className="text-left">
                      <h3 className="font-bold text-gray-900 dark:text-white">Visual Search</h3>
                      <p className="text-sm text-gray-600 dark:text-gray-400">Tìm bằng hình ảnh</p>
                    </div>
                  </div>
                  <div className="absolute inset-0 bg-gradient-to-r from-purple-500/10 to-pink-500/10 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity"></div>
                </button>

                {/* Advanced Search Button - DISABLED */}
                {/* <button
                  onClick={() => setShowAdvancedSearch(true)}
                  className="group relative p-6 glass-card rounded-2xl border border-blue-200/50 dark:border-blue-700/50 hover:border-blue-400/50 transition-all duration-300 hover:scale-105"
                >
                  <div className="flex items-center space-x-4">
                    <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform">
                      <FiSliders className="h-6 w-6 text-white" />
                    </div>
                    <div className="text-left">
                      <h3 className="font-bold text-gray-900 dark:text-white">Advanced Search</h3>
                      <p className="text-sm text-gray-600 dark:text-gray-400">Bộ lọc chi tiết</p>
                    </div>
                  </div>
                  <div className="absolute inset-0 bg-gradient-to-r from-blue-500/10 to-indigo-500/10 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity"></div>
                </button> */}

                <Link
                  to="/search"
                  className="group relative p-6 glass-card rounded-2xl border border-green-200/50 dark:border-green-700/50 hover:border-green-400/50 transition-all duration-300 hover:scale-105"
                >
                  <div className="flex items-center space-x-4">
                    <div className="w-12 h-12 bg-gradient-to-r from-green-500 to-teal-500 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform">
                      <FiSearch className="h-6 w-6 text-white" />
                    </div>
                    <div className="text-left">
                      <h3 className="font-bold text-gray-900 dark:text-white">Browse All</h3>
                      <p className="text-sm text-gray-600 dark:text-gray-400">Xem tất cả models</p>
                    </div>
                  </div>
                  <div className="absolute inset-0 bg-gradient-to-r from-green-500/10 to-teal-500/10 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity"></div>
                </Link>

                {/* AI Assistant Feature */}
                <button
                  onClick={() => {
                    // Scroll to chatbot or open chatbot
                    const chatbot = document.querySelector('[data-chatbot="true"]');
                    if (chatbot) {
                      chatbot.scrollIntoView({ behavior: 'smooth' });
                      // Trigger chatbot open if it has a method
                      const openButton = chatbot.querySelector('button');
                      if (openButton) openButton.click();
                    }
                  }}
                  className="group relative p-6 glass-card rounded-2xl border border-orange-200/50 dark:border-orange-700/50 hover:border-orange-400/50 transition-all duration-300 hover:scale-105"
                >
                  <div className="flex items-center space-x-4">
                    <div className="w-12 h-12 bg-gradient-to-r from-orange-500 to-red-500 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform">
                      <svg className="h-6 w-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                      </svg>
                    </div>
                    <div className="text-left">
                      <h3 className="font-bold text-gray-900 dark:text-white">AI Assistant</h3>
                      <p className="text-sm text-gray-600 dark:text-gray-400">Trợ lý AI thông minh</p>
                    </div>
                  </div>
                  <div className="absolute inset-0 bg-gradient-to-r from-orange-500/10 to-red-500/10 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity"></div>
                </button>

                {/* Categories Quick Access */}
                <Link
                  to="/categories"
                  className="group relative p-6 glass-card rounded-2xl border border-indigo-200/50 dark:border-indigo-700/50 hover:border-indigo-400/50 transition-all duration-300 hover:scale-105"
                >
                  <div className="flex items-center space-x-4">
                    <div className="w-12 h-12 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform">
                      <FiDatabase className="h-6 w-6 text-white" />
                    </div>
                    <div className="text-left">
                      <h3 className="font-bold text-gray-900 dark:text-white">Categories</h3>
                      <p className="text-sm text-gray-600 dark:text-gray-400">Duyệt theo danh mục</p>
                    </div>
                  </div>
                  <div className="absolute inset-0 bg-gradient-to-r from-indigo-500/10 to-purple-500/10 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity"></div>
                </Link>
              </div>

              {/* Popular Searches */}
              <div className="text-center">
                <span className="text-sm font-medium text-gray-600 dark:text-gray-400 mb-3 block">🔥 Tìm kiếm phổ biến:</span>
                <div className="flex flex-wrap gap-2 justify-center">
                  {[
                    { label: 'Modern Interior', query: 'modern+interior', emoji: '🏠' },
                    { label: 'Kitchen Design', query: 'kitchen', emoji: '🍳' },
                    { label: 'Office Space', query: 'office', emoji: '💼' },
                    { label: 'Garden & Landscape', query: 'garden', emoji: '🌿' },
                    { label: 'Furniture', query: 'furniture', emoji: '🪑' },
                    { label: 'Lighting', query: 'lighting', emoji: '💡' }
                  ].map((item) => (
                    <Link
                      key={item.query}
                      to={`/search?q=${item.query}`}
                      className="group inline-flex items-center space-x-2 px-4 py-2 bg-white/50 dark:bg-gray-800/50 backdrop-blur-sm border border-gray-200/50 dark:border-gray-600/50 rounded-full hover:bg-white dark:hover:bg-gray-700 transition-all duration-300 hover:scale-105 hover:shadow-lg"
                    >
                      <span className="text-sm">{item.emoji}</span>
                      <span className="text-sm font-medium text-gray-700 dark:text-gray-300 group-hover:text-blue-600 dark:group-hover:text-blue-400">
                        {item.label}
                      </span>
                    </Link>
                  ))}
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Model Preview Carousel Section */}
      <ModelPreviewCarousel
        title="🎨 Khám Phá Bộ Sưu Tập 3D Models"
        subtitle="Hàng nghìn models chất lượng cao từ Interior, Exterior, Landscape đến Furniture - Tất cả đều có sẵn để download"
        autoPlay={true}
        autoPlayInterval={4000}
        showControls={true}
        itemsPerView={4}
      />

      {/* New Features Announcement Section - DISABLED */}
      {/* <section className="py-12 bg-gradient-to-r from-purple-50 to-blue-50 dark:from-purple-900/20 dark:to-blue-900/20">
        <div className="container mx-auto px-4">
          <NewFeaturesAnnouncement />
        </div>
      </section> */}

      {/* Categories Section */}
      <section className="py-16 bg-white dark:bg-gray-800 relative overflow-hidden">
        {/* Background floating previews */}
        <FloatingModelPreviews
          count={6}
          animationDuration={25}
          opacity={0.05}
          size="large"
        />

        <div className="container mx-auto px-4 relative z-10">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4 text-gray-900 dark:text-white">Browse by Category</h2>
            <p className="text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
              Explore our extensive collection of 3D models organized by categories
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
            {categories.map((category, index) => (
              <motion.div
                key={`category-${category.name}-${index}`}
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className="interactive-card card-gradient p-8 text-center group"
              >
                <div className="flex justify-center mb-6">
                  <div className="p-4 bg-gradient-to-br from-primary-500 to-accent-500 rounded-2xl text-white group-hover:scale-110 transition-transform duration-300">
                    {category.icon}
                  </div>
                </div>
                <h3 className="text-xl font-bold mb-3 text-gray-800 dark:text-white group-hover:text-primary-600 dark:group-hover:text-primary-400 transition-colors">{category.name}</h3>
                <p className="text-gray-600 dark:text-gray-300 mb-6 leading-relaxed">{category.description}</p>
                <Link
                  to={category.link}
                  className="inline-flex items-center text-primary-600 hover:text-primary-700 dark:text-primary-400 dark:hover:text-primary-300 font-semibold group-hover:translate-x-1 transition-all duration-300"
                >
                  Explore
                  <svg className="ml-2 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                  </svg>
                </Link>
              </motion.div>
            ))}
          </div>

          {/* Feature Highlights */}
          <div className="mt-16">
            <div className="text-center mb-12">
              <h3 className="text-2xl font-bold mb-4 text-gray-900 dark:text-white">⚡ Tính Năng Nổi Bật</h3>
              <p className="text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
                Khám phá các tính năng mạnh mẽ của nền tảng 3DSKETCHUP.NET
              </p>
            </div>
            <FeatureHighlights />
          </div>
        </div>
      </section>

      {/* Enhanced Stats Section */}
      <section className="stats-enhanced py-16 bg-gradient-to-br from-gray-50 via-blue-50 to-purple-50 dark:from-gray-900 dark:via-blue-900 dark:to-purple-900 relative overflow-hidden">
        {/* Background decorations */}
        <div className="absolute inset-0 opacity-5">
          <div className="absolute top-10 left-10 w-40 h-40 bg-blue-400 rounded-full blur-3xl animate-pulse"></div>
          <div className="absolute bottom-10 right-10 w-32 h-32 bg-purple-400 rounded-full blur-2xl animate-pulse" style={{animationDelay: '2s'}}></div>
        </div>

        <div className="container mx-auto px-4 relative z-10">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-12"
          >
            <h2 className="text-4xl md:text-5xl font-black text-gray-900 dark:text-white mb-4">
              📊 Thống Kê Ấn Tượng
            </h2>
            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
              Con số thực tế về sự phát triển và độ tin cậy của 3DSKETCHUP.NET
            </p>
          </motion.div>

          <AnimatedModelCounter
            showRealTime={true}
            animationDuration={2500}
            updateInterval={8000}
          />
        </div>
      </section>

      {/* Featured Models Section */}
      <section className="py-16 bg-gray-50 dark:bg-gray-900">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4 text-gray-900 dark:text-white">Featured Models</h2>
            <p className="text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
              Discover our handpicked selection of premium 3D models
            </p>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8">
            {featuredModels.map((model, index) => (
              <motion.div
                key={model._id || model.id || `featured-model-list-${index}`}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                className="bg-white dark:bg-gray-800 rounded-xl overflow-hidden shadow-lg hover:shadow-xl transition-shadow"
              >
                <div className="relative">
                  <img
                    src={model.imageUrl}
                    alt={model.title}
                    className="w-full h-48 object-cover"
                  />
                  <div className="absolute top-2 right-2 bg-blue-600 text-white text-xs px-2 py-1 rounded">
                    {model.format}
                  </div>
                </div>
                <div className="p-6">
                  <h3 className="text-xl font-semibold mb-2 text-gray-800 dark:text-white">{model.title}</h3>
                  <div className="flex items-center mb-3">
                    <span className="bg-gray-200 dark:bg-gray-700 px-2 py-1 rounded text-xs text-gray-700 dark:text-gray-300">
                      {model.category}
                    </span>
                    {model.subcategory && (
                      <span className="bg-gray-200 dark:bg-gray-700 px-2 py-1 rounded text-xs text-gray-700 dark:text-gray-300 ml-2">
                        {model.subcategory}
                      </span>
                    )}
                  </div>
                  <p className="text-gray-600 dark:text-gray-300 text-sm mb-4 line-clamp-2">
                    {model.description}
                  </p>
                  <div className="flex justify-between items-center">
                    {(model._id || model.id) ? (
                      <Link
                        to={`/model/${model._id || model.id}`}
                        className="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 font-medium"
                      >
                        View Details
                      </Link>
                    ) : (
                      <span className="text-gray-400 font-medium">No ID Available</span>
                    )}
                    <div className="flex items-center text-gray-500 dark:text-gray-400 text-sm">
                      <FiDownload className="mr-1" />
                      <span>250+</span>
                    </div>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>

          <div className="text-center mt-12">
            <Link
              to="/models"
              className="inline-block bg-blue-600 hover:bg-blue-700 text-white px-8 py-3 rounded-lg font-medium transition-colors"
            >
              View All Models
            </Link>
          </div>
        </div>
      </section>

      {/* Model Masonry Gallery Section */}
      <ModelMasonryGallery
        title="🌟 Showcase Gallery"
        subtitle="Khám phá những models nổi bật được cộng đồng yêu thích nhất"
        columns={4}
        maxItems={16}
        showStats={true}
        autoRefresh={true}
        refreshInterval={15000}
      />

      {/* Popular Models Section */}
      <section className="py-16 bg-white dark:bg-gray-900">
        <div className="container mx-auto px-4">
          <PopularModels limit={8} />
        </div>
      </section>

      {/* Recent Models Section */}
      <section className="py-16 bg-gray-50 dark:bg-gray-800">
        <div className="container mx-auto px-4">
          <RecentModels limit={6} />
        </div>
      </section>

      {/* Statistics Section */}
      <section className="py-16 bg-white dark:bg-gray-900">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <StatisticsDisplay />
          </div>
        </div>
      </section>

      {/* Newsletter Section */}
      <section className="py-16 bg-gradient-to-br from-blue-50 via-purple-50 to-pink-50 dark:from-gray-800 dark:via-blue-900 dark:to-purple-900">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <Newsletter />
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-gray-900 text-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl md:text-4xl font-bold mb-6">Ready to Elevate Your 3D Projects?</h2>
            <p className="text-xl text-gray-300 mb-8">
              Join thousands of professionals who trust 3DSKETCHUP.NET for high-quality 3D models
            </p>
            <div className="flex flex-col sm:flex-row justify-center gap-4">
              <Link
                to="/register"
                className="px-8 py-4 bg-blue-600 hover:bg-blue-700 rounded-lg font-medium text-lg transition-colors"
              >
                Create Free Account
              </Link>
              <Link
                to="/pricing"
                className="px-8 py-4 bg-transparent border-2 border-white hover:bg-white/10 rounded-lg font-medium text-lg transition-colors"
              >
                View Pricing
              </Link>
            </div>
          </div>
        </div>
      </section>
      </PageTransition>

      {/* Image Search Modal */}
      <ImageSearch
        isOpen={showImageSearch}
        onClose={() => setShowImageSearch(false)}
      />

      {/* Advanced Search Modal - DISABLED */}
      {/* <AdvancedSearch
        isOpen={showAdvancedSearch}
        onClose={() => setShowAdvancedSearch(false)}
        onSearch={handleAdvancedSearch}
      /> */}

      {/* Development Test Panel */}
      <DevTestPanel />
    </div>
  );
};

export default NewHome;
