import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Link } from 'react-router-dom';
import { FiChevronLeft, FiChevronRight, FiEye, FiDownload, FiStar } from 'react-icons/fi';
import { useModels } from '../context/ModelContext';
import ImageWithFallback from './ui/ImageWithFallback';

const ModelPreviewCarousel = ({ 
  title = "Khám Phá Bộ Sưu Tập 3D Models", 
  subtitle = "Hàng nghìn models chất lượng cao đang chờ bạn khám phá",
  autoPlay = true,
  autoPlayInterval = 3000,
  showControls = true,
  itemsPerView = 4
}) => {
  const { models, featuredModels } = useModels();
  const [currentIndex, setCurrentIndex] = useState(0);
  const [displayModels, setDisplayModels] = useState([]);
  const [isPlaying, setIsPlaying] = useState(autoPlay);

  // Prepare display models with random selection
  useEffect(() => {
    const allModels = [...(featuredModels || []), ...(models || [])];
    
    if (allModels.length > 0) {
      // Create a shuffled array of models
      const shuffled = [...allModels]
        .filter(model => model && (model._id || model.id) && model.imageUrl)
        .sort(() => 0.5 - Math.random())
        .slice(0, 20); // Take 20 random models
      
      setDisplayModels(shuffled);
    }
  }, [models, featuredModels]);

  // Auto-play functionality
  useEffect(() => {
    if (!isPlaying || displayModels.length === 0) return;

    const interval = setInterval(() => {
      setCurrentIndex(prev => 
        prev + itemsPerView >= displayModels.length ? 0 : prev + 1
      );
    }, autoPlayInterval);

    return () => clearInterval(interval);
  }, [isPlaying, displayModels.length, autoPlayInterval, itemsPerView]);

  // Navigation functions
  const goToNext = () => {
    setCurrentIndex(prev => 
      prev + itemsPerView >= displayModels.length ? 0 : prev + 1
    );
  };

  const goToPrev = () => {
    setCurrentIndex(prev => 
      prev === 0 ? Math.max(0, displayModels.length - itemsPerView) : prev - 1
    );
  };

  // Get visible models
  const getVisibleModels = () => {
    if (displayModels.length === 0) return [];
    
    const visible = [];
    for (let i = 0; i < itemsPerView; i++) {
      const index = (currentIndex + i) % displayModels.length;
      visible.push(displayModels[index]);
    }
    return visible;
  };

  const visibleModels = getVisibleModels();

  if (displayModels.length === 0) {
    return (
      <div className="py-16 bg-gradient-to-br from-gray-50 to-blue-50 dark:from-gray-900 dark:to-blue-900">
        <div className="container mx-auto px-4">
          <div className="text-center">
            <div className="animate-pulse">
              <div className="h-8 bg-gray-300 dark:bg-gray-700 rounded w-1/3 mx-auto mb-4"></div>
              <div className="h-4 bg-gray-300 dark:bg-gray-700 rounded w-1/2 mx-auto mb-8"></div>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                {[...Array(4)].map((_, i) => (
                  <div key={i} className="bg-gray-300 dark:bg-gray-700 rounded-xl h-64"></div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <section className="py-16 bg-gradient-to-br from-gray-50 via-blue-50 to-purple-50 dark:from-gray-900 dark:via-blue-900 dark:to-purple-900 relative overflow-hidden">
      {/* Background decorations */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute top-10 left-10 w-40 h-40 bg-blue-400 rounded-full blur-3xl animate-float"></div>
        <div className="absolute bottom-10 right-10 w-32 h-32 bg-purple-400 rounded-full blur-2xl animate-float" style={{animationDelay: '2s'}}></div>
        <div className="absolute top-1/2 left-1/4 w-24 h-24 bg-pink-400 rounded-full blur-xl animate-float" style={{animationDelay: '4s'}}></div>
      </div>

      <div className="container mx-auto px-4 relative z-10">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="text-center mb-12"
        >
          <h2 className="text-4xl md:text-5xl font-black text-gray-900 dark:text-white mb-4">
            {title}
          </h2>
          <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
            {subtitle}
          </p>
        </motion.div>

        {/* Carousel Container */}
        <div className="relative">
          {/* Navigation Buttons */}
          {showControls && displayModels.length > itemsPerView && (
            <>
              <button
                onClick={goToPrev}
                className="absolute left-0 top-1/2 -translate-y-1/2 -translate-x-4 z-20 w-12 h-12 bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm rounded-full shadow-xl hover:shadow-2xl transition-all duration-300 hover:scale-110 flex items-center justify-center text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400"
                onMouseEnter={() => setIsPlaying(false)}
                onMouseLeave={() => setIsPlaying(autoPlay)}
              >
                <FiChevronLeft className="w-6 h-6" />
              </button>
              
              <button
                onClick={goToNext}
                className="absolute right-0 top-1/2 -translate-y-1/2 translate-x-4 z-20 w-12 h-12 bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm rounded-full shadow-xl hover:shadow-2xl transition-all duration-300 hover:scale-110 flex items-center justify-center text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400"
                onMouseEnter={() => setIsPlaying(false)}
                onMouseLeave={() => setIsPlaying(autoPlay)}
              >
                <FiChevronRight className="w-6 h-6" />
              </button>
            </>
          )}

          {/* Models Grid */}
          <div 
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6"
            onMouseEnter={() => setIsPlaying(false)}
            onMouseLeave={() => setIsPlaying(autoPlay)}
          >
            <AnimatePresence mode="wait">
              {visibleModels.map((model, index) => (
                <motion.div
                  key={`${model._id || model.id}-${currentIndex}-${index}`}
                  initial={{ opacity: 0, scale: 0.9, y: 20 }}
                  animate={{ opacity: 1, scale: 1, y: 0 }}
                  exit={{ opacity: 0, scale: 0.9, y: -20 }}
                  transition={{ 
                    duration: 0.5, 
                    delay: index * 0.1,
                    ease: "easeOut"
                  }}
                  className="group relative bg-white dark:bg-gray-800 rounded-2xl overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-500 hover:-translate-y-2"
                >
                  {/* Image Container */}
                  <div className="relative aspect-square overflow-hidden">
                    <ImageWithFallback
                      src={model.imageUrl}
                      alt={model.title}
                      className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-700"
                      fallbackSrc="/images/placeholder.jpg"
                    />
                    
                    {/* Overlay */}
                    <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                      <div className="absolute bottom-4 left-4 right-4">
                        <h3 className="text-white font-bold text-lg mb-2 line-clamp-2">
                          {model.title}
                        </h3>
                        <div className="flex items-center gap-4 text-white/80 text-sm">
                          <div className="flex items-center gap-1">
                            <FiEye className="w-4 h-4" />
                            <span>{model.views || Math.floor(Math.random() * 1000) + 100}</span>
                          </div>
                          <div className="flex items-center gap-1">
                            <FiDownload className="w-4 h-4" />
                            <span>{model.downloads || Math.floor(Math.random() * 500) + 50}</span>
                          </div>
                          <div className="flex items-center gap-1">
                            <FiStar className="w-4 h-4 fill-yellow-400 text-yellow-400" />
                            <span>{model.rating || (Math.random() * 2 + 3).toFixed(1)}</span>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Category Badge */}
                    <div className="absolute top-3 left-3">
                      <span className="px-3 py-1 bg-blue-600/90 backdrop-blur-sm text-white text-xs font-semibold rounded-full">
                        {model.category || 'Interior'}
                      </span>
                    </div>

                    {/* Premium Badge */}
                    {model.isPremium && (
                      <div className="absolute top-3 right-3">
                        <span className="px-2 py-1 bg-gradient-to-r from-yellow-500 to-orange-500 text-white text-xs font-bold rounded-full">
                          PRO
                        </span>
                      </div>
                    )}
                  </div>

                  {/* Content */}
                  <div className="p-4">
                    <h3 className="font-bold text-gray-900 dark:text-white mb-2 line-clamp-2 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors">
                      {model.title}
                    </h3>
                    
                    {/* Action Button */}
                    <Link
                      to={`/model/${model._id || model.id}`}
                      className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white py-2 px-4 rounded-xl font-semibold text-center transition-all duration-300 hover:scale-105 shadow-md hover:shadow-lg flex items-center justify-center gap-2"
                    >
                      <FiEye className="w-4 h-4" />
                      <span>Xem Chi Tiết</span>
                    </Link>
                  </div>
                </motion.div>
              ))}
            </AnimatePresence>
          </div>

          {/* Dots Indicator */}
          {displayModels.length > itemsPerView && (
            <div className="flex justify-center mt-8 gap-2">
              {Array.from({ length: Math.ceil(displayModels.length / itemsPerView) }).map((_, index) => (
                <button
                  key={index}
                  onClick={() => setCurrentIndex(index * itemsPerView)}
                  className={`w-3 h-3 rounded-full transition-all duration-300 ${
                    Math.floor(currentIndex / itemsPerView) === index
                      ? 'bg-blue-600 w-8'
                      : 'bg-gray-300 dark:bg-gray-600 hover:bg-gray-400 dark:hover:bg-gray-500'
                  }`}
                />
              ))}
            </div>
          )}
        </div>

        {/* View All Button */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.8 }}
          className="text-center mt-12"
        >
          <Link
            to="/models"
            className="inline-flex items-center gap-3 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-8 py-4 rounded-2xl font-bold text-lg shadow-xl hover:shadow-2xl transition-all duration-300 hover:scale-105"
          >
            <span>Xem Tất Cả Models</span>
            <FiChevronRight className="w-5 h-5" />
          </Link>
        </motion.div>
      </div>
    </section>
  );
};

export default ModelPreviewCarousel;
