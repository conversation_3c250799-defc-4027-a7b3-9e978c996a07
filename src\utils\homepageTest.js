// Homepage functionality test
import realDataService from '../services/realDataService';

export const testHomepageComponents = async () => {
  console.log('🧪 Testing Homepage Components...');
  
  const results = {
    realDataService: false,
    models: false,
    featuredModels: false,
    popularModels: false,
    recentModels: false,
    categories: false,
    stats: false
  };

  try {
    // Test realDataService
    console.log('🔄 Testing realDataService...');
    
    // Test getAllModels
    try {
      const allModels = await realDataService.getAllModels();
      console.log(`✅ getAllModels: ${allModels.length} models`);
      results.models = allModels.length > 0;
    } catch (error) {
      console.error('❌ getAllModels failed:', error.message);
    }

    // Test getFeaturedModels
    try {
      const featuredModels = await realDataService.getFeaturedModels();
      console.log(`✅ getFeaturedModels: ${featuredModels.length} models`);
      results.featuredModels = featuredModels.length > 0;
    } catch (error) {
      console.error('❌ getFeaturedModels failed:', error.message);
    }

    // Test getPopularModels
    try {
      const popularModels = await realDataService.getPopularModels();
      console.log(`✅ getPopularModels: ${popularModels.length} models`);
      results.popularModels = popularModels.length > 0;
    } catch (error) {
      console.error('❌ getPopularModels failed:', error.message);
    }

    // Test getRecentModels
    try {
      const recentModels = await realDataService.getRecentModels();
      console.log(`✅ getRecentModels: ${recentModels.length} models`);
      results.recentModels = recentModels.length > 0;
    } catch (error) {
      console.error('❌ getRecentModels failed:', error.message);
    }

    // Test getCategories
    try {
      const categories = await realDataService.getCategories();
      console.log(`✅ getCategories: ${categories.length} categories`);
      results.categories = categories.length > 0;
    } catch (error) {
      console.error('❌ getCategories failed:', error.message);
    }

    // Test getStats
    try {
      const stats = await realDataService.getStats();
      console.log(`✅ getStats:`, stats);
      results.stats = stats && stats.models > 0;
    } catch (error) {
      console.error('❌ getStats failed:', error.message);
    }

    results.realDataService = true;

  } catch (error) {
    console.error('❌ realDataService test failed:', error.message);
  }

  // Summary
  console.log('\n📊 Test Results Summary:');
  console.log('========================');
  Object.entries(results).forEach(([key, value]) => {
    console.log(`${value ? '✅' : '❌'} ${key}: ${value ? 'PASS' : 'FAIL'}`);
  });

  const passedTests = Object.values(results).filter(Boolean).length;
  const totalTests = Object.keys(results).length;
  
  console.log(`\n🎯 Overall: ${passedTests}/${totalTests} tests passed`);
  
  if (passedTests === totalTests) {
    console.log('🎉 All homepage components are working correctly!');
  } else {
    console.log('⚠️ Some components need attention.');
  }

  return results;
};

// Test individual component data
export const testComponentData = async (componentName) => {
  console.log(`🔍 Testing ${componentName} data...`);
  
  try {
    switch (componentName) {
      case 'ModelPreviewCarousel':
        const carouselModels = await realDataService.getPopularModels();
        console.log(`${componentName}: ${carouselModels.length} models available`);
        return carouselModels;

      case 'FloatingModelPreviews':
        const floatingModels = await realDataService.getAllModels();
        console.log(`${componentName}: ${floatingModels.length} models available`);
        return floatingModels;

      case 'ModelMasonryGallery':
        const galleryModels = await realDataService.getPopularModels();
        console.log(`${componentName}: ${galleryModels.length} models available`);
        return galleryModels;

      case 'AnimatedModelCounter':
        const counterStats = await realDataService.getStats();
        console.log(`${componentName}:`, counterStats);
        return counterStats;

      case 'PopularModels':
        const popularModels = await realDataService.getPopularModels();
        console.log(`${componentName}: ${popularModels.length} models available`);
        return popularModels;

      case 'RecentModels':
        const recentModels = await realDataService.getRecentModels();
        console.log(`${componentName}: ${recentModels.length} models available`);
        return recentModels;

      case 'StatisticsDisplay':
        const displayStats = await realDataService.getStats();
        console.log(`${componentName}:`, displayStats);
        return displayStats;

      default:
        console.log(`❌ Unknown component: ${componentName}`);
        return null;
    }
  } catch (error) {
    console.error(`❌ Error testing ${componentName}:`, error.message);
    return null;
  }
};

// Auto-run test when imported in development
if (process.env.NODE_ENV === 'development') {
  // Run test after a short delay to ensure services are initialized
  setTimeout(() => {
    testHomepageComponents();
  }, 2000);
}

export default {
  testHomepageComponents,
  testComponentData
};
