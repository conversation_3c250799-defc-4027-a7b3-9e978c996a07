// Homepage functionality test
import realDataService from '../services/realDataService';

export const testHomepageComponents = async () => {
  const results = {
    realDataService: false,
    models: false,
    featuredModels: false,
    popularModels: false,
    recentModels: false,
    categories: false,
    stats: false
  };

  try {
    // Test realDataService
    // Test getAllModels
    try {
      const allModels = await realDataService.getAllModels();
      results.models = allModels.length > 0;
    } catch (error) {
      }

    // Test getFeaturedModels
    try {
      const featuredModels = await realDataService.getFeaturedModels();
      results.featuredModels = featuredModels.length > 0;
    } catch (error) {
      }

    // Test getPopularModels
    try {
      const popularModels = await realDataService.getPopularModels();
      results.popularModels = popularModels.length > 0;
    } catch (error) {
      }

    // Test getRecentModels
    try {
      const recentModels = await realDataService.getRecentModels();
      results.recentModels = recentModels.length > 0;
    } catch (error) {
      }

    // Test getCategories
    try {
      const categories = await realDataService.getCategories();
      results.categories = categories.length > 0;
    } catch (error) {
      }

    // Test getStats
    try {
      const stats = await realDataService.getStats();
      results.stats = stats && stats.models > 0;
    } catch (error) {
      }

    results.realDataService = true;

  } catch (error) {
    }

  // Summary
  Object.entries(results).forEach(([key, value]) => {
    });

  const passedTests = Object.values(results).filter(Boolean).length;
  const totalTests = Object.keys(results).length;

  if (passedTests === totalTests) {
    } else {
    }

  return results;
};

// Test individual component data
export const testComponentData = async (componentName) => {
  try {
    switch (componentName) {
      case 'ModelPreviewCarousel':
        const carouselModels = await realDataService.getPopularModels();
        return carouselModels;

      case 'FloatingModelPreviews':
        const floatingModels = await realDataService.getAllModels();
        return floatingModels;

      case 'ModelMasonryGallery':
        const galleryModels = await realDataService.getPopularModels();
        return galleryModels;

      case 'AnimatedModelCounter':
        const counterStats = await realDataService.getStats();
        return counterStats;

      case 'PopularModels':
        const popularModels = await realDataService.getPopularModels();
        return popularModels;

      case 'RecentModels':
        const recentModels = await realDataService.getRecentModels();
        return recentModels;

      case 'StatisticsDisplay':
        const displayStats = await realDataService.getStats();
        return displayStats;

      default:
        return null;
    }
  } catch (error) {
    return null;
  }
};

// Auto-run test when imported in development
if (process.env.NODE_ENV === 'development') {
  // Run test after a short delay to ensure services are initialized
  setTimeout(() => {
    testHomepageComponents();
  }, 2000);
}

export default {
  testHomepageComponents,
  testComponentData
};
