import React from 'react';
import { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { FiChevronDown } from 'react-icons/fi';

/**
 * Accordion Item Component
 */
const AccordionItem = ({
  title,
  children,
  isOpen,
  onClick,
  index,
  variant = 'default',
  iconPosition = 'right',
  disabled = false,
  className = '',
}) => {
  const contentRef = useRef(null);
  const [height, setHeight] = useState(0);

  // Update height when content changes or when opened/closed
  useEffect(() => {
    if (isOpen && contentRef.current) {
      setHeight(contentRef.current.scrollHeight);
    } else {
      setHeight(0);
    }
  }, [isOpen, children]);

  // Variant styles
  const variantStyles = {
    default: {
      header: 'bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700',
      border: 'border border-gray-200 dark:border-gray-700',
      content: 'bg-white dark:bg-gray-800',
    },
    bordered: {
      header: 'bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700',
      border: 'border-b border-gray-200 dark:border-gray-700',
      content: 'bg-white dark:bg-gray-800',
    },
    minimal: {
      header: 'bg-transparent hover:bg-gray-50 dark:hover:bg-gray-800',
      border: 'border-b border-gray-200 dark:border-gray-700',
      content: 'bg-transparent',
    },
    filled: {
      header: 'bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600',
      border: 'mb-2 rounded-lg',
      content: 'bg-gray-100 dark:bg-gray-700 rounded-b-lg',
    },
  };

  return (
    <div
      className={`${variantStyles[variant].border} ${className} ${
        disabled ? 'opacity-60 cursor-not-allowed' : ''
      }`}
    >
      <button
        className={`w-full px-4 py-3 flex items-center justify-between text-left font-medium transition-colors duration-200 ${
          variantStyles[variant].header
        } ${isOpen ? 'text-blue-600 dark:text-blue-400' : 'text-gray-700 dark:text-gray-300'}`}
        onClick={() => !disabled && onClick(index)}
        disabled={disabled}
        aria-expanded={isOpen}
        aria-controls={`accordion-content-${index}`}
      >
        {iconPosition === 'left' && (
          <motion.div
            animate={{ rotate: isOpen ? 180 : 0 }}
            transition={{ duration: 0.2 }}
            className="mr-2 flex-shrink-0"
          >
            <FiChevronDown className="w-5 h-5" />
          </motion.div>
        )}

        <span className="flex-1">{title}</span>

        {iconPosition === 'right' && (
          <motion.div
            animate={{ rotate: isOpen ? 180 : 0 }}
            transition={{ duration: 0.2 }}
            className="ml-2 flex-shrink-0"
          >
            <FiChevronDown className="w-5 h-5" />
          </motion.div>
        )}
      </button>

      <motion.div
        id={`accordion-content-${index}`}
        className="overflow-hidden"
        animate={{ height }}
        transition={{ duration: 0.2, ease: 'easeInOut' }}
      >
        <div
          ref={contentRef}
          className={`px-4 py-3 ${variantStyles[variant].content}`}
        >
          {children}
        </div>
      </motion.div>
    </div>
  );
};

/**
 * Accordion Component
 *
 * @param {Object} props - Component props
 * @param {Array} props.items - Array of accordion items with title and content
 * @param {boolean} props.allowMultiple - Whether multiple items can be open at once
 * @param {number|number[]} props.defaultOpen - Index or array of indices of items open by default
 * @param {string} props.variant - Accordion style variant: 'default', 'bordered', 'minimal', 'filled'
 * @param {string} props.iconPosition - Position of the icon: 'left', 'right'
 * @param {string} props.className - Additional CSS classes
 */
const Accordion = ({
  items = [],
  allowMultiple = false,
  defaultOpen = [],
  variant = 'default',
  iconPosition = 'right',
  className = '',
  ...props
}) => {
  // Convert defaultOpen to array if it's a number
  const initialOpenState = Array.isArray(defaultOpen)
    ? defaultOpen
    : typeof defaultOpen === 'number'
      ? [defaultOpen]
      : [];

  const [openItems, setOpenItems] = useState(initialOpenState);

  // Handle item click
  const handleItemClick = (index) => {
    if (allowMultiple) {
      // Toggle the clicked item
      setOpenItems(prev =>
        prev.includes(index)
          ? prev.filter(i => i !== index)
          : [...prev, index]
      );
    } else {
      // Close all items if the clicked one is already open, otherwise open only the clicked one
      setOpenItems(prev =>
        prev.includes(index) && prev.length === 1
          ? []
          : [index]
      );
    }
  };

  return (
    <div className={`space-y-2 ${className}`} {...props}>
      {items.map((item, index) => (
        <AccordionItem
          key={index}
          title={item.title}
          isOpen={openItems.includes(index)}
          onClick={handleItemClick}
          index={index}
          variant={variant}
          iconPosition={iconPosition}
          disabled={item.disabled}
          className={item.className}
        >
          {item.content}
        </AccordionItem>
      ))}
    </div>
  );
};

export default Accordion;
