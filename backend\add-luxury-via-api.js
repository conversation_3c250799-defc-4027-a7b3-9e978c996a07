import fetch from 'node-fetch';

const addLuxuryModel = async () => {
  try {
    console.log('Adding luxury bedroom model via API...');

    const luxuryModel = {
      title: 'Luxury Bedroom Interior - Enscape Model',
      description: 'Stunning luxury bedroom interior design with premium furniture, elegant lighting, and sophisticated decor. Perfect for high-end residential projects and architectural visualization. Features detailed textures, realistic materials, and optimized for Enscape rendering. Includes king-size bed, premium bedding, designer furniture, ambient lighting, and luxury accessories.',
      category: 'Residential',
      subcategory: 'Interior',
      format: 'Sketchup 2023',
      year: '2024',
      imageUrl: 'https://3dsketchup.net/wp-content/uploads/2024/01/luxury-bedroom-interior-skp_model-enscape-0401080323-1.jpg',
      fileUrl: 'https://3dsketchup.net/free-download/luxury-bedroom-interior-skp_model-enscape-0401080323/',
      modelUrl: '/uploads/previews/luxury_bedroom_preview.glb',
      fileSize: 45 * 1024 * 1024, // 45 MB
      fileFormat: 'skp',
      polygonCount: 180000,
      textured: true,
      rigged: false,
      animated: false,
      dimensions: {
        width: 6.5,
        height: 3.2,
        depth: 5.8,
        unit: 'm'
      },
      tags: ['luxury', 'bedroom', 'interior', 'residential', 'enscape', 'premium', 'furniture', 'lighting', 'modern', 'elegant'],
      downloads: 1247,
      views: 3892,
      rating: 4.8,
      isPremium: true
    };

    const response = await fetch('http://localhost:5002/api/models', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(luxuryModel)
    });

    if (response.ok) {
      const result = await response.json();
      console.log('Luxury bedroom model added successfully!');
      console.log('Model ID:', result.data._id);
      console.log('Model Title:', result.data.title);
    } else {
      const error = await response.text();
      console.error('Error adding model:', error);
    }

  } catch (error) {
    console.error('Error:', error);
  }
};

addLuxuryModel();
