import fetch from 'node-fetch';

const updateModel18WithAuth = async () => {
  try {
    console.log('Logging in as admin...');

    // Step 1: Login as admin to get token
    const loginResponse = await fetch('http://localhost:5002/api/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'password123'
      })
    });

    if (!loginResponse.ok) {
      const loginError = await loginResponse.text();
      console.error('Login failed:', loginError);
      return;
    }

    const loginData = await loginResponse.json();
    const token = loginData.token;
    console.log('Login successful! Token obtained.');

    // Step 2: Get Model 18 ID
    const modelsResponse = await fetch('http://localhost:5002/api/models?limit=25');
    const modelsData = await modelsResponse.json();
    
    const model18 = modelsData.data.find(model => model.title === 'Sample 3D Model 18');
    if (!model18) {
      console.error('Model 18 not found');
      return;
    }

    console.log('Found Model 18:', model18._id);

    // Step 3: Update Model 18 with luxury bedroom data
    const updateData = {
      title: 'Luxury Bedroom Interior - Enscape Model',
      description: 'Stunning luxury bedroom interior design with premium furniture, elegant lighting, and sophisticated decor. Perfect for high-end residential projects and architectural visualization. Features detailed textures, realistic materials, and optimized for Enscape rendering. Includes king-size bed, premium bedding, designer furniture, ambient lighting, and luxury accessories.',
      category: 'Residential',
      subcategory: 'Interior',
      format: 'Sketchup 2023',
      year: '2024',
      imageUrl: 'https://3dsketchup.net/wp-content/uploads/2024/01/luxury-bedroom-interior-skp_model-enscape-0401080323-1.jpg',
      fileUrl: 'https://3dsketchup.net/free-download/luxury-bedroom-interior-skp_model-enscape-0401080323/',
      modelUrl: '/uploads/previews/luxury_bedroom_preview.glb',
      fileSize: 45 * 1024 * 1024, // 45 MB
      fileFormat: 'skp',
      polygonCount: 180000,
      textured: true,
      rigged: false,
      animated: false,
      dimensions: {
        width: 6.5,
        height: 3.2,
        depth: 5.8,
        unit: 'm'
      },
      tags: ['luxury', 'bedroom', 'interior', 'residential', 'enscape', 'premium', 'furniture', 'lighting', 'modern', 'elegant'],
      downloads: 1247,
      views: 3892,
      rating: 4.8,
      isPremium: true
    };

    const updateResponse = await fetch(`http://localhost:5002/api/models/${model18._id}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify(updateData)
    });

    if (updateResponse.ok) {
      const updatedModel = await updateResponse.json();
      console.log('Model 18 updated successfully!');
      console.log('New title:', updatedModel.data.title);
      console.log('New fileUrl:', updatedModel.data.fileUrl);
      console.log('New imageUrl:', updatedModel.data.imageUrl);
      console.log('Premium status:', updatedModel.data.isPremium);
      console.log('Rating:', updatedModel.data.rating);
      console.log('Model ID:', updatedModel.data._id);
    } else {
      const updateError = await updateResponse.text();
      console.error('Update failed:', updateError);
    }

  } catch (error) {
    console.error('Error:', error);
  }
};

updateModel18WithAuth();
