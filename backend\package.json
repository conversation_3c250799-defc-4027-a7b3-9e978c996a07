{"name": "3dsketchup-backend", "version": "1.0.0", "description": "Backend API for 3DSKETCHUP.NET", "main": "src/server.js", "type": "module", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "test": "echo \"Error: no test specified\" && exit 1", "seed": "node seed.js"}, "keywords": ["3d", "models", "api", "express", "mongodb"], "author": "3DSKETCHUP.NET", "license": "MIT", "dependencies": {"@google/generative-ai": "^0.24.1", "bcryptjs": "^2.4.3", "cheerio": "^1.0.0", "cors": "^2.8.5", "dotenv": "^16.0.3", "express": "^4.18.2", "express-async-handler": "^1.2.0", "express-fileupload": "^1.4.0", "express-validator": "^7.0.1", "file-type": "^21.0.0", "jsdom": "^26.1.0", "jsonwebtoken": "^9.0.0", "mongoose": "^7.0.3", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "node-fetch": "^3.3.2", "playwright": "^1.52.0", "puppeteer": "^24.9.0", "rollup-plugin-visualizer": "^5.14.0", "sharp": "^0.33.5", "slugify": "^1.6.6", "stripe": "^12.0.0", "vite-plugin-compression": "^0.5.1", "vite-plugin-pwa": "^1.0.0", "workbox-window": "^7.3.0"}, "devDependencies": {"nodemon": "^2.0.22"}}