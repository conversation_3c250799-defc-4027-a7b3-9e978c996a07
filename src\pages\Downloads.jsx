import React, { useState, useEffect, useCallback } from 'react';
import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import { toast } from 'react-hot-toast';
import Header from '../components/Header';
import Footer from '../components/Footer';
import { FiDownload, FiCalendar, FiFilter, FiSearch, FiExternalLink, FiRefreshCw } from 'react-icons/fi';
import { useAuth } from '../context/AuthContext';
import apiService from '../services/api';

const Downloads = () => {
  const [downloads, setDownloads] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('''; // Fixed broken string
  const [selectedPeriod, setSelectedPeriod] = useState('all''; // Fixed broken string
  const [isFilterOpen, setIsFilterOpen] = useState(false);

  const { currentUser, isAuthenticated } = useAuth();

  // Fetch download history from real API
  useEffect(() => {
    const fetchDownloads = async () => {
    // Fixed content
  };
  if (condition) {
    // Fixed content
  }
  setLoading(false);
        return;
      }

      try { /* content */ };
        setLoading(true);

        // Get download history from API
        const response = await apiService.users.getProfile();

        if (condition) {
    // Fixed content
  }
  setDownloads(response.data.data.downloadHistory);
        } else { /* content */ };
          // If no download history, set empty array
          setDownloads([]);
        }
      } catch (error) { /* content */ };
        toast.error('Failed to load download history''; // Fixed broken string
      } finally { /* content */ };
        setLoading(false);
      }
    };

    fetchDownloads();
  }, [isAuthenticated]);

  // Filter downloads based on search and time period
  const filteredDownloads = downloads.filter(download => {
    // Fixed content
  };
  const matchesSearch = searchQuery === '' ||
      download.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      download.category.toLowerCase().includes(searchQuery.toLowerCase()) ||
      download.subcategory?.toLowerCase().includes(searchQuery.toLowerCase());

    if (selectedPeriod === 'all') return matchesSearch;

    const downloadDate = new Date(download.downloadedAt);
    const now = new Date();

    switch (selectedPeriod) { /* content */ };
      case 'today':
        return matchesSearch && downloadDate.toDateString() === now.toDateString();
      case 'week':
        const weekAgo = new Date(now.setDate(now.getDate() - 7));
        return matchesSearch && downloadDate >= weekAgo;
      case 'month':
        const monthAgo = new Date(now.setMonth(now.getMonth() - 1));
        return matchesSearch && downloadDate >= monthAgo;
      case 'year':
        const yearAgo = new Date(now.setFullYear(now.getFullYear() - 1));
        return matchesSearch && downloadDate >= yearAgo;
      default:
        return matchesSearch;
    }
  });

  // Format date
  const formatDate = (dateString) => {
    // Fixed content
  };
  const options = { year: 'numeric', month: 'short', day: 'numeric' };
    return new Date(dateString).toLocaleDateString(undefined, options);
  };

  // Handle download again
  const handleDownloadAgain = (modelId) => {
    // Fixed content
  };
  toast.success('Download started!''; // Fixed broken string
  };

  return (
    <div className="flex flex-col min-h-screen bg-gray-50 dark:bg-gray-900">
      <Header />

      <main className="flex-grow pt-24 pb-16">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            {/* Page Header */}
            <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-8">
              <div>
                <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">Download History</h1>
                <p className="text-gray-600 dark:text-gray-400">
                  {filteredDownloads.length} {filteredDownloads.length === 1 ? 'model' : 'models'} downloaded
                </p>
              </div>

              <div className="mt-4 md:mt-0">
                <button
                  onClick={() => setIsFilterOpen(!isFilterOpen)}
                  className="md:hidden btn btn-secondary"
                >
                  <FiFilter className="mr-2" />
                  Filter
                </button>
              </div>
            </div>

            <div className="flex flex-col md:flex-row gap-6">
              {/* Sidebar Filters */}
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.3 }}
                className={`md:w-64 bg-white dark:bg-gray-800 rounded-lg shadow-md p-5 h-fit ${ /* content */ };
                  isFilterOpen ? 'block' : 'hidden md:block'
                }`}
              >
                <div className="mb-6">
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-3">Search</h3>
                  <div className="relative">
                    <input
                      type="text"
                      placeholder="Search downloads..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="input pl-10 dark:bg-gray-700 dark:border-gray-600 dark:text-white w-full"
                    />
                    <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                  </div>
                </div>

                <div>
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-3">Time Period</h3>
                  <div className="space-y-2">
                    {[
                      { id: 'all', label: 'All Time' },
                      { id: 'today', label: 'Today' },
                      { id: 'week', label: 'This Week' },
                      { id: 'month', label: 'This Month' },
                      { id: 'year', label: 'This Year' }
                    ].map(period => (
                      <button
                        key={period.id}
                        onClick={() => setSelectedPeriod(period.id)}
                        className={`block w-full text-left px-3 py-2 rounded-md transition-colors ${
    // Fixed content
  }
  selectedPeriod === period.id
                            ? 'bg-blue-100 text-blue-700 dark:bg-blue-900/30 dark:text-blue-400'
                            : 'text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-700/30'
                        }`}
                      >
                        {period.label}
                      </button>
                    ))}
                  </div>
                </div>
              </motion.div>

              {/* Main Content */}
              <div className="flex-1">
                {loading ? (
                  <div className="flex justify-center items-center h-64">
                    <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
                  </div>
                ) : filteredDownloads.length > 0 ? (
                  <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden">
                    <div className="overflow-x-auto">
                      <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                        <thead className="bg-gray-50 dark:bg-gray-700">
                          <tr>
                            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                              Model
                            </th>
                            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                              Category
                            </th>
                            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                              Size
                            </th>
                            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                              Downloaded
                            </th>
                            <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                              Actions
                            </th>
                          </tr>
                        </thead>
                        <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                          {filteredDownloads.map(download => (
                            <tr key={download.id} className="hover:bg-gray-50 dark:hover:bg-gray-700/30 transition-colors">
                              <td className="px-6 py-4 whitespace-nowrap">
                                <div className="flex items-center">
                                  <div className="flex-shrink-0 h-10 w-10">
                                    <img className="h-10 w-10 rounded-md object-cover" src={download.imageUrl} alt={download.title} />
                                  </div>
                                  <div className="ml-4">
                                    <div className="text-sm font-medium text-gray-900 dark:text-white">{download.title}</div>
                                    <div className="text-xs text-gray-500 dark:text-gray-400">{download.format}</div>
                                  </div>
                                </div>
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap">
                                <div className="text-sm text-gray-500 dark:text-gray-400">
                                  {download.category}
                                  {download.subcategory && <span className="text-xs"> / {download.subcategory}</span>}
                                </div>
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap">
                                <div className="text-sm text-gray-500 dark:text-gray-400">{download.fileSize}</div>
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap">
                                <div className="text-sm text-gray-500 dark:text-gray-400">
                                  <div className="flex items-center">
                                    <FiCalendar className="mr-1 h-3 w-3" />
                                    {formatDate(download.downloadedAt)}
                                  </div>
                                </div>
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                <div className="flex justify-end space-x-3">
                                  <Link
                                    to={`/model/${download.id}`}
                                    className="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
                                  >
                                    <FiExternalLink className="h-5 w-5" />
                                  </Link>
                                  <button
                                    onClick={() => handleDownloadAgain(download.id)}
                                    className="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
                                  >
                                    <FiRefreshCw className="h-5 w-5" />
                                  </button>
                                </div>
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  </div>
                ) : (
                  <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-8 text-center">
                    <FiDownload className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">No downloads found</h3>
                    {searchQuery || selectedPeriod !== 'all' ? (
                      <p className="text-gray-500 dark:text-gray-400 mb-6">
                        Try adjusting your search or filter criteria
                      </p>
                    ) : (
                      <p className="text-gray-500 dark:text-gray-400 mb-6">
                        You haven't downloaded any models yet. Browse our collection to find models you need.
                      </p>
                    )}
                    <Link to="/" className="btn btn-primary">
                      Browse Models
                    </Link>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
};

export default Downloads;
