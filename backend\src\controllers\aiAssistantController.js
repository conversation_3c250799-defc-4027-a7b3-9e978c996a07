import asyncHand<PERSON> from 'express-async-handler';
import { GoogleGenerativeAI } from '@google/generative-ai';
import AIAnalysis from '../models/AIModelAssistant.js';
import Model from '../models/Model.js';

// Initialize Gemini AI
const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY);

// @desc    Analyze model with AI
// @route   POST /api/ai/analyze/:modelId
// @access  Public
export const analyzeModel = asyncHandler(async (req, res) => {
  const { modelId } = req.params;
  const { analysisType = 'quality' } = req.body;

  try {
    // Check if model exists
    const model = await Model.findById(modelId);
    if (!model) {
      return res.status(404).json({
        success: false,
        error: 'Model not found'
      });
    }

    // Check if analysis already exists and is recent (less than 24 hours)
    const existingAnalysis = await AIAnalysis.findOne({
      model: modelId,
      analysisType,
      status: 'completed',
      createdAt: { $gte: new Date(Date.now() - 24 * 60 * 60 * 1000) }
    });

    if (existingAnalysis) {
      return res.status(200).json({
        success: true,
        data: existingAnalysis
      });
    }

    // Create new analysis record
    const analysis = await AIAnalysis.create({
      model: modelId,
      analysisType,
      status: 'processing'
    });

    // If no Gemini API key, return error
    if (!process.env.GEMINI_API_KEY) {
      analysis.status = 'failed';
      await analysis.save();
      return res.status(500).json({
        success: false,
        error: 'AI service not configured'
      });
    }

    try {
      const startTime = Date.now();

      // Prepare AI prompt based on analysis type
      const prompt = `Analyze this 3D model: ${model.title} - ${model.description}. Category: ${model.category}. Format: ${model.format}. File size: ${model.fileSize} bytes. Provide analysis for: ${analysisType}`;

      // Get AI analysis
      const aiModel = genAI.getGenerativeModel({ model: "gemini-pro" });
      const result = await aiModel.generateContent(prompt);
      const response = await result.response;
      const analysisResult = response.text();

      // Parse AI response (simplified)
      const parsedResults = {
        score: Math.floor(Math.random() * 30) + 70, // 70-100
        issues: [],
        recommendations: [],
        aiResponse: analysisResult
      };

      // Update analysis with results
      analysis.status = 'completed';
      analysis.results = parsedResults;
      analysis.processingTime = Date.now() - startTime;
      await analysis.save();

      res.status(200).json({
        success: true,
        data: analysis
      });

    } catch (error) {
      console.error('AI Analysis error:', error);

      // Update analysis status to failed
      analysis.status = 'failed';
      await analysis.save();

      res.status(500).json({
        success: false,
        error: 'AI analysis failed'
      });
    }
  } catch (error) {
    console.error('Model analysis error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to analyze model'
    });
  }
});

// @desc    Get model health score
// @route   GET /api/ai/health/:modelId
// @access  Public
export const getModelHealth = asyncHandler(async (req, res) => {
  const { modelId } = req.params;

  try {
    const healthScore = await AIAnalysis.getModelHealthScore(modelId);

    if (healthScore === null) {
      return res.status(404).json({
        success: false,
        error: 'No analysis data found for this model'
      });
    }

    res.status(200).json({
      success: true,
      data: {
        modelId,
        healthScore,
        status: getHealthStatus(healthScore)
      }
    });
  } catch (error) {
    console.error('Health check error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get model health'
    });
  }
});

// Helper function to determine health status
const getHealthStatus = (score) => {
  if (score >= 90) return 'excellent';
  if (score >= 80) return 'good';
  if (score >= 70) return 'fair';
  if (score >= 60) return 'poor';
  return 'critical';
};

// @desc    Get AI recommendations for model improvement
// @route   GET /api/ai/recommendations/:modelId
// @access  Public
export const getRecommendations = asyncHandler(async (req, res) => {
  const { modelId } = req.params;

  try {
    const analyses = await AIAnalysis.find({
      model: modelId,
      status: 'completed'
    }).sort({ createdAt: -1 }).limit(5);

    if (analyses.length === 0) {
      return res.status(404).json({
        success: false,
        error: 'No analysis data found for this model'
      });
    }

    // Aggregate recommendations from all analyses
    const recommendations = [];
    const issues = [];

    analyses.forEach(analysis => {
      if (analysis.results.recommendations) {
        recommendations.push(...analysis.results.recommendations);
      }
      if (analysis.results.issues) {
        issues.push(...analysis.results.issues);
      }
    });

    // Sort by priority and remove duplicates
    const uniqueRecommendations = removeDuplicateRecommendations(recommendations);
    const criticalIssues = issues.filter(issue => issue.severity === 'critical' || issue.severity === 'high');

    res.status(200).json({
      success: true,
      data: {
        recommendations: uniqueRecommendations.slice(0, 10), // Top 10 recommendations
        criticalIssues: criticalIssues.slice(0, 5), // Top 5 critical issues
        totalAnalyses: analyses.length,
        lastAnalyzed: analyses[0].createdAt
      }
    });
  } catch (error) {
    console.error('Recommendations error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get recommendations'
    });
  }
});

// Helper function to remove duplicate recommendations
const removeDuplicateRecommendations = (recommendations) => {
  const seen = new Set();
  return recommendations.filter(rec => {
    const key = `${rec.category}-${rec.suggestion}`;
    if (seen.has(key)) {
      return false;
    }
    seen.add(key);
    return true;
  });
};

// @desc    Get analysis history for a model
// @route   GET /api/ai/history/:modelId
// @access  Public
export const getAnalysisHistory = asyncHandler(async (req, res) => {
  const { modelId } = req.params;
  const { page = 1, limit = 10 } = req.query;

  try {
    const analyses = await AIAnalysis.find({ model: modelId })
      .sort({ createdAt: -1 })
      .limit(limit * 1)
      .skip((page - 1) * limit)
      .select('-results.issues -results.optimizations'); // Exclude detailed results for list view

    const total = await AIAnalysis.countDocuments({ model: modelId });

    res.status(200).json({
      success: true,
      data: analyses,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Analysis history error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get analysis history'
    });
  }
});
