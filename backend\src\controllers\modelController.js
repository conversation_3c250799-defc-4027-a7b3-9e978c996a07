import Model from '../models/Model.js';
import User from '../models/User.js';
import mongoose from 'mongoose';
import imageUploadService from '../services/imageUploadService.js';

// @desc    Get all models
// @route   GET /api/models
// @access  Public
export const getModels = async (req, res, next) => {
  try {
    // Check if MongoDB is connected
    if (mongoose.connection.readyState !== 1) {
      return res.status(200).json({
        success: true,
        count: 0,
        pagination: {},
        data: []
      });
    }

    let query;

    // Copy req.query
    const reqQuery = { ...req.query };

    // Fields to exclude
    const removeFields = ['select', 'sort', 'page', 'limit', 'search'];

    // Loop over removeFields and delete them from reqQuery
    removeFields.forEach(param => delete reqQuery[param]);

    // Create query string
    let queryStr = JSON.stringify(reqQuery);

    // Create operators ($gt, $gte, etc)
    queryStr = queryStr.replace(/\\b(gt|gte|lt|lte|in)\\b/g, match => `$${match}`);

    // Finding resource
    query = Model.find(JSON.parse(queryStr));

    // Search functionality
    if (req.query.search) {
      query = query.find({ $text: { $search: req.query.search } });
    }

    // Select Fields
    if (req.query.select) {
      const fields = req.query.select.split(',').join(' ');
      query = query.select(fields);
    }

    // Sort
    if (req.query.sort) {
      const sortBy = req.query.sort.split(',').join(' ');
      query = query.sort(sortBy);
    } else {
      query = query.sort('-createdAt');
    }

    // Pagination
    const page = parseInt(req.query.page, 10) || 1;
    const limit = parseInt(req.query.limit, 10) || 10;
    const startIndex = (page - 1) * limit;
    const endIndex = page * limit;
    const total = await Model.countDocuments(JSON.parse(queryStr));

    query = query.skip(startIndex).limit(limit);

    // Executing query
    const models = await query;

    // Pagination result
    const pagination = {};

    if (endIndex < total) {
      pagination.next = {
        page: page + 1,
        limit
      };
    }

    if (startIndex > 0) {
      pagination.prev = {
        page: page - 1,
        limit
      };
    }

    res.status(200).json({
      success: true,
      count: models.length,
      pagination,
      data: models
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Get single model
// @route   GET /api/models/:id
// @access  Public
export const getModel = async (req, res, next) => {
  try {
    const { id } = req.params;

    // Validate ID
    if (!id || id === 'undefined' || id === 'null') {
      return res.status(400).json({
        success: false,
        error: 'Invalid model ID'
      });
    }

    // Check if ID is a valid MongoDB ObjectId
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid model ID format'
      });
    }

    // Check if MongoDB is connected
    if (mongoose.connection.readyState !== 1) {
      return res.status(404).json({
        success: false,
        error: 'Model not found'
      });
    }

    const model = await Model.findById(id);

    if (!model) {
      return res.status(404).json({
        success: false,
        error: 'Model not found'
      });
    }

    // Increment views
    model.views += 1;
    await model.save();

    res.status(200).json({
      success: true,
      data: model
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Get featured models
// @route   GET /api/models/featured
// @access  Public
export const getFeaturedModels = async (req, res, next) => {
  try {
    // Check if MongoDB is connected
    if (mongoose.connection.readyState !== 1) {
      return res.status(200).json({
        success: true,
        count: 0,
        data: []
      });
    }

    // Get featured models (premium models with highest downloads)
    const models = await Model.find({ isPremium: true })
      .sort('-downloads')
      .limit(6);

    res.status(200).json({
      success: true,
      count: models.length,
      data: models
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Get popular models
// @route   GET /api/models/popular
// @access  Public
export const getPopularModels = async (req, res, next) => {
  try {
    // Get popular models (highest downloads)
    const models = await Model.find()
      .sort('-downloads')
      .limit(8);

    res.status(200).json({
      success: true,
      count: models.length,
      data: models
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Get recent models
// @route   GET /api/models/recent
// @access  Public
export const getRecentModels = async (req, res, next) => {
  try {
    // Get recent models (newest first)
    const models = await Model.find()
      .sort('-createdAt')
      .limit(8);

    res.status(200).json({
      success: true,
      count: models.length,
      data: models
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Create new model
// @route   POST /api/models
// @access  Private
export const createModel = async (req, res, next) => {
  try {
    // Add user to req.body (use dummy ID if no user)
    req.body.createdBy = req.user?.id || new mongoose.Types.ObjectId();

    // Import file downloader utility
    const fileDownloader = await import('../utils/fileDownloader.js');

    // Check if this is a URL-based upload or file upload
    const isUrlUpload = req.body.fileUrl || req.body.imageUrl;

    if (isUrlUpload) {
      // Handle URL-based upload (simple - just store URLs)
      return await handleSimpleUrlUpload(req, res, next);
    } else {
      // Handle traditional file upload
      return await handleFileUpload(req, res, next);
    }
  } catch (error) {
    console.error('Create model error:', error);
    next(error);
  }
};

/**
 * Handle simple URL-based upload (just store URLs)
 */
const handleSimpleUrlUpload = async (req, res, next) => {
  try {
    const { fileUrl, imageUrl } = req.body;

    // Basic URL validation
    if (!fileUrl || !fileUrl.startsWith('http')) {
      return res.status(400).json({
        success: false,
        error: 'Valid model file URL is required'
      });
    }

    if (!imageUrl || !imageUrl.startsWith('http')) {
      return res.status(400).json({
        success: false,
        error: 'Valid preview image URL is required'
      });
    }

    // Parse tags if they're a string
    if (typeof req.body.tags === 'string') {
      try {
        req.body.tags = JSON.parse(req.body.tags);
      } catch (e) {
        // If JSON parse fails, split by comma
        req.body.tags = req.body.tags.split(',').map(tag => tag.trim()).filter(tag => tag);
      }
    }

    // Create the model in the database
    const model = await Model.create(req.body);

    res.status(201).json({
      success: true,
      data: model
    });
  } catch (error) {
    console.error('Simple URL upload error:', error);
    next(error);
  }
};

/**
 * Handle traditional file upload
 */
const handleFileUpload = async (req, res, next) => {
  try {
    // Check if files are uploaded
    if (!req.files) {
      return res.status(400).json({
        success: false,
        error: 'Please upload model files'
      });
    }

    // Handle main model file upload
    const modelFile = req.files.modelFile;

    // Check if it's a valid file type
    const validFileTypes = ['application/octet-stream', 'application/x-zip-compressed', 'model/gltf-binary', 'model/gltf+json'];
    const validExtensions = ['skp', 'max', 'blend', 'fbx', 'obj', 'gltf', 'glb', 'zip'];

    // Get file extension
    const fileExtension = modelFile.name.split('.').pop().toLowerCase();

    if (!validExtensions.includes(fileExtension)) {
      return res.status(400).json({
        success: false,
        error: `Please upload a valid 3D model file. Supported formats: ${validExtensions.join(', ')}`
      });
    }

    // Create custom filename
    const fileName = `model_${req.user.id}_${Date.now()}.${fileExtension}`;

    // Move file to uploads directory
    modelFile.mv(`./uploads/models/${fileName}`, async (err) => {
      if (err) {
        console.error(err);
        return res.status(500).json({
          success: false,
          error: 'Problem with file upload'
        });
      }

      // Set file URL in request body
      req.body.fileUrl = `/uploads/models/${fileName}`;
      req.body.fileSize = modelFile.size;
      req.body.fileFormat = fileExtension;

      // Handle preview image upload
      if (req.files.previewImage) {
        const previewImage = req.files.previewImage;
        const imageExtension = previewImage.name.split('.').pop().toLowerCase();
        const validImageExtensions = ['jpg', 'jpeg', 'png', 'webp'];

        if (!validImageExtensions.includes(imageExtension)) {
          return res.status(400).json({
            success: false,
            error: `Please upload a valid image file. Supported formats: ${validImageExtensions.join(', ')}`
          });
        }

        const imageFileName = `preview_${req.user.id}_${Date.now()}.${imageExtension}`;

        previewImage.mv(`./uploads/images/${imageFileName}`, async (err) => {
          if (err) {
            console.error(err);
            return res.status(500).json({
              success: false,
              error: 'Problem with image upload'
            });
          }

          req.body.imageUrl = `/uploads/images/${imageFileName}`;

          // Handle 3D preview model if available
          if (req.files.previewModel) {
            const previewModel = req.files.previewModel;
            const previewExtension = previewModel.name.split('.').pop().toLowerCase();

            if (!['gltf', 'glb'].includes(previewExtension)) {
              return res.status(400).json({
                success: false,
                error: 'Preview model must be in GLTF or GLB format'
              });
            }

            const previewFileName = `preview3d_${req.user.id}_${Date.now()}.${previewExtension}`;

            previewModel.mv(`./uploads/previews/${previewFileName}`, async (err) => {
              if (err) {
                console.error(err);
                return res.status(500).json({
                  success: false,
                  error: 'Problem with 3D preview upload'
                });
              }

              req.body.modelUrl = `/uploads/previews/${previewFileName}`;

              // Create the model in the database
              const model = await Model.create(req.body);

              res.status(201).json({
                success: true,
                data: model
              });
            });
          } else {
            // Create the model without 3D preview
            const model = await Model.create(req.body);

            res.status(201).json({
              success: true,
              data: model
            });
          }
        });
      } else {
        // No preview image provided
        return res.status(400).json({
          success: false,
          error: 'Please upload a preview image'
        });
      }
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Handle URL-based upload
 */
const handleUrlBasedUpload = async (req, res, next, fileDownloader) => {
  try {
    const { fileUrl: modelFileUrl, imageUrl: previewImageUrl, previewModelUrl } = req.body;
    const validModelExtensions = ['skp', 'max', 'blend', 'fbx', 'obj', 'gltf', 'glb', 'zip'];
    const validImageExtensions = ['jpg', 'jpeg', 'png', 'webp'];
    const validPreviewModelExtensions = ['gltf', 'glb'];

    // Validate model file URL
    if (!modelFileUrl) {
      return res.status(400).json({
        success: false,
        error: 'Model file URL is required'
      });
    }

    // Validate preview image URL
    if (!previewImageUrl) {
      return res.status(400).json({
        success: false,
        error: 'Preview image URL is required'
      });
    }

    // Validate model file URL
    const modelUrlValidation = await fileDownloader.validateUrl(modelFileUrl, {
      timeout: 10000
    });

    if (!modelUrlValidation.valid) {
      return res.status(400).json({
        success: false,
        error: `Invalid model file URL: ${modelUrlValidation.error}`
      });
    }

    // Validate preview image URL
    const imageUrlValidation = await fileDownloader.validateUrl(previewImageUrl, {
      timeout: 5000,
      allowedMimeTypes: ['image/']
    });

    if (!imageUrlValidation.valid) {
      return res.status(400).json({
        success: false,
        error: `Invalid preview image URL: ${imageUrlValidation.error}`
      });
    }

    // Validate preview model URL if provided
    let previewModelUrlValidation = { valid: true };
    if (previewModelUrl) {
      previewModelUrlValidation = await fileDownloader.validateUrl(previewModelUrl, {
        timeout: 10000,
        allowedMimeTypes: ['model/', 'application/octet-stream']
      });

      if (!previewModelUrlValidation.valid) {
        return res.status(400).json({
          success: false,
          error: `Invalid preview model URL: ${previewModelUrlValidation.error}`
        });
      }
    }

    // Download model file
    const modelFileExtension = fileDownloader.getFileExtension(
      modelFileUrl,
      modelUrlValidation.contentType
    );

    if (!validModelExtensions.includes(modelFileExtension)) {
      return res.status(400).json({
        success: false,
        error: `Invalid model file format. Supported formats: ${validModelExtensions.join(', ')}`
      });
    }

    const modelFileName = `model_${req.user.id}_${Date.now()}.${modelFileExtension}`;
    const modelFilePath = `./uploads/models/${modelFileName}`;

    console.log(`Downloading model file from ${modelFileUrl} to ${modelFilePath}`);

    const modelFileResult = await fileDownloader.downloadFile(
      modelFileUrl,
      modelFilePath,
      {
        onProgress: (progress) => {
          console.log(`Model download progress: ${progress}%`);
        }
      }
    );

    // Download preview image
    const imageFileExtension = fileDownloader.getFileExtension(
      previewImageUrl,
      imageUrlValidation.contentType
    );

    if (!validImageExtensions.includes(imageFileExtension)) {
      return res.status(400).json({
        success: false,
        error: `Invalid image file format. Supported formats: ${validImageExtensions.join(', ')}`
      });
    }

    const imageFileName = `preview_${req.user.id}_${Date.now()}.${imageFileExtension}`;
    const imageFilePath = `./uploads/images/${imageFileName}`;

    console.log(`Downloading preview image from ${previewImageUrl} to ${imageFilePath}`);

    const imageFileResult = await fileDownloader.downloadFile(
      previewImageUrl,
      imageFilePath
    );

    // Set file URLs in request body
    req.body.fileUrl = `/uploads/models/${modelFileName}`;
    req.body.fileSize = modelFileResult.fileSize;
    req.body.fileFormat = modelFileExtension;
    req.body.imageUrl = `/uploads/images/${imageFileName}`;

    // Download preview model if provided
    if (previewModelUrl && previewModelUrlValidation.valid) {
      const previewModelExtension = fileDownloader.getFileExtension(
        previewModelUrl,
        previewModelUrlValidation.contentType
      );

      if (!validPreviewModelExtensions.includes(previewModelExtension)) {
        return res.status(400).json({
          success: false,
          error: `Invalid preview model format. Supported formats: ${validPreviewModelExtensions.join(', ')}`
        });
      }

      const previewModelFileName = `preview3d_${req.user.id}_${Date.now()}.${previewModelExtension}`;
      const previewModelFilePath = `./uploads/previews/${previewModelFileName}`;

      console.log(`Downloading preview model from ${previewModelUrl} to ${previewModelFilePath}`);

      const previewModelResult = await fileDownloader.downloadFile(
        previewModelUrl,
        previewModelFilePath
      );

      req.body.modelUrl = `/uploads/previews/${previewModelFileName}`;
    }

    // Create the model in the database
    const model = await Model.create(req.body);

    res.status(201).json({
      success: true,
      data: model
    });
  } catch (error) {
    console.error('URL upload error:', error);
    next(error);
  }
};

// @desc    Update model
// @route   PUT /api/models/:id
// @access  Private
export const updateModel = async (req, res, next) => {
  try {
    let model = await Model.findById(req.params.id);

    if (!model) {
      return res.status(404).json({
        success: false,
        error: 'Model not found'
      });
    }

    // Make sure user is model owner or admin
    if (model.createdBy.toString() !== req.user.id && req.user.role !== 'admin') {
      return res.status(401).json({
        success: false,
        error: 'Not authorized to update this model'
      });
    }

    // Update timestamp
    req.body.updatedAt = Date.now();

    model = await Model.findByIdAndUpdate(req.params.id, req.body, {
      new: true,
      runValidators: true
    });

    res.status(200).json({
      success: true,
      data: model
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Delete model
// @route   DELETE /api/models/:id
// @access  Private
export const deleteModel = async (req, res, next) => {
  try {
    const model = await Model.findById(req.params.id);

    if (!model) {
      return res.status(404).json({
        success: false,
        error: 'Model not found'
      });
    }

    // Make sure user is model owner or admin
    if (model.createdBy.toString() !== req.user.id && req.user.role !== 'admin') {
      return res.status(401).json({
        success: false,
        error: 'Not authorized to delete this model'
      });
    }

    await model.deleteOne();

    res.status(200).json({
      success: true,
      data: {}
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Download model
// @route   GET /api/models/:id/download
// @access  Private
export const downloadModel = async (req, res, next) => {
  try {
    const model = await Model.findById(req.params.id);

    if (!model) {
      return res.status(404).json({
        success: false,
        error: 'Model not found'
      });
    }

    // Get user with subscription details
    const user = await User.findById(req.user.id);

    // Check if premium model and user has access
    if (model.isPremium && req.user.role !== 'admin') {
      // Check if user has premium access based on subscription
      const hasPremiumAccess = ['premium', 'professional'].includes(user.subscription.type);

      if (!hasPremiumAccess) {
        return res.status(403).json({
          success: false,
          error: 'Premium subscription required to download this model'
        });
      }
    }

    // Check if user has enough download credits
    if (!user.hasDownloadCredits() && req.user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        error: `You've used all your download credits for this month. Please upgrade your subscription or wait until next month.`
      });
    }

    // Use a download credit (if not admin and not on professional plan)
    if (req.user.role !== 'admin') {
      user.useDownloadCredit();
      await user.save();
    }

    // Increment downloads
    model.downloads += 1;
    await model.save();

    // Add to user's download history
    await User.findByIdAndUpdate(req.user.id, {
      $push: {
        downloadHistory: {
          model: model._id,
          downloadedAt: Date.now()
        }
      }
    });

    // Return download information
    res.status(200).json({
      success: true,
      data: {
        downloadUrl: model.fileUrl,
        fileName: model.title.replace(/\s+/g, '_').toLowerCase() + '.' + model.fileFormat,
        fileSize: model.fileSize,
        remainingCredits: user.downloadCredits
      }
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Create new model with enhanced image support
// @route   POST /api/models/enhanced
// @access  Private (Admin)
export const createEnhancedModel = async (req, res, next) => {
  try {
    // Add user to req.body
    req.body.createdBy = req.user.id;

    // Create model first without images
    const modelData = { ...req.body };
    delete modelData.previewImages;

    const model = await Model.create(modelData);

    // Handle multiple preview images if provided
    if (req.files && req.files.previewImages) {
      const files = Array.isArray(req.files.previewImages)
        ? req.files.previewImages
        : [req.files.previewImages];

      const result = await imageUploadService.processMultipleImages(files, model._id);

      if (result.success && result.data.length > 0) {
        // Format images for database
        const previewImages = result.data.map((imageData, index) =>
          imageUploadService.formatImageForDatabase(imageData, index === 0)
        );

        // Update model with preview images
        model.previewImages = previewImages;

        // Set main image if not already set
        if (!model.imageUrl && previewImages.length > 0) {
          model.imageUrl = previewImages[0].url;
        }

        await model.save();
      }
    }

    res.status(201).json({
      success: true,
      data: model
    });

  } catch (error) {
    console.error('Enhanced model creation error:', error);
    next(error);
  }
};