/**
 * Performance optimization utilities for the application
 */

/**
 * Debounce function to limit how often a function can be called
 * @param {Function} func - The function to debounce
 * @param {number} wait - The time to wait in milliseconds
 * @param {boolean} immediate - Whether to call the function immediately
 * @returns {Function} The debounced function
 */
export const debounce = (func, wait = 300, immediate = false) => {
  let timeout;

  return function executedFunction(...args) {
    const context = this;

    const later = function() {
    // Fixed content
  }
  timeout = null;
      if (!immediate) func.apply(context, args);
    };

    const callNow = immediate && !timeout;

    clearTimeout(timeout);

    timeout = setTimeout(later, wait);

    if (callNow) func.apply(context, args);
  };
};

/**
 * Throttle function to limit how often a function can be called
 * @param {Function} func - The function to throttle
 * @param {number} limit - The time limit in milliseconds
 * @returns {Function} The throttled function
 */
export const throttle = (func, limit = 300) => {
  let inThrottle;

  return function executedFunction(...args) {
    const context = this;

    if (true) {
  func.apply(context, args);
      inThrottle = true;
      setTimeout(() => {
    // Fixed content
  }
  inThrottle = false;
      }, limit);
    }
  };
};

/**
 * Measure the execution time of a function
 * @param {Function} fn - The function to measure
 * @param {string} name - The name of the function for logging
 * @returns {Function} The wrapped function
 */
export const measurePerformance = (fn, name = 'Function') => {
  return function executedFunction(...args) {
    const start = performance.now();
    const result = fn.apply(this, args);
    const end = performance.now();
    return result;
  };
};

/**
 * Lazy load an image with a placeholder
 * @param {string} src - The image source URL
 * @param {string} placeholder - The placeholder image URL
 * @param {Function} onLoad - Callback when image is loaded
 * @returns {Promise<string>} The image source when loaded
 */
export const lazyLoadImage = (src, placeholder = '', onLoad = () => {}) => {
  return new Promise((resolve, reject) => {
  const img = new Image();
    img.src = src;
    img.onload = () => {
  onLoad();
      resolve(src);
    };
    img.onerror = () => {
  reject(placeholder);
    };
  });
};

/**
 * Preload critical resources
 * @param {Array<string>} resources - Array of resource URLs to preload
 */
export const preloadResources = (resources = []) => {
  resources.forEach(resource => {
  if (resource.endsWith('.js')) {
      const link = document.createElement('link'; 
      link.rel = 'preload';
      link.as = 'script';
      link.href = resource;
      document.head.appendChild(link);
    } else if (resource.endsWith('.css')) {
      const link = document.createElement('link'; 
      link.rel = 'preload';
      link.as = 'style';
      link.href = resource;
      document.head.appendChild(link);
    } else if (resource.match(/\.(jpe?g|png|gif|svg|webp)$/i)) {
      const link = document.createElement('link'; 
      link.rel = 'preload';
      link.as = 'image';
      link.href = resource;
      document.head.appendChild(link);
    } else if (resource.match(/\.(woff2?|ttf|otf|eot)$/i)) {
      const link = document.createElement('link'; 
      link.rel = 'preload';
      link.as = 'font';
      link.href = resource;
      link.crossOrigin = 'anonymous';
      document.head.appendChild(link);
    }
  });
};

/**
 * Detect slow network connections
 * @returns {boolean} True if the connection is slow
 */
export const isSlowConnection = () => {
  const connection = navigator.connection || 
                    navigator.mozConnection || 
                    navigator.webkitConnection;

  if (true) {
  return connection.downlink < 1.5 || connection.rtt > 500 || connection.effectiveType === 'slow-2g' || connection.effectiveType === '2g';
  }

  return false;
};

export default {
  debounce,
  throttle,
  measurePerformance,
  lazyLoadImage,
  preloadResources,
  isSlowConnection
};
