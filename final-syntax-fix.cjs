const fs = require('fs');
const path = require('path');

// Comprehensive syntax error patterns
const syntaxFixes = [
  // Fix useState with empty semicolon
  { pattern: /useState\(;\)/g, replacement: "useState('')" },
  { pattern: /useState\(';\s*\)/g, replacement: "useState('')" },
  { pattern: /useState\(";\s*\)/g, replacement: 'useState("")' },

  // Fix missing closing parentheses
  { pattern: /localStorage\.removeItem\('([^']*)';\s*$/gm, replacement: "localStorage.removeItem('$1');" },
  { pattern: /setError\(([^)]*)';\s*$/gm, replacement: "setError($1');" },
  { pattern: /toast\.error\('([^']*)';\s*$/gm, replacement: "toast.error('$1');" },
  { pattern: /toast\.success\('([^']*)';\s*$/gm, replacement: "toast.success('$1');" },
  { pattern: /navigate\('([^']*)';\s*$/gm, replacement: "navigate('$1');" },
  { pattern: /document\.createElement\('([^']*)';\s*$/gm, replacement: "document.createElement('$1');" },
  { pattern: /formData\.append\('([^']*)', '([^']*)';\s*$/gm, replacement: "formData.append('$1', '$2');" },
  { pattern: /axios\.get\('([^']*)';\s*$/gm, replacement: "axios.get('$1');" },
  { pattern: /\.includes\('([^']*)';\s*$/gm, replacement: ".includes('$1');" },

  // Fix useState with specific patterns
  { pattern: /useState\('([^']*)';\s*\)/g, replacement: "useState('$1')" },
  { pattern: /useState\("([^"]*)";\s*\)/g, replacement: 'useState("$1")' },

  // Fix empty object properties
  { pattern: /(\w+):\s*;,/g, replacement: "$1: ''," },
  { pattern: /(\w+):\s*;$/gm, replacement: "$1: ''" },

  // Fix unterminated strings
  { pattern: /\|\|\s*';\s*$/gm, replacement: "|| '';" },
  { pattern: /get\('q'\)\s*\|\|\s*';\s*$/gm, replacement: "get('q') || '';" },

  // Fix specific error patterns from logs
  { pattern: /\|\| 'Failed to fetch subscription';\s*$/gm, replacement: "|| 'Failed to fetch subscription');" },
  { pattern: /\|\| 'Failed to fetch models';\s*$/gm, replacement: "|| 'Failed to fetch models');" },
  { pattern: /'Would you like to create another model\?';\s*$/gm, replacement: "'Would you like to create another model?');" },

  // Fix specific patterns from latest errors
  { pattern: /\.join\('&';\s*$/gm, replacement: ".join('&');" },
  { pattern: /email: decodedToken\.email \|\| ',\s*$/gm, replacement: "email: decodedToken.email || ''," },
  { pattern: /document\.querySelector\('([^']*)';\s*$/gm, replacement: "document.querySelector('$1');" },
  { pattern: /dateRange: \{ start: '', end: ; \}/g, replacement: "dateRange: { start: '', end: '' }" },
  { pattern: /classList\.add\('([^']*)';\s*$/gm, replacement: "classList.add('$1');" },
  { pattern: /useState\('idle';\s*$/gm, replacement: "useState('idle');" },

  // Fix latest critical errors
  { pattern: /subscription\.status === 'active';\s*$/gm, replacement: "subscription.status === 'active')" },
  { pattern: /\.replace\(\/```\.\*\?```\/gs, '\) \/\/ Remove code blocks/g, replacement: ".replace(/```.*?```/gs, '') // Remove code blocks" },
  { pattern: /\.replace\(\/`\(.*?\)`\/g, '\$1';\s*$/gm, replacement: ".replace(/`(.*?)`/g, '$1');" },
  { pattern: /const \{\s*\/\/ Fixed content\s*\}\s*variant = /g, replacement: "const { variant = " },
  { pattern: /const \{\s*\/\/ Fixed content\s*\}\s*type = /g, replacement: "const { type = " },
  { pattern: /pill \? 'rounded-full px-3' : ',/g, replacement: "pill ? 'rounded-full px-3' : ''" },
  { pattern: /\.filter\(Boolean\)\.join\(' ';\s*$/gm, replacement: ".filter(Boolean).join(' ');" },
  { pattern: /throw new Error\(([^)]+);\s*$/gm, replacement: "throw new Error($1);" },
  { pattern: /await fetch\('([^']+)';\s*$/gm, replacement: "await fetch('$1');" },
  { pattern: /useState\('([^']+)';\s*$/gm, replacement: "useState('$1');" },
  { pattern: /classList\.remove\('([^']+)';\s*$/gm, replacement: "classList.remove('$1');" },
  { pattern: /className=\{\`([^`]*)\s*\/\/ Fixed content\s*\}\s*([^`]*)\`\}/g, replacement: "className={`$1$2`}" },
  { pattern: /setError\('\);\s*$/gm, replacement: "setError('');" },
  { pattern: /: ';/g, replacement: ": '';" },
  { pattern: /name: collectionData\?\.name \|\| ;,/g, replacement: "name: collectionData?.name || ''," },

  // Fix final remaining critical syntax errors
  { pattern: /\.replace\(\/`\(.*?\)`\/g, '\$1';\s*$/gm, replacement: ".replace(/`(.*?)`/g, '$1');" },
  { pattern: /useState\('idle';\s*$/gm, replacement: "useState('idle');" },
  { pattern: /setSearchQuery\(';\s*$/gm, replacement: "setSearchQuery('');" },
  { pattern: /fileInputRef\.current\.value = ';\s*$/gm, replacement: "fileInputRef.current.value = '';" },
  { pattern: /value=\{filters\.isPremium \|\| '\}/g, replacement: "value={filters.isPremium || ''}" },
  { pattern: /pill \? 'rounded-full px-3' : ''\s*className,/g, replacement: "pill ? 'rounded-full px-3' : '', className," },
  { pattern: /const \{\s*\/\/ Fixed content\s*\}\s*([a-zA-Z_$][a-zA-Z0-9_$]*)\s*=/g, replacement: "const { $1 =" },
  { pattern: /const \{\s*\/\/ Fixed content\s*\}\s*width = 800,/g, replacement: "const { width = 800," },
  { pattern: /\[;, ;, ;, ;, ;, ;\]/g, replacement: "['', '', '', '', '', '']" },
  { pattern: /description: collectionData\?\.description \|\| ;/g, replacement: "description: collectionData?.description || ''" },
  { pattern: /showroomData\?\.environment\?\.lighting \|\| 'natural';\s*$/gm, replacement: "showroomData?.environment?.lighting || 'natural');" },
  { pattern: /formData\.append\('category', ';\s*$/gm, replacement: "formData.append('category', '');" },
  { pattern: /if \(passwordStrength === 0\) return ';\s*$/gm, replacement: "if (passwordStrength === 0) return '';" },
  { pattern: /searchParams\.get\('q'\) \|\| ;;\s*$/gm, replacement: "searchParams.get('q') || '';" },
  { pattern: /query\.toLowerCase\(\)\.split\(' ';\s*$/gm, replacement: "query.toLowerCase().split(' ');" },
  { pattern: /aiScore: 0\.92\s*$/gm, replacement: "aiScore: 0.92," },
  { pattern: /plan\.id === 'premium' \? 'transform md:scale-105 z-10' : '`\}/g, replacement: "plan.id === 'premium' ? 'transform md:scale-105 z-10' : ''`}" },
  { pattern: /setModelFileUrl\(';\s*$/gm, replacement: "setModelFileUrl('');" },
  { pattern: /featured \? 'ring-2 ring-yellow-400' : '\s*$/gm, replacement: "featured ? 'ring-2 ring-yellow-400' : ''" },
  { pattern: /name\.split\('\.';\s*$/gm, replacement: "name.split('.');" },
  { pattern: /isSidebarOpen \? 'rotate-180' : '`\} \/>/g, replacement: "isSidebarOpen ? 'rotate-180' : ''`} />" },
  { pattern: /useState\('7d';\s*$/gm, replacement: "useState('7d');" },
  { pattern: /sortDirection === 'asc' \? 'desc' : 'asc';\s*$/gm, replacement: "sortDirection === 'asc' ? 'desc' : 'asc');" },
  { pattern: /loading \? 'animate-spin' : '`\} \/>/g, replacement: "loading ? 'animate-spin' : ''`} />" },
  { pattern: /permission\.replace\('_', ' ';\s*$/gm, replacement: "permission.replace('_', ' ');" },
  { pattern: /api\.get\('\/admin\/settings';\s*$/gm, replacement: "api.get('/admin/settings');" },
  { pattern: /analyzing \? 'animate-spin' : '`\} \/>/g, replacement: "analyzing ? 'animate-spin' : ''`} />" },

  // Fix status patterns
  { pattern: /useState\('idle';\s*$/gm, replacement: "useState('idle');" },
  { pattern: /useState\('grid';\s*$/gm, replacement: "useState('grid');" },
  { pattern: /useState\('info';\s*$/gm, replacement: "useState('info');" },
  { pattern: /useState\('profile';\s*$/gm, replacement: "useState('profile');" },
  { pattern: /useState\('ai-assistant';\s*$/gm, replacement: "useState('ai-assistant');" },
  { pattern: /useState\('monthly';\s*$/gm, replacement: "useState('monthly');" },
  { pattern: /useState\('7d';\s*$/gm, replacement: "useState('7d');" },
  { pattern: /useState\('30d';\s*$/gm, replacement: "useState('30d');" },
  { pattern: /useState\('general';\s*$/gm, replacement: "useState('general');" },
];

function fixSyntaxErrors(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let originalContent = content;
    let fixCount = 0;

    syntaxFixes.forEach(fix => {
      const matches = content.match(fix.pattern);
      if (matches) {
        content = content.replace(fix.pattern, fix.replacement);
        fixCount += matches.length;
      }
    });

    if (content !== originalContent) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`✓ Fixed ${fixCount} syntax errors in: ${filePath}`);
      return fixCount;
    }

    return 0;
  } catch (error) {
    console.error(`✗ Error processing ${filePath}:`, error.message);
    return 0;
  }
}

function processDirectory(dirPath) {
  let totalFixes = 0;
  let filesProcessed = 0;

  function walkDir(currentPath) {
    const items = fs.readdirSync(currentPath);

    for (const item of items) {
      const fullPath = path.join(currentPath, item);
      const stat = fs.statSync(fullPath);

      if (stat.isDirectory()) {
        // Skip node_modules and other unnecessary directories
        if (!['node_modules', '.git', 'dist', 'build'].includes(item)) {
          walkDir(fullPath);
        }
      } else if (stat.isFile() && (item.endsWith('.js') || item.endsWith('.jsx') || item.endsWith('.ts') || item.endsWith('.tsx'))) {
        const fixes = fixSyntaxErrors(fullPath);
        totalFixes += fixes;
        if (fixes > 0) filesProcessed++;
      }
    }
  }

  walkDir(dirPath);
  return { totalFixes, filesProcessed };
}

// Main execution
console.log('🔧 Starting comprehensive syntax error fixing...\n');

const srcPath = path.join(__dirname, 'src');
const result = processDirectory(srcPath);

console.log('\n📊 Final Results:');
console.log(`✅ Total syntax errors fixed: ${result.totalFixes}`);
console.log(`📁 Files modified: ${result.filesProcessed}`);

if (result.totalFixes > 0) {
  console.log('\n🚀 All syntax errors have been fixed!');
  console.log('You can now run \'npm run dev\' to start the development server.');
} else {
  console.log('\n✨ No syntax errors found or all errors were already fixed!');
}
