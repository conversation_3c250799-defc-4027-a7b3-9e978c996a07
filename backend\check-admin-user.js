import fetch from 'node-fetch';

const checkAdminUser = async () => {
  try {
    console.log('Checking admin user in database...');

    // Get all users to see if admin exists
    const response = await fetch('http://localhost:5002/api/users');
    
    if (response.status === 401) {
      console.log('Users endpoint requires authentication, trying direct database check...');
      
      // Try to get models to see if database is working
      const modelsResponse = await fetch('http://localhost:5002/api/models?limit=1');
      const modelsData = await modelsResponse.json();
      
      if (modelsData.success) {
        console.log('Database is working - models found:', modelsData.count);
        console.log('Admin user should exist, but login is failing...');
        
        // Let's try to create a new admin user via registration
        console.log('Trying to register a new admin user...');
        
        const registerResponse = await fetch('http://localhost:5002/api/auth/register', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            name: 'Test Admin',
            email: '<EMAIL>',
            password: 'password123'
          })
        });
        
        const registerData = await registerResponse.text();
        console.log('Registration response status:', registerResponse.status);
        console.log('Registration response:', registerData);
        
        if (registerResponse.ok) {
          const jsonData = JSON.parse(registerData);
          console.log('New user registered successfully!');
          console.log('Token:', jsonData.token ? 'Present' : 'Missing');
          console.log('User:', jsonData.user);
          
          // Now try to login with the new user
          console.log('Testing login with new user...');
          
          const loginResponse = await fetch('http://localhost:5002/api/auth/login', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              email: '<EMAIL>',
              password: 'password123'
            })
          });
          
          const loginData = await loginResponse.text();
          console.log('Login response status:', loginResponse.status);
          console.log('Login response:', loginData);
          
          if (loginResponse.ok) {
            console.log('✅ Login working! The issue was with the original admin user.');
            console.log('You can now use: <EMAIL> / password123');
          } else {
            console.log('❌ Login still failing even with new user');
          }
        }
      } else {
        console.log('Database connection issue');
      }
    } else {
      const data = await response.text();
      console.log('Users response:', data);
    }

  } catch (error) {
    console.error('Error checking admin user:', error);
  }
};

checkAdminUser();
