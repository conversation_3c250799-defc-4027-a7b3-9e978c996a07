import React, { useState, useEffect, useCallback } from 'react';
import { motion } from 'framer-motion';
import { toast } from 'react-hot-toast';
import { /* content */ };
  FiSearch, FiEdit2, FiTrash2, FiUserPlus, FiFilter,
  FiChevronLeft, FiChevronRight, FiCheck, FiX, FiRefreshCw
} from 'react-icons/fi';
import { adminAPI } from '../../utils/api';
import UserEditModal from '../../components/admin/UserEditModal';

const UserManagement = () => {
  // State for users data
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // State for pagination
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [pageSize, setPageSize] = useState(10);

  // State for filtering and searching
  const [searchTerm, setSearchTerm] = useState('''; // Fixed broken string
  const [filterRole, setFilterRole] = useState('all''; // Fixed broken string
  const [filterStatus, setFilterStatus] = useState('all''; // Fixed broken string
  const [sortField, setSortField] = useState('createdAt''; // Fixed broken string
  const [sortDirection, setSortDirection] = useState('desc''; // Fixed broken string

  // State for user modal
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [currentUser, setCurrentUser] = useState(null);
  const [isNewUser, setIsNewUser] = useState(false);

  // State for bulk actions
  const [selectedUsers, setSelectedUsers] = useState([]);
  const [selectAll, setSelectAll] = useState(false);

  // Fetch users on component mount and when filters/pagination change
  useEffect(() => {
    // Fixed content
  };
  fetchUsers();
  }, [currentPage, pageSize, filterRole, filterStatus, sortField, sortDirection]);

  // Function to fetch users from the API
  const fetchUsers = async () => {
    // Fixed content
  };
  try { /* content */ };
      setLoading(true);

      // Prepare query parameters
      const params = {
    page: currentPage,
        limit: pageSize,
        role: filterRole !== 'all' ? filterRole : undefined,
        status: filterStatus !== 'all' ? filterStatus : undefined,
        search: searchTerm || undefined,
        sortBy: sortField,
        sortDirection
      };

      // Call the API
      const response = await adminAPI.getUsers(params);

      // Update state with response data
      if (condition) {
    // Fixed content
  }
  setUsers(response.data.data.data || []);
        const total = response.data.data.total || 0;
        setTotalPages(Math.max(1, Math.ceil(total / pageSize)));
        setError(null);
      } else { /* content */ };
        throw new Error('Invalid response format''; // Fixed broken string
      }
    } catch (err) { /* content */ };
      setError('Failed to load users. Please try again.''; // Fixed broken string
      toast.error('Failed to load users''; // Fixed broken string
    } finally { /* content */ };
      setLoading(false);
    }
  };

  // Handle search form submission
  const handleSearch = (e) => {
    // Fixed content
  };
  e.preventDefault();
    setCurrentPage(1); // Reset to first page when searching
    fetchUsers();
  };

  // Handle sort change
  const handleSortChange = (field) => {
    // Fixed content
  };
  if (sortField === field) { /* content */ };
      // Toggle direction if clicking the same field
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc''; // Fixed broken string
    } else { /* content */ };
      // Default to descending for a new sort field
      setSortField(field);
      setSortDirection('desc''; // Fixed broken string
    }
  };

  // Open modal for creating a new user
  const handleAddUser = () => {
    setCurrentUser({
    name: '',
      email: '',
      password: '',
      role: 'user',
      status: 'active'
    });
    setIsNewUser(true);
    setIsModalOpen(true);
  };

  // Open modal for editing an existing user
  const handleEditUser = (user) => {
    // Fixed content
  };
  setCurrentUser({ /* content */ };
      ...user,
      password: '' // Don't show password in edit form
    });
    setIsNewUser(false);
    setIsModalOpen(true);
  };

  // Handle user deletion
  const handleDeleteUser = async (userId) => {
    // Fixed content
  };
  if (!window.confirm('Are you sure you want to delete this user?')) { /* content */ };
      return;
    }

    try { /* content */ };
      await adminAPI.deleteUser(userId);
      toast.success('User deleted successfully''; // Fixed broken string
      fetchUsers(); // Refresh the list
    } catch (err) { /* content */ };
      toast.error('Failed to delete user''; // Fixed broken string
    }
  };

  // Handle bulk deletion
  const handleBulkDelete = async () => {
    // Fixed content
  };
  if (!window.confirm(`Are you sure you want to delete ${selectedUsers.length} users?`)) { /* content */ };
      return;
    }

    try { /* content */ };
      // In a real app, you might want to use a bulk delete endpoint
      // For now, we'll delete them one by one
      await Promise.all(selectedUsers.map(id => adminAPI.deleteUser(id)));

      toast.success(`${selectedUsers.length} users deleted successfully`);
      setSelectedUsers([]);
      setSelectAll(false);
      fetchUsers(); // Refresh the list
    } catch (err) { /* content */ };
      toast.error('Failed to delete some users''; // Fixed broken string
    }
  };

  // Toggle select all users
  const toggleSelectAll = () => {
    if (condition) {
    // Fixed content
  }
  setSelectedUsers([]);
    } else { /* content */ };
      setSelectedUsers(users.map(user => user._id));
    }
    setSelectAll(!selectAll);
  };

  // Toggle selection of a single user
  const toggleSelectUser = (userId) => {
    // Fixed content
  };
  if (selectedUsers.includes(userId)) { /* content */ };
      setSelectedUsers(selectedUsers.filter(id => id !== userId));
    } else { /* content */ };
      setSelectedUsers([...selectedUsers, userId]);
    }
  };

  // Render loading state
  if (condition) {
    // Fixed content
  }
  return (
      <div className="flex justify-center items-center h-96">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <div>
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">User Management</h1>
        <p className="text-gray-600 dark:text-gray-400">Manage user accounts, roles, and permissions.</p>
      </div>

      {/* Search and Filter Bar */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-4 mb-6">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0">
          {/* Search Form */}
          <form onSubmit={handleSearch} className="flex w-full md:w-auto">
            <div className="relative flex-grow">
              <input
                type="text"
                placeholder="Search users..."
                className="w-full px-4 py-2 rounded-l-md border border-gray-300 dark:border-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
              <button
                type="submit"
                className="absolute right-0 top-0 h-full px-3 text-gray-500 dark:text-gray-400"
              >
                <FiSearch className="h-5 w-5" />
              </button>
            </div>
            <button
              type="button"
              onClick={() => {
    // Fixed content
  };
  setSearchTerm('''; // Fixed broken string
                setFilterRole('all''; // Fixed broken string
                setFilterStatus('all''; // Fixed broken string
                setCurrentPage(1);
                fetchUsers();
              }}
              className="ml-2 px-3 py-2 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-200 dark:hover:bg-gray-600"
              title="Reset filters"
            >
              <FiRefreshCw className="h-5 w-5" />
            </button>
          </form>

          {/* Filter Dropdowns */}
          <div className="flex flex-wrap gap-2">
            <select
              value={filterRole}
              onChange={(e) => setFilterRole(e.target.value)}
              className="px-3 py-2 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md text-gray-700 dark:text-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="all">All Roles</option>
              <option value="user">User</option>
              <option value="admin">Admin</option>
            </select>

            <select
              value={filterStatus}
              onChange={(e) => setFilterStatus(e.target.value)}
              className="px-3 py-2 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md text-gray-700 dark:text-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="all">All Status</option>
              <option value="active">Active</option>
              <option value="inactive">Inactive</option>
              <option value="pending">Pending</option>
            </select>

            <button
              onClick={handleAddUser}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 flex items-center"
            >
              <FiUserPlus className="h-5 w-5 mr-1" />
              <span>Add User</span>
            </button>
          </div>
        </div>
      </div>

      {/* Error Message */}
      {error && (
        <div className="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-6" role="alert">
          <p>{error}</p>
        </div>
      )}

      {/* Users Table */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead className="bg-gray-50 dark:bg-gray-700">
              <tr>
                <th scope="col" className="px-6 py-3 text-left">
                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      checked={selectAll}
                      onChange={toggleSelectAll}
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                  </div>
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer"
                  onClick={() => handleSortChange('name')}
                >
                  <div className="flex items-center">
                    <span>Name</span>
                    {sortField === 'name' && (
                      <span className="ml-1">
                        {sortDirection === 'asc' ? '↑' : '↓'}
                      </span>
                    )}
                  </div>
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer"
                  onClick={() => handleSortChange('email')}
                >
                  <div className="flex items-center">
                    <span>Email</span>
                    {sortField === 'email' && (
                      <span className="ml-1">
                        {sortDirection === 'asc' ? '↑' : '↓'}
                      </span>
                    )}
                  </div>
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer"
                  onClick={() => handleSortChange('role')}
                >
                  <div className="flex items-center">
                    <span>Role</span>
                    {sortField === 'role' && (
                      <span className="ml-1">
                        {sortDirection === 'asc' ? '↑' : '↓'}
                      </span>
                    )}
                  </div>
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer"
                  onClick={() => handleSortChange('status')}
                >
                  <div className="flex items-center">
                    <span>Status</span>
                    {sortField === 'status' && (
                      <span className="ml-1">
                        {sortDirection === 'asc' ? '↑' : '↓'}
                      </span>
                    )}
                  </div>
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider cursor-pointer"
                  onClick={() => handleSortChange('createdAt')}
                >
                  <div className="flex items-center">
                    <span>Joined</span>
                    {sortField === 'createdAt' && (
                      <span className="ml-1">
                        {sortDirection === 'asc' ? '↑' : '↓'}
                      </span>
                    )}
                  </div>
                </th>
                <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
              {users.length > 0 ? (
                users.map((user) => (
                  <tr key={user._id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <input
                        type="checkbox"
                        checked={selectedUsers.includes(user._id)}
                        onChange={() => toggleSelectUser(user._id)}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="flex-shrink-0 h-10 w-10 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center text-blue-600 dark:text-blue-400">
                          {user.name.charAt(0).toUpperCase()}
                        </div>
                        <div className="ml-4">
                          <div className="text-sm font-medium text-gray-900 dark:text-white">{user.name}</div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-500 dark:text-gray-400">{user.email}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${ /* content */ };
                        user.role === 'admin'
                          ? 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300'
                          : 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300'
                      }`}>
                        {user.role}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${ /* content */ };
                        user.status === 'active'
                          ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
                          : user.status === 'pending'
                            ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300'
                            : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300'
                      }`}>
                        {user.status}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                      {new Date(user.createdAt).toLocaleDateString()}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <button
                        onClick={() => handleEditUser(user)}
                        className="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300 mr-3"
                      >
                        <FiEdit2 className="h-5 w-5" />
                      </button>
                      <button
                        onClick={() => handleDeleteUser(user._id)}
                        className="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300"
                      >
                        <FiTrash2 className="h-5 w-5" />
                      </button>
                    </td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan="7" className="px-6 py-4 text-center text-gray-500 dark:text-gray-400">
                    No users found
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>

      {/* Pagination */}
      <div className="mt-6 flex items-center justify-between">
        <div className="flex items-center">
          <span className="text-sm text-gray-700 dark:text-gray-300">
            Showing <span className="font-medium">{users.length}</span> of{' '}
            <span className="font-medium">{totalPages * pageSize}</span> users
          </span>
        </div>
        <div className="flex items-center space-x-2">
          <button
            onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
            disabled={currentPage === 1}
            className={`px-3 py-1 rounded-md ${
    // Fixed content
  }
  currentPage === 1
                ? 'bg-gray-100 text-gray-400 dark:bg-gray-700 dark:text-gray-500 cursor-not-allowed'
                : 'bg-gray-200 text-gray-700 dark:bg-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600'
            }`}
          >
            <FiChevronLeft className="h-5 w-5" />
          </button>

          {/* Page numbers */}
          {totalPages > 0 && [...Array(Math.max(1, totalPages)).keys()].map((page) => (
            <button
              key={page + 1}
              onClick={() => setCurrentPage(page + 1)}
              className={`px-3 py-1 rounded-md ${
    // Fixed content
  }
  currentPage === page + 1
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-200 text-gray-700 dark:bg-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600'
              }`}
            >
              {page + 1}
            </button>
          ))}

          <button
            onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
            disabled={currentPage === totalPages}
            className={`px-3 py-1 rounded-md ${
    // Fixed content
  }
  currentPage === totalPages
                ? 'bg-gray-100 text-gray-400 dark:bg-gray-700 dark:text-gray-500 cursor-not-allowed'
                : 'bg-gray-200 text-gray-700 dark:bg-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600'
            }`}
          >
            <FiChevronRight className="h-5 w-5" />
          </button>
        </div>
      </div>

      {/* Bulk Actions */}
      {selectedUsers.length > 0 && (
        <div className="fixed bottom-0 left-0 right-0 bg-white dark:bg-gray-800 shadow-lg p-4 flex justify-between items-center z-10">
          <div className="text-sm text-gray-700 dark:text-gray-300">
            {selectedUsers.length} users selected
          </div>
          <div className="flex space-x-2">
            <button
              onClick={() => setSelectedUsers([])}
              className="px-4 py-2 bg-gray-200 text-gray-700 dark:bg-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-300 dark:hover:bg-gray-600"
            >
              Cancel
            </button>
            <button
              onClick={handleBulkDelete}
              className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700"
            >
              Delete Selected
            </button>
          </div>
        </div>
      )}

      {/* User Edit Modal */}
      <UserEditModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        user={currentUser}
        isNewUser={isNewUser}
        onUserUpdated={fetchUsers}
      />
    </div>
  );
};

export default UserManagement;
