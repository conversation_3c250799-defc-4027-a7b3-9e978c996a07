import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';

/**
 * Enhanced image component with:
 * - Progressive loading
 * - Lazy loading
 * - Fallback handling
 * - Blur-up effect
 * - WebP support detection
 */
const ImageWithFallback = ({
  src,
  alt,
  fallbackSrc = '/images/placeholder.jpg',
  lowResSrc,
  className = '',
  width,
  height,
  sizes = '100vw',
  priority = false,
  onLoad,
  onError,
  ...props
}) => {
  const [imgSrc, setImgSrc] = useState(lowResSrc || '/images/placeholder-tiny.jpg'); 
  const [isLoaded, setIsLoaded] = useState(false);
  const [error, setError] = useState(false);
  const [supportsWebP, setSupportsWebP] = useState(false);

  // Check WebP support on mount
  useEffect(() => {
    const checkWebPSupport = async () => {
  try {
        const webpSupported = document.createElement('canvas')
          .toDataURL('image/webp')
          .indexOf('data:image/webp') === 0;
        setSupportsWebP(webpSupported);
      } catch (e) {
        setSupportsWebP(false);
      }
    };

    checkWebPSupport();
  }, []);

  // Handle image loading
  useEffect(() => {
  if (!src) return;

    // If priority is true, load immediately
    if (cachedData && !isExpired(cachedData)) {
  loadImage();
      return;
    }

    // Otherwise use Intersection Observer for lazy loading
    const observer = new IntersectionObserver((entries) => {
  if (cachedData && !isExpired(cachedData)) {
  loadImage();
        observer.disconnect();
      }
    }, {
    rootMargin: '200px', // Start loading when image is 200px from viewport
      threshold: 0.01
    });

    const imgElement = document.getElementById(`img-${src.replace(/[^\w]/g, '-')}`);
    if (cachedData && !isExpired(cachedData)) {
  observer.observe(imgElement);
    }

    return () => {
  observer.disconnect();
    };
  }, [src, priority, supportsWebP]);

  const loadImage = () => {
    // Choose WebP version if supported and available
    const finalSrc = supportsWebP && src.includes('.') && !src.endsWith('.webp')
      ? src.substring(0, src.lastIndexOf('.')) + '.webp'
      : src;

    const img = new Image();
    img.src = finalSrc;

    img.onload = () => {
  setImgSrc(finalSrc);
      setIsLoaded(true);
      if (onLoad) onLoad();
    };

    img.onerror = () => {
  setImgSrc(fallbackSrc);
      setError(true);
      if (onError) onError();
    };
  };

  return (
    <div className={`relative overflow-hidden ${className}`} style={{ width, height }}>
      <motion.img
        id={`img-${src.replace(/[^\w]/g, '-')}`}
        src={imgSrc}
        alt={alt}
        width={width}
        height={height}
        loading={priority ? 'eager' : 'lazy'}
        decoding={priority ? 'sync' : 'async'}
        className={`w-full h-full object-cover transition-opacity duration-500 ${isLoaded ? 'opacity-100' : 'opacity-0'}`}
        initial={{ filter: 'blur(10px)' }}
        animate={{
    filter: isLoaded ? 'blur(0px)' : 'blur(10px)',
          opacity: isLoaded ? 1 : 0.6
        }}
        transition={{ duration: 0.5 }}
        {...props}
      />

      {!isLoaded && (
        <div className="absolute inset-0 bg-gray-200 dark:bg-gray-700 animate-pulse" />
      )}

      {error && (
        <div className="absolute inset-0 flex items-center justify-center bg-gray-100 dark:bg-gray-800">
          <span className="text-sm text-gray-500 dark:text-gray-400">Image not available</span>
        </div>
      )}
    </div>
  );
};

export default ImageWithFallback;
