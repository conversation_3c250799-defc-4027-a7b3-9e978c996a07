import mongoose from 'mongoose';
import dotenv from 'dotenv';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

// Load environment variables
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
dotenv.config({ path: join(__dirname, '../../.env') }); // Go up two levels to root .env

// Import models
import User from '../src/models/User.js';
import Model from '../src/models/Model.js';
import Category from '../src/models/Category.js';

// Connect to MongoDB
const connectDB = async () => {
  try {
    console.log('Connecting to MongoDB...');
    console.log('URI:', process.env.MONGODB_URI ? 'Found' : 'Not found');
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('MongoDB Connected for seeding...');
  } catch (error) {
    console.error('Error connecting to MongoDB:', error);
    process.exit(1);
  }
};

// Sample categories
const categories = [
  {
    name: 'Interior Design',
    description: 'Interior design models and furniture',
    slug: 'interior-design',
    icon: 'home',
    subcategories: ['Living Room', 'Bedroom', 'Kitchen', 'Bathroom', 'Office']
  },
  {
    name: 'Architecture',
    description: 'Architectural models and buildings',
    slug: 'architecture',
    icon: 'building',
    subcategories: ['Residential', 'Commercial', 'Industrial', 'Public Buildings']
  },
  {
    name: 'Furniture',
    description: 'Furniture models and accessories',
    slug: 'furniture',
    icon: 'chair',
    subcategories: ['Chairs', 'Tables', 'Sofas', 'Storage', 'Lighting']
  },
  {
    name: 'Landscape',
    description: 'Landscape and outdoor models',
    slug: 'landscape',
    icon: 'tree',
    subcategories: ['Gardens', 'Parks', 'Outdoor Furniture', 'Plants', 'Water Features']
  }
];

// Sample users
const users = [
  {
    name: 'Admin User',
    email: '<EMAIL>',
    password: 'admin123', // Will be hashed by pre-save middleware
    role: 'admin',
    bio: 'System Administrator',
    profileImage: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150'
  },
  {
    name: 'John Designer',
    email: '<EMAIL>',
    password: 'designer123', // Will be hashed by pre-save middleware
    role: 'user',
    bio: 'Professional 3D Designer',
    profileImage: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150'
  }
];

// Sample models
const models = [
  {
    title: 'Modern Living Room Set',
    description: 'Complete modern living room furniture set with sofa, coffee table, and accessories',
    category: 'Residential',
    subcategory: 'Living Room',
    tags: ['modern', 'living room', 'furniture', 'interior'],
    fileUrl: 'https://3dsketchup.net/models/modern-living-room.skp',
    thumbnailUrl: 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=400',
    previewImages: [
      'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=800',
      'https://images.unsplash.com/photo-1567538096630-e0c55bd6374c?w=800'
    ],
    fileSize: 15.2,
    downloadCount: 1250,
    views: 3500,
    likes: 89,
    isPremium: false,
    isPublic: true,
    uploadedBy: null, // Will be set to user ID
    specifications: {
      polygons: 45000,
      vertices: 23000,
      materials: 12,
      textures: 8
    }
  },
  {
    title: 'Luxury Bedroom Interior',
    description: 'Elegant luxury bedroom with premium furniture and lighting',
    category: 'Residential',
    subcategory: 'Bedroom',
    tags: ['luxury', 'bedroom', 'interior', 'premium'],
    fileUrl: 'https://3dsketchup.net/models/luxury-bedroom.skp',
    thumbnailUrl: 'https://images.unsplash.com/photo-1560448204-e02f11c3d0e2?w=400',
    previewImages: [
      'https://images.unsplash.com/photo-1560448204-e02f11c3d0e2?w=800',
      'https://images.unsplash.com/photo-1560448075-bb485b067938?w=800'
    ],
    fileSize: 22.8,
    downloadCount: 890,
    views: 2100,
    likes: 156,
    isPremium: true,
    isPublic: true,
    uploadedBy: null,
    specifications: {
      polygons: 67000,
      vertices: 34000,
      materials: 18,
      textures: 15
    }
  }
];

// Seed function
const seedData = async () => {
  try {
    console.log('Starting data seeding...');

    // Clear existing data
    await User.deleteMany({});
    await Model.deleteMany({});
    await Category.deleteMany({});
    console.log('Cleared existing data');

    // Create categories
    const createdCategories = await Category.insertMany(categories);
    console.log(`Created ${createdCategories.length} categories`);

    // Create users
    const createdUsers = await User.insertMany(users);
    console.log(`Created ${createdUsers.length} users`);

    // Update models with user IDs and required fields
    models[0].createdBy = createdUsers[1]._id; // designer1
    models[0].imageUrl = models[0].thumbnailUrl;
    models[0].format = 'Sketchup 2023';
    models[0].fileFormat = 'skp';
    models[0].fileSize = Math.floor(models[0].fileSize * 1024 * 1024); // Convert MB to bytes
    models[0].downloads = models[0].downloadCount;

    models[1].createdBy = createdUsers[1]._id; // designer1
    models[1].imageUrl = models[1].thumbnailUrl;
    models[1].format = 'Sketchup 2023';
    models[1].fileFormat = 'skp';
    models[1].fileSize = Math.floor(models[1].fileSize * 1024 * 1024); // Convert MB to bytes
    models[1].downloads = models[1].downloadCount;

    // Create models
    const createdModels = await Model.insertMany(models);
    console.log(`Created ${createdModels.length} models`);

    console.log('✅ Data seeding completed successfully!');
    console.log('\nSample login credentials:');
    console.log('Admin: <EMAIL> / admin123');
    console.log('User: <EMAIL> / designer123');

  } catch (error) {
    console.error('❌ Error seeding data:', error);
  } finally {
    mongoose.connection.close();
  }
};

// Run seeding
connectDB().then(() => {
  seedData();
});
