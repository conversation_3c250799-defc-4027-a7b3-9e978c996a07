import React, { useState, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  FiFilter, FiX, FiCalendar, FiStar, FiDownload, 
  FiTag, FiUser, FiFileText, FiSliders, FiSearch 
} from 'react-icons/fi';

const AdvancedSearchFilters = ({ onApplyFilters, onClose, initialFilters = {} }) => {
  const [filters, setFilters] = useState({
    dateRange: { start: '', end: '' },
    rating: { min: 0, max: 5 },
    downloads: { min: 0, max: 10000 },
    fileSize: { min: 0, max: 100 }, // MB
    tags: [],
    author: '',
    license: '',
    format: [],
    category: [],
    subcategory: [],
    isPremium: null,
    hasTextures: null,
    isAnimated: null,
    polygonCount: { min: 0, max: 100000 },
    ...initialFilters
  });

  const [activeTab, setActiveTab] = useState('general');
  const categories = [
    'Architecture', 'Interior', 'Furniture', 'Vehicles', 
    'Characters', 'Nature', 'Electronics', 'Sports'
  ];

  const formats = [
    'SKP', 'FBX', 'OBJ', 'DAE', '3DS', 'MAX', 'BLEND', 'C4D'
  ];

  const licenses = [
    'Free for Commercial Use',
    'Personal Use Only', 
    'Creative Commons',
    'Royalty Free',
    'Extended License'
  ];

  const updateFilter = (key, value) => {
  setFilters(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const updateNestedFilter = (parentKey, childKey, value) => {
  setFilters(prev => ({
      ...prev,
      [parentKey]: {
        ...prev[parentKey],
        [childKey]: value
      }
    }));
  };

  const resetFilters = () => {
    setFilters({
    dateRange: { start: '', end: '' },
      rating: { min: 0, max: 5 },
      downloads: { min: 0, max: 10000 },
      fileSize: { min: 0, max: 100 },
      tags: [],
      author: '',
      license: '',
      format: [],
      category: [],
      subcategory: [],
      isPremium: null,
      hasTextures: null,
      isAnimated: null,
      polygonCount: { min: 0, max: 100000 }
    });
  };

  const handleApply = () => {
    onApplyFilters(filters);
    onClose();
  };

  const tabs = [
    { id: 'general', label: 'Tổng Quan', icon: FiFilter },
    { id: 'technical', label: 'Kỹ Thuật', icon: FiSliders },
    { id: 'metadata', label: 'Thông Tin', icon: FiFileText }
  ];

  const RangeSlider = ({ label, min, max, value, onChange, unit = '', step = 1 }) => (
    <div className="space-y-3">
      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
        {label}: {value.min}{unit} - {value.max}{unit}
      </label>
      <div className="space-y-2">
        <input
          type="range"
          min={min}
          max={max}
          step={step}
          value={value.min}
          onChange={(e) => onChange({ ...value, min: Number(e.target.value) })}
          className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700"
        />
        <input
          type="range"
          min={min}
          max={max}
          step={step}
          value={value.max}
          onChange={(e) => onChange({ ...value, max: Number(e.target.value) })}
          className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700"
        />
      </div>
    </div>
  );

  const CheckboxGroup = ({ label, options, value, onChange }) => (
    <div className="space-y-3">
      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
        {label}
      </label>
      <div className="grid grid-cols-2 gap-2">
        {options.map((option) => (
          <label key={option} className="flex items-center space-x-2 cursor-pointer">
            <input
              type="checkbox"
              checked={value.includes(option)}
              onChange={(e) => {
  if (true) {
  onChange([...value, option]);
                } else {
                  onChange(value.filter(v => v !== option));
                }
              }}
              className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
            <span className="text-sm text-gray-700 dark:text-gray-300">{option}</span>
          </label>
        ))}
      </div>
    </div>
  );

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 bg-black/60 backdrop-blur-sm z-50 flex items-center justify-center p-4"
        onClick={onClose}
      >
        <motion.div
          initial={{ opacity: 0, scale: 0.9, y: 20 }}
          animate={{ opacity: 1, scale: 1, y: 0 }}
          exit={{ opacity: 0, scale: 0.9, y: 20 }}
          className="glass-card rounded-3xl border border-white/20 shadow-professional-lg max-w-4xl w-full max-h-[90vh] overflow-hidden"
          onClick={(e) => e.stopPropagation()}
        >
          {/* Header */}
          <div className="p-6 border-b border-white/10">
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center mr-4">
                  <FiSearch className="h-6 w-6 text-white" />
                </div>
                <div>
                  <h2 className="text-2xl font-black text-gray-900 dark:text-white">
                    Bộ Lọc Nâng Cao
                  </h2>
                  <p className="text-gray-600 dark:text-gray-400">
                    Tùy chỉnh tìm kiếm theo nhu cầu của bạn
                  </p>
                </div>
              </div>
              <button
                onClick={onClose}
                className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-xl transition-colors"
              >
                <FiX className="h-6 w-6 text-gray-500" />
              </button>
            </div>
          </div>

          {/* Tabs */}
          <div className="border-b border-white/10">
            <div className="flex">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`flex-1 flex items-center justify-center px-6 py-4 text-sm font-medium transition-all duration-200 ${
    activeTab === tab.id
                      ? 'bg-gradient-to-r from-blue-600 to-purple-600 text-white'
                      : 'text-gray-600 dark:text-gray-400 hover:bg-white/10'
                  }`}
                >
                  <tab.icon className="h-4 w-4 mr-2" />
                  {tab.label}
                </button>
              ))}
            </div>
          </div>

          {/* Content */}
          <div className="p-6 max-h-96 overflow-y-auto">
            {activeTab === 'general' && (
              <div className="space-y-6">
                <CheckboxGroup
                  label="Danh Mục"
                  options={categories}
                  value={filters.category}
                  onChange={(value) => updateFilter('category', value)}
                />

                <CheckboxGroup
                  label="Định Dạng"
                  options={formats}
                  value={filters.format}
                  onChange={(value) => updateFilter('format', value)}
                />

                <RangeSlider
                  label="Đánh Giá"
                  min={0}
                  max={5}
                  step={0.1}
                  value={filters.rating}
                  onChange={(value) => updateFilter('rating', value)}
                  unit="⭐"
                />

                <div className="grid grid-cols-3 gap-4">
                  <label className="flex flex-col space-y-2">
                    <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Loại</span>
                    <select
                      value={filters.isPremium || '}
                      onChange={(e) => updateFilter('isPremium', e.target.value === ' ? null : e.target.value === 'true')}
                      className="glass-card border border-white/10 rounded-xl px-3 py-2 text-sm"
                    >
                      <option value="">Tất cả</option>
                      <option value="false">Miễn phí</option>
                      <option value="true">Premium</option>
                    </select>
                  </label>

                  <label className="flex flex-col space-y-2">
                    <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Có Texture</span>
                    <select
                      value={filters.hasTextures || '}
                      onChange={(e) => updateFilter('hasTextures', e.target.value === ' ? null : e.target.value === 'true')}
                      className="glass-card border border-white/10 rounded-xl px-3 py-2 text-sm"
                    >
                      <option value="">Tất cả</option>
                      <option value="true">Có</option>
                      <option value="false">Không</option>
                    </select>
                  </label>

                  <label className="flex flex-col space-y-2">
                    <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Animation</span>
                    <select
                      value={filters.isAnimated || '}
                      onChange={(e) => updateFilter('isAnimated', e.target.value === ' ? null : e.target.value === 'true')}
                      className="glass-card border border-white/10 rounded-xl px-3 py-2 text-sm"
                    >
                      <option value="">Tất cả</option>
                      <option value="true">Có</option>
                      <option value="false">Không</option>
                    </select>
                  </label>
                </div>
              </div>
            )}

            {activeTab === 'technical' && (
              <div className="space-y-6">
                <RangeSlider
                  label="Kích Thước File"
                  min={0}
                  max={100}
                  value={filters.fileSize}
                  onChange={(value) => updateFilter('fileSize', value)}
                  unit="MB"
                />

                <RangeSlider
                  label="Số Polygon"
                  min={0}
                  max={100000}
                  step={1000}
                  value={filters.polygonCount}
                  onChange={(value) => updateFilter('polygonCount', value)}
                  unit=""
                />

                <RangeSlider
                  label="Lượt Tải"
                  min={0}
                  max={10000}
                  step={100}
                  value={filters.downloads}
                  onChange={(value) => updateFilter('downloads', value)}
                  unit=""
                />
              </div>
            )}

            {activeTab === 'metadata' && (
              <div className="space-y-6">
                <div className="grid grid-cols-2 gap-4">
                  <label className="flex flex-col space-y-2">
                    <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Tác Giả</span>
                    <input
                      type="text"
                      value={filters.author}
                      onChange={(e) => updateFilter('author', e.target.value)}
                      className="glass-card border border-white/10 rounded-xl px-3 py-2 text-sm"
                      placeholder="Tên tác giả..."
                    />
                  </label>

                  <label className="flex flex-col space-y-2">
                    <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Giấy Phép</span>
                    <select
                      value={filters.license}
                      onChange={(e) => updateFilter('license', e.target.value)}
                      className="glass-card border border-white/10 rounded-xl px-3 py-2 text-sm"
                    >
                      <option value="">Tất cả</option>
                      {licenses.map(license => (
                        <option key={license} value={license}>{license}</option>
                      ))}
                    </select>
                  </label>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <label className="flex flex-col space-y-2">
                    <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Từ Ngày</span>
                    <input
                      type="date"
                      value={filters.dateRange.start}
                      onChange={(e) => updateNestedFilter('dateRange', 'start', e.target.value)}
                      className="glass-card border border-white/10 rounded-xl px-3 py-2 text-sm"
                    />
                  </label>

                  <label className="flex flex-col space-y-2">
                    <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Đến Ngày</span>
                    <input
                      type="date"
                      value={filters.dateRange.end}
                      onChange={(e) => updateNestedFilter('dateRange', 'end', e.target.value)}
                      className="glass-card border border-white/10 rounded-xl px-3 py-2 text-sm"
                    />
                  </label>
                </div>
              </div>
            )}
          </div>

          {/* Footer */}
          <div className="p-6 border-t border-white/10 bg-gradient-to-r from-blue-50/50 to-purple-50/50 dark:from-blue-900/10 dark:to-purple-900/10">
            <div className="flex items-center justify-between">
              <button
                onClick={resetFilters}
                className="px-6 py-2 glass-card border border-white/10 text-gray-700 dark:text-gray-300 rounded-xl font-medium hover:bg-white/20 transition-all duration-300"
              >
                Reset Tất Cả
              </button>
              <div className="flex space-x-3">
                <button
                  onClick={onClose}
                  className="px-6 py-2 glass-card border border-white/10 text-gray-700 dark:text-gray-300 rounded-xl font-medium hover:bg-white/20 transition-all duration-300"
                >
                  Hủy
                </button>
                <button
                  onClick={handleApply}
                  className="px-6 py-2 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-xl font-medium hover:shadow-lg transition-all duration-300"
                >
                  Áp Dụng Bộ Lọc
                </button>
              </div>
            </div>
          </div>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
};

export default AdvancedSearchFilters;
