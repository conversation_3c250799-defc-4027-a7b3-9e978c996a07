// Import React directly to ensure Component is available
import React, { Component } from 'react';
import { Fi<PERSON>lertTriangle, FiRefreshCw, FiHome, FiExternalLink } from 'react-icons/fi';
import toast from 'react-hot-toast';

class ErrorBoundary extends Component {
  constructor(props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      errorCount: 0,
      lastErrorTime: null
    };
  }

  static getDerivedStateFromError(error) {
    // Update state so the next render will show the fallback UI
    return { hasError: true, error };
  }

  componentDidCatch(error, errorInfo) {
    // Log the error to an error reporting service
    console.error('Error caught by ErrorBoundary:', error, errorInfo);

    // Update error state with additional information
    this.setState(prevState => ({
      errorInfo,
      errorCount: prevState.errorCount + 1,
      lastErrorTime: new Date().toISOString()
    }));

    // Show toast notification
    toast.error('An error occurred in the application. The error has been logged.');

    // You could also log to an error tracking service here
    // Example: Sentry.captureException(error);

    // Log additional browser information for debugging
    console.log('Browser Information:', {
      userAgent: navigator.userAgent,
      language: navigator.language,
      platform: navigator.platform,
      screenWidth: window.screen.width,
      screenHeight: window.screen.height,
      viewportWidth: window.innerWidth,
      viewportHeight: window.innerHeight
    });
  }

  handleReset = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null
    });
  };

  render() {
    const { hasError, error, errorInfo, errorCount, lastErrorTime } = this.state;
    const { fallback, children } = this.props;

    if (hasError) {
      // You can render any custom fallback UI
      if (fallback) {
        return fallback(error, errorInfo, this.handleReset);
      }

      return (
        <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-50 via-red-50 to-orange-50 dark:from-gray-900 dark:via-red-900 dark:to-orange-900 p-4">
          {/* Animated Background */}
          <div className="absolute inset-0 opacity-20">
            <div className="absolute top-10 left-10 w-32 h-32 bg-red-400 rounded-full blur-2xl animate-float"></div>
            <div className="absolute bottom-10 right-10 w-24 h-24 bg-orange-400 rounded-full blur-xl animate-float" style={{animationDelay: '2s'}}></div>
            <div className="absolute top-1/2 left-1/3 w-20 h-20 bg-yellow-400 rounded-full blur-lg animate-float" style={{animationDelay: '4s'}}></div>
          </div>

          <div className="max-w-2xl w-full glass-card rounded-3xl shadow-professional-lg p-8 text-center border border-white/20 relative z-10">
            {/* Error Icon */}
            <div className="flex justify-center mb-6">
              <div className="w-24 h-24 bg-gradient-to-r from-red-500 to-orange-600 rounded-full flex items-center justify-center shadow-xl animate-pulse">
                <FiAlertTriangle className="h-12 w-12 text-white" />
              </div>
            </div>

            {/* Error Title */}
            <h2 className="text-4xl font-black text-gray-800 dark:text-white mb-4 bg-gradient-to-r from-red-600 to-orange-600 bg-clip-text text-transparent">
              Oops! Có lỗi xảy ra
            </h2>

            {/* Error Description */}
            <p className="text-lg text-gray-600 dark:text-gray-300 mb-8 leading-relaxed">
              Chúng tôi xin lỗi, đã có lỗi xảy ra khi hiển thị component này.
              {errorCount > 1 && (
                <span className="block mt-2 text-red-600 dark:text-red-400 font-semibold">
                  Lỗi này đã xảy ra {errorCount} lần.
                </span>
              )}
            </p>

            {process.env.NODE_ENV !== 'production' && error && (
              <div className="mb-6 text-left">
                <div className="flex justify-between items-center">
                  <p className="text-red-600 dark:text-red-400 font-medium mb-2">Error:</p>
                  {lastErrorTime && (
                    <span className="text-xs text-gray-500 dark:text-gray-400">
                      {new Date(lastErrorTime).toLocaleTimeString()}
                    </span>
                  )}
                </div>
                <pre className="bg-gray-100 dark:bg-gray-700 p-3 rounded text-sm overflow-auto text-left max-h-40">
                  {error.toString()}
                </pre>

                {errorInfo && (
                  <div className="mt-4">
                    <p className="text-red-600 dark:text-red-400 font-medium mb-2">Component Stack:</p>
                    <pre className="bg-gray-100 dark:bg-gray-700 p-3 rounded text-sm overflow-auto text-left max-h-40">
                      {errorInfo.componentStack}
                    </pre>
                  </div>
                )}

                <div className="mt-4 p-3 bg-blue-50 dark:bg-blue-900/20 text-blue-800 dark:text-blue-200 rounded text-sm">
                  <p className="font-medium">Troubleshooting Tips:</p>
                  <ul className="list-disc list-inside mt-1">
                    <li>Check if the backend server is running on port 5002</li>
                    <li>Clear your browser cache and reload the page</li>
                    <li>Check browser console for additional errors</li>
                    <li>Try using a different browser</li>
                  </ul>
                </div>
              </div>
            )}

            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button
                onClick={this.handleReset}
                className="inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                <FiRefreshCw className="mr-2" />
                Try Again
              </button>
              <button
                onClick={() => window.location.href = '/'}
                className="inline-flex items-center justify-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md shadow-sm text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                <FiHome className="mr-2" />
                Go to Homepage
              </button>
              <button
                onClick={() => window.location.reload()}
                className="inline-flex items-center justify-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md shadow-sm text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                <FiRefreshCw className="mr-2" />
                Reload Page
              </button>
            </div>
          </div>
        </div>
      );
    }

    // If there's no error, render children normally
    return children;
  }
}

export default ErrorBoundary;
