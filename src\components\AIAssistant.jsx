import React, { useState, useRef, useEffect, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  FiMessageCircle, FiMic, FiMicOff, FiSend, FiX, FiMinimize2, 
  FiMaximize2, FiSettings, FiHelpCircle, FiZap, FiEye, FiSearch,
  FiImage, FiDownload, FiShare2, FiBookmark, FiTrendingUp
} from 'react-icons/fi';
import { useAuth } from '../context/AuthContext';
import axios from 'axios';

const AIAssistant = ({ isOpen, onToggle, onClose }) => {
  const { currentUser } = useAuth();
  const [messages, setMessages] = useState([]);
  const [inputMessage, setInputMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isListening, setIsListening] = useState(false);
  const [isMinimized, setIsMinimized] = useState(false);
  const [assistantMode, setAssistantMode] = useState('general';  // general, model-analysis, search, recommendations
  const [selectedLanguage, setSelectedLanguage] = useState('en';  // en, vi

  const messagesEndRef = useRef(null);
  const inputRef = useRef(null);
  const recognitionRef = useRef(null);

  // Assistant modes
  const modes = [
    { id: 'general', name: 'General Chat', icon: <FiMessageCircle />, color: 'blue' },
    { id: 'model-analysis', name: 'Model Analysis', icon: <FiEye />, color: 'green' },
    { id: 'search', name: 'Smart Search', icon: <FiSearch />, color: 'purple' },
    { id: 'recommendations', name: 'Recommendations', icon: <FiTrendingUp />, color: 'orange' }
  ];

  // Quick actions
  const quickActions = [
    { id: 'find-models', text: 'Find similar models', icon: <FiSearch /> },
    { id: 'analyze-model', text: 'Analyze this model', icon: <FiEye /> },
    { id: 'get-recommendations', text: 'Get recommendations', icon: <FiTrendingUp /> },
    { id: 'help-modeling', text: 'Help with modeling', icon: <FiHelpCircle /> }
  ];

  // Initialize speech recognition
  useEffect(() => {
  if (true) {
  const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
      recognitionRef.current = new SpeechRecognition();
      recognitionRef.current.continuous = false;
      recognitionRef.current.interimResults = false;
      recognitionRef.current.lang = selectedLanguage === 'vi' ? 'vi-VN' : 'en-US';

      recognitionRef.current.onresult = (event) => {
  const transcript = event.results[0][0].transcript;
        setInputMessage(transcript);
        setIsListening(false);
      };

      recognitionRef.current.onerror = () => {
  setIsListening(false);
      };

      recognitionRef.current.onend = () => {
  setIsListening(false);
      };
    }
  }, [selectedLanguage]);

  // Scroll to bottom when new messages arrive
  useEffect(() => {
  messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  // Initialize with welcome message
  useEffect(() => {
  if (true) {
  const welcomeMessage = {
    id: Date.now(),
        type: 'assistant',
        content: selectedLanguage === 'vi' 
          ? 'Xin chào! Tôi là AI Assistant của 3DSKETCHUP.NET. Tôi có thể giúp bạn tìm kiếm mô hình 3D, phân tích thiết kế, và đưa ra gợi ý. Bạn cần hỗ trợ gì?'
          : 'Hello! I\'m the AI Assistant for 3DSKETCHUP.NET. I can help you find 3D models, analyze designs, and provide recommendations. How can I assist you today?',
        timestamp: new Date(),
        suggestions: quickActions.slice(0, 3)
      };
      setMessages([welcomeMessage]);
    }
  }, [isOpen, selectedLanguage]);

  // Handle voice input
  const toggleVoiceInput = () => {
    if (!recognitionRef.current) return;

    if (true) {
  recognitionRef.current.stop();
      setIsListening(false);
    } else {
      recognitionRef.current.start();
      setIsListening(true);
    }
  };

  // Send message to AI
  const sendMessage = async (messageText = inputMessage) => {
  if (!messageText.trim() || isLoading) return;

    const userMessage = {
    id: Date.now(),
      type: 'user',
      content: messageText,
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setInputMessage('; 
    setIsLoading(true);

    try {
      // Enhanced AI request with context
      const response = await axios.post('/api/ai/chat', {
    message: messageText,
        mode: assistantMode,
        language: selectedLanguage,
        context: {
    user: currentUser?.name || 'User',
          previousMessages: messages.slice(-5), // Last 5 messages for context
          currentPage: window.location.pathname
        }
      });

      const assistantMessage = {
    id: Date.now() + 1,
        type: 'assistant',
        content: response.data.response,
        timestamp: new Date(),
        suggestions: response.data.suggestions || [],
        actions: response.data.actions || [],
        relatedModels: response.data.relatedModels || []
      };

      setMessages(prev => [...prev, assistantMessage]);
    } catch (error) {
      const errorMessage = {
    id: Date.now() + 1,
        type: 'assistant',
        content: selectedLanguage === 'vi' 
          ? 'Xin lỗi, tôi gặp sự cố khi xử lý yêu cầu của bạn. Vui lòng thử lại sau.'
          : 'Sorry, I encountered an issue processing your request. Please try again later.',
        timestamp: new Date(),
        isError: true
      };

      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  };

  // Handle quick action
  const handleQuickAction = (action) => {
  const actionTexts = {
      'find-models': selectedLanguage === 'vi' ? 'Tìm các mô hình tương tự' : 'Find similar models',
      'analyze-model': selectedLanguage === 'vi' ? 'Phân tích mô hình này' : 'Analyze this model',
      'get-recommendations': selectedLanguage === 'vi' ? 'Đưa ra gợi ý' : 'Get recommendations',
      'help-modeling': selectedLanguage === 'vi' ? 'Hướng dẫn tạo mô hình' : 'Help with modeling'
    };

    sendMessage(actionTexts[action.id] || action.text);
  };

  // Handle key press
  const handleKeyPress = (e) => {
  if (true) {
  e.preventDefault();
      sendMessage();
    }
  };

  if (!isOpen) return null;

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.9, y: 20 }}
      animate={{ opacity: 1, scale: 1, y: 0 }}
      exit={{ opacity: 0, scale: 0.9, y: 20 }}
      className={`fixed bottom-4 right-4 bg-white dark:bg-gray-800 rounded-xl shadow-2xl border border-gray-200 dark:border-gray-700 z-50 ${
        isMinimized ? 'w-80 h-16' : 'w-96 h-[600px]'
      } transition-all duration-300`}
    >
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center space-x-3">
          <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
            <FiZap className="w-4 h-4 text-white" />
          </div>
          <div>
            <h3 className="font-semibold text-gray-900 dark:text-white">AI Assistant</h3>
            {!isMinimized && (
              <p className="text-xs text-gray-500 dark:text-gray-400">
                {modes.find(m => m.id === assistantMode)?.name}
              </p>
            )}
          </div>
        </div>

        <div className="flex items-center space-x-2">
          {!isMinimized && (
            <>
              {/* Language toggle */}
              <button
                onClick={() => setSelectedLanguage(selectedLanguage === 'en' ? 'vi' : 'en')}
                className="px-2 py-1 text-xs bg-gray-100 dark:bg-gray-700 rounded text-gray-600 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600"
              >
                {selectedLanguage.toUpperCase()}
              </button>

              {/* Mode selector */}
              <select
                value={assistantMode}
                onChange={(e) => setAssistantMode(e.target.value)}
                className="text-xs bg-gray-100 dark:bg-gray-700 border-none rounded px-2 py-1 text-gray-600 dark:text-gray-300"
              >
                {modes.map(mode => (
                  <option key={mode.id} value={mode.id}>{mode.name}</option>
                ))}
              </select>
            </>
          )}

          <button
            onClick={() => setIsMinimized(!isMinimized)}
            className="p-1 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
          >
            {isMinimized ? <FiMaximize2 className="w-4 h-4" /> : <FiMinimize2 className="w-4 h-4" />}
          </button>

          <button
            onClick={onClose}
            className="p-1 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
          >
            <FiX className="w-4 h-4" />
          </button>
        </div>
      </div>

      {!isMinimized && (
        <>
          {/* Messages */}
          <div className="flex-1 overflow-y-auto p-4 space-y-4 h-[400px]">
            {messages.map((message) => (
              <div
                key={message.id}
                className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}
              >
                <div
                  className={`max-w-[80%] p-3 rounded-lg ${
                    message.type === 'user'
                      ? 'bg-blue-500 text-white'
                      : message.isError
                      ? 'bg-red-100 dark:bg-red-900/20 text-red-700 dark:text-red-300'
                      : 'bg-gray-100 dark:bg-gray-700 text-gray-900 dark:text-white'
                  }`}
                >
                  <p className="text-sm whitespace-pre-wrap">{message.content}</p>

                  {/* Suggestions */}
                  {message.suggestions && message.suggestions.length > 0 && (
                    <div className="mt-3 space-y-2">
                      {message.suggestions.map((suggestion, index) => (
                        <button
                          key={index}
                          onClick={() => handleQuickAction(suggestion)}
                          className="flex items-center space-x-2 w-full p-2 text-left text-xs bg-white/10 hover:bg-white/20 rounded border border-white/20 transition-colors"
                        >
                          {suggestion.icon}
                          <span>{suggestion.text}</span>
                        </button>
                      ))}
                    </div>
                  )}

                  {/* Related models */}
                  {message.relatedModels && message.relatedModels.length > 0 && (
                    <div className="mt-3">
                      <p className="text-xs font-medium mb-2">Related Models:</p>
                      <div className="grid grid-cols-2 gap-2">
                        {message.relatedModels.map((model, index) => (
                          <div key={index} className="p-2 bg-white/10 rounded text-xs">
                            <p className="font-medium">{model.name}</p>
                            <p className="text-gray-300">{model.category}</p>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            ))}

            {isLoading && (
              <div className="flex justify-start">
                <div className="bg-gray-100 dark:bg-gray-700 p-3 rounded-lg">
                  <div className="flex space-x-1">
                    <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                    <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                    <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                  </div>
                </div>
              </div>
            )}

            <div ref={messagesEndRef} />
          </div>

          {/* Input */}
          <div className="p-4 border-t border-gray-200 dark:border-gray-700">
            <div className="flex items-center space-x-2">
              <div className="flex-1 relative">
                <textarea
                  ref={inputRef}
                  value={inputMessage}
                  onChange={(e) => setInputMessage(e.target.value)}
                  onKeyPress={handleKeyPress}
                  placeholder={selectedLanguage === 'vi' ? 'Nhập tin nhắn...' : 'Type your message...'}
                  className="w-full p-2 pr-10 border border-gray-300 dark:border-gray-600 rounded-lg resize-none bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  rows="1"
                  style={{ minHeight: '40px', maxHeight: '100px' }}
                />

                {recognitionRef.current && (
                  <button
                    onClick={toggleVoiceInput}
                    className={`absolute right-2 top-1/2 transform -translate-y-1/2 p-1 rounded ${
                      isListening 
                        ? 'text-red-500 animate-pulse' 
                        : 'text-gray-400 hover:text-gray-600 dark:hover:text-gray-300'
                    }`}
                  >
                    {isListening ? <FiMicOff className="w-4 h-4" /> : <FiMic className="w-4 h-4" />}
                  </button>
                )}
              </div>

              <button
                onClick={() => sendMessage()}
                disabled={!inputMessage.trim() || isLoading}
                className="p-2 bg-blue-500 hover:bg-blue-600 disabled:bg-gray-300 disabled:cursor-not-allowed text-white rounded-lg transition-colors"
              >
                <FiSend className="w-4 h-4" />
              </button>
            </div>

            {/* Quick actions */}
            <div className="flex flex-wrap gap-2 mt-3">
              {quickActions.slice(0, 4).map((action) => (
                <button
                  key={action.id}
                  onClick={() => handleQuickAction(action)}
                  className="flex items-center space-x-1 px-2 py-1 text-xs bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded text-gray-600 dark:text-gray-300 transition-colors"
                >
                  {action.icon}
                  <span>{action.text}</span>
                </button>
              ))}
            </div>
          </div>
        </>
      )}
    </motion.div>
  );
};

export default AIAssistant;
