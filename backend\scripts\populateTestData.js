import mongoose from 'mongoose';
import Model from '../src/models/Model.js';
import User from '../src/models/User.js';
import Category from '../src/models/Category.js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Connect to MongoDB
const connectDB = async () => {
  try {
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('✅ MongoDB Connected');
  } catch (error) {
    console.error('❌ MongoDB connection error:', error);
    process.exit(1);
  }
};

// Sample models data
const sampleModels = [
  {
    title: 'Modern Living Room Set',
    description: 'Complete modern living room with sofa, coffee table, and decorative elements',
    category: 'Residential',
    subcategory: 'Living Room',
    imageUrl: 'https://i.imgur.com/8YzQZ9K.gif',
    thumbnails: [
      'https://i.imgur.com/8YzQZ9K.gif',
      'https://i.imgur.com/7KzQX2M.gif'
    ],
    format: 'Sketchup 2023',
    fileUrl: 'https://icedrive.net/s/sample1',
    downloadUrl: 'https://icedrive.net/s/sample1',
    fileSize: 15.2,
    fileFormat: 'skp',
    polygonCount: 45000,
    textured: true,
    rigged: false,
    animated: false,
    dimensions: {
      width: 5.2,
      height: 2.8,
      depth: 3.5,
      unit: 'm'
    },
    tags: ['modern', 'living room', 'furniture', 'interior'],
    downloads: 1250,
    views: 3420,
    rating: 4.8,
    isPremium: false,
    source: 'upload'
  },
  {
    title: 'Luxury Kitchen Design',
    description: 'High-end kitchen with marble countertops and premium appliances',
    category: 'Residential',
    subcategory: 'Kitchen',
    imageUrl: 'https://i.imgur.com/7KzQX2M.gif',
    thumbnails: [
      'https://i.imgur.com/7KzQX2M.gif',
      'https://i.imgur.com/5YzQA1B.gif'
    ],
    format: 'Sketchup 2022',
    fileUrl: 'https://icedrive.net/s/sample2',
    downloadUrl: 'https://icedrive.net/s/sample2',
    fileSize: 22.8,
    fileFormat: 'skp',
    polygonCount: 68000,
    textured: true,
    rigged: false,
    animated: false,
    dimensions: {
      width: 4.5,
      height: 2.6,
      depth: 3.2,
      unit: 'm'
    },
    tags: ['luxury', 'kitchen', 'marble', 'appliances'],
    downloads: 980,
    views: 2850,
    rating: 4.6,
    isPremium: true,
    source: 'upload'
  },
  {
    title: 'Garden Landscape Scene',
    description: 'Beautiful garden with trees, flowers, and walking paths',
    category: 'Landscape/Garden',
    subcategory: 'Garden',
    imageUrl: 'https://i.imgur.com/5YzQA1B.gif',
    thumbnails: [
      'https://i.imgur.com/5YzQA1B.gif',
      'https://i.imgur.com/9XzQB3C.gif'
    ],
    format: 'Sketchup 2021',
    fileUrl: 'https://icedrive.net/s/sample3',
    downloadUrl: 'https://icedrive.net/s/sample3',
    fileSize: 18.5,
    fileFormat: 'skp',
    polygonCount: 52000,
    textured: true,
    rigged: false,
    animated: false,
    dimensions: {
      width: 20.0,
      height: 1.5,
      depth: 15.0,
      unit: 'm'
    },
    tags: ['garden', 'landscape', 'trees', 'flowers'],
    downloads: 750,
    views: 2100,
    rating: 4.4,
    isPremium: false,
    source: 'upload'
  },
  {
    title: 'Office Furniture Set',
    description: 'Complete office setup with desk, chairs, and storage',
    category: 'Furniture',
    subcategory: 'Office',
    imageUrl: 'https://i.imgur.com/9XzQB3C.gif',
    thumbnails: [
      'https://i.imgur.com/9XzQB3C.gif',
      'https://i.imgur.com/6YzQD4E.gif'
    ],
    format: 'Sketchup 2020',
    fileUrl: 'https://icedrive.net/s/sample4',
    downloadUrl: 'https://icedrive.net/s/sample4',
    fileSize: 12.3,
    fileFormat: 'skp',
    polygonCount: 35000,
    textured: true,
    rigged: false,
    animated: false,
    dimensions: {
      width: 3.5,
      height: 1.2,
      depth: 2.0,
      unit: 'm'
    },
    tags: ['office', 'furniture', 'desk', 'chair'],
    downloads: 650,
    views: 1890,
    rating: 4.2,
    isPremium: false,
    source: 'upload'
  },
  {
    title: 'Modern House Exterior',
    description: 'Contemporary house design with clean lines and large windows',
    category: 'Exterior',
    subcategory: 'House',
    imageUrl: 'https://i.imgur.com/6YzQD4E.gif',
    thumbnails: [
      'https://i.imgur.com/6YzQD4E.gif',
      'https://i.imgur.com/3YzQF5G.gif'
    ],
    format: 'Sketchup 2023',
    fileUrl: 'https://icedrive.net/s/sample5',
    downloadUrl: 'https://icedrive.net/s/sample5',
    fileSize: 35.7,
    fileFormat: 'skp',
    polygonCount: 95000,
    textured: true,
    rigged: false,
    animated: false,
    dimensions: {
      width: 12.0,
      height: 8.5,
      depth: 10.0,
      unit: 'm'
    },
    tags: ['modern', 'house', 'exterior', 'contemporary'],
    downloads: 1100,
    views: 3200,
    rating: 4.7,
    isPremium: true,
    source: 'upload'
  },
  {
    title: 'Bedroom Interior Design',
    description: 'Cozy bedroom with bed, nightstands, and wardrobe',
    category: 'Residential',
    subcategory: 'Bedroom',
    imageUrl: 'https://i.imgur.com/3YzQF5G.gif',
    thumbnails: [
      'https://i.imgur.com/3YzQF5G.gif',
      'https://i.imgur.com/8YzQZ9K.gif'
    ],
    format: 'Sketchup 2022',
    fileUrl: 'https://icedrive.net/s/sample6',
    downloadUrl: 'https://icedrive.net/s/sample6',
    fileSize: 16.8,
    fileFormat: 'skp',
    polygonCount: 42000,
    textured: true,
    rigged: false,
    animated: false,
    dimensions: {
      width: 4.0,
      height: 2.7,
      depth: 3.8,
      unit: 'm'
    },
    tags: ['bedroom', 'interior', 'cozy', 'furniture'],
    downloads: 890,
    views: 2450,
    rating: 4.5,
    isPremium: false,
    source: 'upload'
  }
];

// Sample categories
const sampleCategories = [
  {
    name: 'Residential',
    slug: 'residential',
    description: 'Residential buildings and interiors',
    modelCount: 0
  },
  {
    name: 'Commercial',
    slug: 'commercial',
    description: 'Commercial buildings and spaces',
    modelCount: 0
  },
  {
    name: 'Exterior',
    slug: 'exterior',
    description: 'Exterior designs and facades',
    modelCount: 0
  },
  {
    name: 'Landscape/Garden',
    slug: 'landscape-garden',
    description: 'Landscape and garden designs',
    modelCount: 0
  },
  {
    name: 'Furniture',
    slug: 'furniture',
    description: 'Furniture and decorative objects',
    modelCount: 0
  },
  {
    name: 'Flower/Shrub/Bush',
    slug: 'flower-shrub-bush',
    description: 'Plants and vegetation',
    modelCount: 0
  }
];

// Create admin user
const createAdminUser = async () => {
  try {
    const existingAdmin = await User.findOne({ email: '<EMAIL>' });

    if (!existingAdmin) {
      const adminUser = new User({
        name: 'Admin User',
        email: '<EMAIL>',
        password: 'admin123',
        role: 'admin',
        isVerified: true
      });

      await adminUser.save();
      console.log('✅ Admin user created');
      return adminUser;
    } else {
      console.log('✅ Admin user already exists');
      return existingAdmin;
    }
  } catch (error) {
    console.error('❌ Error creating admin user:', error);
    return null;
  }
};

// Populate database
const populateDatabase = async () => {
  try {
    console.log('🚀 Starting database population...');

    // Connect to database
    await connectDB();

    // Create admin user
    const adminUser = await createAdminUser();
    if (!adminUser) {
      console.error('❌ Failed to create admin user');
      return;
    }

    // Clear existing data
    console.log('🧹 Clearing existing data...');
    await Model.deleteMany({});
    await Category.deleteMany({});

    // Create categories
    console.log('📁 Creating categories...');
    const createdCategories = await Category.insertMany(sampleCategories);
    console.log(`✅ Created ${createdCategories.length} categories`);

    // Create models with admin user as creator
    console.log('🎨 Creating models...');
    const modelsWithCreator = sampleModels.map(model => ({
      ...model,
      createdBy: adminUser._id,
      createdAt: new Date(),
      updatedAt: new Date()
    }));

    const createdModels = await Model.insertMany(modelsWithCreator);
    console.log(`✅ Created ${createdModels.length} models`);

    // Update category counts
    console.log('📊 Updating category counts...');
    for (const category of createdCategories) {
      const count = await Model.countDocuments({ category: category.name });
      await Category.findByIdAndUpdate(category._id, { modelCount: count });
    }

    console.log('🎉 Database population completed successfully!');
    console.log(`📈 Total models: ${createdModels.length}`);
    console.log(`📂 Total categories: ${createdCategories.length}`);

  } catch (error) {
    console.error('❌ Error populating database:', error);
  } finally {
    await mongoose.disconnect();
    console.log('🔌 Disconnected from MongoDB');
    process.exit(0);
  }
};

// Run the script
populateDatabase();
