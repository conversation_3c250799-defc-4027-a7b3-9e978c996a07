import React, { useState, useEffect, useCallback } from 'react';
import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import { FiArrowRight, FiChevronLeft, FiChevronRight } from 'react-icons/fi';
import ImageWithFallback from './ui/ImageWithFallback';
import { useModels } from '../context/ModelContext';

/**
 * Related Models Component
 * Displays a carousel of related models based on category, tags, or other criteria
 */
const RelatedModels = ({
  currentModelId,
  category,
  tags = [],
  limit = 6
}) => {
  const { models } = useModels();
  const [relatedModels, setRelatedModels] = useState([]);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [visibleCount, setVisibleCount] = useState(3);

  // Determine how many models to show based on screen size
  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth < 640) {
        setVisibleCount(1);
      } else if (window.innerWidth < 1024) {
        setVisibleCount(2);
      } else {
        setVisibleCount(3);
      }
    };

    handleResize();
    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  // Find related models
  useEffect(() => {
    if (!models || models.length === 0) return;

    // Filter out current model
    const filteredModels = models.filter(model => model.id.toString() !== currentModelId.toString());

    // Find models with same category
    const sameCategory = filteredModels.filter(model => model.category === category);

    // Find models with matching tags
    const matchingTags = filteredModels.filter(model => {
      if (!model.tags || !tags.length) return false;
      return model.tags.some(tag => tags.includes(tag));
    });

    // Combine and deduplicate
    const combined = [...sameCategory, ...matchingTags];
    const uniqueModels = Array.from(new Set(combined.map(model => model.id)))
      .map(id => combined.find(model => model.id === id));

    // If we don't have enough related models, add some random ones
    if (uniqueModels.length < limit) {
      const randomModels = filteredModels
        .filter(model => !uniqueModels.some(m => m.id === model.id))
        .sort(() => 0.5 - Math.random())
        .slice(0, limit - uniqueModels.length);

      uniqueModels.push(...randomModels);
    }

    // Limit to specified number
    setRelatedModels(uniqueModels.slice(0, limit));
  }, [models, currentModelId, category, tags, limit]);

  // Navigate carousel
  const nextSlide = () => {
    if (currentIndex + visibleCount < relatedModels.length) {
      setCurrentIndex(currentIndex + 1);
    } else {
      setCurrentIndex(0); // Loop back to start
    }
  };

  const prevSlide = () => {
    if (currentIndex > 0) {
      setCurrentIndex(currentIndex - 1);
    } else {
      setCurrentIndex(relatedModels.length - visibleCount); // Loop to end
    }
  };

  // If no related models, don't render
  if (relatedModels.length === 0) {
    return null;
  }

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 mt-8">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-xl font-bold text-gray-900 dark:text-white">Related Models</h2>
        <Link
          to={`/category/${category?.toLowerCase()}`}
          className="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 flex items-center text-sm font-medium"
        >
          View All <FiArrowRight className="ml-1" />
        </Link>
      </div>

      <div className="relative">
        {/* Carousel navigation buttons */}
        {relatedModels.length > visibleCount && (
          <>
            <button
              onClick={prevSlide}
              className="absolute left-0 top-1/2 -translate-y-1/2 -ml-4 z-10 bg-white dark:bg-gray-700 rounded-full p-2 shadow-md hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors"
              aria-label="Previous"
            >
              <FiChevronLeft className="w-5 h-5 text-gray-600 dark:text-gray-300" />
            </button>

            <button
              onClick={nextSlide}
              className="absolute right-0 top-1/2 -translate-y-1/2 -mr-4 z-10 bg-white dark:bg-gray-700 rounded-full p-2 shadow-md hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors"
              aria-label="Next"
            >
              <FiChevronRight className="w-5 h-5 text-gray-600 dark:text-gray-300" />
            </button>
          </>
        )}

        {/* Carousel content */}
        <div className="overflow-hidden">
          <motion.div
            className="flex"
            initial={false}
            animate={{
              x: `-${currentIndex * (100 / visibleCount)}%`
            }}
            transition={{ type: 'spring', stiffness: 300, damping: 30 }}
          >
            {relatedModels.map((model) => (
              <div
                key={model.id}
                className="flex-shrink-0"
                style={{ width: `${100 / visibleCount}%` }}
              >
                <div className="p-2">
                  <Link
                    to={`/model/${model._id || model.id}`}
                    className="block bg-white dark:bg-gray-700 rounded-lg overflow-hidden shadow-md hover:shadow-lg transition-shadow"
                  >
                    <div className="relative pb-[75%]">
                      <ImageWithFallback
                        src={model.imageUrl}
                        alt={model.title}
                        className="absolute inset-0 w-full h-full object-cover"
                        lowResSrc="/images/placeholder-tiny.jpg"
                      />
                      {model.isPremium && (
                        <div className="absolute top-2 right-2 bg-yellow-500 text-white text-xs font-bold px-2 py-1 rounded">
                          Premium
                        </div>
                      )}
                    </div>

                    <div className="p-4">
                      <h3 className="text-gray-900 dark:text-white font-medium text-sm line-clamp-1">
                        {model.title}
                      </h3>

                      <div className="flex justify-between items-center mt-2">
                        <span className="text-xs text-gray-500 dark:text-gray-400">
                          {model.category}
                        </span>

                        <span className="text-xs font-medium text-gray-900 dark:text-white">
                          {model.format}
                        </span>
                      </div>
                    </div>
                  </Link>
                </div>
              </div>
            ))}
          </motion.div>
        </div>

        {/* Carousel indicators */}
        {relatedModels.length > visibleCount && (
          <div className="flex justify-center mt-4">
            {Array.from({ length: relatedModels.length - visibleCount + 1 }).map((_, index) => (
              <button
                key={index}
                onClick={() => setCurrentIndex(index)}
                className={`w-2 h-2 mx-1 rounded-full ${
                  currentIndex === index
                    ? 'bg-blue-600 dark:bg-blue-500'
                    : 'bg-gray-300 dark:bg-gray-600'
                }`}
                aria-label={`Go to slide ${index + 1}`}
              />
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default RelatedModels;
