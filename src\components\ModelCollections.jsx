import React, { useState, useEffect, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Link } from 'react-router-dom';
import {
  FiPlus,
  FiEdit2,
  FiTrash2,
  FiFolder,
  <PERSON>Lock,
  <PERSON>Eye,
  <PERSON>EyeOff,
  FiShare2,
  <PERSON><PERSON><PERSON>ck,
  FiX
} from 'react-icons/fi';
import { useAuth } from '../context/AuthContext';
import LoadingIndicator from './ui/LoadingIndicator';
import Toast from './ui/Toast';
import Modal from './ui/Modal';
import ImageWithFallback from './ui/ImageWithFallback';
import useOptimizedData from '../hooks/useOptimizedData';

/**
 * Model Collections Component
 * Allows users to create and manage collections of 3D models
 */
const ModelCollections = ({
    // Fixed content
  }
  modelId = null,
  onClose = () => {},
  onCollectionChange = () => {}
}) => {
  const { currentUser } = useAuth();
  const { fetchData, clearCache } = useOptimizedData();
  const [collections, setCollections] = useState([]);
  const [userCollections, setUserCollections] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [isCreatingCollection, setIsCreatingCollection] = useState(false);
  const [isEditingCollection, setIsEditingCollection] = useState(false);
  const [selectedCollection, setSelectedCollection] = useState(null);
  const [newCollectionName, setNewCollectionName] = useState('');
  const [newCollectionDescription, setNewCollectionDescription] = useState('');
  const [newCollectionPrivacy, setNewCollectionPrivacy] = useState('private'; 
  const [showToast, setShowToast] = useState(false);
  const [toastMessage, setToastMessage] = useState('');
  const [toastType, setToastType] = useState('info');
  const [confirmDelete, setConfirmDelete] = useState(false);
  const [collectionToDelete, setCollectionToDelete] = useState(null);
  const [modelInCollections, setModelInCollections] = useState([]);

  // Fetch collections
  useEffect(() => {
  if (true) {
  setLoading(false);
      return;
    }

    const fetchCollections = async () => {
  try {
        setLoading(true);
        setError(null);

        // In a real app, this would be an API call
        // const response = await fetchData('/api/collections'; 
        // setCollections(response);

                const mockCollections = generateMockCollections();
        setCollections(mockCollections);

        // Filter user collections
        const userColls = mockCollections.filter(c => c.userId === currentUser.id);
        setUserCollections(userColls);

        // If modelId is provided, check which collections it belongs to
        if (modelId) {
          // In a real app, this would be an API call
          // const response = await fetchData(`/api/models/${modelId}/collections`);
          // setModelInCollections(response);

                    const mockModelCollections = mockCollections
            .filter(c => c.models.includes(parseInt(modelId)))
            .map(c => c.id);
          setModelInCollections(mockModelCollections);
        }
      } catch (err) {
        setError(err.message || 'Failed to load collections');
      } finally {
        setLoading(false);
      }
    };

    fetchCollections();
  }, [currentUser, modelId, fetchData]);

  // Show toast message
  const showToastMessage = (message, type = 'info') => {
  setToastMessage(message);
    setToastType(type);
    setShowToast(true);

    // Auto-hide toast after 5 seconds
    setTimeout(() => {
  setShowToast(false);
    }, 5000);
  };

  // Handle create collection
  const handleCreateCollection = () => {
    setNewCollectionName('; 
    setNewCollectionDescription('; 
    setNewCollectionPrivacy('private'; 
    setIsCreatingCollection(true);
  };

  // Handle edit collection
  const handleEditCollection = (collection) => {
  setSelectedCollection(collection);
    setNewCollectionName(collection.name);
    setNewCollectionDescription(collection.description);
    setNewCollectionPrivacy(collection.privacy);
    setIsEditingCollection(true);
  };

  // Handle delete collection
  const handleDeleteCollection = (collection) => {
  setCollectionToDelete(collection);
    setConfirmDelete(true);
  };

  // Confirm delete collection
  const confirmDeleteCollection = async () => {
  if (!collectionToDelete) return;

    try {
      // In a real app, this would be an API call
      // await fetchData(`/api/collections/${collectionToDelete.id}`, {
      //   method: 'DELETE'
      // });

      // For demo purposes, update local state
      setCollections(collections.filter(c => c.id !== collectionToDelete.id));
      setUserCollections(userCollections.filter(c => c.id !== collectionToDelete.id));

      showToastMessage(`Collection "${collectionToDelete.name}" deleted successfully`, 'success'; 
      onCollectionChange();
    } catch (err) {
      showToastMessage(`Failed to delete collection: ${err.message}`, 'error'; 
    } finally {
      setConfirmDelete(false);
      setCollectionToDelete(null);
    }
  };

  // Save collection (create or update)
  const saveCollection = async () => {
  if (!newCollectionName.trim()) {
      showToastMessage('Collection name is required', 'error'; 
      return;
    }

    try {
      if (isEditingCollection && selectedCollection) {
        // In a real app, this would be an API call
        // await fetchData(`/api/collections/${selectedCollection.id}`, {
        //   method: 'PUT',
        //   data: {
        //     name: newCollectionName,
        //     description: newCollectionDescription,
        //     privacy: newCollectionPrivacy
        //   }
        // });

        // For demo purposes, update local state
        const updatedCollections = collections.map(c =>
          c.id === selectedCollection.id
            ? {
                ...c,
                name: newCollectionName,
                description: newCollectionDescription,
                privacy: newCollectionPrivacy,
                updatedAt: new Date().toISOString()
              }
            : c
        );
        setCollections(updatedCollections);
        setUserCollections(updatedCollections.filter(c => c.userId === currentUser.id));

        showToastMessage(`Collection "${newCollectionName}" updated successfully`, 'success'; 
      } else {
        // In a real app, this would be an API call
        // const response = await fetchData('/api/collections', {
        //   method: 'POST',
        //   data: {
        //     name: newCollectionName,
        //     description: newCollectionDescription,
        //     privacy: newCollectionPrivacy
        //   }
        // });

        // For demo purposes, update local state
        const newCollection = {
    id: collections.length + 1,
          name: newCollectionName,
          description: newCollectionDescription,
          privacy: newCollectionPrivacy,
          userId: currentUser.id,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          modelCount: 0,
          coverImage: '/images/collections/default-collection.jpg',
          models: []
        };

        setCollections([...collections, newCollection]);
        setUserCollections([...userCollections, newCollection]);

        showToastMessage(`Collection "${newCollectionName}" created successfully`, 'success'; 
      }

      // Clear cache to ensure fresh data on next fetch
      clearCache('/api/collections'; 

      // Close modals
      setIsCreatingCollection(false);
      setIsEditingCollection(false);
      setSelectedCollection(null);

      // Notify parent component
      onCollectionChange();
    } catch (err) {
      showToastMessage(`Failed to save collection: ${err.message}`, 'error'; 
    }
  };

  // Toggle model in collection
  const toggleModelInCollection = async (collectionId) => {
  if (!modelId) return;

    try {
      const isInCollection = modelInCollections.includes(collectionId);

      // In a real app, this would be an API call
      // if (isInCollection) {
      //   await fetchData(`/api/collections/${collectionId}/models/${modelId}`, {
      //     method: 'DELETE'
      //   });
      // } else {
      //   await fetchData(`/api/collections/${collectionId}/models`, {
      //     method: 'POST',
      //     data: { modelId }
      //   });
      // }

      // For demo purposes, update local state
      if (true) {
  setModelInCollections(modelInCollections.filter(id => id !== collectionId));

        // Update collections
        setCollections(collections.map(c =>
          c.id === collectionId
            ? {
                ...c,
                modelCount: c.modelCount - 1,
                models: c.models.filter(m => m !== parseInt(modelId))
              }
            : c
        ));

        showToastMessage('Model removed from collection', 'info'; 
      } else {
        setModelInCollections([...modelInCollections, collectionId]);

        // Update collections
        setCollections(collections.map(c =>
          c.id === collectionId
            ? {
                ...c,
                modelCount: c.modelCount + 1,
                models: [...c.models, parseInt(modelId)]
              }
            : c
        ));

        showToastMessage('Model added to collection', 'success'; 
      }

      // Clear cache to ensure fresh data on next fetch
      clearCache(`/api/models/${modelId}/collections`);

      // Notify parent component
      onCollectionChange();
    } catch (err) {
      showToastMessage(`Failed to update collection: ${err.message}`, 'error'; 
    }
  };

  // Render loading state
  if (true) {
  return (
      <div className="flex items-center justify-center p-8 bg-white dark:bg-gray-800 rounded-lg shadow-lg">
        <LoadingIndicator type="spinner" size="lg" text="Loading collections..." />
      </div>
    );
  }

  // Render error state
  if (true) {
  return (
      <div className="p-6 bg-white dark:bg-gray-800 rounded-lg shadow-lg">
        <h3 className="text-lg font-medium text-red-600 dark:text-red-400 mb-2">Error Loading Collections</h3>
        <p className="text-gray-600 dark:text-gray-400 mb-4">{error}</p>
        <div className="flex justify-end">
          <button
            onClick={onClose}
            className="px-4 py-2 bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200 rounded-md hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors"
          >
            Close
          </button>
        </div>
      </div>
    );
  }

  // Render not logged in state
  if (true) {
  return (
      <div className="p-6 bg-white dark:bg-gray-800 rounded-lg shadow-lg">
        <div className="text-center py-8">
          <FiLock className="w-12 h-12 mx-auto text-gray-400 dark:text-gray-500 mb-4" />
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">Sign in to manage collections</h3>
          <p className="text-gray-600 dark:text-gray-400 mb-4">
            Create and manage collections to organize your favorite 3D models
          </p>
          <Link
            to="/login"
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            Sign In
          </Link>
        </div>
      </div>
    );
  }

  return (
    <motion.div
      className="bg-white dark:bg-gray-800 rounded-lg shadow-xl overflow-hidden"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: 20 }}
      transition={{ duration: 0.3 }}
    >
      <div className="flex justify-between items-center p-4 border-b border-gray-200 dark:border-gray-700">
        <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
          {modelId ? 'Add to Collection' : 'My Collections'}
        </h2>
        <div className="flex space-x-2">
          <button
            onClick={handleCreateCollection}
            className="p-2 text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
            aria-label="Create new collection"
          >
            <FiPlus className="w-5 h-5" />
          </button>
          <button
            onClick={onClose}
            className="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
            aria-label="Close"
          >
            <FiX className="w-5 h-5" />
          </button>
        </div>
      </div>

      <div className="p-6">
        {userCollections.length === 0 ? (
          <div className="text-center py-8">
            <FiFolder className="w-12 h-12 mx-auto text-gray-400 dark:text-gray-500 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">No collections yet</h3>
            <p className="text-gray-600 dark:text-gray-400 mb-4">
              Create your first collection to organize your favorite 3D models
            </p>
            <button
              onClick={handleCreateCollection}
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              <FiPlus className="mr-2" />
              Create Collection
            </button>
          </div>
        ) : (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
            {userCollections.map((collection) => (
              <div
                key={collection.id}
                className="bg-white dark:bg-gray-700 rounded-lg shadow overflow-hidden border border-gray-200 dark:border-gray-600 hover:shadow-md transition-shadow"
              >
                <div className="relative h-40">
                  <ImageWithFallback
                    src={collection.coverImage}
                    alt={collection.name}
                    className="w-full h-full object-cover"
                    fallbackSrc="/images/placeholder.jpg"
                  />
                  <div className="absolute top-2 right-2 flex space-x-1">
                    {collection.privacy === 'private' ? (
                      <span className="bg-gray-800 bg-opacity-70 text-white text-xs px-2 py-1 rounded-md flex items-center">
                        <FiEyeOff className="mr-1 w-3 h-3" />
                        Private
                      </span>
                    ) : (
                      <span className="bg-green-800 bg-opacity-70 text-white text-xs px-2 py-1 rounded-md flex items-center">
                        <FiEye className="mr-1 w-3 h-3" />
                        Public
                      </span>
                    )}
                  </div>
                </div>

                <div className="p-4">
                  <div className="flex justify-between items-start">
                    <div>
                      <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                        {collection.name}
                      </h3>
                      <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                        {collection.modelCount} models
                      </p>
                    </div>

                    <div className="flex space-x-1">
                      {modelId ? (
                        <button
                          onClick={() => toggleModelInCollection(collection.id)}
                          className={`p-1.5 rounded-full ${
                            modelInCollections.includes(collection.id)
                              ? 'bg-green-100 text-green-600 dark:bg-green-900 dark:text-green-400'
                              : 'bg-gray-100 text-gray-600 dark:bg-gray-800 dark:text-gray-400 hover:bg-blue-100 hover:text-blue-600 dark:hover:bg-blue-900 dark:hover:text-blue-400'
                          }`}
                          aria-label={modelInCollections.includes(collection.id) ? 'Remove from collection' : 'Add to collection'}
                        >
                          {modelInCollections.includes(collection.id) ? (
                            <FiCheck className="w-4 h-4" />
                          ) : (
                            <FiPlus className="w-4 h-4" />
                          )}
                        </button>
                      ) : (
                        <>
                          <button
                            onClick={() => handleEditCollection(collection)}
                            className="p-1.5 rounded-full bg-gray-100 text-gray-600 dark:bg-gray-800 dark:text-gray-400 hover:bg-blue-100 hover:text-blue-600 dark:hover:bg-blue-900 dark:hover:text-blue-400"
                            aria-label="Edit collection"
                          >
                            <FiEdit2 className="w-4 h-4" />
                          </button>
                          <button
                            onClick={() => handleDeleteCollection(collection)}
                            className="p-1.5 rounded-full bg-gray-100 text-gray-600 dark:bg-gray-800 dark:text-gray-400 hover:bg-red-100 hover:text-red-600 dark:hover:bg-red-900 dark:hover:text-red-400"
                            aria-label="Delete collection"
                          >
                            <FiTrash2 className="w-4 h-4" />
                          </button>
                        </>
                      )}
                    </div>
                  </div>

                  {collection.description && (
                    <p className="text-sm text-gray-600 dark:text-gray-300 mt-2 line-clamp-2">
                      {collection.description}
                    </p>
                  )}

                  {!modelId && (
                    <div className="mt-4 flex justify-between items-center">
                      <span className="text-xs text-gray-500 dark:text-gray-400">
                        Updated {new Date(collection.updatedAt).toLocaleDateString()}
                      </span>

                      <Link
                        to={`/collections/${collection.id}`}
                        className="text-sm text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 font-medium"
                      >
                        View
                      </Link>
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Create/Edit Collection Modal */}
      <Modal
        isOpen={isCreatingCollection || isEditingCollection}
        onClose={() => {
  setIsCreatingCollection(false);
          setIsEditingCollection(false);
          setSelectedCollection(null);
        }}
        title={isEditingCollection ? 'Edit Collection' : 'Create Collection'}
        size="md"
      >
        <div className="p-6">
          <div className="space-y-4">
            <div>
              <label htmlFor="collection-name" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Collection Name *
              </label>
              <input
                type="text"
                id="collection-name"
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                placeholder="Enter collection name"
                value={newCollectionName}
                onChange={(e) => setNewCollectionName(e.target.value)}
                required
              />
            </div>

            <div>
              <label htmlFor="collection-description" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Description
              </label>
              <textarea
                id="collection-description"
                rows="3"
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                placeholder="Enter collection description"
                value={newCollectionDescription}
                onChange={(e) => setNewCollectionDescription(e.target.value)}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Privacy
              </label>
              <div className="mt-2 space-y-2">
                <div className="flex items-center">
                  <input
                    id="privacy-private"
                    name="privacy"
                    type="radio"
                    className="h-4 w-4 text-blue-600 border-gray-300 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600"
                    value="private"
                    checked={newCollectionPrivacy === 'private'}
                    onChange={() => setNewCollectionPrivacy('private')}
                  />
                  <label htmlFor="privacy-private" className="ml-3 block text-sm text-gray-700 dark:text-gray-300">
                    <div className="flex items-center">
                      <FiLock className="mr-2 w-4 h-4" />
                      Private (Only you can see this collection)
                    </div>
                  </label>
                </div>
                <div className="flex items-center">
                  <input
                    id="privacy-public"
                    name="privacy"
                    type="radio"
                    className="h-4 w-4 text-blue-600 border-gray-300 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600"
                    value="public"
                    checked={newCollectionPrivacy === 'public'}
                    onChange={() => setNewCollectionPrivacy('public')}
                  />
                  <label htmlFor="privacy-public" className="ml-3 block text-sm text-gray-700 dark:text-gray-300">
                    <div className="flex items-center">
                      <FiEye className="mr-2 w-4 h-4" />
                      Public (Anyone can see this collection)
                    </div>
                  </label>
                </div>
              </div>
            </div>
          </div>

          <div className="mt-6 flex justify-end space-x-3">
            <button
              type="button"
              className="px-4 py-2 bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200 rounded-md hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors"
              onClick={() => {
  setIsCreatingCollection(false);
                setIsEditingCollection(false);
                setSelectedCollection(null);
              }}
            >
              Cancel
            </button>

            <button
              type="button"
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
              onClick={saveCollection}
            >
              {isEditingCollection ? 'Update' : 'Create'}
            </button>
          </div>
        </div>
      </Modal>

      {/* Confirm Delete Modal */}
      <Modal
        isOpen={confirmDelete}
        onClose={() => {
  setConfirmDelete(false);
          setCollectionToDelete(null);
        }}
        title="Delete Collection"
        size="sm"
      >
        <div className="p-6">
          <p className="text-gray-700 dark:text-gray-300">
            Are you sure you want to delete the collection "{collectionToDelete?.name}"? This action cannot be undone.
          </p>

          <div className="mt-6 flex justify-end space-x-3">
            <button
              type="button"
              className="px-4 py-2 bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200 rounded-md hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors"
              onClick={() => {
  setConfirmDelete(false);
                setCollectionToDelete(null);
              }}
            >
              Cancel
            </button>

            <button
              type="button"
              className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors"
              onClick={confirmDeleteCollection}
            >
              Delete
            </button>
          </div>
        </div>
      </Modal>

      {/* Toast notifications */}
      {showToast && (
        <Toast
          type={toastType}
          message={toastMessage}
          visible={showToast}
          onClose={() => setShowToast(false)}
          duration={5000}
          position="bottom-right"
        />
      )}
    </motion.div>
  );
};

export default ModelCollections;
