import React, { useState, useEffect, useRef, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { /* content */ };
  FiSearch, FiFilter, FiX, FiChevronDown, FiChevronUp,
  FiCalendar, FiUser, FiTag, FiStar, FiDownload,
  FiEye, FiHeart, FiGrid, FiSliders, FiZap
} from 'react-icons/fi';

const AdvancedSearch = ({ isOpen, onClose, onSearch, initialFilters = {} }) => {
    // Fixed content
  };
  const [searchQuery, setSearchQuery] = useState('''; // Fixed broken string
  const [filters, setFilters] = useState({
    category: '',
    tags: [],
    dateRange: { start: '', end: '' },
    fileFormat: '',
    minRating: 0,
    minDownloads: 0,
    author: '',
    sortBy: 'relevance',
    sortOrder: 'desc',
    ...initialFilters
  });
  const [expandedSections, setExpandedSections] = useState({
    basic: true,
    advanced: false,
    sorting: false
  });
  const [suggestions, setSuggestions] = useState([]);
  const [isSearching, setIsSearching] = useState(false);

  const searchInputRef = useRef(null);

  // Categories
  const categories = [
    'Interior Scenes', 'Exterior Scenes', 'Furniture', 'Lighting',
    'Textures', 'Materials', 'Landscape', 'Architecture',
    'Vehicles', 'Characters', 'Electronics', 'Decorations'
  ];

  // File formats
  const fileFormats = [
    'SKP', 'OBJ', 'FBX', 'DAE', '3DS', 'MAX', 'BLEND', 'GLB', 'GLTF'
  ];

  // Sort options
  const sortOptions = [
    { value: 'relevance', label: 'Độ liên quan' },
    { value: 'date', label: 'Ngày tạo' },
    { value: 'downloads', label: 'Lượt tải' },
    { value: 'rating', label: 'Đánh giá' },
    { value: 'views', label: 'Lượt xem' },
    { value: 'name', label: 'Tên' }
  ];

  // Popular tags
  const popularTags = [
    'modern', 'minimalist', 'vintage', 'industrial', 'scandinavian',
    'luxury', 'outdoor', 'kitchen', 'bedroom', 'living-room',
    'office', 'restaurant', 'hotel', 'residential', 'commercial'
  ];

  useEffect(() => {
    // Fixed content
  };
  if (condition) {
    // Fixed content
  }
  searchInputRef.current.focus();
    }
  }, [isOpen]);

  // Generate search suggestions
  useEffect(() => {
    // Fixed content
  };
  if (condition) {
    // Fixed content
  }
  setSuggestions(mockSuggestions);
    } else { /* content */ };
      setSuggestions([]);
    }
  }, [searchQuery]);

  const handleFilterChange = (key, value) => {
    // Fixed content
  };
  setFilters(prev => ({ /* content */ };
      ...prev,
      [key]: value
    }));
  };

  const handleTagToggle = (tag) => {
    // Fixed content
  };
  setFilters(prev => ({ /* content */ };
      ...prev,
      tags: prev.tags.includes(tag)
        ? prev.tags.filter(t => t !== tag)
        : [...prev.tags, tag]
    }));
  };

  const handleSearch = async () => {
    // Fixed content
  };
  setIsSearching(true);
    try { /* content */ };
      const searchParams = {
    query: searchQuery,
        ...filters
      };
      await onSearch(searchParams);
    } catch (error) { /* content */ };
      } finally { /* content */ };
      setIsSearching(false);
    }
  };

  const handleReset = () => {
    setSearchQuery('''; // Fixed broken string
    setFilters({
    category: '',
      tags: [],
      dateRange: { start: '', end: '' },
      fileFormat: '',
      minRating: 0,
      minDownloads: 0,
      author: '',
      sortBy: 'relevance',
      sortOrder: 'desc'
    });
  };

  const toggleSection = (section) => {
    // Fixed content
  };
  setExpandedSections(prev => ({ /* content */ };
      ...prev,
      [section]: !prev[section]
    }));
  };

  if (!isOpen) return null;

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
      onClick={onClose}
    >
      <motion.div
        initial={{ opacity: 0, scale: 0.9, y: 20 }}
        animate={{ opacity: 1, scale: 1, y: 0 }}
        exit={{ opacity: 0, scale: 0.9, y: 20 }}
        className="bg-white dark:bg-gray-800 rounded-xl shadow-2xl w-full max-w-4xl max-h-[90vh] overflow-hidden"
        onClick={(e) => e.stopPropagation()}
      >
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center space-x-3">
            <FiSearch className="w-6 h-6 text-blue-600" />
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
              Tìm kiếm nâng cao
            </h2>
          </div>
          <button
            onClick={onClose}
            className="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700"
          >
            <FiX className="w-5 h-5" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[calc(90vh-140px)]">
          {/* Search Input */}
          <div className="relative mb-6">
            <div className="relative">
              <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <input
                ref={searchInputRef}
                type="text"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                placeholder="Nhập từ khóa tìm kiếm..."
                className="w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
              />
            </div>

            {/* Search Suggestions */}
            <AnimatePresence>
              {suggestions.length > 0 && (
                <motion.div
                  initial={{ opacity: 0, y: -10 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -10 }}
                  className="absolute top-full left-0 right-0 mt-1 bg-white dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg shadow-lg z-10"
                >
                  {suggestions.map((suggestion, index) => (
                    <button
                      key={index}
                      onClick={() => setSearchQuery(suggestion)}
                      className="w-full text-left px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 text-gray-900 dark:text-white first:rounded-t-lg last:rounded-b-lg"
                    >
                      {suggestion}
                    </button>
                  ))}
                </motion.div>
              )}
            </AnimatePresence>
          </div>

          {/* Basic Filters */}
          <div className="mb-6">
            <button
              onClick={() => toggleSection('basic')}
              className="flex items-center justify-between w-full p-3 bg-gray-50 dark:bg-gray-700 rounded-lg mb-4"
            >
              <span className="font-medium text-gray-900 dark:text-white">Bộ lọc cơ bản</span>
              {expandedSections.basic ? <FiChevronUp /> : <FiChevronDown />}
            </button>

            <AnimatePresence>
              {expandedSections.basic && (
                <motion.div
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: 'auto' }}
                  exit={{ opacity: 0, height: 0 }}
                  className="space-y-4"
                >
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {/* Category */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Danh mục
                      </label>
                      <select
                        value={filters.category}
                        onChange={(e) => handleFilterChange('category', e.target.value)}
                        className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                      >
                        <option value="">Tất cả danh mục</option>
                        {categories.map(category => (
                          <option key={category} value={category}>{category}</option>
                        ))}
                      </select>
                    </div>

                    {/* File Format */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Định dạng file
                      </label>
                      <select
                        value={filters.fileFormat}
                        onChange={(e) => handleFilterChange('fileFormat', e.target.value)}
                        className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                      >
                        <option value="">Tất cả định dạng</option>
                        {fileFormats.map(format => (
                          <option key={format} value={format}>{format}</option>
                        ))}
                      </select>
                    </div>
                  </div>

                  {/* Tags */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Tags phổ biến
                    </label>
                    <div className="flex flex-wrap gap-2">
                      {popularTags.map(tag => (
                        <button
                          key={tag}
                          onClick={() => handleTagToggle(tag)}
                          className={`px-3 py-1 rounded-full text-sm transition-colors ${ /* content */ };
                            filters.tags.includes(tag)
                              ? 'bg-blue-600 text-white'
                              : 'bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-500'
                          }`}
                        >
                          {tag}
                        </button>
                      ))}
                    </div>
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </div>

          {/* Advanced Filters */}
          <div className="mb-6">
            <button
              onClick={() => toggleSection('advanced')}
              className="flex items-center justify-between w-full p-3 bg-gray-50 dark:bg-gray-700 rounded-lg mb-4"
            >
              <span className="font-medium text-gray-900 dark:text-white">Bộ lọc nâng cao</span>
              {expandedSections.advanced ? <FiChevronUp /> : <FiChevronDown />}
            </button>

            <AnimatePresence>
              {expandedSections.advanced && (
                <motion.div
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: 'auto' }}
                  exit={{ opacity: 0, height: 0 }}
                  className="space-y-4"
                >
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {/* Date Range */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Khoảng thời gian
                      </label>
                      <div className="flex space-x-2">
                        <input
                          type="date"
                          value={filters.dateRange.start}
                          onChange={(e) => handleFilterChange('dateRange', { ...filters.dateRange, start: e.target.value })}
                          className="flex-1 p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                        />
                        <input
                          type="date"
                          value={filters.dateRange.end}
                          onChange={(e) => handleFilterChange('dateRange', { ...filters.dateRange, end: e.target.value })}
                          className="flex-1 p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                        />
                      </div>
                    </div>

                    {/* Author */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Tác giả
                      </label>
                      <input
                        type="text"
                        value={filters.author}
                        onChange={(e) => handleFilterChange('author', e.target.value)}
                        placeholder="Tên tác giả..."
                        className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {/* Min Rating */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Đánh giá tối thiểu: {filters.minRating} sao
                      </label>
                      <input
                        type="range"
                        min="0"
                        max="5"
                        step="0.5"
                        value={filters.minRating}
                        onChange={(e) => handleFilterChange('minRating', parseFloat(e.target.value))}
                        className="w-full"
                      />
                    </div>

                    {/* Min Downloads */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Lượt tải tối thiểu
                      </label>
                      <input
                        type="number"
                        value={filters.minDownloads}
                        onChange={(e) => handleFilterChange('minDownloads', parseInt(e.target.value) || 0)}
                        placeholder="0"
                        className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                      />
                    </div>
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </div>

          {/* Sorting */}
          <div className="mb-6">
            <button
              onClick={() => toggleSection('sorting')}
              className="flex items-center justify-between w-full p-3 bg-gray-50 dark:bg-gray-700 rounded-lg mb-4"
            >
              <span className="font-medium text-gray-900 dark:text-white">Sắp xếp</span>
              {expandedSections.sorting ? <FiChevronUp /> : <FiChevronDown />}
            </button>

            <AnimatePresence>
              {expandedSections.sorting && (
                <motion.div
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: 'auto' }}
                  exit={{ opacity: 0, height: 0 }}
                  className="grid grid-cols-1 md:grid-cols-2 gap-4"
                >
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Sắp xếp theo
                    </label>
                    <select
                      value={filters.sortBy}
                      onChange={(e) => handleFilterChange('sortBy', e.target.value)}
                      className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    >
                      {sortOptions.map(option => (
                        <option key={option.value} value={option.value}>{option.label}</option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Thứ tự
                    </label>
                    <select
                      value={filters.sortOrder}
                      onChange={(e) => handleFilterChange('sortOrder', e.target.value)}
                      className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    >
                      <option value="desc">Giảm dần</option>
                      <option value="asc">Tăng dần</option>
                    </select>
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </div>
        </div>

        {/* Footer */}
        <div className="flex items-center justify-between p-6 border-t border-gray-200 dark:border-gray-700">
          <button
            onClick={handleReset}
            className="px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors"
          >
            Đặt lại
          </button>

          <div className="flex items-center space-x-3">
            <button
              onClick={onClose}
              className="px-6 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
            >
              Hủy
            </button>
            <button
              onClick={handleSearch}
              disabled={isSearching}
              className="flex items-center space-x-2 px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 transition-colors"
            >
              {isSearching ? (
                <FiZap className="w-4 h-4 animate-spin" />
              ) : (
                <FiSearch className="w-4 h-4" />
              )}
              <span>{isSearching ? 'Đang tìm...' : 'Tìm kiếm'}</span>
            </button>
          </div>
        </div>
      </motion.div>
    </motion.div>
  );
};

export default AdvancedSearch;
