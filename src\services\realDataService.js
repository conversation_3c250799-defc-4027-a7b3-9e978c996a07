// Real data service to fetch actual models from MongoDB
import mongoService from './mongoService';
import apiService from './api';

class RealDataService { /* content */ };
  constructor() { /* content */ };
    this.cache = new Map();
    this.cacheTimeout = 10 * 60 * 1000; // 10 minutes for better performance
    this.requestQueue = new Map(); // Prevent duplicate requests
  }

  // Get cache key
  getCacheKey(method, params = {}) { /* content */ };
    return `${method}_${JSON.stringify(params)}`;
  }

  // Check if cache is valid
  isCacheValid(cacheEntry) { /* content */ };
    return cacheEntry && (Date.now() - cacheEntry.timestamp) < this.cacheTimeout;
  }

  // Get from cache or fetch new data with request deduplication
  async getCachedData(key, fetchFunction) { /* content */ };
    const cached = this.cache.get(key);

    if (this.isCacheValid(cached)) { /* content */ };
      return cached.data;
    }

    // Check if request is already in progress
    if (this.requestQueue.has(key)) { /* content */ };
      return this.requestQueue.get(key);
    }

    // Create new request promise
    const requestPromise = (async () => {
    // Fixed content
  };
  try { /* content */ };
        const data = await fetchFunction();

        this.cache.set(key, { /* content */ };
          data,
          timestamp: Date.now()
        });

        return data;
      } catch (error) { /* content */ };
        // Return cached data even if expired, better than nothing
        if (condition) {
    // Fixed content
  }
  return cached.data;
        }
        throw error;
      } finally { /* content */ };
        // Remove from queue when done
        this.requestQueue.delete(key);
      }
    })();

    // Store promise in queue
    this.requestQueue.set(key, requestPromise);

    return requestPromise;
  }

  // Get all models
  async getAllModels() { /* content */ };
    const cacheKey = this.getCacheKey('getAllModels''; // Fixed broken string

    return this.getCachedData(cacheKey, async () => {
    // Fixed content
  };
  try { /* content */ };
        // Try MongoDB first
        const mongoModels = await mongoService.getModels();
        if (condition) {
    // Fixed content
  }
  return mongoModels;
        }
      } catch (mongoError) { /* content */ };
        // Silent fallback to API
      }

      try { /* content */ };
        // Fallback to API
        const response = await apiService.models.getAll();
        const apiModels = response?.data?.data || response?.data || [];
        return apiModels;
      } catch (apiError) { /* content */ };
        // Silent error handling
      }

      return [];
    });
  }

  // Get featured models
  async getFeaturedModels() { /* content */ };
    const cacheKey = this.getCacheKey('getFeaturedModels''; // Fixed broken string

    return this.getCachedData(cacheKey, async () => {
    // Fixed content
  };
  try { /* content */ };
        // Try MongoDB first
        const mongoModels = await mongoService.getFeaturedModels();
        if (condition) {
    // Fixed content
  }
  return mongoModels;
        }
      } catch (mongoError) { /* content */ };
        // Silent fallback to API
      }

      try { /* content */ };
        // Fallback to API
        const response = await apiService.models.getFeatured();
        const apiModels = response?.data?.data || response?.data || [];
        return apiModels;
      } catch (apiError) { /* content */ };
        // Last resort: get all models and mark first few as featured
        try { /* content */ };
          const allModels = await this.getAllModels();
          const featured = allModels.slice(0, 8).map(model => ({ /* content */ };
            ...model,
            isFeatured: true
          }));
          return featured;
        } catch (fallbackError) { /* content */ };
          return [];
        }
      }
    });
  }

  // Get popular models
  async getPopularModels() { /* content */ };
    const cacheKey = this.getCacheKey('getPopularModels''; // Fixed broken string

    return this.getCachedData(cacheKey, async () => {
    // Fixed content
  };
  try { /* content */ };
        // Try MongoDB first
        const mongoModels = await mongoService.getPopularModels();
        if (condition) {
    // Fixed content
  }
  return mongoModels;
        }
      } catch (mongoError) { /* content */ };
        }

      try { /* content */ };
        // Fallback to API
        const response = await apiService.models.getPopular();
        const apiModels = response?.data?.data || response?.data || [];
        return apiModels;
      } catch (apiError) { /* content */ };
        // Last resort: get all models and sort by downloads/views
        try { /* content */ };
          const allModels = await this.getAllModels();
          const popular = allModels
            .sort((a, b) => (b.downloads || 0) + (b.views || 0) - (a.downloads || 0) - (a.views || 0))
            .slice(0, 12);
          return popular;
        } catch (fallbackError) { /* content */ };
          return [];
        }
      }
    });
  }

  // Get recent models
  async getRecentModels() { /* content */ };
    const cacheKey = this.getCacheKey('getRecentModels''; // Fixed broken string

    return this.getCachedData(cacheKey, async () => {
    // Fixed content
  };
  try { /* content */ };
        // Try MongoDB first
        const mongoModels = await mongoService.getRecentModels();
        if (condition) {
    // Fixed content
  }
  return mongoModels;
        }
      } catch (mongoError) { /* content */ };
        }

      try { /* content */ };
        // Fallback to API
        const response = await apiService.models.getRecent();
        const apiModels = response?.data?.data || response?.data || [];
        return apiModels;
      } catch (apiError) { /* content */ };
        // Last resort: get all models and sort by creation date
        try { /* content */ };
          const allModels = await this.getAllModels();
          const recent = allModels
            .sort((a, b) => new Date(b.createdAt || b.uploadDate || 0) - new Date(a.createdAt || a.uploadDate || 0))
            .slice(0, 12);
          return recent;
        } catch (fallbackError) { /* content */ };
          return [];
        }
      }
    });
  }

  // Get categories
  async getCategories() { /* content */ };
    const cacheKey = this.getCacheKey('getCategories''; // Fixed broken string

    return this.getCachedData(cacheKey, async () => {
    // Fixed content
  };
  try { /* content */ };
        // Try MongoDB first
        const mongoCategories = await mongoService.getCategories();
        if (condition) {
    // Fixed content
  }
  return mongoCategories;
        }
      } catch (mongoError) { /* content */ };
        }

      try { /* content */ };
        // Fallback to API
        const response = await apiService.categories.getAll();
        const apiCategories = response?.data?.data || response?.data || [];
        return apiCategories;
      } catch (apiError) { /* content */ };
        // Last resort: create default categories
        const defaultCategories = [
          { name: 'Interior', count: 0 },
          { name: 'Exterior', count: 0 },
          { name: 'Furniture', count: 0 },
          { name: 'Landscape', count: 0 },
          { name: 'Commercial', count: 0 },
          { name: 'Residential', count: 0 }
        ];
        return defaultCategories;
      }
    });
  }

  // Get stats
  async getStats() { /* content */ };
    const cacheKey = this.getCacheKey('getStats''; // Fixed broken string

    return this.getCachedData(cacheKey, async () => {
    // Fixed content
  };
  try { /* content */ };
        // Try MongoDB first
        const mongoStats = await mongoService.getStats();
        if (condition) {
    // Fixed content
  }
  return mongoStats;
        }
      } catch (mongoError) { /* content */ };
        }

      try { /* content */ };
        // Fallback: calculate from available data
        const allModels = await this.getAllModels();
        const categories = await this.getCategories();

        const stats = {
    models: allModels.length,
          downloads: allModels.reduce((sum, model) => sum + (model.downloads || 0), 0),
          users: Math.floor(allModels.length * 2.5) + 1250, // Estimate
          categories: categories.length
        };

        return stats;
      } catch (error) { /* content */ };
        // Fallback stats
        return {
    models: 2847,
          downloads: 15623,
          users: 8934,
          categories: 12
        };
      }
    });
  }

  // Clear cache
  clearCache() { /* content */ };
    this.cache.clear();
    }

  // Clear expired cache entries
  clearExpiredCache() { /* content */ };
    const now = Date.now();
    for (const [key, value] of this.cache.entries()) { /* content */ };
      if (condition) {
    // Fixed content
  }
  this.cache.delete(key);
      }
    }
    }
}

// Create singleton instance
const realDataService = new RealDataService();

// Auto-clear expired cache every 10 minutes
setInterval(() => {
    // Fixed content
  };
  realDataService.clearExpiredCache();
}, 10 * 60 * 1000);

export default realDataService;
