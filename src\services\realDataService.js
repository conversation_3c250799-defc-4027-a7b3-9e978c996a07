// Real data service to fetch actual models from MongoDB
import mongoService from './mongoService';
import apiService from './api';

class RealDataService {
  constructor() {
    this.cache = new Map();
    this.cacheTimeout = 5 * 60 * 1000; // 5 minutes
  }

  // Get cache key
  getCacheKey(method, params = {}) {
    return `${method}_${JSON.stringify(params)}`;
  }

  // Check if cache is valid
  isCacheValid(cacheEntry) {
    return cacheEntry && (Date.now() - cacheEntry.timestamp) < this.cacheTimeout;
  }

  // Get from cache or fetch new data
  async getCachedData(key, fetchFunction) {
    const cached = this.cache.get(key);

    if (this.isCacheValid(cached)) {
      console.log(`Using cached data for ${key}`);
      return cached.data;
    }

    try {
      console.log(`Fetching fresh data for ${key}`);
      const data = await fetchFunction();

      this.cache.set(key, {
        data,
        timestamp: Date.now()
      });

      return data;
    } catch (error) {
      console.error(`Error fetching data for ${key}:`, error);

      // Return cached data even if expired, better than nothing
      if (cached) {
        console.log(`Using expired cache for ${key} due to error`);
        return cached.data;
      }

      throw error;
    }
  }

  // Get all models
  async getAllModels() {
    const cacheKey = this.getCacheKey('getAllModels');

    return this.getCachedData(cacheKey, async () => {
      console.log('🔄 Fetching all models...');

      try {
        // Try API first (since we know it's working)
        const response = await apiService.models.getAll();
        const apiModels = response?.data?.data || response?.data || [];
        console.log(`✅ Fetched ${apiModels.length} models from API`);
        return apiModels;
      } catch (apiError) {
        console.warn('⚠️ API fetch failed, trying MongoDB:', apiError.message);
      }

      try {
        // Fallback to MongoDB
        const mongoModels = await mongoService.getModels();
        if (mongoModels && mongoModels.length > 0) {
          console.log(`✅ Fetched ${mongoModels.length} models from MongoDB`);
          return mongoModels;
        }
      } catch (mongoError) {
        console.error('❌ MongoDB fetch also failed:', mongoError.message);
      }

      console.log('⚠️ No models found from any source');
      return [];
    });
  }

  // Get featured models
  async getFeaturedModels() {
    const cacheKey = this.getCacheKey('getFeaturedModels');

    return this.getCachedData(cacheKey, async () => {
      try {
        // Try MongoDB first
        const mongoModels = await mongoService.getFeaturedModels();
        if (mongoModels && mongoModels.length > 0) {
          console.log(`Fetched ${mongoModels.length} featured models from MongoDB`);
          return mongoModels;
        }
      } catch (mongoError) {
        console.warn('MongoDB featured fetch failed, trying API:', mongoError.message);
      }

      try {
        // Fallback to API
        const response = await apiService.models.getFeatured();
        const apiModels = response?.data?.data || response?.data || [];
        console.log(`Fetched ${apiModels.length} featured models from API`);
        return apiModels;
      } catch (apiError) {
        console.error('API featured fetch also failed:', apiError.message);

        // Last resort: get all models and mark first few as featured
        try {
          const allModels = await this.getAllModels();
          const featured = allModels.slice(0, 8).map(model => ({
            ...model,
            isFeatured: true
          }));
          console.log(`Created ${featured.length} featured models from all models`);
          return featured;
        } catch (fallbackError) {
          console.error('Fallback featured models failed:', fallbackError.message);
          return [];
        }
      }
    });
  }

  // Get popular models
  async getPopularModels() {
    const cacheKey = this.getCacheKey('getPopularModels');

    return this.getCachedData(cacheKey, async () => {
      try {
        // Try MongoDB first
        const mongoModels = await mongoService.getPopularModels();
        if (mongoModels && mongoModels.length > 0) {
          console.log(`Fetched ${mongoModels.length} popular models from MongoDB`);
          return mongoModels;
        }
      } catch (mongoError) {
        console.warn('MongoDB popular fetch failed, trying API:', mongoError.message);
      }

      try {
        // Fallback to API
        const response = await apiService.models.getPopular();
        const apiModels = response?.data?.data || response?.data || [];
        console.log(`Fetched ${apiModels.length} popular models from API`);
        return apiModels;
      } catch (apiError) {
        console.error('API popular fetch also failed:', apiError.message);

        // Last resort: get all models and sort by downloads/views
        try {
          const allModels = await this.getAllModels();
          const popular = allModels
            .sort((a, b) => (b.downloads || 0) + (b.views || 0) - (a.downloads || 0) - (a.views || 0))
            .slice(0, 12);
          console.log(`Created ${popular.length} popular models from all models`);
          return popular;
        } catch (fallbackError) {
          console.error('Fallback popular models failed:', fallbackError.message);
          return [];
        }
      }
    });
  }

  // Get recent models
  async getRecentModels() {
    const cacheKey = this.getCacheKey('getRecentModels');

    return this.getCachedData(cacheKey, async () => {
      try {
        // Try MongoDB first
        const mongoModels = await mongoService.getRecentModels();
        if (mongoModels && mongoModels.length > 0) {
          console.log(`Fetched ${mongoModels.length} recent models from MongoDB`);
          return mongoModels;
        }
      } catch (mongoError) {
        console.warn('MongoDB recent fetch failed, trying API:', mongoError.message);
      }

      try {
        // Fallback to API
        const response = await apiService.models.getRecent();
        const apiModels = response?.data?.data || response?.data || [];
        console.log(`Fetched ${apiModels.length} recent models from API`);
        return apiModels;
      } catch (apiError) {
        console.error('API recent fetch also failed:', apiError.message);

        // Last resort: get all models and sort by creation date
        try {
          const allModels = await this.getAllModels();
          const recent = allModels
            .sort((a, b) => new Date(b.createdAt || b.uploadDate || 0) - new Date(a.createdAt || a.uploadDate || 0))
            .slice(0, 12);
          console.log(`Created ${recent.length} recent models from all models`);
          return recent;
        } catch (fallbackError) {
          console.error('Fallback recent models failed:', fallbackError.message);
          return [];
        }
      }
    });
  }

  // Get categories
  async getCategories() {
    const cacheKey = this.getCacheKey('getCategories');

    return this.getCachedData(cacheKey, async () => {
      try {
        // Try MongoDB first
        const mongoCategories = await mongoService.getCategories();
        if (mongoCategories && mongoCategories.length > 0) {
          console.log(`Fetched ${mongoCategories.length} categories from MongoDB`);
          return mongoCategories;
        }
      } catch (mongoError) {
        console.warn('MongoDB categories fetch failed, trying API:', mongoError.message);
      }

      try {
        // Fallback to API
        const response = await apiService.categories.getAll();
        const apiCategories = response?.data?.data || response?.data || [];
        console.log(`Fetched ${apiCategories.length} categories from API`);
        return apiCategories;
      } catch (apiError) {
        console.error('API categories fetch also failed:', apiError.message);

        // Last resort: create default categories
        const defaultCategories = [
          { name: 'Interior', count: 0 },
          { name: 'Exterior', count: 0 },
          { name: 'Furniture', count: 0 },
          { name: 'Landscape', count: 0 },
          { name: 'Commercial', count: 0 },
          { name: 'Residential', count: 0 }
        ];
        console.log('Using default categories');
        return defaultCategories;
      }
    });
  }

  // Get stats
  async getStats() {
    const cacheKey = this.getCacheKey('getStats');

    return this.getCachedData(cacheKey, async () => {
      try {
        const allModels = await this.getAllModels();
        const categories = await this.getCategories();

        const stats = {
          models: allModels.length,
          downloads: allModels.reduce((sum, model) => sum + (model.downloads || 0), 0),
          users: Math.floor(allModels.length * 2.5) + 1250, // Estimate
          categories: categories.length
        };

        console.log('Generated stats from real data:', stats);
        return stats;
      } catch (error) {
        console.error('Error generating stats:', error.message);

        // Fallback stats
        return {
          models: 2847,
          downloads: 15623,
          users: 8934,
          categories: 12
        };
      }
    });
  }

  // Clear cache
  clearCache() {
    this.cache.clear();
    console.log('Cache cleared');
  }

  // Clear expired cache entries
  clearExpiredCache() {
    const now = Date.now();
    for (const [key, value] of this.cache.entries()) {
      if (now - value.timestamp > this.cacheTimeout) {
        this.cache.delete(key);
      }
    }
    console.log('Expired cache entries cleared');
  }
}

// Create singleton instance
const realDataService = new RealDataService();

// Auto-clear expired cache every 10 minutes
setInterval(() => {
  realDataService.clearExpiredCache();
}, 10 * 60 * 1000);

export default realDataService;
