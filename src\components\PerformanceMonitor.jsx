import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  FiCpu, 
  FiClock, 
  FiDownload, 
  FiBarChart2, 
  FiMaximize2, 
  FiMinimize2,
  FiX,
  FiAlertTriangle,
  FiCheckCircle
} from 'react-icons/fi';
import usePageLoadOptimization from '../hooks/usePageLoadOptimization';

/**
 * Performance Monitor Component
 * Displays real-time performance metrics for the application
 */
const PerformanceMonitor = ({ 
  showInitially = false,
  position = 'bottom-right',
  criticalResources = [],
  nonCriticalResources = []
}) => {
  const [isVisible, setIsVisible] = useState(showInitially);
  const [isExpanded, setIsExpanded] = useState(false);
  const [refreshInterval, setRefreshInterval] = useState(5000);
  const [lastUpdated, setLastUpdated] = useState(new Date());
  
  // Initialize performance optimization hook
  const { 
    isInitialLoadComplete,
    getPerformanceMetrics,
    metrics
  } = usePageLoadOptimization({
    criticalResources,
    nonCriticalResources
  });
  
  // State for current metrics
  const [currentMetrics, setCurrentMetrics] = useState({
    memory: null,
    cpu: null,
    fps: null,
    networkRequests: 0,
    errors: []
  });
  
  // Update metrics periodically
  useEffect(() => {
    if (!isVisible) return;
    
    const updateMetrics = () => {
      // Get performance metrics
      const performanceMetrics = getPerformanceMetrics();
      
      // Get memory usage if available
      let memory = null;
      if (performance.memory) {
        memory = {
          usedJSHeapSize: performance.memory.usedJSHeapSize,
          totalJSHeapSize: performance.memory.totalJSHeapSize,
          jsHeapSizeLimit: performance.memory.jsHeapSizeLimit
        };
      }
      
      // Get network requests
      const networkRequests = performance.getEntriesByType('resource').length;
      
      // Get errors from console
      const errors = window.errors || [];
      
      // Update metrics
      setCurrentMetrics({
        ...currentMetrics,
        memory,
        networkRequests,
        errors
      });
      
      setLastUpdated(new Date());
    };
    
    // Initial update
    updateMetrics();
    
    // Set up interval for updates
    const interval = setInterval(updateMetrics, refreshInterval);
    
    // Cleanup
    return () => clearInterval(interval);
  }, [isVisible, refreshInterval, getPerformanceMetrics, currentMetrics]);
  
  // Format bytes to human-readable format
  const formatBytes = (bytes, decimals = 2) => {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const dm = decimals < 0 ? 0 : decimals;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
  };
  
  // Format time to human-readable format
  const formatTime = (time) => {
    if (!time) return 'N/A';
    
    if (time < 1) {
      return `${(time * 1000).toFixed(2)} μs`;
    } else if (time < 1000) {
      return `${time.toFixed(2)} ms`;
    } else {
      return `${(time / 1000).toFixed(2)} s`;
    }
  };
  
  // Get position classes
  const getPositionClasses = () => {
    switch (position) {
      case 'top-left':
        return 'top-4 left-4';
      case 'top-right':
        return 'top-4 right-4';
      case 'bottom-left':
        return 'bottom-4 left-4';
      case 'bottom-right':
      default:
        return 'bottom-4 right-4';
    }
  };
  
  // Get status indicator
  const getStatusIndicator = () => {
    // Check if any critical metrics are concerning
    const hasCriticalIssues = 
      (metrics.timeToFirstContentfulPaint > 2000) || // Slow FCP
      (metrics.domContentLoaded > 3000) || // Slow DCL
      (currentMetrics.memory && currentMetrics.memory.usedJSHeapSize > 0.8 * currentMetrics.memory.jsHeapSizeLimit) || // High memory usage
      (currentMetrics.errors.length > 0); // Errors
    
    if (hasCriticalIssues) {
      return {
        icon: <FiAlertTriangle className="text-red-500" />,
        text: 'Issues detected',
        color: 'text-red-500'
      };
    }
    
    // Check if any metrics are concerning but not critical
    const hasWarnings =
      (metrics.timeToFirstContentfulPaint > 1000) || // Moderate FCP
      (metrics.domContentLoaded > 1500); // Moderate DCL
    
    if (hasWarnings) {
      return {
        icon: <FiAlertTriangle className="text-yellow-500" />,
        text: 'Performance warnings',
        color: 'text-yellow-500'
      };
    }
    
    // All good
    return {
      icon: <FiCheckCircle className="text-green-500" />,
      text: 'Performance good',
      color: 'text-green-500'
    };
  };
  
  // Toggle visibility
  const toggleVisibility = () => {
    setIsVisible(!isVisible);
  };
  
  // Toggle expanded view
  const toggleExpanded = () => {
    setIsExpanded(!isExpanded);
  };
  
  // Render nothing if not visible
  if (!isVisible) {
    return (
      <button
        onClick={toggleVisibility}
        className={`fixed ${getPositionClasses()} z-50 bg-gray-800 text-white p-2 rounded-full shadow-lg hover:bg-gray-700 transition-colors`}
        aria-label="Show performance monitor"
      >
        <FiCpu className="w-5 h-5" />
      </button>
    );
  }
  
  // Get status indicator
  const statusIndicator = getStatusIndicator();
  
  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: 20 }}
        transition={{ duration: 0.3 }}
        className={`fixed ${getPositionClasses()} z-50 ${
          isExpanded ? 'w-96' : 'w-64'
        } bg-white dark:bg-gray-800 rounded-lg shadow-xl overflow-hidden`}
      >
        {/* Header */}
        <div className="flex justify-between items-center p-3 bg-gray-100 dark:bg-gray-700 border-b border-gray-200 dark:border-gray-600">
          <div className="flex items-center">
            <FiCpu className="w-5 h-5 mr-2 text-gray-600 dark:text-gray-300" />
            <h3 className="text-sm font-medium text-gray-700 dark:text-gray-200">Performance Monitor</h3>
          </div>
          
          <div className="flex items-center space-x-1">
            <button
              onClick={toggleExpanded}
              className="p-1 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
              aria-label={isExpanded ? 'Collapse' : 'Expand'}
            >
              {isExpanded ? <FiMinimize2 className="w-4 h-4" /> : <FiMaximize2 className="w-4 h-4" />}
            </button>
            
            <button
              onClick={toggleVisibility}
              className="p-1 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
              aria-label="Close"
            >
              <FiX className="w-4 h-4" />
            </button>
          </div>
        </div>
        
        {/* Status indicator */}
        <div className="flex items-center p-3 bg-gray-50 dark:bg-gray-750 border-b border-gray-200 dark:border-gray-600">
          <div className="mr-2">
            {statusIndicator.icon}
          </div>
          <div className={`text-sm ${statusIndicator.color}`}>
            {statusIndicator.text}
          </div>
          <div className="ml-auto text-xs text-gray-500 dark:text-gray-400">
            Updated: {lastUpdated.toLocaleTimeString()}
          </div>
        </div>
        
        {/* Metrics */}
        <div className="p-3 overflow-y-auto" style={{ maxHeight: isExpanded ? '400px' : '200px' }}>
          {/* Page Load Metrics */}
          <div className="mb-4">
            <h4 className="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider mb-2">
              Page Load
            </h4>
            
            <div className="space-y-2">
              <div className="flex justify-between items-center">
                <div className="flex items-center text-sm text-gray-600 dark:text-gray-300">
                  <FiClock className="w-4 h-4 mr-1" />
                  <span>First Paint</span>
                </div>
                <div className="text-sm font-medium text-gray-900 dark:text-white">
                  {formatTime(metrics.timeToFirstPaint)}
                </div>
              </div>
              
              <div className="flex justify-between items-center">
                <div className="flex items-center text-sm text-gray-600 dark:text-gray-300">
                  <FiClock className="w-4 h-4 mr-1" />
                  <span>First Contentful Paint</span>
                </div>
                <div className="text-sm font-medium text-gray-900 dark:text-white">
                  {formatTime(metrics.timeToFirstContentfulPaint)}
                </div>
              </div>
              
              <div className="flex justify-between items-center">
                <div className="flex items-center text-sm text-gray-600 dark:text-gray-300">
                  <FiClock className="w-4 h-4 mr-1" />
                  <span>DOM Content Loaded</span>
                </div>
                <div className="text-sm font-medium text-gray-900 dark:text-white">
                  {formatTime(metrics.domContentLoaded)}
                </div>
              </div>
              
              <div className="flex justify-between items-center">
                <div className="flex items-center text-sm text-gray-600 dark:text-gray-300">
                  <FiClock className="w-4 h-4 mr-1" />
                  <span>Window Loaded</span>
                </div>
                <div className="text-sm font-medium text-gray-900 dark:text-white">
                  {formatTime(metrics.windowLoaded)}
                </div>
              </div>
            </div>
          </div>
          
          {/* Resource Metrics */}
          <div className="mb-4">
            <h4 className="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider mb-2">
              Resources
            </h4>
            
            <div className="space-y-2">
              <div className="flex justify-between items-center">
                <div className="flex items-center text-sm text-gray-600 dark:text-gray-300">
                  <FiDownload className="w-4 h-4 mr-1" />
                  <span>Network Requests</span>
                </div>
                <div className="text-sm font-medium text-gray-900 dark:text-white">
                  {currentMetrics.networkRequests}
                </div>
              </div>
              
              {currentMetrics.memory && (
                <div className="flex justify-between items-center">
                  <div className="flex items-center text-sm text-gray-600 dark:text-gray-300">
                    <FiBarChart2 className="w-4 h-4 mr-1" />
                    <span>Memory Usage</span>
                  </div>
                  <div className="text-sm font-medium text-gray-900 dark:text-white">
                    {formatBytes(currentMetrics.memory.usedJSHeapSize)} / {formatBytes(currentMetrics.memory.jsHeapSizeLimit)}
                  </div>
                </div>
              )}
            </div>
          </div>
          
          {/* Errors */}
          {currentMetrics.errors.length > 0 && (
            <div className="mb-4">
              <h4 className="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider mb-2">
                Errors ({currentMetrics.errors.length})
              </h4>
              
              <div className="space-y-2">
                {currentMetrics.errors.slice(0, 3).map((error, index) => (
                  <div key={index} className="text-xs text-red-500 bg-red-50 dark:bg-red-900/20 p-2 rounded">
                    {error.message || String(error)}
                  </div>
                ))}
                
                {currentMetrics.errors.length > 3 && (
                  <div className="text-xs text-gray-500 dark:text-gray-400">
                    And {currentMetrics.errors.length - 3} more errors...
                  </div>
                )}
              </div>
            </div>
          )}
          
          {/* Expanded content */}
          {isExpanded && (
            <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
              <h4 className="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider mb-2">
                Refresh Rate
              </h4>
              
              <div className="flex items-center space-x-2 mb-4">
                <input
                  type="range"
                  min="1000"
                  max="10000"
                  step="1000"
                  value={refreshInterval}
                  onChange={(e) => setRefreshInterval(parseInt(e.target.value))}
                  className="flex-grow"
                />
                <span className="text-xs text-gray-500 dark:text-gray-400 w-16 text-right">
                  {refreshInterval / 1000}s
                </span>
              </div>
              
              <button
                onClick={() => {
                  // Clear performance entries
                  performance.clearResourceTimings();
                  // Reset metrics
                  setCurrentMetrics({
                    ...currentMetrics,
                    networkRequests: 0,
                    errors: []
                  });
                }}
                className="w-full px-3 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors text-sm"
              >
                Reset Metrics
              </button>
            </div>
          )}
        </div>
      </motion.div>
    </AnimatePresence>
  );
};

export default PerformanceMonitor;
