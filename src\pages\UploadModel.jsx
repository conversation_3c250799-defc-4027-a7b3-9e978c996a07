import React, { useState, useEffect, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import {
  FiUpload,
  FiImage,
  FiFile,
  FiTag,
  FiInfo,
  FiDollarSign,
  FiCheck,
  FiX,
  FiAlertTriangle,
  FiShield,
  FiLink
} from 'react-icons/fi';
import Header from '../components/Header';
import Footer from '../components/Footer';
import { useAuth } from '../context/AuthContext';
import { useModels } from '../context/ModelContext';
import toast from 'react-hot-toast';

const UploadModel = () => {
  const navigate = useNavigate();
  const { currentUser, hasPremiumAccess, isAdmin, hasPermission } = useAuth();
  const { categories, loading, uploadModel } = useModels();

  // Check if user is authenticated and has admin privileges
  useEffect(() => {
  if (cachedData && !isExpired(cachedData)) {
  toast.error('Please log in to access this page');
      navigate('/login', { state: { from: '/upload' } });
      return;
    }

    // Check if user is admin or has upload permission
    if (!isAdmin() && !hasPermission('upload')) {
      toast.error('Only administrators can upload models');
      navigate('/');
    }

    // Check for URL method parameter
    const params = new URLSearchParams(window.location.search);
    if (params.get('method') === 'url') {
      setIsUrlUpload(true);
    }
  }, [currentUser, navigate, isAdmin, hasPermission]);

  // Form state
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [category, setCategory] = useState('');
  const [subcategory, setSubcategory] = useState('');
  const [tags, setTags] = useState('');
  const [isPremium, setIsPremium] = useState(false);
  const [price, setPrice] = useState('');
  const [modelFile, setModelFile] = useState(null);
  const [previewImage, setPreviewImage] = useState(null);
  const [previewModel, setPreviewModel] = useState(null);
  const [dimensions, setDimensions] = useState({ width: '', height: '', depth: '', unit: 'm' });
  const [isTextured, setIsTextured] = useState(false);
  const [isRigged, setIsRigged] = useState(false);
  const [isAnimated, setIsAnimated] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [isUploading, setIsUploading] = useState(false);
  const [errors, setErrors] = useState({});

  // URL-based upload state
  const [isUrlUpload, setIsUrlUpload] = useState(false);
  const [modelFileUrl, setModelFileUrl] = useState('');
  const [previewImageUrl, setPreviewImageUrl] = useState('');
  const [previewModelUrl, setPreviewModelUrl] = useState('');
  const [isValidatingUrls, setIsValidatingUrls] = useState(false);

  // Handle file selection
  const handleFileChange = (e, fileType) => {
  const file = e.target.files[0];
    if (!file) return;

    // Reset URL upload mode when selecting files
    setIsUrlUpload(false);

    switch (fileType) {
      case 'model':
        setModelFile(file);
        setModelFileUrl('');
        break;
      case 'preview':
        setPreviewImage(file);
        // Create preview URL for display
        const imageUrl = URL.createObjectURL(file);
        setPreviewImageUrl(imageUrl);
        break;
      case 'previewModel':
        setPreviewModel(file);
        setPreviewModelUrl(''); 
        break;
      default:
        break;
    }
  };

  // Handle URL input change
  const handleUrlChange = (e, urlType) => {
  const url = e.target.value;

    // Set URL upload mode when entering URLs
    if (cachedData && !isExpired(cachedData)) {
  setIsUrlUpload(true);
    }

    switch (urlType) {
      case 'model':
        setModelFileUrl(url);
        setModelFile(null);
        break;
      case 'preview':
        setPreviewImageUrl(url);
        setPreviewImage(null);
        break;
      case 'previewModel':
        setPreviewModelUrl(url);
        setPreviewModel(null);
        break;
      default:
        break;
    }
  };

  // Validate URL format
  const isValidUrl = (url) => {
  try {
      new URL(url);
      return true;
    } catch (e) {
      return false;
    }
  };

  // Handle dimension change
  const handleDimensionChange = (e, dimension) => {
  setDimensions({
      ...dimensions,
      [dimension]: e.target.value
    });
  };

  // Validate form
  const validateForm = () => {
    const newErrors = {};

    // Basic validation
    if (!title.trim()) newErrors.title = 'Title is required';
    if (!description.trim()) newErrors.description = 'Description is required';
    if (!category) newErrors.category = 'Category is required';

    // File or URL validation for model file
    if (cachedData && !isExpired(cachedData)) {
  if (!modelFileUrl) {
        newErrors.modelFileUrl = 'Model file URL is required';
      } else if (!isValidUrl(modelFileUrl)) {
        newErrors.modelFileUrl = 'Please enter a valid URL';
      }

      if (cachedData && !isExpired(cachedData)) {
  newErrors.previewImageUrl = 'Preview image URL is required';
      } else if (!isValidUrl(previewImageUrl)) {
        newErrors.previewImageUrl = 'Please enter a valid URL';
      }

      if (previewModelUrl && !isValidUrl(previewModelUrl)) {
        newErrors.previewModelUrl = 'Please enter a valid URL';
      }
    } else {
      if (!modelFile) newErrors.modelFile = 'Model file is required';
      if (!previewImage) newErrors.previewImage = 'Preview image is required';
    }

    // Price validation for premium models
    if (isPremium && (!price || isNaN(price) || parseFloat(price) <= 0)) {
      newErrors.price = 'Valid price is required for premium models';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle form submission
  const handleSubmit = async (e) => {
  e.preventDefault();

    if (!validateForm()) {
      toast.error('Please fix the errors in the form');
      return;
    }

    try {
      setIsUploading(true);

      // Prepare common model data
      const modelData = {
        title,
        description,
        category,
        subcategory: subcategory || undefined,
        tags: tags.split(',').map(tag => tag.trim()).filter(tag => tag),
        isPremium,
        price: isPremium ? parseFloat(price) : undefined,
        dimensions: {
    width: parseFloat(dimensions.width) || undefined,
          height: parseFloat(dimensions.height) || undefined,
          depth: parseFloat(dimensions.depth) || undefined,
          unit: dimensions.unit
        },
        textured: isTextured,
        rigged: isRigged,
        animated: isAnimated
      };

      // Set up progress tracking
      const progressInterval = setInterval(() => {
  setUploadProgress(prev => {
  if (cachedData && !isExpired(cachedData)) {
  clearInterval(progressInterval);
            return prev;
          }
          return prev + 5;
        });
      }, 300);

      let result;

      // Handle URL-based upload
      if (isUrlUpload) {
        // Add URLs to model data
        modelData.modelFileUrl = modelFileUrl;
        modelData.previewImageUrl = previewImageUrl;
        if (cachedData && !isExpired(cachedData)) {
  modelData.previewModelUrl = previewModelUrl;
        }

        // Upload model with URLs
        result = await uploadModel(modelData);
      } else {
        // Prepare files for traditional upload
        const files = {
          modelFile,
          previewImage,
          previewModel: previewModel || undefined
        };

        // Upload model with files
        result = await uploadModel(modelData, files);
      }

      clearInterval(progressInterval);
      setUploadProgress(100);

      toast.success('Model uploaded successfully!');
      // Navigate to model detail page
      setTimeout(() => {
  navigate(`/model/${result.id}`);
      }, 1000);
    } catch (err) {
      toast.error('Failed to upload model. Please try again.');
      } finally {
      setIsUploading(false);
    }
  };

  // Clean up preview URLs on unmount
  useEffect(() => {
  return () => {
  if (cachedData && !isExpired(cachedData)) {
  URL.revokeObjectURL(previewImageUrl);
      }
    };
  }, [previewImageUrl]);

  return (
    <div className="flex flex-col min-h-screen bg-gray-50 dark:bg-gray-900">
      <Header />

      <main className="flex-grow container mx-auto px-4 py-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="max-w-4xl mx-auto"
        >
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg overflow-hidden">
            <div className="p-6 border-b border-gray-200 dark:border-gray-700">
              <div className="flex items-center justify-between">
                <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Upload 3D Model</h1>
                <div className="flex items-center bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-3 py-1 rounded-full text-sm">
                  <FiShield className="mr-1" />
                  Admin Only
                </div>
              </div>
              <p className="text-gray-600 dark:text-gray-300 mt-2">
                Share your 3D models with the community. High-quality models have a better chance of being featured.
              </p>
              <div className="mt-4 p-4 bg-gray-50 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-md">
                <div className="flex">
                  <FiInfo className="h-5 w-5 text-blue-500 mr-2 flex-shrink-0" />
                  <p className="text-sm text-gray-700 dark:text-gray-300">
                    As an administrator, you have exclusive access to upload models to the platform.
                    Regular users can only browse and download models.
                  </p>
                </div>
              </div>
            </div>

            <form onSubmit={handleSubmit} className="p-6">
              {/* Basic Information */}
              <div className="mb-8">
                <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">Basic Information</h2>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Title */}
                  <div className="col-span-2">
                    <label htmlFor="title" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Title <span className="text-red-500">*</span>
                    </label>
                    <input
                      type="text"
                      id="title"
                      value={title}
                      onChange={(e) => setTitle(e.target.value)}
                      className={`w-full px-4 py-2 border rounded-md focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ${
                        errors.title ? 'border-red-500' : 'border-gray-300'
                      }`}
                      placeholder="Enter a descriptive title"
                    />
                    {errors.title && <p className="mt-1 text-sm text-red-500">{errors.title}</p>}
                  </div>

                  {/* Description */}
                  <div className="col-span-2">
                    <label htmlFor="description" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Description <span className="text-red-500">*</span>
                    </label>
                    <textarea
                      id="description"
                      value={description}
                      onChange={(e) => setDescription(e.target.value)}
                      rows={4}
                      className={`w-full px-4 py-2 border rounded-md focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ${
                        errors.description ? 'border-red-500' : 'border-gray-300'
                      }`}
                      placeholder="Provide a detailed description of your model"
                    />
                    {errors.description && <p className="mt-1 text-sm text-red-500">{errors.description}</p>}
                  </div>

                  {/* Category */}
                  <div>
                    <label htmlFor="category" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Category <span className="text-red-500">*</span>
                    </label>
                    <select
                      id="category"
                      value={category}
                      onChange={(e) => setCategory(e.target.value)}
                      className={`w-full px-4 py-2 border rounded-md focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ${
                        errors.category ? 'border-red-500' : 'border-gray-300'
                      }`}
                    >
                      <option value="">Select a category</option>
                      {categories.map((cat) => (
                        <option key={cat.id} value={cat.id}>
                          {cat.name}
                        </option>
                      ))}
                    </select>
                    {errors.category && <p className="mt-1 text-sm text-red-500">{errors.category}</p>}
                  </div>

                  {/* Subcategory */}
                  <div>
                    <label htmlFor="subcategory" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Subcategory
                    </label>
                    <input
                      type="text"
                      id="subcategory"
                      value={subcategory}
                      onChange={(e) => setSubcategory(e.target.value)}
                      className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                      placeholder="E.g., Living Room, Kitchen, etc."
                    />
                  </div>

                  {/* Tags */}
                  <div className="col-span-2">
                    <label htmlFor="tags" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Tags
                    </label>
                    <input
                      type="text"
                      id="tags"
                      value={tags}
                      onChange={(e) => setTags(e.target.value)}
                      className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                      placeholder="Enter tags separated by commas (e.g., modern, furniture, kitchen)"
                    />
                    <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                      Tags help users find your model. Add relevant keywords separated by commas.
                    </p>
                  </div>
                </div>
              </div>

              {/* Files */}
              <div className="mb-8">
                <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">Files</h2>

                {/* Upload Method Toggle */}
                <div className="mb-4">
                  <div className="flex items-center justify-center space-x-4">
                    <button
                      type="button"
                      onClick={() => setIsUrlUpload(false)}
                      className={`px-4 py-2 rounded-md ${
                        !isUrlUpload
                          ? 'bg-blue-600 text-white'
                          : 'bg-gray-200 text-gray-700 dark:bg-gray-700 dark:text-gray-300'
                      }`}
                    >
                      <FiUpload className="inline-block mr-2" />
                      Upload Files
                    </button>
                    <button
                      type="button"
                      onClick={() => setIsUrlUpload(true)}
                      className={`px-4 py-2 rounded-md ${
                        isUrlUpload
                          ? 'bg-blue-600 text-white'
                          : 'bg-gray-200 text-gray-700 dark:bg-gray-700 dark:text-gray-300'
                      }`}
                    >
                      <FiLink className="inline-block mr-2" />
                      Use URLs
                    </button>
                  </div>
                </div>

                {isUrlUpload ? (
                  // URL-based upload form
                  <div className="grid grid-cols-1 gap-6">
                    {/* Model File URL */}
                    <div>
                      <label htmlFor="modelFileUrl" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        Model File URL <span className="text-red-500">*</span>
                      </label>
                      <input
                        type="url"
                        id="modelFileUrl"
                        value={modelFileUrl}
                        onChange={(e) => handleUrlChange(e, 'model')}
                        placeholder="https://example.com/model.skp"
                        className={`w-full px-4 py-2 border rounded-md focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ${
                          errors.modelFileUrl ? 'border-red-500' : 'border-gray-300'
                        }`}
                      />
                      {errors.modelFileUrl && <p className="mt-1 text-sm text-red-500">{errors.modelFileUrl}</p>}
                      <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                        Supported formats: SKP, MAX, BLEND, FBX, OBJ, GLTF, GLB, ZIP
                      </p>
                    </div>

                    {/* Preview Image URL */}
                    <div>
                      <label htmlFor="previewImageUrl" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        Preview Image URL <span className="text-red-500">*</span>
                      </label>
                      <input
                        type="url"
                        id="previewImageUrl"
                        value={previewImageUrl}
                        onChange={(e) => handleUrlChange(e, 'preview')}
                        placeholder="https://example.com/preview.jpg"
                        className={`w-full px-4 py-2 border rounded-md focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ${
                          errors.previewImageUrl ? 'border-red-500' : 'border-gray-300'
                        }`}
                      />
                      {errors.previewImageUrl && <p className="mt-1 text-sm text-red-500">{errors.previewImageUrl}</p>}
                      <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                        Supported formats: JPG, PNG, WEBP
                      </p>

                      {/* Preview image display */}
                      {previewImageUrl && isValidUrl(previewImageUrl) && (
                        <div className="mt-2 p-2 border border-gray-300 dark:border-gray-600 rounded-md">
                          <img
                            src={previewImageUrl}
                            alt="Preview"
                            className="max-h-32 mx-auto object-contain"
                            onError={(e) => {
  e.target.onerror = null;
                              e.target.src = 'https://via.placeholder.com/150?text=Invalid+Image';
                            }}
                          />
                        </div>
                      )}
                    </div>

                    {/* 3D Preview Model URL */}
                    <div>
                      <label htmlFor="previewModelUrl" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        3D Preview Model URL (Optional)
                      </label>
                      <input
                        type="url"
                        id="previewModelUrl"
                        value={previewModelUrl}
                        onChange={(e) => handleUrlChange(e, 'previewModel')}
                        placeholder="https://example.com/preview.glb"
                        className={`w-full px-4 py-2 border rounded-md focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ${
                          errors.previewModelUrl ? 'border-red-500' : 'border-gray-300'
                        }`}
                      />
                      {errors.previewModelUrl && <p className="mt-1 text-sm text-red-500">{errors.previewModelUrl}</p>}
                      <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                        For 3D preview on the website. GLTF/GLB format only.
                      </p>
                    </div>
                  </div>
                ) : (
                  // File upload form
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {/* Model File */}
                    <div className="col-span-2">
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        Model File <span className="text-red-500">*</span>
                      </label>
                      <div className={`border-2 border-dashed rounded-md p-4 text-center ${
                        errors.modelFile ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'
                      }`}>
                        {modelFile ? (
                          <div className="flex items-center justify-center">
                            <FiFile className="h-8 w-8 text-blue-500 mr-2" />
                            <div className="text-left">
                              <p className="text-sm font-medium text-gray-900 dark:text-white">{modelFile.name}</p>
                              <p className="text-xs text-gray-500 dark:text-gray-400">
                                {(modelFile.size / (1024 * 1024)).toFixed(2)} MB
                              </p>
                            </div>
                            <button
                              type="button"
                              onClick={() => setModelFile(null)}
                              className="ml-auto text-gray-500 hover:text-red-500"
                            >
                              <FiX className="h-5 w-5" />
                            </button>
                          </div>
                        ) : (
                          <div>
                            <FiUpload className="mx-auto h-12 w-12 text-gray-400" />
                            <p className="mt-2 text-sm text-gray-600 dark:text-gray-400">
                              Click to upload or drag and drop
                            </p>
                            <p className="text-xs text-gray-500 dark:text-gray-500">
                              SKP, MAX, BLEND, FBX, OBJ, GLTF, GLB (max 100MB)
                            </p>
                            <input
                              type="file"
                              className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                              onChange={(e) => handleFileChange(e, 'model')}
                              accept=".skp,.max,.blend,.fbx,.obj,.gltf,.glb"
                            />
                          </div>
                        )}
                      </div>
                      {errors.modelFile && <p className="mt-1 text-sm text-red-500">{errors.modelFile}</p>}
                    </div>

                    {/* Preview Image */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        Preview Image <span className="text-red-500">*</span>
                      </label>
                      <div className={`border-2 border-dashed rounded-md p-4 text-center ${
                        errors.previewImage ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'
                      }`}>
                        {previewImage ? (
                          <div className="relative">
                            <img
                              src={previewImageUrl}
                              alt="Preview"
                              className="mx-auto max-h-32 object-contain"
                            />
                            <button
                              type="button"
                              onClick={() => {
  setPreviewImage(null);
                                setPreviewImageUrl('; 
                              }}
                              className="absolute top-0 right-0 bg-red-500 text-white rounded-full p-1"
                            >
                              <FiX className="h-4 w-4" />
                            </button>
                          </div>
                        ) : (
                          <div>
                            <FiImage className="mx-auto h-12 w-12 text-gray-400" />
                            <p className="mt-2 text-sm text-gray-600 dark:text-gray-400">
                              Upload preview image
                            </p>
                            <p className="text-xs text-gray-500 dark:text-gray-500">
                              JPG, PNG, WEBP (max 5MB)
                            </p>
                            <input
                              type="file"
                              className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                              onChange={(e) => handleFileChange(e, 'preview')}
                              accept="image/jpeg,image/png,image/webp"
                            />
                          </div>
                        )}
                      </div>
                      {errors.previewImage && <p className="mt-1 text-sm text-red-500">{errors.previewImage}</p>}
                    </div>

                    {/* 3D Preview Model */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        3D Preview Model (Optional)
                      </label>
                      <div className="border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-md p-4 text-center">
                        {previewModel ? (
                          <div className="flex items-center justify-center">
                            <FiFile className="h-8 w-8 text-blue-500 mr-2" />
                            <div className="text-left">
                              <p className="text-sm font-medium text-gray-900 dark:text-white">{previewModel.name}</p>
                              <p className="text-xs text-gray-500 dark:text-gray-400">
                                {(previewModel.size / (1024 * 1024)).toFixed(2)} MB
                              </p>
                            </div>
                            <button
                              type="button"
                              onClick={() => setPreviewModel(null)}
                              className="ml-auto text-gray-500 hover:text-red-500"
                            >
                              <FiX className="h-5 w-5" />
                            </button>
                          </div>
                        ) : (
                          <div>
                            <FiUpload className="mx-auto h-12 w-12 text-gray-400" />
                            <p className="mt-2 text-sm text-gray-600 dark:text-gray-400">
                              Upload 3D preview model
                            </p>
                            <p className="text-xs text-gray-500 dark:text-gray-500">
                              GLTF or GLB format only (max 20MB)
                            </p>
                            <input
                              type="file"
                              className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                              onChange={(e) => handleFileChange(e, 'previewModel')}
                              accept=".gltf,.glb"
                            />
                          </div>
                        )}
                      </div>
                      <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                        For 3D preview on the website. GLTF/GLB format recommended.
                      </p>
                    </div>
                  </div>
                )}
              </div>

              {/* Model Details */}
              <div className="mb-8">
                <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">Model Details</h2>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  {/* Dimensions */}
                  <div className="col-span-3">
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Dimensions (Optional)
                    </label>
                    <div className="grid grid-cols-4 gap-2">
                      <div>
                        <label htmlFor="width" className="block text-xs text-gray-500 dark:text-gray-400 mb-1">
                          Width
                        </label>
                        <input
                          type="number"
                          id="width"
                          value={dimensions.width}
                          onChange={(e) => handleDimensionChange(e, 'width')}
                          min="0"
                          step="0.01"
                          className="w-full px-3 py-1.5 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                        />
                      </div>
                      <div>
                        <label htmlFor="height" className="block text-xs text-gray-500 dark:text-gray-400 mb-1">
                          Height
                        </label>
                        <input
                          type="number"
                          id="height"
                          value={dimensions.height}
                          onChange={(e) => handleDimensionChange(e, 'height')}
                          min="0"
                          step="0.01"
                          className="w-full px-3 py-1.5 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                        />
                      </div>
                      <div>
                        <label htmlFor="depth" className="block text-xs text-gray-500 dark:text-gray-400 mb-1">
                          Depth
                        </label>
                        <input
                          type="number"
                          id="depth"
                          value={dimensions.depth}
                          onChange={(e) => handleDimensionChange(e, 'depth')}
                          min="0"
                          step="0.01"
                          className="w-full px-3 py-1.5 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                        />
                      </div>
                      <div>
                        <label htmlFor="unit" className="block text-xs text-gray-500 dark:text-gray-400 mb-1">
                          Unit
                        </label>
                        <select
                          id="unit"
                          value={dimensions.unit}
                          onChange={(e) => handleDimensionChange(e, 'unit')}
                          className="w-full px-3 py-1.5 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                        >
                          <option value="mm">mm</option>
                          <option value="cm">cm</option>
                          <option value="m">m</option>
                          <option value="in">in</option>
                          <option value="ft">ft</option>
                        </select>
                      </div>
                    </div>
                  </div>

                  {/* Model Properties */}
                  <div className="col-span-3">
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Model Properties
                    </label>
                    <div className="flex flex-wrap gap-4">
                      <div className="flex items-center">
                        <input
                          type="checkbox"
                          id="textured"
                          checked={isTextured}
                          onChange={(e) => setIsTextured(e.target.checked)}
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                        />
                        <label htmlFor="textured" className="ml-2 text-sm text-gray-700 dark:text-gray-300">
                          Textured
                        </label>
                      </div>
                      <div className="flex items-center">
                        <input
                          type="checkbox"
                          id="rigged"
                          checked={isRigged}
                          onChange={(e) => setIsRigged(e.target.checked)}
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                        />
                        <label htmlFor="rigged" className="ml-2 text-sm text-gray-700 dark:text-gray-300">
                          Rigged
                        </label>
                      </div>
                      <div className="flex items-center">
                        <input
                          type="checkbox"
                          id="animated"
                          checked={isAnimated}
                          onChange={(e) => setIsAnimated(e.target.checked)}
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                        />
                        <label htmlFor="animated" className="ml-2 text-sm text-gray-700 dark:text-gray-300">
                          Animated
                        </label>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Pricing */}
              <div className="mb-8">
                <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">Pricing</h2>

                {!hasPremiumAccess() && (
                  <div className="mb-4 p-4 bg-yellow-50 dark:bg-yellow-900/30 border border-yellow-200 dark:border-yellow-700 rounded-md">
                    <div className="flex">
                      <FiAlertTriangle className="h-5 w-5 text-yellow-500 mr-2 flex-shrink-0" />
                      <p className="text-sm text-yellow-700 dark:text-yellow-400">
                        You need a premium subscription to upload premium (paid) models.{' '}
                        <a href="/subscription" className="font-medium underline">
                          Upgrade your subscription
                        </a>
                      </p>
                    </div>
                  </div>
                )}

                <div className="flex items-center mb-4">
                  <input
                    type="checkbox"
                    id="isPremium"
                    checked={isPremium}
                    onChange={(e) => setIsPremium(e.target.checked)}
                    disabled={!hasPremiumAccess()}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <label htmlFor="isPremium" className="ml-2 text-sm text-gray-700 dark:text-gray-300">
                    This is a premium (paid) model
                  </label>
                </div>

                {isPremium && (
                  <div>
                    <label htmlFor="price" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Price (USD) <span className="text-red-500">*</span>
                    </label>
                    <div className="relative">
                      <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <span className="text-gray-500 dark:text-gray-400">$</span>
                      </div>
                      <input
                        type="number"
                        id="price"
                        value={price}
                        onChange={(e) => setPrice(e.target.value)}
                        min="0.99"
                        step="0.01"
                        className={`w-full pl-8 pr-4 py-2 border rounded-md focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white ${
                          errors.price ? 'border-red-500' : 'border-gray-300'
                        }`}
                        placeholder="9.99"
                        disabled={!hasPremiumAccess()}
                      />
                    </div>
                    {errors.price && <p className="mt-1 text-sm text-red-500">{errors.price}</p>}
                    <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                      You will receive 70% of the price after platform fees.
                    </p>
                  </div>
                )}
              </div>

              {/* Submit Button */}
              <div className="flex justify-end">
                {isUploading ? (
                  <div className="w-full">
                    <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2.5 mb-2">
                      <div
                        className="bg-blue-600 h-2.5 rounded-full"
                        style={{ width: `${uploadProgress}%` }}
                      ></div>
                    </div>
                    <p className="text-sm text-gray-600 dark:text-gray-400 text-center">
                      Uploading... {uploadProgress}%
                    </p>
                  </div>
                ) : (
                  <button
                    type="submit"
                    className="px-6 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md font-medium flex items-center"
                    disabled={loading}
                  >
                    <FiUpload className="mr-2" />
                    Upload Model
                  </button>
                )}
              </div>
            </form>
          </div>
        </motion.div>
      </main>

      <Footer />
    </div>
  );
};

export default UploadModel;
