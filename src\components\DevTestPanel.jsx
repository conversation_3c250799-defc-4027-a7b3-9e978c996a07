import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { <PERSON>Check, FiX, FiRefreshC<PERSON>, <PERSON>Eye, FiEyeOff } from 'react-icons/fi';
import { testHomepageComponents, testComponentData } from '../utils/homepageTest';

const DevTestPanel = () => {
  const [isVisible, setIsVisible] = useState(false);
  const [testResults, setTestResults] = useState(null);
  const [isRunning, setIsRunning] = useState(false);
  const [lastRun, setLastRun] = useState(null);

  // Only show in development mode
  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  const runTests = async () => {
    setIsRunning(true);
    try {
      const results = await testHomepageComponents();
      setTestResults(results);
      setLastRun(new Date());
    } catch (error) {
      } finally {
      setIsRunning(false);
    }
  };

  const testComponent = async (componentName) => {
    const data = await testComponentData(componentName);
    };

  const components = [
    'ModelPreviewCarousel',
    'FloatingModelPreviews', 
    'ModelMasonryGallery',
    'AnimatedModelCounter',
    'PopularModels',
    'RecentModels',
    'StatisticsDisplay'
  ];

  return (
    <>
      {/* Toggle Button */}
      <motion.button
        onClick={() => setIsVisible(!isVisible)}
        className="fixed bottom-4 right-4 z-50 w-12 h-12 bg-blue-600 hover:bg-blue-700 text-white rounded-full shadow-lg flex items-center justify-center"
        whileHover={{ scale: 1.1 }}
        whileTap={{ scale: 0.9 }}
      >
        {isVisible ? <FiEyeOff /> : <FiEye />}
      </motion.button>

      {/* Test Panel */}
      <AnimatePresence>
        {isVisible && (
          <motion.div
            initial={{ opacity: 0, x: 300 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: 300 }}
            className="fixed top-4 right-4 z-40 w-80 bg-white dark:bg-gray-800 rounded-lg shadow-xl border border-gray-200 dark:border-gray-700 max-h-96 overflow-y-auto"
          >
            <div className="p-4 border-b border-gray-200 dark:border-gray-700">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-bold text-gray-900 dark:text-white">
                  🧪 Dev Test Panel
                </h3>
                <button
                  onClick={runTests}
                  disabled={isRunning}
                  className="px-3 py-1 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white rounded text-sm flex items-center"
                >
                  <FiRefreshCw className={`mr-1 ${isRunning ? 'animate-spin' : '}`} />
                  {isRunning ? 'Testing...' : 'Run Tests'}
                </button>
              </div>

              {lastRun && (
                <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                  Last run: {lastRun.toLocaleTimeString()}
                </p>
              )}
            </div>

            <div className="p-4">
              {/* Overall Test Results */}
              {testResults && (
                <div className="mb-4">
                  <h4 className="font-semibold text-gray-900 dark:text-white mb-2">
                    Overall Results
                  </h4>
                  <div className="space-y-1">
                    {Object.entries(testResults).map(([key, value]) => (
                      <div key={key} className="flex items-center justify-between text-sm">
                        <span className="text-gray-600 dark:text-gray-400">{key}</span>
                        <span className={`flex items-center ${value ? 'text-green-600' : 'text-red-600'}`}>
                          {value ? <FiCheck className="w-4 h-4" /> : <FiX className="w-4 h-4" />}
                          {value ? 'PASS' : 'FAIL'}
                        </span>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Component Tests */}
              <div>
                <h4 className="font-semibold text-gray-900 dark:text-white mb-2">
                  Component Tests
                </h4>
                <div className="space-y-1">
                  {components.map((component) => (
                    <button
                      key={component}
                      onClick={() => testComponent(component)}
                      className="w-full text-left px-2 py-1 text-sm text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 rounded"
                    >
                      Test {component}
                    </button>
                  ))}
                </div>
              </div>

              {/* Quick Actions */}
              <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
                <h4 className="font-semibold text-gray-900 dark:text-white mb-2">
                  Quick Actions
                </h4>
                <div className="space-y-1">
                  <button
                    onClick={() => window.location.reload()}
                    className="w-full text-left px-2 py-1 text-sm text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 rounded"
                  >
                    🔄 Reload Page
                  </button>
                  <button
                    onClick={() => console.clear()}
                    className="w-full text-left px-2 py-1 text-sm text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 rounded"
                  >
                    🧹 Clear Console
                  </button>
                  <button
                    onClick={() => {
                      localStorage.clear();
                      sessionStorage.clear();
                      }}
                    className="w-full text-left px-2 py-1 text-sm text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 rounded"
                  >
                    🗑️ Clear Storage
                  </button>
                </div>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
};

export default DevTestPanel;
