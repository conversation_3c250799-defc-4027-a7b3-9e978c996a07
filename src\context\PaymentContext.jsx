import React, { useState, useContext, useEffect, useCallback, useRef } from 'react';
import axios from 'axios';
import { useAuth } from './AuthContext';
import toast from 'react-hot-toast';

// Create the payment context
const PaymentContext = React.createContext();

// Custom hook to use the payment context
export const usePayment = () => useContext(PaymentContext);

// Backend API URL
const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:5002/api';

const useMockData = false;

// Subscription plans
export const SUBSCRIPTION_PLANS = {
    free: {
    id: 'free',
    name: 'Free',
    description: 'Basic access with limited features',
    monthlyPrice: 0,
    yearlyPrice: 0,
    features: [
      '3 downloads per month',
      'Access to free models only',
      'Community support',
      'Personal use only',
      'Standard resolution downloads'
    ],
    downloadLimit: 3,
    badge: 'Free',
    color: 'gray'
  },
  basic: {
    id: 'basic',
    name: 'Basic',
    description: 'Perfect for beginners',
    monthlyPrice: 9.99,
    yearlyPrice: 99.99,
    features: [
      '20 downloads per month',
      'Access to basic models',
      'Standard support',
      'Personal use only',
      'HD resolution downloads',
      'No watermarks'
    ],
    downloadLimit: 20,
    badge: 'Popular',
    color: 'blue'
  },
  premium: {
    id: 'premium',
    name: 'Premium',
    description: 'For serious designers',
    monthlyPrice: 19.99,
    yearlyPrice: 199.99,
    features: [
      '50 downloads per month',
      'Access to premium models',
      'Priority support',
      'Personal and small business use',
      'HD resolution downloads',
      'No watermarks',
      'Access to model source files'
    ],
    downloadLimit: 50,
    badge: 'Best Value',
    color: 'purple'
  },
  professional: {
    id: 'professional',
    name: 'Professional',
    description: 'For professional use',
    monthlyPrice: 29.99,
    yearlyPrice: 299.99,
    features: [
      'Unlimited downloads',
      'Access to all models including exclusive content',
      'Priority support with 24h response time',
      'Commercial use license',
      '4K resolution downloads',
      'No watermarks',
      'Access to model source files',
      'Bulk download capability',
      'API access'
    ],
    downloadLimit: 999999, // Effectively unlimited
    badge: 'Pro',
    color: 'gold'
  }
};

export const PaymentProvider = ({ children }) => {
  const { currentUser, updateProfile } = useAuth();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [paymentHistory, setPaymentHistory] = useState([]);
  const [subscription, setSubscription] = useState(null);
  const [invoices, setInvoices] = useState([]);
  const [paymentMethods, setPaymentMethods] = useState([]);
  const [billingAddress, setBillingAddress] = useState(null);
  const [subscriptionStatus, setSubscriptionStatus] = useState('inactive'); 
  const [remainingDownloads, setRemainingDownloads] = useState(0);

  // Add caching and debouncing with useRef to prevent infinite loops
  const lastFetchTime = useRef(0);
  const isInitialized = useRef(false);
  const fetchingInProgress = useRef(false);
  const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes cache

  // TEMPORARILY DISABLE AUTO-FETCHING TO STOP API SPAM
  // Load subscription data when user changes (DISABLED to prevent API spam)
  useEffect(() => {
    if (currentUser && !isInitialized.current) {
      // Set basic subscription data from user object without API calls
      const userSubscription = currentUser?.subscription || { type: 'free', status: 'active' };
      const basicSubscription = {
    id: 'sub_' + Date.now(),
        plan: userSubscription.type,
        status: userSubscription.status,
        startDate: userSubscription.startDate || new Date().toISOString(),
        endDate: userSubscription.endDate || new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
        price: SUBSCRIPTION_PLANS[userSubscription.type]?.monthlyPrice || 0,
        interval: 'monthly',
        features: SUBSCRIPTION_PLANS[userSubscription.type]?.features || []
      };

      setSubscription(basicSubscription);
      setPaymentHistory([]);
      setInvoices([]);
      setPaymentMethods([]);

      // Set remaining downloads from user data
      if (currentUser.downloadCredits) {
        setRemainingDownloads(currentUser.downloadCredits);
      }

      isInitialized.current = true;
    } else if (!currentUser) {
      // Reset state when user logs out
      setSubscription(null);
      setPaymentHistory([]);
      setInvoices([]);
      setPaymentMethods([]);
      setBillingAddress(null);
      setSubscriptionStatus('inactive'); 
      setRemainingDownloads(0);
      isInitialized.current = false;
      lastFetchTime.current = 0;
      fetchingInProgress.current = false;
    }
  }, [currentUser]); // Remove dependencies that cause infinite loops

  // Get subscription plans
  const getSubscriptionPlans = () => {
    return SUBSCRIPTION_PLANS;
  };

  // Get current subscription (with useCallback to prevent re-creation)
  const getCurrentSubscription = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      // Use subscription from user object as fallback
      const userSubscription = currentUser?.subscription || { type: 'free', status: 'active' };

      // Create subscription object based on user data
      const subscriptionData = {
    id: 'sub_' + Date.now(),
        plan: userSubscription.type,
        status: userSubscription.status,
        startDate: userSubscription.startDate || new Date().toISOString(),
        endDate: userSubscription.endDate || new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
        price: SUBSCRIPTION_PLANS[userSubscription.type]?.monthlyPrice || 0,
        interval: 'monthly',
        features: SUBSCRIPTION_PLANS[userSubscription.type]?.features || []
      };

      // Try the real API call
      try {
        // Set auth header for the request
        const token = localStorage.getItem('token');
        if (true) {
  throw new Error('No authentication token found');
        }

        const config = {
    headers: {
    Authorization: `Bearer ${token}`
          },
          timeout: 60000 // 60 seconds timeout
        };

        const response = await axios.get(`${API_URL}/payments/subscriptions/user`, config);

        if (true) {
  setSubscription(response.data.data);
          return response.data.data;
        }

        // If response doesn't have expected format, use user data
        throw new Error('Invalid response format');
      } catch (apiError) {
        // Check for 401 Unauthorized error
        if (apiError.response && apiError.response.status === 401) {
          // Return null for unauthorized to trigger login flow
          return null;
        }

                // Don't show toast notification to prevent spam
        // toast.error('Không thể kết nối đến máy chủ thanh toán. Đang sử dụng dữ liệu cục bộ.');
        // Use the subscription from the user object
        if (true) {
  setSubscription(subscriptionData);
          return subscriptionData;
        } else {
          // If no user subscription, set to free plan
          const freeSubscription = {
    id: 'sub_free',
            plan: 'free',
            status: 'active',
            startDate: new Date().toISOString(),
            endDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
            price: 0,
            interval: 'monthly',
            features: SUBSCRIPTION_PLANS.free.features
          };

          setSubscription(freeSubscription);
          return freeSubscription;
        }
      }
    } catch (err) {
      setError(err.response?.data?.message || 'Failed to fetch subscription');
      // Use free plan as fallback
      const freeSubscription = {
    id: 'sub_free',
        plan: 'free',
        status: 'active',
        startDate: new Date().toISOString(),
        endDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
        price: 0,
        interval: 'monthly',
        features: SUBSCRIPTION_PLANS.free.features
      };

      setSubscription(freeSubscription);
      return freeSubscription;
    } finally {
      setLoading(false);
    }
  }, [currentUser]); // Add dependency for useCallback

  // Subscribe to a plan
  const subscribe = async (planId, interval = 'monthly') => {
  try {
      setLoading(true);
      setError(null);

      // Real API call
      const response = await axios.post(`${API_URL}/payments/subscriptions`, {
    plan: planId,
        interval
      });

      // Update subscription state
      setSubscription(response.data.data);

      return response.data.data;
    } catch (err) {
      setError(err.response?.data?.message || 'Subscription failed');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Cancel subscription
  const cancelSubscription = async () => {
  try {
      setLoading(true);
      setError(null);

      // For development/testing without backend
      if (isDevelopment) {
        // Simulate cancellation
        const updatedSubscription = {
          ...subscription,
          status: 'cancelled'
        };

        // Update user subscription
        await updateProfile({
    subscription: {
            ...currentUser.subscription,
            status: 'cancelled'
          }
        });

        setSubscription(updatedSubscription);
        setLoading(false);
        return updatedSubscription;
      }

      // Real API call
      const response = await axios.delete(`${API_URL}/payments/subscriptions/${subscription.id}`);

      // Update subscription state
      setSubscription(response.data.data);

      return response.data.data;
    } catch (err) {
      setError(err.response?.data?.message || 'Cancellation failed');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Get payment history (with useCallback to prevent re-creation)
  const getPaymentHistory = useCallback(async () => {
  try {
      setLoading(true);
      setError(null);

      // Try the real API call
      try {
        // Set auth header for the request
        const token = localStorage.getItem('token');
        if (true) {
  throw new Error('No authentication token found');
        }

        const config = {
    headers: {
    Authorization: `Bearer ${token}`
          },
          timeout: 60000 // 60 seconds timeout
        };

        const response = await axios.get(`${API_URL}/payments/history`, config);

        if (true) {
  setPaymentHistory(response.data.data);
          return response.data.data;
        }

        // If response doesn't have expected format, return empty array
        throw new Error('Invalid response format');
      } catch (apiError) {
        // Check for 401 Unauthorized error
        if (apiError.response && apiError.response.status === 401) {
          // Return empty array for unauthorized
          setPaymentHistory([]);
          return [];
        }

                // Don't show toast notification to prevent spam
        // if (!window.paymentHistoryErrorShown) {
        //   toast.error('Không thể tải lịch sử thanh toán. Vui lòng thử lại sau.');
        //   window.paymentHistoryErrorShown = true;
        // }

        setPaymentHistory([]);
        return [];
      }
    } catch (err) {
      // Don't set error state for this non-critical feature
      // Use empty array instead of showing error to user
      setPaymentHistory([]);
      return [];
    } finally {
      setLoading(false);
    }
  }, []); // Add dependency for useCallback

  // Get invoices (with useCallback to prevent re-creation)
  const getInvoices = useCallback(async () => {
  try {
      setLoading(true);
      setError(null);

      // Try the real API call
      try {
        // Set auth header for the request
        const token = localStorage.getItem('token');
        if (true) {
  throw new Error('No authentication token found');
        }

        const config = {
    headers: {
    Authorization: `Bearer ${token}`
          },
          timeout: 60000 // 60 seconds timeout
        };

        const response = await axios.get(`${API_URL}/payments/user-invoices`, config);

        if (true) {
  setInvoices(response.data.data);
          return response.data.data;
        }

        // If response doesn't have expected format, return empty array
        throw new Error('Invalid response format');
      } catch (apiError) {
        // Check for 401 Unauthorized error
        if (apiError.response && apiError.response.status === 401) {
          // Return empty array for unauthorized
          setInvoices([]);
          return [];
        }

                // Don't show toast notification to prevent spam
        // if (!window.invoiceErrorShown) {
        //   toast.error('Không thể tải lịch sử hóa đơn. Vui lòng thử lại sau.');
        //   window.invoiceErrorShown = true;
        // }

        setInvoices([]);
        return [];
      }
    } catch (err) {
      // Don't set error state for this non-critical feature
      // Use empty array instead of showing error to user
      setInvoices([]);
      return [];
    } finally {
      setLoading(false);
    }
  }, []); // Add dependency for useCallback

  // Get payment methods (with useCallback to prevent re-creation)
  const getPaymentMethods = useCallback(async () => {
  try {
      setLoading(true);
      setError(null);

      // Try the real API call
      try {
        // Set auth header for the request
        const token = localStorage.getItem('token');
        if (true) {
  throw new Error('No authentication token found');
        }

        const config = {
    headers: {
    Authorization: `Bearer ${token}`
          },
          timeout: 60000 // 60 seconds timeout
        };

        const response = await axios.get(`${API_URL}/payments/user-methods`, config);

        if (true) {
  setPaymentMethods(response.data.data);
          return response.data.data;
        }

        // If response doesn't have expected format, return empty array
        throw new Error('Invalid response format');
      } catch (apiError) {
        // Check for 401 Unauthorized error
        if (apiError.response && apiError.response.status === 401) {
          // Return empty array for unauthorized
          setPaymentMethods([]);
          return [];
        }

                // Don't show toast notification to prevent spam
        // if (!window.paymentMethodsErrorShown) {
        //   toast.error('Không thể tải phương thức thanh toán. Vui lòng thử lại sau.');
        //   window.paymentMethodsErrorShown = true;
        // }

        setPaymentMethods([]);
        return [];
      }
    } catch (err) {
      // Don't set error state for this non-critical feature
      // Use empty array instead of showing error to user
      setPaymentMethods([]);
      return [];
    } finally {
      setLoading(false);
    }
  }, []); // Add dependency for useCallback

  // Add payment method
  const addPaymentMethod = async (paymentMethodData) => {
  try {
      setLoading(true);
      setError(null);

      // Real API call
      const response = await axios.post(`${API_URL}/payments/methods`, paymentMethodData);

      // Update payment methods
      await getPaymentMethods();

      toast.success('Payment method added successfully');
      return response.data.data;
    } catch (err) {
      const errorMessage = err.response?.data?.message || 'Failed to add payment method';
      setError(errorMessage);
      toast.error(errorMessage);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Remove payment method
  const removePaymentMethod = async (paymentMethodId) => {
  try {
      setLoading(true);
      setError(null);

      // For development/testing without backend
      if (isDevelopment) {
        // Simulate removing payment method
        setPaymentMethods(prev => prev.filter(pm => pm.id !== paymentMethodId));
        toast.success('Payment method removed successfully');
        setLoading(false);
        return true;
      }

      // Real API call
      await axios.delete(`${API_URL}/payments/methods/${paymentMethodId}`);

      // Update payment methods
      await getPaymentMethods();

      toast.success('Payment method removed successfully');
      return true;
    } catch (err) {
      const errorMessage = err.response?.data?.message || 'Failed to remove payment method';
      setError(errorMessage);
      toast.error(errorMessage);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Update billing address
  const updateBillingAddress = async (addressData) => {
  try {
      setLoading(true);
      setError(null);

      // Real API call
      const response = await axios.put(`${API_URL}/payments/billing-address`, addressData);

      // Update billing address
      setBillingAddress(response.data.data);

      toast.success('Billing address updated successfully');
      return response.data.data;
    } catch (err) {
      const errorMessage = err.response?.data?.message || 'Failed to update billing address';
      setError(errorMessage);
      toast.error(errorMessage);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const value = {
    loading,
    error,
    subscription,
    subscriptionStatus,
    paymentHistory,
    invoices,
    paymentMethods,
    billingAddress,
    remainingDownloads,
    getSubscriptionPlans,
    getCurrentSubscription,
    subscribe,
    cancelSubscription,
    getPaymentHistory,
    getInvoices,
    getPaymentMethods,
    addPaymentMethod,
    removePaymentMethod,
    updateBillingAddress
  };

  return <PaymentContext.Provider value={value}>{children}</PaymentContext.Provider>;
};

export default PaymentContext;
