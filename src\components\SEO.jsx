import React from 'react';
import PropTypes from 'prop-types';
import { Helmet } from 'react-helmet-async';

/**
 * Enhanced SEO component for managing meta tags and structured data
 *
 * @param {Object} props - Component props
 * @param {string} props.title - Page title
 * @param {string} props.description - Page description
 * @param {string} props.canonical - Canonical URL
 * @param {string} props.image - Open Graph image URL
 * @param {string} props.type - Open Graph type (website, article, etc.)
 * @param {Object} props.structuredData - Structured data for the page (JSON-LD)
 * @param {Array|string} props.keywords - Keywords for meta tags (array of strings or comma-separated string)
 * @param {Array} props.alternateLanguages - Alternate language versions [{code: 'en', url: 'https://example.com/en'}]
 * @param {string} props.robots - Robots meta tag content
 * @param {string} props.author - Author meta tag content
 * @param {string} props.publishedTime - Article published time (ISO format)
 * @param {string} props.modifiedTime - Article modified time (ISO format)
 * @param {Array} props.articleTags - Article tags for Open Graph
 * @param {React.ReactNode} props.children - Additional head elements
 * @returns {React.ReactElement} The SEO component
 */
const SEO = ({
  title,
  description,
  canonical,
  image,
  type = 'website',
  structuredData,
  keywords = [],
  alternateLanguages = [],
  robots,
  author,
  publishedTime,
  modifiedTime,
  articleTags = [],
  children
}) => {
  // Default site name
  const siteName = '3DSKETCHUP.NET';

  // Default description if not provided
  const defaultDescription = 'Download high-quality 3D models for SketchUp, 3ds Max, Blender, and more. Find free and premium 3D assets for your projects.';

  // Default image if not provided
  const defaultImage = 'https://3dsketchup.net/images/og-image.jpg';

  // Format title with site name
  const formattedTitle = title ? `${title} | ${siteName}` : siteName;

  // Use provided values or defaults
  const metaDescription = description || defaultDescription;
  const metaImage = image || defaultImage;
  const metaUrl = canonical || 'https://3dsketchup.net';

  // Format keywords as a string - handle both array and string inputs
  const keywordsString = Array.isArray(keywords)
    ? keywords.join(', ')
    : typeof keywords === 'string'
      ? keywords
      : '';

  return (
    <Helmet>
      {/* Basic meta tags */}
      <title>{formattedTitle}</title>
      <meta name="description" content={metaDescription} />
      {keywordsString && <meta name="keywords" content={keywordsString} />}
      {robots && <meta name="robots" content={robots} />}
      {author && <meta name="author" content={author} />}

      {/* Canonical link */}
      <link rel="canonical" href={metaUrl} />

      {/* Alternate language versions */}
      {alternateLanguages.map((lang) => (
        <link
          key={lang.code}
          rel="alternate"
          hrefLang={lang.code}
          href={lang.url}
        />
      ))}

      {/* Open Graph meta tags */}
      <meta property="og:title" content={formattedTitle} />
      <meta property="og:description" content={metaDescription} />
      <meta property="og:type" content={type} />
      <meta property="og:url" content={metaUrl} />
      <meta property="og:image" content={metaImage} />
      <meta property="og:site_name" content={siteName} />

      {/* Article specific Open Graph tags */}
      {type === 'article' && publishedTime && (
        <meta property="article:published_time" content={publishedTime} />
      )}
      {type === 'article' && modifiedTime && (
        <meta property="article:modified_time" content={modifiedTime} />
      )}
      {type === 'article' && articleTags.length > 0 &&
        articleTags.map((tag, index) => (
          <meta key={index} property="article:tag" content={tag} />
        ))
      }

      {/* Twitter Card meta tags */}
      <meta name="twitter:card" content="summary_large_image" />
      <meta name="twitter:title" content={formattedTitle} />
      <meta name="twitter:description" content={metaDescription} />
      <meta name="twitter:image" content={metaImage} />

      {/* Structured data (JSON-LD) */}
      {structuredData && (
        <script type="application/ld+json">
          {JSON.stringify(structuredData)}
        </script>
      )}

      {/* Additional head elements */}
      {children}
    </Helmet>
  );
};

SEO.propTypes = {
  title: PropTypes.string,
  description: PropTypes.string,
  canonical: PropTypes.string,
  image: PropTypes.string,
  type: PropTypes.string,
  structuredData: PropTypes.object,
  keywords: PropTypes.oneOfType([
    PropTypes.arrayOf(PropTypes.string),
    PropTypes.string
  ]),
  alternateLanguages: PropTypes.arrayOf(
    PropTypes.shape({
      code: PropTypes.string.isRequired,
      url: PropTypes.string.isRequired
    })
  ),
  robots: PropTypes.string,
  author: PropTypes.string,
  publishedTime: PropTypes.string,
  modifiedTime: PropTypes.string,
  articleTags: PropTypes.arrayOf(PropTypes.string),
  children: PropTypes.node
};

export default SEO;
