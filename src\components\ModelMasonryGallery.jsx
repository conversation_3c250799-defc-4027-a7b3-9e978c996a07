import React, { useState, useEffect, useRef } from 'react';
import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';
import { FiEye, FiDownload, FiStar, FiHeart, FiShare2 } from 'react-icons/fi';
import { useModels } from '../context/ModelContext';
import realDataService from '../services/realDataService';
import ImageWithFallback from './ui/ImageWithFallback';

const ModelMasonryGallery = ({
  title = "🌟 Showcase Gallery",
  subtitle = "Khám phá những models nổi bật được cộng đồng yêu thích",
  columns = 4,
  maxItems = 16,
  showStats = true,
  autoRefresh = true,
  refreshInterval = 10000
}) => {
  const { models, featuredModels } = useModels();
  const [galleryModels, setGalleryModels] = useState([]);
  const [hoveredModel, setHoveredModel] = useState(null);
  const intervalRef = useRef(null);

  // Heights for masonry layout
  const heights = [250, 300, 350, 280, 320, 270, 340, 290];

  // Prepare gallery models using realDataService
  useEffect(() => {
    const fetchGalleryModels = async () => {
      try {
        console.log('🔄 Fetching models for MasonryGallery...');

        // Get popular models from realDataService
        const popularModels = await realDataService.getPopularModels();

        if (popularModels.length > 0) {
          const validModels = popularModels.filter(model =>
            model && (model._id || model.id) && model.imageUrl
          );

          if (validModels.length > 0) {
            const shuffled = [...validModels]
              .sort(() => 0.5 - Math.random())
              .slice(0, maxItems)
              .map((model, index) => ({
                ...model,
                height: heights[index % heights.length],
                delay: index * 0.1
              }));

            console.log('✅ Gallery models prepared:', shuffled.length);
            setGalleryModels(shuffled);
          }
        } else {
          console.log('⚠️ No models available for gallery');
          setGalleryModels([]);
        }
      } catch (error) {
        console.error('❌ Error fetching gallery models:', error);
        setGalleryModels([]);
      }
    };

    fetchGalleryModels();
  }, [maxItems]);

  // Auto refresh functionality using realDataService
  useEffect(() => {
    if (!autoRefresh || galleryModels.length === 0) return;

    intervalRef.current = setInterval(async () => {
      try {
        console.log('🔄 Auto-refreshing gallery models...');

        // Get fresh popular models
        const popularModels = await realDataService.getPopularModels();
        const validModels = popularModels.filter(model =>
          model && (model._id || model.id) && model.imageUrl
        );

        if (validModels.length > 0) {
          const shuffled = [...validModels]
            .sort(() => 0.5 - Math.random())
            .slice(0, maxItems)
            .map((model, index) => ({
              ...model,
              height: heights[index % heights.length],
              delay: index * 0.05
            }));

          console.log('✅ Gallery auto-refreshed with', shuffled.length, 'models');
          setGalleryModels(shuffled);
        }
      } catch (error) {
        console.error('❌ Error auto-refreshing gallery:', error);
      }
    }, refreshInterval);

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [autoRefresh, refreshInterval, maxItems]);

  // Create columns for masonry layout
  const createColumns = () => {
    const cols = Array.from({ length: columns }, () => []);
    const colHeights = Array(columns).fill(0);

    galleryModels.forEach((model) => {
      // Find column with minimum height
      const minHeightIndex = colHeights.indexOf(Math.min(...colHeights));
      cols[minHeightIndex].push(model);
      colHeights[minHeightIndex] += model.height + 24; // Add gap
    });

    return cols;
  };

  const columnData = createColumns();

  if (galleryModels.length === 0) {
    return (
      <div className="py-16 bg-gradient-to-br from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20">
        <div className="container mx-auto px-4">
          <div className="text-center">
            <div className="animate-pulse">
              <div className="h-8 bg-gray-300 dark:bg-gray-700 rounded w-1/3 mx-auto mb-4"></div>
              <div className="h-4 bg-gray-300 dark:bg-gray-700 rounded w-1/2 mx-auto mb-8"></div>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                {[...Array(8)].map((_, i) => (
                  <div key={i} className="bg-gray-300 dark:bg-gray-700 rounded-xl h-64"></div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <section className="py-16 bg-gradient-to-br from-purple-50 via-pink-50 to-orange-50 dark:from-purple-900/20 dark:via-pink-900/20 dark:to-orange-900/20 relative overflow-hidden">
      {/* Background decorations */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute top-20 left-20 w-60 h-60 bg-purple-400 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-20 right-20 w-40 h-40 bg-pink-400 rounded-full blur-2xl animate-pulse" style={{animationDelay: '2s'}}></div>
        <div className="absolute top-1/2 left-1/3 w-32 h-32 bg-orange-400 rounded-full blur-xl animate-pulse" style={{animationDelay: '4s'}}></div>
      </div>

      <div className="container mx-auto px-4 relative z-10">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="text-center mb-12"
        >
          <h2 className="text-4xl md:text-5xl font-black text-gray-900 dark:text-white mb-4">
            {title}
          </h2>
          <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
            {subtitle}
          </p>
        </motion.div>

        {/* Masonry Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {columnData.map((column, columnIndex) => (
            <div key={columnIndex} className="flex flex-col gap-6">
              {column.map((model) => (
                <motion.div
                  key={model._id || model.id}
                  initial={{ opacity: 0, y: 20, scale: 0.9 }}
                  animate={{ opacity: 1, y: 0, scale: 1 }}
                  transition={{
                    duration: 0.6,
                    delay: model.delay,
                    ease: "easeOut"
                  }}
                  className="group relative bg-white dark:bg-gray-800 rounded-2xl overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-500"
                  style={{ height: `${model.height}px` }}
                  onMouseEnter={() => setHoveredModel(model._id || model.id)}
                  onMouseLeave={() => setHoveredModel(null)}
                >
                  {/* Image */}
                  <div className="relative w-full h-full overflow-hidden">
                    <ImageWithFallback
                      src={model.imageUrl}
                      alt={model.title}
                      className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-700"
                      fallbackSrc="/images/placeholder.jpg"
                    />

                    {/* Gradient Overlay */}
                    <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

                    {/* Content Overlay */}
                    <div className={`absolute inset-0 p-4 flex flex-col justify-between transition-all duration-300 ${
                      hoveredModel === (model._id || model.id) ? 'opacity-100' : 'opacity-0'
                    }`}>
                      {/* Top badges */}
                      <div className="flex justify-between items-start">
                        <span className="px-3 py-1 bg-blue-600/90 backdrop-blur-sm text-white text-xs font-semibold rounded-full">
                          {model.category || 'Interior'}
                        </span>
                        {model.isPremium && (
                          <span className="px-2 py-1 bg-gradient-to-r from-yellow-500 to-orange-500 text-white text-xs font-bold rounded-full">
                            PRO
                          </span>
                        )}
                      </div>

                      {/* Bottom content */}
                      <div className="space-y-3">
                        <div>
                          <h3 className="text-white font-bold text-lg mb-1 line-clamp-2">
                            {model.title}
                          </h3>
                          {showStats && (
                            <div className="flex items-center gap-4 text-white/80 text-sm">
                              <div className="flex items-center gap-1">
                                <FiEye className="w-4 h-4" />
                                <span>{model.views || Math.floor(Math.random() * 1000) + 100}</span>
                              </div>
                              <div className="flex items-center gap-1">
                                <FiDownload className="w-4 h-4" />
                                <span>{model.downloads || Math.floor(Math.random() * 500) + 50}</span>
                              </div>
                              <div className="flex items-center gap-1">
                                <FiStar className="w-4 h-4 fill-yellow-400 text-yellow-400" />
                                <span>{model.rating || (Math.random() * 2 + 3).toFixed(1)}</span>
                              </div>
                            </div>
                          )}
                        </div>

                        {/* Action buttons */}
                        <div className="flex gap-2">
                          <Link
                            to={`/model/${model._id || model.id}`}
                            className="flex-1 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white py-2 px-3 rounded-xl font-semibold text-center transition-all duration-300 hover:scale-105 text-sm flex items-center justify-center gap-2"
                          >
                            <FiEye className="w-4 h-4" />
                            <span>Xem</span>
                          </Link>
                          <button className="p-2 bg-white/20 backdrop-blur-sm hover:bg-white/30 text-white rounded-xl transition-all duration-300 hover:scale-110">
                            <FiHeart className="w-4 h-4" />
                          </button>
                          <button className="p-2 bg-white/20 backdrop-blur-sm hover:bg-white/30 text-white rounded-xl transition-all duration-300 hover:scale-110">
                            <FiShare2 className="w-4 h-4" />
                          </button>
                        </div>
                      </div>
                    </div>

                    {/* Shimmer effect */}
                    <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-1000" />
                  </div>
                </motion.div>
              ))}
            </div>
          ))}
        </div>

        {/* View More Button */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 1 }}
          className="text-center mt-12"
        >
          <Link
            to="/models"
            className="inline-flex items-center gap-3 bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white px-8 py-4 rounded-2xl font-bold text-lg shadow-xl hover:shadow-2xl transition-all duration-300 hover:scale-105"
          >
            <span>Khám Phá Thêm Models</span>
            <FiEye className="w-5 h-5" />
          </Link>
        </motion.div>
      </div>
    </section>
  );
};

export default ModelMasonryGallery;
