import React, { useState, useRef, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { FiUpload, FiX, FiImage, FiStar, FiLoader } from 'react-icons/fi';
import { toast } from 'react-hot-toast';

const ImageUploadComponent = ({ images, onImagesChange, maxImages = 10 }) => {
  const [isDragging, setIsDragging] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const fileInputRef = useRef(null);

  const validateImage = (file) => {
    const validTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'image/gif'];
    const maxSize = 20 * 1024 * 1024; // 20MB (increased for GIF support)

    if (!validTypes.includes(file.type)) {
      return { valid: false, error: 'Invalid file type. Please use JPG, PNG, WebP, or GIF.' };
    }

    if (file.size > maxSize) {
      return { valid: false, error: 'File too large. Maximum size is 20MB.' };
    }

    return { valid: true };
  };

  const processImages = async (files) => {
    setIsProcessing(true);
    const newImages = [];
    const errors = [];

    for (let i = 0; i < files.length; i++) {
      const file = files[i];
      const validation = validateImage(file);

      if (!validation.valid) {
        errors.push(`${file.name}: ${validation.error}`);
        continue;
      }

      if (images.length + newImages.length >= maxImages) {
        errors.push(`Maximum ${maxImages} images allowed`);
        break;
      }

      // Create preview URL
      const previewUrl = URL.createObjectURL(file);

      newImages.push({
        id: Date.now() + i,
        file,
        preview: previewUrl,
        name: file.name,
        size: file.size,
        isMain: images.length === 0 && newImages.length === 0
      });
    }

    if (errors.length > 0) {
      toast.error(errors.join('\n'));
    }

    if (newImages.length > 0) {
      onImagesChange([...images, ...newImages]);
      toast.success(`${newImages.length} image(s) added successfully`);
    }

    setIsProcessing(false);
  };

  const handleFileSelect = (e) => {
    const files = Array.from(e.target.files);
    if (files.length > 0) {
      processImages(files);
    }
    // Reset input
    e.target.value = '';
  };

  const handleDrop = (e) => {
    e.preventDefault();
    setIsDragging(false);

    const files = Array.from(e.dataTransfer.files);
    if (files.length > 0) {
      processImages(files);
    }
  };

  const handleDragOver = (e) => {
    e.preventDefault();
    setIsDragging(true);
  };

  const handleDragLeave = (e) => {
    e.preventDefault();
    setIsDragging(false);
  };

  const removeImage = (imageId) => {
    const updatedImages = images.filter(img => img.id !== imageId);

    // If removed image was main, set first image as main
    if (updatedImages.length > 0) {
      const removedImage = images.find(img => img.id === imageId);
      if (removedImage?.isMain) {
        updatedImages[0].isMain = true;
      }
    }

    onImagesChange(updatedImages);

    // Clean up object URL
    const removedImage = images.find(img => img.id === imageId);
    if (removedImage?.preview) {
      URL.revokeObjectURL(removedImage.preview);
    }
  };

  const setMainImage = (imageId) => {
    const updatedImages = images.map(img => ({
      ...img,
      isMain: img.id === imageId
    }));
    onImagesChange(updatedImages);
  };

  const reorderImages = (dragIndex, hoverIndex) => {
    const draggedImage = images[dragIndex];
    const updatedImages = [...images];
    updatedImages.splice(dragIndex, 1);
    updatedImages.splice(hoverIndex, 0, draggedImage);
    onImagesChange(updatedImages);
  };

  return (
    <div className="space-y-4">
      {/* Upload Area */}
      <div
        className={`border-2 border-dashed rounded-lg p-6 transition-colors ${
          isDragging
            ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
            : 'border-gray-300 dark:border-gray-600'
        } ${isProcessing ? 'opacity-50 pointer-events-none' : ''}`}
        onDrop={handleDrop}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
      >
        <div className="text-center">
          {isProcessing ? (
            <div className="flex flex-col items-center">
              <FiLoader className="h-12 w-12 text-blue-500 animate-spin mb-4" />
              <p className="text-gray-600 dark:text-gray-400">Processing images...</p>
            </div>
          ) : (
            <>
              <FiUpload className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-600 dark:text-gray-400 mb-2">
                Drag and drop images here, or click to select
              </p>
              <p className="text-sm text-gray-500 mb-4">
                JPG, PNG, WebP, GIF (max 20MB each) • {images.length}/{maxImages} images
              </p>
              <input
                ref={fileInputRef}
                type="file"
                multiple
                accept="image/*"
                onChange={handleFileSelect}
                className="hidden"
              />
              <button
                type="button"
                onClick={() => fileInputRef.current?.click()}
                disabled={images.length >= maxImages}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Choose Images
              </button>
            </>
          )}
        </div>
      </div>

      {/* Image Preview Grid */}
      {images.length > 0 && (
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300">
              Preview Images ({images.length})
            </h3>
            <p className="text-xs text-gray-500">
              Click star to set as main image
            </p>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
            <AnimatePresence>
              {images.map((image, index) => (
                <motion.div
                  key={image.id}
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  exit={{ opacity: 0, scale: 0.8 }}
                  className="relative group"
                >
                  <div className="aspect-square bg-gray-100 dark:bg-gray-700 rounded-lg overflow-hidden">
                    <img
                      src={image.preview}
                      alt={image.name}
                      className="w-full h-full object-cover"
                    />

                    {/* Overlay */}
                    <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-40 transition-all duration-200 flex items-center justify-center">
                      <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex gap-2">
                        <button
                          type="button"
                          onClick={() => setMainImage(image.id)}
                          className={`p-2 rounded-full transition-colors ${
                            image.isMain
                              ? 'bg-yellow-500 text-white'
                              : 'bg-white text-gray-700 hover:bg-yellow-500 hover:text-white'
                          }`}
                          title={image.isMain ? 'Main image' : 'Set as main image'}
                        >
                          <FiStar className="h-4 w-4" />
                        </button>

                        <button
                          type="button"
                          onClick={() => removeImage(image.id)}
                          className="p-2 bg-red-500 text-white rounded-full hover:bg-red-600 transition-colors"
                          title="Remove image"
                        >
                          <FiX className="h-4 w-4" />
                        </button>
                      </div>
                    </div>
                  </div>

                  {/* Image Info */}
                  <div className="mt-2">
                    <p className="text-xs text-gray-600 dark:text-gray-400 truncate">
                      {image.name}
                    </p>
                    <p className="text-xs text-gray-500">
                      {(image.size / 1024 / 1024).toFixed(1)} MB
                    </p>
                    {image.isMain && (
                      <div className="flex items-center gap-1 mt-1">
                        <FiStar className="h-3 w-3 text-yellow-500" />
                        <span className="text-xs text-yellow-600 dark:text-yellow-400 font-medium">
                          Main Image
                        </span>
                      </div>
                    )}
                  </div>
                </motion.div>
              ))}
            </AnimatePresence>
          </div>
        </div>
      )}

      {/* Instructions */}
      {images.length === 0 && (
        <div className="text-center py-4">
          <FiImage className="h-8 w-8 text-gray-300 mx-auto mb-2" />
          <p className="text-sm text-gray-500">
            No images uploaded yet. Add some preview images to showcase your 3D model.
          </p>
        </div>
      )}
    </div>
  );
};

export default ImageUploadComponent;
