import React, { useState, useRef, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  FiSearch, FiImage, FiUpload, FiX, FiFilter, FiEye,
  FiLoader, FiCheck, FiAlertCircle, FiStar, FiDownload
} from 'react-icons/fi';
import { toast } from 'react-hot-toast';

const VisualExtensionSearch = ({ onSearch, onFilterChange, categories = [] }) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('All Categories');
  const [showImageUpload, setShowImageUpload] = useState(false);
  const [uploadedImage, setUploadedImage] = useState(null);
  const [imagePreview, setImagePreview] = useState(null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [visualSearchResults, setVisualSearchResults] = useState([]);
  const [searchMode, setSearchMode] = useState('text'); // 'text', 'image', 'hybrid'
  const fileInputRef = useRef(null);

  // Handle image upload
  const handleImageUpload = useCallback((e) => {
    const file = e.target.files?.[0];
    if (!file) return;

    // Validate file type
    const validTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
    if (!validTypes.includes(file.type)) {
      toast.error('Please upload a valid image file (JPG, PNG, WebP)');
      return;
    }

    // Validate file size (5MB max)
    const maxSize = 5 * 1024 * 1024;
    if (file.size > maxSize) {
      toast.error('Image size must be less than 5MB');
      return;
    }

    // Create preview
    const reader = new FileReader();
    reader.onload = (e) => {
      setImagePreview(e.target.result);
    };
    reader.readAsDataURL(file);

    setUploadedImage(file);
    setSearchMode('image');
  }, []);

  // Handle visual search
  const handleVisualSearch = async () => {
    if (!uploadedImage) return;

    setIsAnalyzing(true);
    try {
      const formData = new FormData();
      formData.append('image', uploadedImage);
      formData.append('searchType', 'extensions');
      formData.append('category', selectedCategory === 'All Categories' ? '' : selectedCategory);

      const response = await fetch('/api/extensions/visual-search', {
        method: 'POST',
        body: formData
      });

      if (!response.ok) {
        throw new Error('Visual search failed');
      }

      const result = await response.json();
      
      if (result.success) {
        setVisualSearchResults(result.data.extensions);
        if (onSearch) {
          onSearch({
            query: '',
            category: selectedCategory === 'All Categories' ? '' : selectedCategory,
            visualResults: result.data.extensions,
            searchMode: 'image',
            imageAnalysis: result.data.analysis
          });
        }
        toast.success('Visual search completed!');
      } else {
        throw new Error(result.error || 'Visual search failed');
      }

    } catch (error) {
      console.error('Visual search error:', error);
      toast.error('Failed to perform visual search. Please try again.');
    } finally {
      setIsAnalyzing(false);
    }
  };

  // Handle hybrid search (text + image)
  const handleHybridSearch = async () => {
    if (!searchQuery.trim() && !uploadedImage) {
      toast.error('Please enter search terms or upload an image');
      return;
    }

    setIsAnalyzing(true);
    try {
      const formData = new FormData();
      if (uploadedImage) {
        formData.append('image', uploadedImage);
      }
      formData.append('query', searchQuery);
      formData.append('category', selectedCategory === 'All Categories' ? '' : selectedCategory);
      formData.append('searchType', 'hybrid');

      const response = await fetch('/api/extensions/hybrid-search', {
        method: 'POST',
        body: formData
      });

      if (!response.ok) {
        throw new Error('Hybrid search failed');
      }

      const result = await response.json();
      
      if (result.success) {
        if (onSearch) {
          onSearch({
            query: searchQuery,
            category: selectedCategory === 'All Categories' ? '' : selectedCategory,
            results: result.data.extensions,
            searchMode: 'hybrid',
            textScore: result.data.textScore,
            visualScore: result.data.visualScore,
            combinedScore: result.data.combinedScore
          });
        }
        toast.success('Hybrid search completed!');
      } else {
        throw new Error(result.error || 'Hybrid search failed');
      }

    } catch (error) {
      console.error('Hybrid search error:', error);
      toast.error('Failed to perform hybrid search. Please try again.');
    } finally {
      setIsAnalyzing(false);
    }
  };

  // Handle text search
  const handleTextSearch = () => {
    if (!searchQuery.trim()) {
      toast.error('Please enter search terms');
      return;
    }

    if (onSearch) {
      onSearch({
        query: searchQuery,
        category: selectedCategory === 'All Categories' ? '' : selectedCategory,
        searchMode: 'text'
      });
    }
  };

  // Clear image
  const clearImage = () => {
    setUploadedImage(null);
    setImagePreview(null);
    setVisualSearchResults([]);
    setSearchMode('text');
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  // Handle search based on mode
  const handleSearch = () => {
    switch (searchMode) {
      case 'image':
        handleVisualSearch();
        break;
      case 'hybrid':
        handleHybridSearch();
        break;
      default:
        handleTextSearch();
    }
  };

  return (
    <div className="w-full max-w-4xl mx-auto">
      {/* Search Mode Selector */}
      <div className="flex items-center justify-center mb-4">
        <div className="flex bg-gray-100 dark:bg-gray-700 rounded-lg p-1">
          <button
            onClick={() => setSearchMode('text')}
            className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
              searchMode === 'text'
                ? 'bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm'
                : 'text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white'
            }`}
          >
            <FiSearch className="inline-block w-4 h-4 mr-2" />
            Text Search
          </button>
          <button
            onClick={() => setSearchMode('image')}
            className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
              searchMode === 'image'
                ? 'bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm'
                : 'text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white'
            }`}
          >
            <FiImage className="inline-block w-4 h-4 mr-2" />
            Visual Search
          </button>
          <button
            onClick={() => setSearchMode('hybrid')}
            className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
              searchMode === 'hybrid'
                ? 'bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm'
                : 'text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white'
            }`}
          >
            <FiEye className="inline-block w-4 h-4 mr-2" />
            Hybrid Search
          </button>
        </div>
      </div>

      {/* Main Search Interface */}
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
        {/* Search Bar */}
        <div className="flex items-center p-4 border-b border-gray-200 dark:border-gray-700">
          {/* Category Selector */}
          <select
            value={selectedCategory}
            onChange={(e) => setSelectedCategory(e.target.value)}
            className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-l-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="All Categories">All Categories</option>
            {categories.map((category) => (
              <option key={category} value={category}>
                {category}
              </option>
            ))}
          </select>

          {/* Search Input */}
          <input
            type="text"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
            placeholder={
              searchMode === 'image' 
                ? 'Upload an image for visual search...'
                : searchMode === 'hybrid'
                  ? 'Enter keywords and/or upload an image...'
                  : 'Search for extensions...'
            }
            disabled={searchMode === 'image'}
            className="flex-1 px-4 py-2 border-t border-b border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-100 dark:disabled:bg-gray-600"
          />

          {/* Image Upload Button */}
          {(searchMode === 'image' || searchMode === 'hybrid') && (
            <div className="relative">
              <input
                ref={fileInputRef}
                type="file"
                accept="image/*"
                onChange={handleImageUpload}
                className="hidden"
              />
              <button
                onClick={() => fileInputRef.current?.click()}
                className="px-4 py-2 border border-gray-300 dark:border-gray-600 bg-gray-50 dark:bg-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors"
                title="Upload image"
              >
                <FiUpload className="w-5 h-5" />
              </button>
            </div>
          )}

          {/* Search Button */}
          <button
            onClick={handleSearch}
            disabled={isAnalyzing || (searchMode === 'image' && !uploadedImage) || (searchMode === 'text' && !searchQuery.trim())}
            className="px-6 py-2 bg-blue-600 text-white rounded-r-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            {isAnalyzing ? (
              <FiLoader className="w-5 h-5 animate-spin" />
            ) : (
              <FiSearch className="w-5 h-5" />
            )}
          </button>
        </div>

        {/* Image Preview */}
        {uploadedImage && imagePreview && (
          <div className="p-4 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-700">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <img
                  src={imagePreview}
                  alt="Upload preview"
                  className="w-16 h-16 object-cover rounded-lg border border-gray-300 dark:border-gray-600"
                />
                <div>
                  <p className="text-sm font-medium text-gray-900 dark:text-white">
                    Image uploaded for {searchMode} search
                  </p>
                  <p className="text-xs text-gray-500 dark:text-gray-400">
                    {uploadedImage.name} • {(uploadedImage.size / 1024 / 1024).toFixed(2)} MB
                  </p>
                </div>
              </div>
              <button
                onClick={clearImage}
                className="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors"
                title="Remove image"
              >
                <FiX className="w-5 h-5" />
              </button>
            </div>
          </div>
        )}

        {/* Search Mode Description */}
        <div className="p-4 bg-blue-50 dark:bg-blue-900/20 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0 mt-0.5">
              {searchMode === 'text' && <FiSearch className="w-5 h-5 text-blue-600" />}
              {searchMode === 'image' && <FiImage className="w-5 h-5 text-purple-600" />}
              {searchMode === 'hybrid' && <FiEye className="w-5 h-5 text-green-600" />}
            </div>
            <div>
              <h3 className="text-sm font-medium text-gray-900 dark:text-white">
                {searchMode === 'text' && 'Text Search'}
                {searchMode === 'image' && 'Visual Search'}
                {searchMode === 'hybrid' && 'Hybrid Search'}
              </h3>
              <p className="text-sm text-gray-600 dark:text-gray-300 mt-1">
                {searchMode === 'text' && 'Search extensions using keywords and filters'}
                {searchMode === 'image' && 'Upload an image to find visually similar extensions based on screenshots and previews'}
                {searchMode === 'hybrid' && 'Combine text search with visual analysis for more accurate results'}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Visual Search Results Preview */}
      {visualSearchResults.length > 0 && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mt-6 bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 p-6"
        >
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            Visual Search Results ({visualSearchResults.length})
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {visualSearchResults.slice(0, 6).map((extension, index) => (
              <div
                key={index}
                className="border border-gray-200 dark:border-gray-600 rounded-lg p-4 hover:shadow-md transition-shadow"
              >
                <div className="flex items-start justify-between mb-2">
                  <h4 className="font-medium text-gray-900 dark:text-white text-sm truncate">
                    {extension.name}
                  </h4>
                  {extension.visualSimilarity && (
                    <span className="text-xs bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300 px-2 py-1 rounded">
                      {Math.round(extension.visualSimilarity * 100)}% match
                    </span>
                  )}
                </div>
                <p className="text-xs text-gray-600 dark:text-gray-400 mb-2 line-clamp-2">
                  {extension.description}
                </p>
                <div className="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400">
                  <div className="flex items-center space-x-2">
                    <FiStar className="w-3 h-3" />
                    <span>{extension.rating || 'N/A'}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <FiDownload className="w-3 h-3" />
                    <span>{extension.downloads || 0}</span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </motion.div>
      )}
    </div>
  );
};

export default VisualExtensionSearch;
