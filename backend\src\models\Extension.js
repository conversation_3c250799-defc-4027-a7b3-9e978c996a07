import mongoose from 'mongoose';

const extensionSchema = new mongoose.Schema({
  // Basic Information
  name: {
    type: String,
    required: [true, 'Please add an extension name'],
    trim: true,
    maxlength: [100, 'Name cannot be more than 100 characters']
  },
  description: {
    type: String,
    required: [true, 'Please add a description'],
    maxlength: [500, 'Description cannot be more than 500 characters']
  },
  developer: {
    type: String,
    required: [true, 'Please add developer name'],
    trim: true
  },
  version: {
    type: String,
    required: [true, 'Please add version'],
    trim: true
  },

  // Category and Classification
  category: {
    type: String,
    required: [true, 'Please add a category'],
    enum: [
      'Architecture',
      'Construction', 
      'Engineering',
      'Interior Design',
      'Landscape',
      'Rendering',
      'Animation',
      'Import/Export',
      'Utilities',
      'Drawing',
      'Modeling',
      'Visualization'
    ]
  },
  subcategory: {
    type: String,
    trim: true
  },
  tags: [{
    type: String,
    trim: true
  }],

  // Pricing and Availability
  price: {
    type: String,
    required: [true, 'Please add price'],
    trim: true
  },
  isPaid: {
    type: Boolean,
    default: function() {
      return this.price !== 'Free' && this.price !== '$0.00';
    }
  },

  // Ratings and Statistics
  rating: {
    type: Number,
    min: [1, 'Rating must be at least 1'],
    max: [5, 'Rating cannot be more than 5'],
    default: 4.0
  },
  downloads: {
    type: Number,
    default: 0
  },
  views: {
    type: Number,
    default: 0
  },
  likes: {
    type: Number,
    default: 0
  },

  // Technical Information
  features: [{
    type: String,
    trim: true
  }],
  compatibility: [{
    type: String,
    trim: true
  }],
  fileSize: {
    type: String,
    trim: true
  },
  lastUpdated: {
    type: Date,
    default: Date.now
  },

  // Media and Assets
  screenshots: [{
    type: String,
    trim: true
  }],
  icon: {
    type: String,
    trim: true,
    default: '/images/extensions/default-icon.png'
  },
  video: {
    type: String,
    trim: true
  },

  // Download Information
  downloadUrl: {
    type: String,
    required: [true, 'Please add a download URL'],
    trim: true
  },
  backupLinks: [{
    type: String,
    trim: true
  }],
  fileFormat: {
    type: String,
    default: 'RBZ',
    enum: ['RBZ', 'RB', 'ZIP']
  },

  // Source and Attribution
  source: {
    type: String,
    default: 'sketchup-extensions',
    enum: ['sketchup-extensions', 'sketchucation', 'third-party', 'custom']
  },
  extensionInfo: {
    originalUrl: String,
    extensionId: String,
    developer: String,
    scrapedAt: Date
  },

  // Status and Moderation
  status: {
    type: String,
    enum: ['active', 'inactive', 'pending', 'rejected'],
    default: 'active'
  },
  isVerified: {
    type: Boolean,
    default: false
  },
  isFeatured: {
    type: Boolean,
    default: false
  },

  // User Interaction
  reviews: [{
    user: {
      type: mongoose.Schema.ObjectId,
      ref: 'User'
    },
    rating: {
      type: Number,
      min: 1,
      max: 5
    },
    comment: String,
    createdAt: {
      type: Date,
      default: Date.now
    }
  }],

  // System Information
  createdBy: {
    type: mongoose.Schema.ObjectId,
    ref: 'User',
    required: true
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

// Indexes for better performance
extensionSchema.index({ name: 'text', description: 'text', tags: 'text' });
extensionSchema.index({ category: 1, rating: -1 });
extensionSchema.index({ downloads: -1 });
extensionSchema.index({ price: 1 });
extensionSchema.index({ 'extensionInfo.extensionId': 1 });
extensionSchema.index({ source: 1 });
extensionSchema.index({ status: 1 });

// Virtual for average rating
extensionSchema.virtual('averageRating').get(function() {
  if (this.reviews && this.reviews.length > 0) {
    const sum = this.reviews.reduce((acc, review) => acc + review.rating, 0);
    return (sum / this.reviews.length).toFixed(1);
  }
  return this.rating;
});

// Virtual for review count
extensionSchema.virtual('reviewCount').get(function() {
  return this.reviews ? this.reviews.length : 0;
});

// Pre-save middleware to update timestamps
extensionSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  next();
});

// Static method to get extensions by category
extensionSchema.statics.getByCategory = function(category, limit = 10) {
  return this.find({ category, status: 'active' })
    .sort({ downloads: -1, rating: -1 })
    .limit(limit)
    .populate('createdBy', 'name');
};

// Static method to get featured extensions
extensionSchema.statics.getFeatured = function(limit = 6) {
  return this.find({ isFeatured: true, status: 'active' })
    .sort({ downloads: -1 })
    .limit(limit)
    .populate('createdBy', 'name');
};

// Static method to search extensions
extensionSchema.statics.searchExtensions = function(query, options = {}) {
  const {
    category,
    priceFilter,
    sortBy = 'downloads',
    limit = 20,
    page = 1
  } = options;

  let searchQuery = {
    status: 'active',
    $or: [
      { name: { $regex: query, $options: 'i' } },
      { description: { $regex: query, $options: 'i' } },
      { tags: { $in: [new RegExp(query, 'i')] } },
      { developer: { $regex: query, $options: 'i' } }
    ]
  };

  if (category) {
    searchQuery.category = category;
  }

  if (priceFilter === 'free') {
    searchQuery.price = 'Free';
  } else if (priceFilter === 'paid') {
    searchQuery.price = { $ne: 'Free' };
  }

  const sortOptions = {};
  switch (sortBy) {
    case 'rating':
      sortOptions.rating = -1;
      break;
    case 'name':
      sortOptions.name = 1;
      break;
    case 'newest':
      sortOptions.createdAt = -1;
      break;
    case 'updated':
      sortOptions.lastUpdated = -1;
      break;
    default:
      sortOptions.downloads = -1;
  }

  return this.find(searchQuery)
    .sort(sortOptions)
    .limit(limit)
    .skip((page - 1) * limit)
    .populate('createdBy', 'name');
};

// Instance method to increment downloads
extensionSchema.methods.incrementDownloads = function() {
  this.downloads += 1;
  return this.save();
};

// Instance method to add review
extensionSchema.methods.addReview = function(userId, rating, comment) {
  this.reviews.push({
    user: userId,
    rating,
    comment
  });
  
  // Recalculate average rating
  const sum = this.reviews.reduce((acc, review) => acc + review.rating, 0);
  this.rating = (sum / this.reviews.length);
  
  return this.save();
};

export default mongoose.model('Extension', extensionSchema);
