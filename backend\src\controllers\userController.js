import User from '../models/User.js';
import Model from '../models/Model.js';
import Subscription from '../models/Subscription.js';
import { logActivity } from './activityController.js';
import bcrypt from 'bcryptjs';
import path from 'path';
import fs from 'fs';

// @desc    Get all users
// @route   GET /api/users
// @access  Private/Admin
export const getUsers = async (req, res, next) => {
  try {
    // Get query parameters
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const search = req.query.search || '';
    const role = req.query.role;
    const status = req.query.status;
    const sortBy = req.query.sortBy || 'createdAt';
    const sortDirection = req.query.sortDirection || 'desc';

    // Build query
    let query = {};

    // Search filter
    if (search) {
      query.$or = [
        { name: { $regex: search, $options: 'i' } },
        { email: { $regex: search, $options: 'i' } }
      ];
    }

    // Role filter
    if (role && role !== 'all') {
      query.role = role;
    }

    // Status filter (assuming you have a status field)
    if (status && status !== 'all') {
      query.status = status;
    }

    // Calculate skip
    const skip = (page - 1) * limit;

    // Build sort object
    const sort = {};
    sort[sortBy] = sortDirection === 'asc' ? 1 : -1;

    // Get total count
    const total = await User.countDocuments(query);

    // Get users with pagination
    const users = await User.find(query)
      .sort(sort)
      .skip(skip)
      .limit(limit)
      .select('-password'); // Don't return password

    res.status(200).json({
      success: true,
      data: {
        data: users,
        total: total,
        page: page,
        pages: Math.ceil(total / limit),
        limit: limit
      }
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Get single user
// @route   GET /api/users/:id
// @access  Private/Admin
export const getUser = async (req, res, next) => {
  try {
    const user = await User.findById(req.params.id);

    if (!user) {
      return res.status(404).json({
        success: false,
        error: 'User not found'
      });
    }

    res.status(200).json({
      success: true,
      data: user
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Create user
// @route   POST /api/users
// @access  Private/Admin
export const createUser = async (req, res, next) => {
  try {
    const user = await User.create(req.body);

    res.status(201).json({
      success: true,
      data: user
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Update user
// @route   PUT /api/users/:id
// @access  Private/Admin
export const updateUser = async (req, res, next) => {
  try {
    const user = await User.findByIdAndUpdate(req.params.id, req.body, {
      new: true,
      runValidators: true
    });

    if (!user) {
      return res.status(404).json({
        success: false,
        error: 'User not found'
      });
    }

    res.status(200).json({
      success: true,
      data: user
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Delete user
// @route   DELETE /api/users/:id
// @access  Private/Admin
export const deleteUser = async (req, res, next) => {
  try {
    const user = await User.findById(req.params.id);

    if (!user) {
      return res.status(404).json({
        success: false,
        error: 'User not found'
      });
    }

    await user.deleteOne();

    res.status(200).json({
      success: true,
      data: {}
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Get user's saved models
// @route   GET /api/users/saved-models
// @access  Private
export const getSavedModels = async (req, res, next) => {
  try {
    const user = await User.findById(req.user.id).populate('savedModels');

    res.status(200).json({
      success: true,
      count: user.savedModels.length,
      data: user.savedModels
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Get user profile
// @route   GET /api/users/profile
// @access  Private
export const getUserProfile = async (req, res, next) => {
  try {
    const user = await User.findById(req.user.id);

    if (!user) {
      return res.status(404).json({
        success: false,
        error: 'User not found'
      });
    }

    // Format user data for frontend
    const userData = {
      name: user.name,
      email: user.email,
      bio: user.bio || '',
      company: user.company || '',
      website: user.website || '',
      location: user.location || '',
      profileImage: user.profileImage,
      phone: user.phone || '',
      jobTitle: user.jobTitle || '',
      role: user.role,
      createdAt: user.createdAt
    };

    res.status(200).json({
      success: true,
      data: userData
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Update user profile
// @route   PUT /api/users/profile
// @access  Private
export const updateUserProfile = async (req, res, next) => {
  try {
    const { name, bio, company, website, location, phone, jobTitle } = req.body;

    // Find user
    const user = await User.findById(req.user.id);

    if (!user) {
      return res.status(404).json({
        success: false,
        error: 'User not found'
      });
    }

    // Update fields
    if (name) user.name = name;
    if (bio !== undefined) user.bio = bio;
    if (company !== undefined) user.company = company;
    if (website !== undefined) user.website = website;
    if (location !== undefined) user.location = location;
    if (phone !== undefined) user.phone = phone;
    if (jobTitle !== undefined) user.jobTitle = jobTitle;

    // Save user
    await user.save();

    // Log activity
    await logActivity(user._id, 'profile_update', {
      details: { action: 'profile_update' }
    });

    res.status(200).json({
      success: true,
      data: user
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Update user password
// @route   PUT /api/users/password
// @access  Private
export const updatePassword = async (req, res, next) => {
  try {
    const { currentPassword, newPassword } = req.body;

    // Validate input
    if (!currentPassword || !newPassword) {
      return res.status(400).json({
        success: false,
        error: 'Please provide current and new password'
      });
    }

    // Get user with password
    const user = await User.findById(req.user.id).select('+password');

    // Check current password
    const isMatch = await user.matchPassword(currentPassword);

    if (!isMatch) {
      return res.status(401).json({
        success: false,
        error: 'Current password is incorrect'
      });
    }

    // Update password
    user.password = newPassword;
    await user.save();

    // Log activity
    await logActivity(user._id, 'password_update', {
      details: { action: 'password_update' }
    });

    res.status(200).json({
      success: true,
      message: 'Password updated successfully'
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Get user's subscription
// @route   GET /api/users/subscription
// @access  Private
export const getUserSubscription = async (req, res, next) => {
  try {
    // Find user with subscription details
    const user = await User.findById(req.user.id);

    // Find active subscription in Subscription model
    const subscription = await Subscription.findOne({
      user: req.user.id,
      status: 'active'
    }).sort({ createdAt: -1 });

    // Format subscription data
    const subscriptionData = {
      plan: user.subscription?.type || 'free',
      status: user.subscription?.status || 'active',
      startDate: user.subscription?.startDate || null,
      endDate: user.subscription?.endDate || null,
      nextBillingDate: subscription?.renewalDate || null,
      downloadCredits: user.downloadCredits || 0,
      features: subscription?.features || {
        downloadLimit: 5,
        accessToPremiumModels: false,
        prioritySupport: false,
        commercialUse: false
      },
      paymentMethod: subscription ? {
        type: 'card',
        last4: subscription.stripeCustomerId ? '4242' : null, // This would come from Stripe in a real implementation
        expiryMonth: 12,
        expiryYear: 2025,
        brand: 'Visa'
      } : null
    };

    res.status(200).json({
      success: true,
      data: subscriptionData
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Update user's subscription
// @route   PUT /api/users/subscription
// @access  Private
export const updateSubscription = async (req, res, next) => {
  try {
    const { plan, interval } = req.body;

    // Validate input
    if (!plan) {
      return res.status(400).json({
        success: false,
        error: 'Please provide a subscription plan'
      });
    }

    // Check if plan is valid
    if (!['basic', 'premium', 'professional'].includes(plan)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid subscription plan'
      });
    }

    // Get user
    const user = await User.findById(req.user.id);

    // Check if user already has an active subscription
    const existingSubscription = await Subscription.findOne({
      user: req.user.id,
      status: 'active'
    });

    if (existingSubscription) {
      // Update existing subscription
      existingSubscription.plan = plan;

      if (interval) {
        existingSubscription.interval = interval;
      }

      // Update features based on plan
      switch (plan) {
        case 'basic':
          existingSubscription.features = {
            downloadLimit: 20,
            accessToPremiumModels: false,
            prioritySupport: false,
            commercialUse: false
          };
          break;
        case 'premium':
          existingSubscription.features = {
            downloadLimit: 50,
            accessToPremiumModels: true,
            prioritySupport: false,
            commercialUse: false
          };
          break;
        case 'professional':
          existingSubscription.features = {
            downloadLimit: 100,
            accessToPremiumModels: true,
            prioritySupport: true,
            commercialUse: true
          };
          break;
      }

      // Update renewal date
      const now = new Date();
      existingSubscription.renewalDate = interval === 'yearly'
        ? new Date(now.setFullYear(now.getFullYear() + 1))
        : new Date(now.setMonth(now.getMonth() + 1));

      // Save subscription
      await existingSubscription.save();

      // Update user subscription
      user.subscription = {
        type: plan,
        startDate: existingSubscription.startDate,
        endDate: existingSubscription.endDate,
        status: 'active'
      };

      // Update download credits based on plan
      switch (plan) {
        case 'basic':
          user.downloadCredits = 20;
          break;
        case 'premium':
          user.downloadCredits = 50;
          break;
        case 'professional':
          user.downloadCredits = 100;
          break;
      }

      // Save user
      await user.save();

      // Log activity
      await logActivity(user._id, 'subscription_update', {
        details: {
          action: 'subscription_update',
          plan,
          interval: interval || existingSubscription.interval
        }
      });

      res.status(200).json({
        success: true,
        data: existingSubscription
      });
    } else {
      // Create new subscription
      const now = new Date();
      const endDate = interval === 'yearly'
        ? new Date(now.setFullYear(now.getFullYear() + 1))
        : new Date(now.setMonth(now.getMonth() + 1));

      // Set features based on plan
      let features = {};
      switch (plan) {
        case 'basic':
          features = {
            downloadLimit: 20,
            accessToPremiumModels: false,
            prioritySupport: false,
            commercialUse: false
          };
          break;
        case 'premium':
          features = {
            downloadLimit: 50,
            accessToPremiumModels: true,
            prioritySupport: false,
            commercialUse: false
          };
          break;
        case 'professional':
          features = {
            downloadLimit: 100,
            accessToPremiumModels: true,
            prioritySupport: true,
            commercialUse: true
          };
          break;
      }

      // Create subscription
      const newSubscription = await Subscription.create({
        user: req.user.id,
        plan,
        status: 'active',
        startDate: new Date(),
        endDate,
        renewalDate: endDate,
        autoRenew: true,
        interval: interval || 'monthly',
        price: plan === 'basic' ? 9.99 : plan === 'premium' ? 19.99 : 29.99,
        features
      });

      // Update user subscription
      user.subscription = {
        type: plan,
        startDate: newSubscription.startDate,
        endDate: newSubscription.endDate,
        status: 'active'
      };

      // Update download credits based on plan
      switch (plan) {
        case 'basic':
          user.downloadCredits = 20;
          break;
        case 'premium':
          user.downloadCredits = 50;
          break;
        case 'professional':
          user.downloadCredits = 100;
          break;
      }

      // Save user
      await user.save();

      // Log activity
      await logActivity(user._id, 'subscription_create', {
        details: {
          action: 'subscription_create',
          plan,
          interval: interval || 'monthly'
        }
      });

      res.status(201).json({
        success: true,
        data: newSubscription
      });
    }
  } catch (error) {
    next(error);
  }
};

// @desc    Cancel user's subscription
// @route   POST /api/users/subscription/cancel
// @access  Private
export const cancelSubscription = async (req, res, next) => {
  try {
    // Get user
    const user = await User.findById(req.user.id);

    // Check if user has an active subscription
    const subscription = await Subscription.findOne({
      user: req.user.id,
      status: 'active'
    });

    if (!subscription) {
      return res.status(400).json({
        success: false,
        error: 'No active subscription found'
      });
    }

    // Update subscription
    subscription.status = 'cancelled';
    subscription.cancelledAt = new Date();
    subscription.cancelReason = req.body.reason || 'User cancelled';

    // Save subscription
    await subscription.save();

    // Update user subscription
    user.subscription.status = 'cancelled';

    // Save user
    await user.save();

    // Log activity
    await logActivity(user._id, 'subscription_cancel', {
      details: {
        action: 'subscription_cancel',
        reason: req.body.reason || 'User cancelled'
      }
    });

    res.status(200).json({
      success: true,
      data: {}
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Reactivate user's subscription
// @route   POST /api/users/subscription/reactivate
// @access  Private
export const reactivateSubscription = async (req, res, next) => {
  try {
    // Get user
    const user = await User.findById(req.user.id);

    // Check if user has a cancelled subscription
    const subscription = await Subscription.findOne({
      user: req.user.id,
      status: 'cancelled'
    }).sort({ cancelledAt: -1 });

    if (!subscription) {
      return res.status(400).json({
        success: false,
        error: 'No cancelled subscription found'
      });
    }

    // Update subscription
    subscription.status = 'active';
    subscription.cancelledAt = null;
    subscription.cancelReason = null;

    // Update renewal date
    const now = new Date();
    subscription.renewalDate = subscription.interval === 'yearly'
      ? new Date(now.setFullYear(now.getFullYear() + 1))
      : new Date(now.setMonth(now.getMonth() + 1));

    // Save subscription
    await subscription.save();

    // Update user subscription
    user.subscription.status = 'active';

    // Save user
    await user.save();

    // Log activity
    await logActivity(user._id, 'subscription_reactivate', {
      details: { action: 'subscription_reactivate' }
    });

    res.status(200).json({
      success: true,
      data: subscription
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Save model to user's saved models
// @route   POST /api/users/saved-models/:id
// @access  Private
export const saveModel = async (req, res, next) => {
  try {
    const model = await Model.findById(req.params.id);

    if (!model) {
      return res.status(404).json({
        success: false,
        error: 'Model not found'
      });
    }

    // Check if model is already saved
    const user = await User.findById(req.user.id);
    if (user.savedModels.includes(model._id)) {
      return res.status(400).json({
        success: false,
        error: 'Model already saved'
      });
    }

    // Add model to saved models
    await User.findByIdAndUpdate(req.user.id, {
      $push: { savedModels: model._id }
    });

    res.status(200).json({
      success: true,
      data: {}
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Remove model from user's saved models
// @route   DELETE /api/users/saved-models/:id
// @access  Private
export const removeSavedModel = async (req, res, next) => {
  try {
    const model = await Model.findById(req.params.id);

    if (!model) {
      return res.status(404).json({
        success: false,
        error: 'Model not found'
      });
    }

    // Remove model from saved models
    await User.findByIdAndUpdate(req.user.id, {
      $pull: { savedModels: model._id }
    });

    res.status(200).json({
      success: true,
      data: {}
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Get user's download history
// @route   GET /api/users/download-history
// @access  Private
export const getDownloadHistory = async (req, res, next) => {
  try {
    const user = await User.findById(req.user.id).populate({
      path: 'downloadHistory.model',
      select: 'title imageUrl category format'
    });

    // Format download history for frontend
    const formattedHistory = user.downloadHistory.map(item => ({
      id: item._id,
      title: item.model ? item.model.title : 'Deleted Model',
      category: item.model ? item.model.category : '',
      date: item.downloadedAt,
      imageUrl: item.model ? item.model.imageUrl : 'https://via.placeholder.com/150',
      modelId: item.model ? item.model._id : null
    }));

    res.status(200).json({
      success: true,
      count: formattedHistory.length,
      data: formattedHistory
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Get user's security settings
// @route   GET /api/users/security
// @access  Private
export const getUserSecurity = async (req, res, next) => {
  try {
    const user = await User.findById(req.user.id);

    // Format security data
    const securityData = {
      twoFactorEnabled: user.twoFactorEnabled || false,
      lastPasswordChange: user.lastPasswordChange || user.updatedAt,
      loginHistory: user.loginHistory || []
    };

    res.status(200).json({
      success: true,
      data: securityData
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Enable two-factor authentication
// @route   POST /api/users/security/2fa/enable
// @access  Private
export const enable2FA = async (req, res, next) => {
  try {
    const user = await User.findById(req.user.id);

    // In a real implementation, we would generate a secret and QR code here
    // For now, we'll just return a mock QR code URL

    res.status(200).json({
      success: true,
      data: {
        qrCodeUrl: 'https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=otpauth://totp/3DSKETCHUP:' + user.email + '?secret=JBSWY3DPEHPK3PXP&issuer=3DSKETCHUP',
        secret: 'JBSWY3DPEHPK3PXP' // This would be a real secret in production
      }
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Verify two-factor authentication
// @route   POST /api/users/security/2fa/verify
// @access  Private
export const verify2FA = async (req, res, next) => {
  try {
    const { code } = req.body;

    if (!code) {
      return res.status(400).json({
        success: false,
        error: 'Please provide a verification code'
      });
    }

    // In a real implementation, we would verify the code against the user's secret
    // For now, we'll just check if the code is 6 digits

    if (code.length !== 6 || isNaN(code)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid verification code'
      });
    }

    // Update user
    const user = await User.findById(req.user.id);
    user.twoFactorEnabled = true;
    await user.save();

    // Log activity
    await logActivity(user._id, 'security_update', {
      details: { action: '2fa_enabled' }
    });

    res.status(200).json({
      success: true,
      message: 'Two-factor authentication enabled successfully'
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Disable two-factor authentication
// @route   POST /api/users/security/2fa/disable
// @access  Private
export const disable2FA = async (req, res, next) => {
  try {
    // Update user
    const user = await User.findById(req.user.id);
    user.twoFactorEnabled = false;
    await user.save();

    // Log activity
    await logActivity(user._id, 'security_update', {
      details: { action: '2fa_disabled' }
    });

    res.status(200).json({
      success: true,
      message: 'Two-factor authentication disabled successfully'
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Upload profile image
// @route   POST /api/users/profile/image
// @access  Private
export const uploadProfileImage = async (req, res, next) => {
  try {
    // Check if file exists
    if (!req.files || !req.files.profileImage) {
      return res.status(400).json({
        success: false,
        error: 'Please upload a file'
      });
    }

    const file = req.files.profileImage;

    // Check file type
    if (!file.mimetype.startsWith('image')) {
      return res.status(400).json({
        success: false,
        error: 'Please upload an image file'
      });
    }

    // Check file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
      return res.status(400).json({
        success: false,
        error: 'Image size should be less than 5MB'
      });
    }

    // Create custom filename
    const fileName = `user-${req.user.id}-${Date.now()}${path.parse(file.name).ext}`;

    // Create uploads directory if it doesn't exist
    const uploadDir = './uploads/profiles';
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }

    // Move file to upload directory
    file.mv(`${uploadDir}/${fileName}`, async (err) => {
      if (err) {
        console.error('File upload error:', err);
        return res.status(500).json({
          success: false,
          error: 'Problem with file upload'
        });
      }

      // Update user profile image
      const user = await User.findById(req.user.id);

      // Delete old profile image if it's not the default
      if (user.profileImage && user.profileImage !== 'default-profile.jpg' && fs.existsSync(`./uploads/profiles/${user.profileImage}`)) {
        fs.unlinkSync(`./uploads/profiles/${user.profileImage}`);
      }

      // Update user with new profile image
      user.profileImage = fileName;
      await user.save();

      // Log activity
      await logActivity(user._id, 'profile_update', {
        details: { action: 'profile_image_update' }
      });

      res.status(200).json({
        success: true,
        data: {
          profileImage: `/uploads/profiles/${fileName}`
        }
      });
    });
  } catch (error) {
    next(error);
  }
};
