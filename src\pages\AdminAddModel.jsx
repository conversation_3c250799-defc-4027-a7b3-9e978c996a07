import React, { useState, useCallback } from 'react';
import { motion } from 'framer-motion';
import { FiArrowLeft, FiPlus, FiList } from 'react-icons/fi';
import { useNavigate } from 'react-router-dom';
import { toast } from 'react-hot-toast';
import AddModelForm from '../components/admin/AddModelForm';

const AdminAddModel = () => {
  const navigate = useNavigate();
  const [showForm, setShowForm] = useState(true);

  const handleSuccess = (newModel) => {
  toast.success(`Model "${newModel.title}" created successfully!`);

    // Option to create another model or go back
    setTimeout(() => {
  const createAnother = window.confirm('Model created successfully! Would you like to create another model?'); 
      if (cachedData && !isExpired(cachedData)) {
  setShowForm(false);
        setTimeout(() => setShowForm(true), 100); // Reset form
      } else {
        navigate('/admin/models');
      }
    }, 1000);
  };

  const handleCancel = () => {
    const confirmCancel = window.confirm('Are you sure you want to cancel? Any unsaved changes will be lost.'; 
    if (cachedData && !isExpired(cachedData)) {
  navigate('/admin/models');
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Header */}
      <div className="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center gap-4">
              <button
                onClick={() => navigate('/admin/models')}
                className="flex items-center gap-2 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-colors"
              >
                <FiArrowLeft className="h-5 w-5" />
                <span>Back to Models</span>
              </button>

              <div className="h-6 w-px bg-gray-300 dark:bg-gray-600" />

              <div>
                <h1 className="text-xl font-semibold text-gray-900 dark:text-white">
                  Add New 3D Model
                </h1>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  Upload and manage 3D models with multiple preview images
                </p>
              </div>
            </div>

            <div className="flex items-center gap-3">
              <button
                onClick={() => navigate('/admin/models')}
                className="flex items-center gap-2 px-4 py-2 text-gray-700 dark:text-gray-300 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
              >
                <FiList className="h-4 w-4" />
                View All Models
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Instructions */}
        <div className="mb-8 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-6">
          <h2 className="text-lg font-semibold text-blue-900 dark:text-blue-100 mb-3">
            📋 Model Upload Guidelines
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 text-sm text-blue-800 dark:text-blue-200">
            <div>
              <h3 className="font-medium mb-2">📁 3D Model File:</h3>
              <ul className="space-y-1 list-disc list-inside">
                <li>Supported formats: SKP, MAX, BLEND, FBX, OBJ, GLTF</li>
                <li>Maximum file size: 100MB</li>
                <li>Ensure model is optimized and clean</li>
                <li>Include textures if applicable</li>
              </ul>
            </div>
            <div>
              <h3 className="font-medium mb-2">🖼️ Preview Images:</h3>
              <ul className="space-y-1 list-disc list-inside">
                <li>Upload 2-10 high-quality screenshots</li>
                <li>Supported formats: JPG, PNG, WebP</li>
                <li>Maximum size: 10MB per image</li>
                <li>Show different angles and details</li>
              </ul>
            </div>
          </div>
        </div>

        {/* Form */}
        {showForm && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
          >
            <AddModelForm
              onSuccess={handleSuccess}
              onCancel={handleCancel}
            />
          </motion.div>
        )}

        {/* Features Info */}
        <div className="mt-12 grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700">
            <div className="flex items-center gap-3 mb-4">
              <div className="w-10 h-10 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center">
                <FiPlus className="h-5 w-5 text-blue-600 dark:text-blue-400" />
              </div>
              <h3 className="font-semibold text-gray-900 dark:text-white">
                Multiple Images
              </h3>
            </div>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Upload multiple preview images to showcase your 3D model from different angles and highlight key features.
            </p>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700">
            <div className="flex items-center gap-3 mb-4">
              <div className="w-10 h-10 bg-green-100 dark:bg-green-900/30 rounded-lg flex items-center justify-center">
                <FiList className="h-5 w-5 text-green-600 dark:text-green-400" />
              </div>
              <h3 className="font-semibold text-gray-900 dark:text-white">
                Smart Organization
              </h3>
            </div>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Automatically organize images with thumbnail generation, main image selection, and gallery view for users.
            </p>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700">
            <div className="flex items-center gap-3 mb-4">
              <div className="w-10 h-10 bg-purple-100 dark:bg-purple-900/30 rounded-lg flex items-center justify-center">
                <FiArrowLeft className="h-5 w-5 text-purple-600 dark:text-purple-400" />
              </div>
              <h3 className="font-semibold text-gray-900 dark:text-white">
                Easy Management
              </h3>
            </div>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Drag and drop interface, image reordering, and bulk operations make model management simple and efficient.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AdminAddModel;
