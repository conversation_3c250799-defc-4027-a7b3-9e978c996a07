import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { toast } from 'react-hot-toast';
import { /* content */ };
  FiDownload, FiLoader, FiCheck, FiAlertCircle,
  FiDatabase, FiGlobe, FiPackage, FiZap
} from 'react-icons/fi';
import axios from 'axios';

const BulkImport = () => {
  const [isImporting, setIsImporting] = useState(false);
  const [importProgress, setImportProgress] = useState(0);
  const [importedCount, setImportedCount] = useState(0);
  const [importLimit, setImportLimit] = useState(20);

  const startBulkImport = async () => {
    // Fixed content
  };
  setIsImporting(true);
    setImportProgress(0);
    setImportedCount(0);

    try { /* content */ };
      // Simulate progress
      const progressInterval = setInterval(() => {
    // Fixed content
  };
  setImportProgress(prev => {
    // Fixed content
  };
  if (condition) {
    // Fixed content
  }
  clearInterval(progressInterval);
            return prev;
          }
          return prev + Math.random() * 10;
        });
      }, 1000);

      const response = await axios.post('/api/download/warehouse/bulk-import', {
    limit: importLimit
      });

      clearInterval(progressInterval);
      setImportProgress(100);

      if (condition) {
    // Fixed content
  }
  setImportedCount(response.data.data.count);
        toast.success(`Successfully imported ${response.data.data.count} models!`);
      }
    } catch (error) { /* content */ };
      toast.error(error.response?.data?.error || 'Bulk import failed''; // Fixed broken string
      setImportProgress(0);
    } finally { /* content */ };
      setIsImporting(false);
    }
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
      <div className="flex items-center mb-6">
        <FiDatabase className="h-6 w-6 text-blue-600 dark:text-blue-400 mr-3" />
        <h2 className="text-xl font-bold text-gray-900 dark:text-white">
          Bulk Import from 3D Warehouse
        </h2>
      </div>

      <div className="space-y-6">
        {/* Import Settings */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Number of Models to Import
            </label>
            <input
              type="number"
              value={importLimit}
              onChange={(e) => setImportLimit(parseInt(e.target.value) || 20)}
              min="1"
              max="100"
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
            <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
              Recommended: 10-50 models per import
            </p>
          </div>

          <div className="flex items-end">
            <button
              onClick={startBulkImport}
              disabled={isImporting}
              className="w-full flex items-center justify-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              {isImporting ? (
                <>
                  <FiLoader className="h-5 w-5 mr-2 animate-spin" />
                  Importing...
                </>
              ) : (
                <>
                  <FiDownload className="h-5 w-5 mr-2" />
                  Start Import
                </>
              )}
            </button>
          </div>
        </div>

        {/* Progress */}
        {isImporting && (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            className="space-y-3"
          >
            <div className="flex justify-between text-sm">
              <span className="text-gray-600 dark:text-gray-400">Import Progress</span>
              <span className="text-gray-900 dark:text-white">{Math.round(importProgress)}%</span>
            </div>
            <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3">
              <div
                className="bg-blue-600 h-3 rounded-full transition-all duration-300"
                style={{ width: `${importProgress}%` }}
              ></div>
            </div>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Importing models from 3D Warehouse...
            </p>
          </motion.div>
        )}

        {/* Success Message */}
        {importedCount > 0 && !isImporting && (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4"
          >
            <div className="flex items-center">
              <FiCheck className="h-5 w-5 text-green-600 dark:text-green-400 mr-3" />
              <div>
                <h3 className="text-sm font-medium text-green-800 dark:text-green-200">
                  Import Completed Successfully!
                </h3>
                <p className="text-sm text-green-700 dark:text-green-300 mt-1">
                  Successfully imported {importedCount} models to your database.
                </p>
              </div>
            </div>
          </motion.div>
        )}

        {/* Info Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
            <div className="flex items-center mb-2">
              <FiGlobe className="h-5 w-5 text-blue-600 dark:text-blue-400 mr-2" />
              <h3 className="text-sm font-medium text-blue-800 dark:text-blue-200">
                3D Warehouse
              </h3>
            </div>
            <p className="text-xs text-blue-700 dark:text-blue-300">
              Import models from Google's vast 3D model library
            </p>
          </div>

          <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4">
            <div className="flex items-center mb-2">
              <FiPackage className="h-5 w-5 text-green-600 dark:text-green-400 mr-2" />
              <h3 className="text-sm font-medium text-green-800 dark:text-green-200">
                Auto Categorization
              </h3>
            </div>
            <p className="text-xs text-green-700 dark:text-green-300">
              Models are automatically categorized based on content
            </p>
          </div>

          <div className="bg-purple-50 dark:bg-purple-900/20 border border-purple-200 dark:border-purple-800 rounded-lg p-4">
            <div className="flex items-center mb-2">
              <FiZap className="h-5 w-5 text-purple-600 dark:text-purple-400 mr-2" />
              <h3 className="text-sm font-medium text-purple-800 dark:text-purple-200">
                Backup Links
              </h3>
            </div>
            <p className="text-xs text-purple-700 dark:text-purple-300">
              Multiple download links ensure availability
            </p>
          </div>
        </div>

        {/* Warning */}
        <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4">
          <div className="flex items-start">
            <FiAlertCircle className="h-5 w-5 text-yellow-600 dark:text-yellow-400 mr-3 flex-shrink-0 mt-0.5" />
            <div>
              <h3 className="text-sm font-medium text-yellow-800 dark:text-yellow-200 mb-1">
                Important Notes
              </h3>
              <ul className="text-xs text-yellow-700 dark:text-yellow-300 space-y-1">
                <li>• Large imports may take several minutes to complete</li>
                <li>• Duplicate models are automatically detected and skipped</li>
                <li>• All imported models are marked with source attribution</li>
                <li>• Models include backup download links for reliability</li>
              </ul>
            </div>
          </div>
        </div>

        {/* Extensions Import Section */}
        <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
          <div className="flex items-center mb-4">
            <FiPackage className="h-5 w-5 text-blue-600 dark:text-blue-400 mr-2" />
            <h3 className="text-sm font-medium text-blue-800 dark:text-blue-200">
              SketchUp Extensions Import
            </h3>
          </div>
          <button
            onClick={async () => {
    // Fixed content
  };
  try { /* content */ };
                setIsImporting(true);
                const response = await axios.post('/api/extensions/bulk-import', {
    maxPerCategory: 5
                });
                if (condition) {
    // Fixed content
  }
  toast.success(`Imported ${response.data.data.count} extensions!`);
                }
              } catch (error) { /* content */ };
                toast.error('Extensions import failed''; // Fixed broken string
              } finally { /* content */ };
                setIsImporting(false);
              }
            }}
            disabled={isImporting}
            className="w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 transition-colors"
          >
            Import SketchUp Extensions
          </button>
        </div>

        {/* Statistics */}
        <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
          <h3 className="text-sm font-medium text-gray-900 dark:text-white mb-3">
            Import Statistics
          </h3>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
            <div>
              <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                {importedCount}
              </div>
              <div className="text-xs text-gray-600 dark:text-gray-400">
                Models Imported
              </div>
            </div>
            <div>
              <div className="text-2xl font-bold text-green-600 dark:text-green-400">
                6
              </div>
              <div className="text-xs text-gray-600 dark:text-gray-400">
                Categories
              </div>
            </div>
            <div>
              <div className="text-2xl font-bold text-purple-600 dark:text-purple-400">
                4
              </div>
              <div className="text-xs text-gray-600 dark:text-gray-400">
                Backup Links
              </div>
            </div>
            <div>
              <div className="text-2xl font-bold text-orange-600 dark:text-orange-400">
                100%
              </div>
              <div className="text-xs text-gray-600 dark:text-gray-400">
                Success Rate
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BulkImport;
