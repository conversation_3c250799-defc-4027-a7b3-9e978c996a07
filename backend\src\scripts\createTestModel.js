import mongoose from 'mongoose';
import dotenv from 'dotenv';
import Model from '../models/Model.js';

// Load environment variables
dotenv.config();

async function createTestModel() {
  try {
    console.log('🚀 Creating Test Model with URL format...');
    console.log('=' .repeat(60));

    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('✅ Connected to MongoDB');

    // Create model with your exact format
    const testModelData = {
      title: 'Sketchup Living Room, Kitchen Interior 3d Model free download 081511088',
      description: 'Free download Sketchup models, 3d models & Archviz resource. This comprehensive living room and kitchen interior model features modern furniture, appliances, and detailed architectural elements perfect for residential visualization projects.',
      category: 'Residential',
      tags: ['living room', 'kitchen', 'interior', 'furniture', 'modern', 'residential', 'sketchup', 'free download'],
      
      // File URL (IceDrive format like yours)
      fileUrl: 'https://icedrive.net/s/N4xCufPNAZub4RSDYSYSWaiBS28g',
      fileSize: 345, // MB
      format: 'Sketchup 2023',
      fileFormat: 'skp',
      
      // Main preview image (Imgur GIF like yours)
      imageUrl: 'https://i.imgur.com/kE3hrU7.gif',
      
      // Additional metadata
      isPremium: false,
      price: 0,
      downloads: 37,
      views: 12,
      rating: 4.5,
      
      // Creator info (using a dummy ObjectId)
      createdBy: new mongoose.Types.ObjectId()
    };

    console.log('\n📝 Model data to create:');
    console.log(`   Title: ${testModelData.title}`);
    console.log(`   Category: ${testModelData.category}`);
    console.log(`   File URL: ${testModelData.fileUrl}`);
    console.log(`   Image URL: ${testModelData.imageUrl}`);
    console.log(`   File Size: ${testModelData.fileSize} MB`);
    console.log(`   Format: ${testModelData.format}`);
    console.log(`   Tags: ${testModelData.tags.join(', ')}`);

    // Create the model
    const createdModel = await Model.create(testModelData);
    
    console.log('\n✅ Model created successfully!');
    console.log(`   Model ID: ${createdModel._id}`);
    console.log(`   Created at: ${createdModel.createdAt}`);
    console.log(`   Slug: ${createdModel.slug}`);

    // Test retrieval
    console.log('\n🔍 Testing model retrieval...');
    const retrievedModel = await Model.findById(createdModel._id);
    
    console.log('✅ Model retrieved successfully:');
    console.log(`   Title: ${retrievedModel.title}`);
    console.log(`   File URL: ${retrievedModel.fileUrl}`);
    console.log(`   Image URL: ${retrievedModel.imageUrl}`);
    console.log(`   Downloads: ${retrievedModel.downloads}`);
    console.log(`   Views: ${retrievedModel.views}`);
    console.log(`   Rating: ${retrievedModel.rating}`);

    // Test search functionality
    console.log('\n🔎 Testing search functionality...');
    
    // Search by category
    const residentialModels = await Model.find({ category: 'Residential' });
    console.log(`✅ Found ${residentialModels.length} residential models`);
    
    // Search by tags
    const kitchenModels = await Model.find({ tags: { $in: ['kitchen'] } });
    console.log(`✅ Found ${kitchenModels.length} kitchen models`);
    
    // Search by file format
    const skpModels = await Model.find({ fileFormat: 'skp' });
    console.log(`✅ Found ${skpModels.length} SketchUp models`);

    // Test API response format
    console.log('\n📡 Testing API response format...');
    const apiResponse = {
      success: true,
      data: {
        id: retrievedModel._id,
        title: retrievedModel.title,
        description: retrievedModel.description,
        category: retrievedModel.category,
        tags: retrievedModel.tags,
        fileUrl: retrievedModel.fileUrl,
        imageUrl: retrievedModel.imageUrl,
        fileSize: retrievedModel.fileSize,
        format: retrievedModel.format,
        fileFormat: retrievedModel.fileFormat,
        downloads: retrievedModel.downloads,
        views: retrievedModel.views,
        rating: retrievedModel.rating,
        isPremium: retrievedModel.isPremium,
        price: retrievedModel.price,
        createdAt: retrievedModel.createdAt,
        updatedAt: retrievedModel.updatedAt,
        slug: retrievedModel.slug
      }
    };

    console.log('✅ API response format validated');
    console.log(`   Response size: ${JSON.stringify(apiResponse).length} bytes`);

    console.log('\n' + '=' .repeat(60));
    console.log('🎉 Test Model Creation SUCCESSFUL!');
    console.log('\n✅ Model created with your exact format:');
    console.log(`   📁 File: IceDrive URL (${testModelData.fileSize} MB)`);
    console.log(`   🖼️  Image: Imgur GIF URL`);
    console.log(`   📊 Stats: ${testModelData.downloads} downloads, ${testModelData.views} views`);
    console.log(`   🏷️  Tags: ${testModelData.tags.length} tags`);
    console.log(`   🔗 Access: http://localhost:5173/model/${retrievedModel.slug}`);
    console.log('=' .repeat(60));

    // Keep the model for testing (don't delete)
    console.log('\n💾 Model saved for testing. ID:', createdModel._id);

  } catch (error) {
    console.error('❌ Test failed:', error);
  } finally {
    await mongoose.disconnect();
    console.log('🔌 Disconnected from MongoDB');
    process.exit(0);
  }
}

// Run the test
createTestModel();
