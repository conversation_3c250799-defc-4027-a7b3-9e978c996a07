import mongoose from 'mongoose';

const VirtualShowroomSchema = new mongoose.Schema({
  name: {
    type: String,
    required: [true, 'Please add a showroom name'],
    trim: true,
    maxlength: [100, 'Name cannot be more than 100 characters']
  },
  description: {
    type: String,
    maxlength: [1000, 'Description cannot be more than 1000 characters']
  },
  owner: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  type: {
    type: String,
    enum: ['personal', 'public', 'collaborative', 'exhibition'],
    default: 'personal'
  },
  theme: {
    type: String,
    enum: ['modern', 'classic', 'industrial', 'minimalist', 'gallery', 'outdoor', 'custom'],
    default: 'modern'
  },
  environment: {
    lighting: {
      type: String,
      enum: ['natural', 'studio', 'dramatic', 'soft', 'custom'],
      default: 'natural'
    },
    background: {
      type: String,
      enum: ['skybox', 'solid', 'gradient', 'hdri', 'custom'],
      default: 'skybox'
    },
    floor: {
      material: String,
      color: String,
      reflectivity: {
        type: Number,
        min: 0,
        max: 1,
        default: 0.3
      }
    }
  },
  layout: {
    type: String,
    enum: ['grid', 'circular', 'linear', 'organic', 'custom'],
    default: 'grid'
  },
  models: [{
    model: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Model',
      required: true
    },
    position: {
      x: { type: Number, default: 0 },
      y: { type: Number, default: 0 },
      z: { type: Number, default: 0 }
    },
    rotation: {
      x: { type: Number, default: 0 },
      y: { type: Number, default: 0 },
      z: { type: Number, default: 0 }
    },
    scale: {
      x: { type: Number, default: 1 },
      y: { type: Number, default: 1 },
      z: { type: Number, default: 1 }
    },
    spotlight: {
      enabled: { type: Boolean, default: false },
      color: { type: String, default: '#ffffff' },
      intensity: { type: Number, default: 1 }
    },
    label: {
      text: String,
      visible: { type: Boolean, default: true },
      position: {
        type: String,
        enum: ['top', 'bottom', 'left', 'right'],
        default: 'bottom'
      }
    },
    order: { type: Number, default: 0 }
  }],
  settings: {
    allowVR: { type: Boolean, default: true },
    allowAR: { type: Boolean, default: true },
    enablePhysics: { type: Boolean, default: false },
    enableShadows: { type: Boolean, default: true },
    enableReflections: { type: Boolean, default: true },
    maxVisitors: { type: Number, default: 50 },
    enableChat: { type: Boolean, default: true },
    enableVoiceChat: { type: Boolean, default: false },
    backgroundMusic: {
      enabled: { type: Boolean, default: false },
      url: String,
      volume: { type: Number, default: 0.3 }
    }
  },
  access: {
    isPublic: { type: Boolean, default: false },
    password: String,
    allowedUsers: [{
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    }],
    allowedRoles: [{
      type: String,
      enum: ['viewer', 'editor', 'admin']
    }]
  },
  analytics: {
    totalVisits: { type: Number, default: 0 },
    uniqueVisitors: { type: Number, default: 0 },
    averageVisitDuration: { type: Number, default: 0 }, // in seconds
    mostViewedModel: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Model'
    },
    peakConcurrentUsers: { type: Number, default: 0 }
  },
  featured: { type: Boolean, default: false },
  tags: [String],
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

// Indexes for performance
VirtualShowroomSchema.index({ owner: 1 });
VirtualShowroomSchema.index({ type: 1, 'access.isPublic': 1 });
VirtualShowroomSchema.index({ featured: 1 });
VirtualShowroomSchema.index({ tags: 1 });
VirtualShowroomSchema.index({ createdAt: -1 });

// Update timestamps pre-save
VirtualShowroomSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  next();
});

// Virtual for model count
VirtualShowroomSchema.virtual('modelCount').get(function() {
  return this.models.length;
});

// Static method to get public showrooms
VirtualShowroomSchema.statics.getPublicShowrooms = async function(limit = 20) {
  return await this.find({ 'access.isPublic': true })
    .populate('owner', 'name profileImage')
    .populate('models.model', 'title imageUrl category')
    .sort({ featured: -1, 'analytics.totalVisits': -1 })
    .limit(limit);
};

export default mongoose.model('VirtualShowroom', VirtualShowroomSchema);
