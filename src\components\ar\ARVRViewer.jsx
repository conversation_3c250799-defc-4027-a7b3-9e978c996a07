import React, { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Smartphone, Glasses, Monitor, Maximize, RotateCcw,
  Move3D, ZoomIn, ZoomOut, Sun, Moon, Palette,
  Camera, Video, Share2, Download, Settings,
  Eye, EyeOff, Grid, Layers, Ruler, Compass
} from 'lucide-react';

const ARVRViewer = ({ modelUrl, modelData }) => {
  const [viewMode, setViewMode] = useState('3d'); // 3d, ar, vr
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [isARSupported, setIsARSupported] = useState(false);
  const [isVRSupported, setIsVRSupported] = useState(false);
  const [lighting, setLighting] = useState('natural');
  const [environment, setEnvironment] = useState('studio');
  const [showGrid, setShowGrid] = useState(false);
  const [showMeasurements, setShowMeasurements] = useState(false);
  const [cameraPosition, setCameraPosition] = useState({ x: 0, y: 0, z: 5 });
  const [modelRotation, setModelRotation] = useState({ x: 0, y: 0, z: 0 });
  const [modelScale, setModelScale] = useState(1);
  const [isRecording, setIsRecording] = useState(false);
  const [annotations, setAnnotations] = useState([]);
  
  const viewerRef = useRef(null);
  const sceneRef = useRef(null);
  const rendererRef = useRef(null);
  const cameraRef = useRef(null);
  const controlsRef = useRef(null);

  // Lighting presets
  const lightingPresets = {
    natural: { name: 'Ánh sáng tự nhiên', intensity: 1, color: '#ffffff' },
    warm: { name: 'Ánh sáng ấm', intensity: 0.8, color: '#ffcc88' },
    cool: { name: 'Ánh sáng lạnh', intensity: 0.9, color: '#88ccff' },
    dramatic: { name: 'Ánh sáng kịch tính', intensity: 1.2, color: '#ffffff' },
    sunset: { name: 'Hoàng hôn', intensity: 0.7, color: '#ff8844' }
  };

  // Environment presets
  const environmentPresets = {
    studio: { name: 'Studio', background: '#f0f0f0' },
    outdoor: { name: 'Ngoài trời', background: 'linear-gradient(to bottom, #87CEEB, #98FB98)' },
    indoor: { name: 'Trong nhà', background: '#e8e8e8' },
    dark: { name: 'Tối', background: '#1a1a1a' },
    custom: { name: 'Tùy chỉnh', background: '#ffffff' }
  };

  useEffect(() => {
    checkARVRSupport();
    initializeViewer();
    
    return () => {
      cleanup();
    };
  }, []);

  const checkARVRSupport = () => {
    // Check WebXR support
    if ('xr' in navigator) {
      navigator.xr.isSessionSupported('immersive-ar').then((supported) => {
        setIsARSupported(supported);
      });
      
      navigator.xr.isSessionSupported('immersive-vr').then((supported) => {
        setIsVRSupported(supported);
      });
    }
  };

  const initializeViewer = () => {
    if (!viewerRef.current) return;

    // Initialize Three.js scene
    const scene = new THREE.Scene();
    const camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
    const renderer = new THREE.WebGLRenderer({ antialias: true, alpha: true });
    
    renderer.setSize(viewerRef.current.clientWidth, viewerRef.current.clientHeight);
    renderer.setClearColor(0x000000, 0);
    renderer.shadowMap.enabled = true;
    renderer.shadowMap.type = THREE.PCFSoftShadowMap;
    
    viewerRef.current.appendChild(renderer.domElement);
    
    // Store references
    sceneRef.current = scene;
    cameraRef.current = camera;
    rendererRef.current = renderer;
    
    // Setup camera position
    camera.position.set(cameraPosition.x, cameraPosition.y, cameraPosition.z);
    
    // Add lighting
    setupLighting();
    
    // Load model
    loadModel();
    
    // Setup controls
    setupControls();
    
    // Start render loop
    animate();
  };

  const setupLighting = () => {
    const scene = sceneRef.current;
    if (!scene) return;
    
    // Clear existing lights
    const lights = scene.children.filter(child => child.isLight);
    lights.forEach(light => scene.remove(light));
    
    const preset = lightingPresets[lighting];
    
    // Ambient light
    const ambientLight = new THREE.AmbientLight(preset.color, preset.intensity * 0.4);
    scene.add(ambientLight);
    
    // Directional light
    const directionalLight = new THREE.DirectionalLight(preset.color, preset.intensity * 0.8);
    directionalLight.position.set(10, 10, 5);
    directionalLight.castShadow = true;
    directionalLight.shadow.mapSize.width = 2048;
    directionalLight.shadow.mapSize.height = 2048;
    scene.add(directionalLight);
    
    // Point lights for dramatic effect
    if (lighting === 'dramatic') {
      const pointLight1 = new THREE.PointLight('#ffffff', 0.5, 100);
      pointLight1.position.set(-10, 10, 10);
      scene.add(pointLight1);
      
      const pointLight2 = new THREE.PointLight('#ffffff', 0.3, 100);
      pointLight2.position.set(10, -10, -10);
      scene.add(pointLight2);
    }
  };

  const loadModel = () => {
    if (!modelUrl || !sceneRef.current) return;
    
    const loader = new THREE.GLTFLoader();
    
    loader.load(
      modelUrl,
      (gltf) => {
        const model = gltf.scene;
        
        // Scale and position model
        model.scale.setScalar(modelScale);
        model.rotation.set(modelRotation.x, modelRotation.y, modelRotation.z);
        
        // Enable shadows
        model.traverse((child) => {
          if (child.isMesh) {
            child.castShadow = true;
            child.receiveShadow = true;
          }
        });
        
        sceneRef.current.add(model);
        
        // Center model
        const box = new THREE.Box3().setFromObject(model);
        const center = box.getCenter(new THREE.Vector3());
        model.position.sub(center);
      },
      (progress) => {
        console.log('Loading progress:', progress);
      },
      (error) => {
        console.error('Error loading model:', error);
      }
    );
  };

  const setupControls = () => {
    if (!cameraRef.current || !rendererRef.current) return;
    
    const controls = new THREE.OrbitControls(cameraRef.current, rendererRef.current.domElement);
    controls.enableDamping = true;
    controls.dampingFactor = 0.05;
    controls.enableZoom = true;
    controls.enablePan = true;
    controls.enableRotate = true;
    
    controlsRef.current = controls;
  };

  const animate = () => {
    requestAnimationFrame(animate);
    
    if (controlsRef.current) {
      controlsRef.current.update();
    }
    
    if (rendererRef.current && sceneRef.current && cameraRef.current) {
      rendererRef.current.render(sceneRef.current, cameraRef.current);
    }
  };

  const enterARMode = async () => {
    if (!isARSupported) {
      alert('AR không được hỗ trợ trên thiết bị này');
      return;
    }
    
    try {
      const session = await navigator.xr.requestSession('immersive-ar', {
        requiredFeatures: ['local', 'hit-test']
      });
      
      setViewMode('ar');
      
      // Setup AR session
      const referenceSpace = await session.requestReferenceSpace('local');
      
      session.addEventListener('end', () => {
        setViewMode('3d');
      });
      
    } catch (error) {
      console.error('Failed to start AR session:', error);
      alert('Không thể khởi động AR');
    }
  };

  const enterVRMode = async () => {
    if (!isVRSupported) {
      alert('VR không được hỗ trợ trên thiết bị này');
      return;
    }
    
    try {
      const session = await navigator.xr.requestSession('immersive-vr');
      
      setViewMode('vr');
      
      session.addEventListener('end', () => {
        setViewMode('3d');
      });
      
    } catch (error) {
      console.error('Failed to start VR session:', error);
      alert('Không thể khởi động VR');
    }
  };

  const toggleFullscreen = () => {
    if (!isFullscreen) {
      if (viewerRef.current.requestFullscreen) {
        viewerRef.current.requestFullscreen();
      }
    } else {
      if (document.exitFullscreen) {
        document.exitFullscreen();
      }
    }
    setIsFullscreen(!isFullscreen);
  };

  const resetCamera = () => {
    if (cameraRef.current && controlsRef.current) {
      cameraRef.current.position.set(0, 0, 5);
      controlsRef.current.reset();
    }
  };

  const takeScreenshot = () => {
    if (!rendererRef.current) return;
    
    const canvas = rendererRef.current.domElement;
    const link = document.createElement('a');
    link.download = `3d-model-screenshot-${Date.now()}.png`;
    link.href = canvas.toDataURL();
    link.click();
  };

  const startRecording = () => {
    if (!rendererRef.current) return;
    
    setIsRecording(true);
    
    // Start screen recording
    const canvas = rendererRef.current.domElement;
    const stream = canvas.captureStream(30);
    
    const mediaRecorder = new MediaRecorder(stream);
    const chunks = [];
    
    mediaRecorder.ondataavailable = (event) => {
      chunks.push(event.data);
    };
    
    mediaRecorder.onstop = () => {
      const blob = new Blob(chunks, { type: 'video/webm' });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.download = `3d-model-recording-${Date.now()}.webm`;
      link.href = url;
      link.click();
      setIsRecording(false);
    };
    
    mediaRecorder.start();
    
    // Stop recording after 30 seconds
    setTimeout(() => {
      mediaRecorder.stop();
    }, 30000);
  };

  const addAnnotation = (position, text) => {
    const annotation = {
      id: Date.now(),
      position,
      text,
      timestamp: new Date()
    };
    
    setAnnotations(prev => [...prev, annotation]);
  };

  const cleanup = () => {
    if (rendererRef.current && viewerRef.current) {
      viewerRef.current.removeChild(rendererRef.current.domElement);
    }
  };

  return (
    <div className="relative w-full h-full bg-gray-900 rounded-xl overflow-hidden">
      {/* 3D Viewer Container */}
      <div
        ref={viewerRef}
        className="w-full h-full relative"
        style={{ background: environmentPresets[environment].background }}
      >
        {/* Grid Overlay */}
        {showGrid && (
          <div className="absolute inset-0 pointer-events-none">
            <div className="w-full h-full opacity-20" style={{
              backgroundImage: `
                linear-gradient(rgba(255,255,255,0.1) 1px, transparent 1px),
                linear-gradient(90deg, rgba(255,255,255,0.1) 1px, transparent 1px)
              `,
              backgroundSize: '20px 20px'
            }} />
          </div>
        )}
        
        {/* Annotations */}
        {annotations.map((annotation) => (
          <div
            key={annotation.id}
            className="absolute bg-blue-600 text-white px-2 py-1 rounded-lg text-sm pointer-events-none"
            style={{
              left: annotation.position.x,
              top: annotation.position.y,
              transform: 'translate(-50%, -100%)'
            }}
          >
            {annotation.text}
          </div>
        ))}
      </div>
      
      {/* Top Controls */}
      <div className="absolute top-4 left-4 right-4 flex items-center justify-between">
        <div className="flex items-center space-x-2">
          {/* View Mode Buttons */}
          <div className="flex bg-black/50 rounded-lg p-1">
            <button
              onClick={() => setViewMode('3d')}
              className={`flex items-center space-x-2 px-3 py-2 rounded-md transition-colors ${
                viewMode === '3d' ? 'bg-blue-600 text-white' : 'text-gray-300 hover:text-white'
              }`}
            >
              <Monitor className="w-4 h-4" />
              <span className="text-sm">3D</span>
            </button>
            
            <button
              onClick={enterARMode}
              disabled={!isARSupported}
              className={`flex items-center space-x-2 px-3 py-2 rounded-md transition-colors ${
                viewMode === 'ar' ? 'bg-green-600 text-white' : 
                isARSupported ? 'text-gray-300 hover:text-white' : 'text-gray-500 cursor-not-allowed'
              }`}
            >
              <Smartphone className="w-4 h-4" />
              <span className="text-sm">AR</span>
            </button>
            
            <button
              onClick={enterVRMode}
              disabled={!isVRSupported}
              className={`flex items-center space-x-2 px-3 py-2 rounded-md transition-colors ${
                viewMode === 'vr' ? 'bg-purple-600 text-white' : 
                isVRSupported ? 'text-gray-300 hover:text-white' : 'text-gray-500 cursor-not-allowed'
              }`}
            >
              <Glasses className="w-4 h-4" />
              <span className="text-sm">VR</span>
            </button>
          </div>
          
          {/* Lighting Control */}
          <select
            value={lighting}
            onChange={(e) => setLighting(e.target.value)}
            className="bg-black/50 text-white px-3 py-2 rounded-lg text-sm"
          >
            {Object.entries(lightingPresets).map(([key, preset]) => (
              <option key={key} value={key}>{preset.name}</option>
            ))}
          </select>
          
          {/* Environment Control */}
          <select
            value={environment}
            onChange={(e) => setEnvironment(e.target.value)}
            className="bg-black/50 text-white px-3 py-2 rounded-lg text-sm"
          >
            {Object.entries(environmentPresets).map(([key, preset]) => (
              <option key={key} value={key}>{preset.name}</option>
            ))}
          </select>
        </div>
        
        <div className="flex items-center space-x-2">
          {/* View Options */}
          <button
            onClick={() => setShowGrid(!showGrid)}
            className={`p-2 rounded-lg transition-colors ${
              showGrid ? 'bg-blue-600 text-white' : 'bg-black/50 text-gray-300 hover:text-white'
            }`}
            title="Hiển thị lưới"
          >
            <Grid className="w-4 h-4" />
          </button>
          
          <button
            onClick={() => setShowMeasurements(!showMeasurements)}
            className={`p-2 rounded-lg transition-colors ${
              showMeasurements ? 'bg-blue-600 text-white' : 'bg-black/50 text-gray-300 hover:text-white'
            }`}
            title="Hiển thị kích thước"
          >
            <Ruler className="w-4 h-4" />
          </button>
          
          <button
            onClick={toggleFullscreen}
            className="p-2 bg-black/50 text-gray-300 hover:text-white rounded-lg transition-colors"
            title="Toàn màn hình"
          >
            <Maximize className="w-4 h-4" />
          </button>
        </div>
      </div>
      
      {/* Bottom Controls */}
      <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2">
        <div className="flex items-center space-x-2 bg-black/50 rounded-lg p-2">
          <button
            onClick={resetCamera}
            className="p-2 text-gray-300 hover:text-white rounded-lg transition-colors"
            title="Đặt lại camera"
          >
            <RotateCcw className="w-5 h-5" />
          </button>
          
          <button
            onClick={takeScreenshot}
            className="p-2 text-gray-300 hover:text-white rounded-lg transition-colors"
            title="Chụp ảnh màn hình"
          >
            <Camera className="w-5 h-5" />
          </button>
          
          <button
            onClick={startRecording}
            disabled={isRecording}
            className={`p-2 rounded-lg transition-colors ${
              isRecording 
                ? 'bg-red-600 text-white animate-pulse' 
                : 'text-gray-300 hover:text-white'
            }`}
            title="Quay video"
          >
            <Video className="w-5 h-5" />
          </button>
          
          <button
            className="p-2 text-gray-300 hover:text-white rounded-lg transition-colors"
            title="Chia sẻ"
          >
            <Share2 className="w-5 h-5" />
          </button>
          
          <button
            className="p-2 text-gray-300 hover:text-white rounded-lg transition-colors"
            title="Tải xuống"
          >
            <Download className="w-5 h-5" />
          </button>
        </div>
      </div>
      
      {/* Right Panel - Model Info */}
      <div className="absolute top-4 right-4 w-64 bg-black/50 rounded-lg p-4 text-white">
        <h3 className="font-semibold mb-3">Thông tin Model</h3>
        <div className="space-y-2 text-sm">
          <div className="flex justify-between">
            <span>Chế độ:</span>
            <span className="capitalize">{viewMode}</span>
          </div>
          <div className="flex justify-between">
            <span>Ánh sáng:</span>
            <span>{lightingPresets[lighting].name}</span>
          </div>
          <div className="flex justify-between">
            <span>Môi trường:</span>
            <span>{environmentPresets[environment].name}</span>
          </div>
          <div className="flex justify-between">
            <span>Tỷ lệ:</span>
            <span>{modelScale.toFixed(2)}x</span>
          </div>
        </div>
        
        {/* Scale Control */}
        <div className="mt-4">
          <label className="block text-xs text-gray-300 mb-1">Tỷ lệ</label>
          <input
            type="range"
            min="0.1"
            max="3"
            step="0.1"
            value={modelScale}
            onChange={(e) => setModelScale(parseFloat(e.target.value))}
            className="w-full"
          />
        </div>
      </div>
      
      {/* Loading Indicator */}
      {viewMode !== '3d' && (
        <div className="absolute inset-0 bg-black/70 flex items-center justify-center">
          <div className="text-center text-white">
            <div className="w-16 h-16 border-4 border-blue-600 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
            <h3 className="text-xl font-bold mb-2">
              {viewMode === 'ar' ? 'Đang khởi động AR...' : 'Đang khởi động VR...'}
            </h3>
            <p className="text-gray-300">
              Vui lòng đợi trong giây lát
            </p>
          </div>
        </div>
      )}
    </div>
  );
};

export default ARVRViewer;
