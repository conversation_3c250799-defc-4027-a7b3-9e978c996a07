import mongoose from 'mongoose';
import dotenv from 'dotenv';
import Model from '../models/Model.js';

// Load environment variables
dotenv.config();

const enhancedModels = [
  {
    title: "Modern Ergonomic Office Chair",
    description: "Contemporary office chair with ergonomic design, adjustable height, lumbar support, and breathable mesh back. Perfect for modern workspaces with black metal frame and fabric upholstery.",
    category: "Furniture",
    tags: ["chair", "office", "modern", "ergonomic", "mesh", "adjustable", "black", "metal", "fabric", "contemporary", "workspace"],
    fileUrl: "https://3dwarehouse.sketchup.com/model/office-chair",
    imageUrl: "https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=400",
    fileSize: 2.8,
    format: "Sketchup 2023",
    fileFormat: "skp",
    downloads: 1250,
    rating: 4.6,
    createdBy: new mongoose.Types.ObjectId(),
    views: 3200
  },
  {
    title: "Industrial Coffee Table with Steel Frame",
    description: "Industrial style coffee table featuring reclaimed wood top and black steel frame. Perfect for modern living rooms with rustic industrial aesthetic.",
    category: "Furniture",
    tags: ["table", "coffee table", "industrial", "steel", "wood", "reclaimed", "black", "metal", "living room", "rustic"],
    fileUrl: "https://3dwarehouse.sketchup.com/model/industrial-coffee-table",
    imageUrl: "https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=400",
    fileSize: 3.2,
    format: "Sketchup 2023",
    fileFormat: "skp",
    downloads: 1580,
    rating: 4.7,
    createdBy: new mongoose.Types.ObjectId(),
    views: 4200
  },
  {
    title: "Contemporary Single Family House",
    description: "Modern single-family house with clean lines, large windows, flat roof, and open floor plan. Features concrete and glass materials with minimalist design.",
    category: "Residential",
    tags: ["house", "contemporary", "modern", "single family", "concrete", "glass", "flat roof", "windows", "residential", "minimalist"],
    fileUrl: "https://3dwarehouse.sketchup.com/model/contemporary-house",
    imageUrl: "https://images.unsplash.com/photo-1600596542815-ffad4c1539a9?w=400",
    fileSize: 15.6,
    format: "Sketchup 2023",
    fileFormat: "skp",
    downloads: 3200,
    rating: 4.9,
    createdBy: new mongoose.Types.ObjectId(),
    views: 8900
  },
  {
    title: "Traditional Victorian House",
    description: "Classic Victorian house with ornate details, bay windows, decorative trim, and traditional proportions. Brick and wood construction with authentic period features.",
    category: "Residential",
    tags: ["house", "victorian", "traditional", "bay windows", "brick", "wood", "ornate", "decorative", "residential", "classic"],
    fileUrl: "https://3dwarehouse.sketchup.com/model/victorian-house",
    imageUrl: "https://images.unsplash.com/photo-1600596542815-ffad4c1539a9?w=400",
    fileSize: 18.3,
    format: "Sketchup 2023",
    fileFormat: "skp",
    downloads: 2800,
    rating: 4.6,
    createdBy: new mongoose.Types.ObjectId(),
    views: 7200
  },
  {
    title: "Modern Office Building",
    description: "Contemporary office building with glass curtain wall, steel structure, and sustainable design features. Multi-story commercial architecture with modern facade.",
    category: "Commercial",
    tags: ["office building", "commercial", "modern", "glass", "steel", "curtain wall", "sustainable", "multi-story", "contemporary"],
    fileUrl: "https://3dwarehouse.sketchup.com/model/office-building",
    imageUrl: "https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?w=400",
    fileSize: 25.7,
    format: "Sketchup 2023",
    fileFormat: "skp",
    downloads: 1600,
    rating: 4.4,
    createdBy: new mongoose.Types.ObjectId(),
    views: 4300
  },
  {
    title: "Modern Garden Pavilion",
    description: "Contemporary garden pavilion with wooden structure, green roof, and integration with landscape. Perfect for outdoor spaces with natural materials.",
    category: "Landscape/Garden",
    tags: ["pavilion", "garden", "modern", "wood", "green roof", "landscape", "outdoor", "structure", "natural", "contemporary"],
    fileUrl: "https://3dwarehouse.sketchup.com/model/garden-pavilion",
    imageUrl: "https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400",
    fileSize: 6.8,
    format: "Sketchup 2023",
    fileFormat: "skp",
    downloads: 980,
    rating: 4.5,
    createdBy: new mongoose.Types.ObjectId(),
    views: 2600
  },
  {
    title: "Modern Entrance Door with Sidelight",
    description: "Contemporary entrance door with glass sidelight, steel frame, and minimalist design. Perfect for modern homes with clean architectural lines.",
    category: "Exterior",
    tags: ["door", "entrance", "modern", "glass", "sidelight", "steel", "minimalist", "exterior", "contemporary", "architectural"],
    fileUrl: "https://3dwarehouse.sketchup.com/model/modern-door",
    imageUrl: "https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400",
    fileSize: 2.1,
    format: "Sketchup 2023",
    fileFormat: "skp",
    downloads: 1800,
    rating: 4.6,
    createdBy: new mongoose.Types.ObjectId(),
    views: 4700
  },
  {
    title: "Large Picture Window",
    description: "Floor-to-ceiling picture window with aluminum frame and energy-efficient glass. Maximizes natural light and views with modern design.",
    category: "Exterior",
    tags: ["window", "picture window", "floor-to-ceiling", "aluminum", "glass", "energy efficient", "natural light", "modern"],
    fileUrl: "https://3dwarehouse.sketchup.com/model/picture-window",
    imageUrl: "https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400",
    fileSize: 1.7,
    format: "Sketchup 2023",
    fileFormat: "skp",
    downloads: 2200,
    rating: 4.8,
    createdBy: new mongoose.Types.ObjectId(),
    views: 5900
  },
  {
    title: "Scandinavian Oak Dining Table",
    description: "Minimalist Scandinavian dining table made from solid oak wood. Clean lines and natural finish for modern homes with Nordic aesthetic.",
    category: "Furniture",
    tags: ["table", "dining table", "scandinavian", "oak", "wood", "minimalist", "natural", "dining room", "nordic", "solid wood"],
    fileUrl: "https://3dwarehouse.sketchup.com/model/oak-dining-table",
    imageUrl: "https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=400",
    fileSize: 4.1,
    format: "Sketchup 2023",
    fileFormat: "skp",
    downloads: 2100,
    rating: 4.8,
    createdBy: new mongoose.Types.ObjectId(),
    views: 5600
  },
  {
    title: "Indoor Plant Collection",
    description: "Collection of popular indoor plants including fiddle leaf fig, monstera, and snake plant. Perfect for adding life to interiors with natural greenery.",
    category: "Furniture",
    tags: ["plants", "indoor", "fiddle leaf fig", "monstera", "snake plant", "decorative", "green", "nature", "interior", "collection"],
    fileUrl: "https://3dwarehouse.sketchup.com/model/indoor-plants",
    imageUrl: "https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=400",
    fileSize: 3.4,
    format: "Sketchup 2023",
    fileFormat: "skp",
    downloads: 1650,
    rating: 4.7,
    createdBy: new mongoose.Types.ObjectId(),
    views: 4400
  }
];

async function addEnhancedModels() {
  try {
    console.log('🚀 Connecting to MongoDB...');
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('✅ Connected to MongoDB');

    console.log('📦 Adding enhanced 3D models...');
    const insertedModels = await Model.insertMany(enhancedModels);
    
    console.log(`✅ Successfully added ${insertedModels.length} enhanced models`);
    
    // Display summary
    const categoryCounts = {};
    insertedModels.forEach(model => {
      categoryCounts[model.category] = (categoryCounts[model.category] || 0) + 1;
    });
    
    console.log('\n📊 Models by category:');
    Object.entries(categoryCounts).forEach(([category, count]) => {
      console.log(`   ${category}: ${count} models`);
    });

    console.log('\n🎯 Enhanced features added:');
    console.log('   ✅ Comprehensive object vocabulary');
    console.log('   ✅ Detailed material specifications');
    console.log('   ✅ Enhanced color descriptions');
    console.log('   ✅ Architectural style variations');
    console.log('   ✅ Room and building type diversity');

  } catch (error) {
    console.error('❌ Error adding enhanced models:', error);
  } finally {
    await mongoose.disconnect();
    console.log('🔌 Disconnected from MongoDB');
    process.exit(0);
  }
}

// Run the script
addEnhancedModels();
