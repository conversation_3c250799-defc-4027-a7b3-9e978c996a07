{"name": "3dsketchup", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "dev:no-hmr": "VITE_DISABLE_HMR=true vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview", "dedupe": "npm dedupe && echo 'Deduplication complete!'", "postinstall": "npm run dedupe && npm run prepare", "prepare": "husky install", "check-react": "npm ls react && npm ls react-dom", "clear-cache": "rimraf node_modules/.vite && echo 'Vite cache cleared!'", "clean": "rimraf node_modules && rimraf dist && echo 'Node modules and dist directory removed!'", "clean:full": "rimraf node_modules && rimraf dist && rimraf .vite && echo 'Full cleanup complete!' && npm install"}, "dependencies": {"@google/generative-ai": "^0.24.1", "@headlessui/react": "^2.2.4", "@heroicons/react": "^2.2.0", "@material-tailwind/react": "^2.1.10", "@react-three/drei": "^9.99.0", "@react-three/fiber": "^8.15.19", "@react-three/xr": "^6.6.17", "axios": "^1.9.0", "file-saver": "^2.0.5", "framer-motion": "^12.12.1", "html2canvas": "^1.4.1", "jspdf": "^3.0.1", "jwt-decode": "^4.0.0", "node-fetch": "^3.3.2", "react": "^18.2.0", "react-dom": "^18.2.0", "react-helmet-async": "^2.0.5", "react-hot-toast": "^2.5.2", "react-icons": "^5.5.0", "react-markdown": "^10.1.0", "react-paginate": "^8.3.0", "react-router-dom": "^6.22.3", "react-slick": "^0.30.3", "react-use": "^17.6.0", "recharts": "^2.15.3", "rollup-plugin-visualizer": "^5.14.0", "sharp": "^0.34.2", "slick-carousel": "^1.8.1", "three": "^0.150.1", "three-stdlib": "^2.36.0", "uuid": "^9.0.1", "vite-plugin-compression": "^0.5.1", "vite-plugin-pwa": "^1.0.0", "workbox-window": "^7.3.0", "zustand": "^4.5.5"}, "devDependencies": {"@babel/plugin-transform-react-jsx": "^7.27.1", "@babel/preset-react": "^7.27.1", "@eslint/js": "^9.25.0", "@tailwindcss/forms": "^0.5.10", "@types/react": "^18.2.66", "@types/react-dom": "^18.2.22", "@vitejs/plugin-react": "^4.4.1", "autoprefixer": "^10.4.16", "bcryptjs": "^2.4.3", "body-parser": "^1.20.2", "concurrently": "^8.2.2", "cors": "^2.8.5", "dotenv": "^16.3.1", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "express": "^4.18.2", "globals": "^16.0.0", "husky": "^8.0.3", "jsonwebtoken": "^9.0.2", "mongoose": "^8.0.3", "postcss": "^8.4.31", "rimraf": "^5.0.5", "tailwindcss": "^3.3.3", "vite": "^6.3.5"}}