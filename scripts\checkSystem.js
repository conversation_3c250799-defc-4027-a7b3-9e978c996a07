#!/usr/bin/env node

import fetch from 'node-fetch';
import { exec } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);

const FRONTEND_URL = 'http://localhost:5173';
const BACKEND_URL = 'http://localhost:5002';

console.log('🔍 3DSKETCHUP.NET System Health Check');
console.log('=====================================\n');

// Check if a service is running
const checkService = async (name, url) => {
  try {
    const response = await fetch(url, { timeout: 5000 });
    if (response.ok) {
      console.log(`✅ ${name}: Running (${response.status})`);
      return true;
    } else {
      console.log(`⚠️ ${name}: Responding but with status ${response.status}`);
      return false;
    }
  } catch (error) {
    console.log(`❌ ${name}: Not running (${error.message})`);
    return false;
  }
};

// Check API endpoints
const checkAPI = async (endpoint, description) => {
  try {
    const response = await fetch(`${BACKEND_URL}/api${endpoint}`, { timeout: 10000 });
    const data = await response.json();
    
    if (response.ok && data.success !== false) {
      const dataSize = JSON.stringify(data).length;
      console.log(`✅ ${description}: OK (${dataSize} bytes)`);
      return { success: true, data };
    } else {
      console.log(`❌ ${description}: Failed (${response.status})`);
      return { success: false, error: data };
    }
  } catch (error) {
    console.log(`❌ ${description}: Error (${error.message})`);
    return { success: false, error: error.message };
  }
};

// Check database connection
const checkDatabase = async () => {
  try {
    const response = await fetch(`${BACKEND_URL}/api/stats`, { timeout: 10000 });
    const data = await response.json();
    
    if (response.ok && data.success && data.data) {
      console.log(`✅ Database: Connected (${data.data.totalModels} models)`);
      return true;
    } else {
      console.log(`❌ Database: Connection issues`);
      return false;
    }
  } catch (error) {
    console.log(`❌ Database: Error (${error.message})`);
    return false;
  }
};

// Check processes
const checkProcesses = async () => {
  try {
    // Check for Node.js processes
    const { stdout } = await execAsync('tasklist /FI "IMAGENAME eq node.exe" /FO CSV');
    const nodeProcesses = stdout.split('\n').filter(line => line.includes('node.exe')).length - 1;
    
    console.log(`📊 Node.js processes: ${nodeProcesses} running`);
    
    return nodeProcesses > 0;
  } catch (error) {
    console.log(`⚠️ Could not check processes: ${error.message}`);
    return false;
  }
};

// Main health check
const runHealthCheck = async () => {
  console.log('🚀 Starting health check...\n');
  
  const results = {
    frontend: false,
    backend: false,
    database: false,
    processes: false,
    apis: {
      models: false,
      categories: false,
      stats: false,
      featured: false,
      popular: false,
      recent: false
    }
  };

  // Check services
  console.log('🔧 Checking Services:');
  results.frontend = await checkService('Frontend (Vite)', FRONTEND_URL);
  results.backend = await checkService('Backend (Express)', BACKEND_URL);
  
  console.log('\n💾 Checking Database:');
  results.database = await checkDatabase();
  
  console.log('\n⚙️ Checking Processes:');
  results.processes = await checkProcesses();
  
  // Check API endpoints
  console.log('\n🌐 Checking API Endpoints:');
  const apiChecks = [
    ['/models', 'Models API'],
    ['/categories', 'Categories API'],
    ['/stats', 'Statistics API'],
    ['/models/featured', 'Featured Models API'],
    ['/models/popular', 'Popular Models API'],
    ['/models/recent', 'Recent Models API']
  ];
  
  for (const [endpoint, description] of apiChecks) {
    const result = await checkAPI(endpoint, description);
    const key = endpoint.split('/').pop() || 'models';
    results.apis[key] = result.success;
  }
  
  // Summary
  console.log('\n📋 Health Check Summary:');
  console.log('========================');
  
  const serviceStatus = results.frontend && results.backend ? '✅' : '❌';
  console.log(`${serviceStatus} Services: ${results.frontend && results.backend ? 'All running' : 'Issues detected'}`);
  
  const dbStatus = results.database ? '✅' : '❌';
  console.log(`${dbStatus} Database: ${results.database ? 'Connected' : 'Issues detected'}`);
  
  const apiCount = Object.values(results.apis).filter(Boolean).length;
  const totalApis = Object.keys(results.apis).length;
  const apiStatus = apiCount === totalApis ? '✅' : '⚠️';
  console.log(`${apiStatus} APIs: ${apiCount}/${totalApis} working`);
  
  const processStatus = results.processes ? '✅' : '⚠️';
  console.log(`${processStatus} Processes: ${results.processes ? 'Running' : 'Check manually'}`);
  
  // Overall status
  const overallHealth = results.frontend && results.backend && results.database && apiCount >= totalApis * 0.8;
  console.log(`\n🎯 Overall Health: ${overallHealth ? '✅ HEALTHY' : '❌ NEEDS ATTENTION'}`);
  
  if (overallHealth) {
    console.log('\n🎉 System is ready! You can access:');
    console.log(`   Frontend: ${FRONTEND_URL}`);
    console.log(`   Backend:  ${BACKEND_URL}`);
    console.log(`   API Docs: ${BACKEND_URL}/api`);
  } else {
    console.log('\n⚠️ System issues detected. Please check:');
    if (!results.frontend) console.log('   - Start frontend: npm run dev');
    if (!results.backend) console.log('   - Start backend: cd backend && npm start');
    if (!results.database) console.log('   - Check MongoDB connection');
    if (apiCount < totalApis) console.log('   - Check API endpoints');
  }
  
  return results;
};

// Run the health check
runHealthCheck().catch(console.error);
