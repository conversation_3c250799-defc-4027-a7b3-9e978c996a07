import sharp from 'sharp';
import fs from 'fs';
import path from 'path';
import { GoogleGenerativeAI } from '@google/generative-ai';

// Initialize Gemini AI for vision processing
const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY);

class ImageProcessor {
  constructor() {
    this.supportedFormats = ['jpeg', 'jpg', 'png', 'webp'];
    this.maxFileSize = 10 * 1024 * 1024; // 10MB
    this.maxDimensions = { width: 2048, height: 2048 };
  }

  /**
   * Validate image file
   * @param {Object} file - File object from multer or express-fileupload
   * @returns {Object} - Validation result
   */
  validateImage(file) {
    const errors = [];

    // Check file size
    if (file.size > this.maxFileSize) {
      errors.push(`File size exceeds maximum limit of ${this.maxFileSize / (1024 * 1024)}MB`);
    }

    // Check file type
    const fileExtension = file.name ? file.name.split('.').pop().toLowerCase() :
                         file.originalname ? file.originalname.split('.').pop().toLowerCase() : '';

    if (!this.supportedFormats.includes(fileExtension)) {
      errors.push(`Unsupported file format. Supported formats: ${this.supportedFormats.join(', ')}`);
    }

    // Check MIME type
    const validMimeTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
    if (file.mimetype && !validMimeTypes.includes(file.mimetype)) {
      errors.push('Invalid MIME type for image file');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Process and optimize image
   * @param {Buffer|string} input - Image buffer or file path
   * @param {Object} options - Processing options
   * @returns {Promise<Object>} - Processed image data
   */
  async processImage(input, options = {}) {
    try {
      const {
        width = 800,
        height = 600,
        quality = 80,
        format = 'jpeg',
        preserveAspectRatio = true,
        removeExif = true
      } = options;

      let sharpInstance = sharp(input);

      // Get image metadata
      const metadata = await sharpInstance.metadata();

      // Remove EXIF data for privacy
      if (removeExif) {
        sharpInstance = sharpInstance.rotate(); // Auto-rotate based on EXIF and remove EXIF
      }

      // Resize image if needed
      if (metadata.width > this.maxDimensions.width || metadata.height > this.maxDimensions.height) {
        const resizeOptions = preserveAspectRatio
          ? { width: this.maxDimensions.width, height: this.maxDimensions.height, fit: 'inside' }
          : { width, height };

        sharpInstance = sharpInstance.resize(resizeOptions);
      }

      // Convert to specified format and compress
      let outputBuffer;
      switch (format.toLowerCase()) {
        case 'jpeg':
        case 'jpg':
          outputBuffer = await sharpInstance.jpeg({ quality }).toBuffer();
          break;
        case 'png':
          outputBuffer = await sharpInstance.png({ compressionLevel: 6 }).toBuffer();
          break;
        case 'webp':
          outputBuffer = await sharpInstance.webp({ quality }).toBuffer();
          break;
        default:
          outputBuffer = await sharpInstance.jpeg({ quality }).toBuffer();
      }

      // Get final metadata
      const finalMetadata = await sharp(outputBuffer).metadata();

      return {
        buffer: outputBuffer,
        metadata: {
          width: finalMetadata.width,
          height: finalMetadata.height,
          format: finalMetadata.format,
          size: outputBuffer.length,
          originalSize: metadata.size || input.length,
          compressionRatio: metadata.size ? (1 - outputBuffer.length / metadata.size) * 100 : 0
        }
      };

    } catch (error) {
      throw new Error(`Image processing failed: ${error.message}`);
    }
  }

  /**
   * Create thumbnail from image
   * @param {Buffer|string} input - Image buffer or file path
   * @param {Object} options - Thumbnail options
   * @returns {Promise<Buffer>} - Thumbnail buffer
   */
  async createThumbnail(input, options = {}) {
    const { width = 150, height = 150, quality = 70 } = options;

    try {
      return await sharp(input)
        .resize(width, height, { fit: 'cover' })
        .jpeg({ quality })
        .toBuffer();
    } catch (error) {
      throw new Error(`Thumbnail creation failed: ${error.message}`);
    }
  }

  /**
   * Analyze image using Google Gemini Vision API
   * @param {Buffer|string} imageInput - Image buffer or base64 string
   * @param {Object} options - Analysis options
   * @returns {Promise<Object>} - Analysis results
   */
  async analyzeImageWithGemini(imageInput, options = {}) {
    try {
      const {
        prompt = "Analyze this image and describe what you see. Focus on architectural elements, objects, furniture, design styles, colors, and spatial relationships.",
        language = 'en'
      } = options;

      // Convert buffer to base64 if needed
      let base64Image;
      if (Buffer.isBuffer(imageInput)) {
        base64Image = imageInput.toString('base64');
      } else if (typeof imageInput === 'string' && imageInput.startsWith('data:')) {
        base64Image = imageInput.split(',')[1];
      } else {
        base64Image = imageInput;
      }

      // Get the generative model for vision
      const model = genAI.getGenerativeModel({ model: "gemini-1.5-flash" });

      // Prepare the image data
      const imagePart = {
        inlineData: {
          data: base64Image,
          mimeType: "image/jpeg"
        }
      };

      // Generate content with image and prompt
      const result = await model.generateContent([prompt, imagePart]);
      const response = await result.response;
      const analysisText = response.text();

      // Parse and structure the analysis
      const analysis = this.parseImageAnalysis(analysisText);

      return {
        success: true,
        analysis: {
          description: analysisText,
          ...analysis
        },
        metadata: {
          model: "gemini-1.5-flash",
          timestamp: new Date().toISOString(),
          language
        }
      };

    } catch (error) {
      console.error('Gemini Vision API error:', error);
      return {
        success: false,
        error: error.message,
        analysis: null
      };
    }
  }

  /**
   * Parse Gemini analysis response into structured data
   * @param {string} analysisText - Raw analysis text from Gemini
   * @returns {Object} - Structured analysis data
   */
  parseImageAnalysis(analysisText) {
    const analysis = {
      elements: [],
      style: 'modern',
      colors: [],
      objects: [],
      categories: [],
      tags: []
    };

    const text = analysisText.toLowerCase();

    // Extract architectural elements
    const architecturalElements = [
      'door', 'window', 'wall', 'ceiling', 'floor', 'roof', 'column', 'beam',
      'stair', 'balcony', 'terrace', 'facade', 'entrance', 'arch', 'pillar'
    ];

    architecturalElements.forEach(element => {
      if (text.includes(element)) {
        analysis.elements.push(element);
      }
    });

    // Extract furniture and objects
    const furnitureObjects = [
      'chair', 'table', 'sofa', 'bed', 'desk', 'cabinet', 'shelf', 'lamp',
      'mirror', 'painting', 'plant', 'vase', 'cushion', 'curtain', 'rug'
    ];

    furnitureObjects.forEach(object => {
      if (text.includes(object)) {
        analysis.objects.push(object);
      }
    });

    // Extract design styles
    const designStyles = [
      'modern', 'contemporary', 'traditional', 'minimalist', 'industrial',
      'scandinavian', 'rustic', 'vintage', 'classic', 'luxury', 'bohemian'
    ];

    designStyles.forEach(style => {
      if (text.includes(style)) {
        analysis.style = style;
      }
    });

    // Extract colors
    const colors = [
      'white', 'black', 'gray', 'brown', 'blue', 'green', 'red', 'yellow',
      'orange', 'purple', 'pink', 'beige', 'cream', 'gold', 'silver'
    ];

    colors.forEach(color => {
      if (text.includes(color)) {
        analysis.colors.push(color);
      }
    });

    // Determine categories based on content
    if (analysis.elements.length > 0) {
      analysis.categories.push('architecture');
    }
    if (analysis.objects.length > 0) {
      analysis.categories.push('interior');
    }

    // Generate tags
    analysis.tags = [...analysis.elements, ...analysis.objects, analysis.style, ...analysis.colors]
      .filter(tag => tag && tag.length > 0);

    return analysis;
  }

  /**
   * Save processed image to disk
   * @param {Buffer} imageBuffer - Processed image buffer
   * @param {string} filename - Output filename
   * @param {string} directory - Output directory
   * @returns {Promise<string>} - File path
   */
  async saveImage(imageBuffer, filename, directory = './uploads/images') {
    try {
      // Ensure directory exists
      if (!fs.existsSync(directory)) {
        fs.mkdirSync(directory, { recursive: true });
      }

      const filePath = path.join(directory, filename);
      await fs.promises.writeFile(filePath, imageBuffer);

      return filePath;
    } catch (error) {
      throw new Error(`Failed to save image: ${error.message}`);
    }
  }

  /**
   * Generate unique filename for image
   * @param {string} originalName - Original filename
   * @param {string} prefix - Filename prefix
   * @returns {string} - Unique filename
   */
  generateUniqueFilename(originalName, prefix = 'img') {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2, 8);
    const extension = originalName ? path.extname(originalName) : '.jpg';

    return `${prefix}_${timestamp}_${random}${extension}`;
  }
}

export default ImageProcessor;
