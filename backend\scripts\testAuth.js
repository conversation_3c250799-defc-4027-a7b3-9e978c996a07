import mongoose from 'mongoose';
import dotenv from 'dotenv';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

// Load environment variables
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
dotenv.config({ path: join(__dirname, '../../.env') });

// Import models
import User from '../src/models/User.js';

// Connect to MongoDB
const connectDB = async () => {
  try {
    console.log('Connecting to MongoDB...');
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('MongoDB Connected for auth testing...');
  } catch (error) {
    console.error('Error connecting to MongoDB:', error);
    process.exit(1);
  }
};

// Test authentication
const testAuth = async () => {
  try {
    console.log('Starting authentication test...');

    // Check if test user exists
    let testUser = await User.findOne({ email: '<EMAIL>' });
    
    if (testUser) {
      console.log('Test user already exists, deleting...');
      await User.deleteOne({ email: '<EMAIL>' });
    }

    // Create test user
    console.log('Creating test user...');
    testUser = await User.create({
      name: 'Test User',
      email: '<EMAIL>',
      password: 'test123',
      role: 'user',
      bio: 'Test user for authentication'
    });
    console.log('✅ Test user created successfully!');
    console.log('Email:', testUser.email);
    console.log('Name:', testUser.name);
    console.log('Role:', testUser.role);

    // Test password matching
    console.log('\nTesting password matching...');
    const isMatch = await testUser.matchPassword('test123');
    console.log('Password match result:', isMatch);

    // Test JWT token generation
    console.log('\nTesting JWT token generation...');
    const token = testUser.getSignedJwtToken();
    console.log('JWT token generated:', token ? 'Yes' : 'No');
    console.log('Token preview:', token ? token.substring(0, 20) + '...' : 'None');

    // Check admin user
    console.log('\nChecking admin user...');
    const adminUser = await User.findOne({ email: '<EMAIL>' });
    if (adminUser) {
      console.log('✅ Admin user exists');
      console.log('Admin email:', adminUser.email);
      console.log('Admin name:', adminUser.name);
      console.log('Admin role:', adminUser.role);
      
      // Test admin password
      const adminPasswordMatch = await adminUser.matchPassword('admin123');
      console.log('Admin password match:', adminPasswordMatch);
    } else {
      console.log('❌ Admin user not found');
    }

    // List all users
    console.log('\nAll users in database:');
    const allUsers = await User.find({}, 'name email role createdAt');
    allUsers.forEach((user, index) => {
      console.log(`${index + 1}. ${user.name} (${user.email}) - ${user.role} - Created: ${user.createdAt}`);
    });

    console.log('\n🎉 Authentication test completed!');
    console.log('\nYou can now test login with:');
    console.log('Email: <EMAIL>');
    console.log('Password: test123');
    console.log('\nOr admin login with:');
    console.log('Email: <EMAIL>');
    console.log('Password: admin123');

  } catch (error) {
    console.error('❌ Error in authentication test:', error);
    throw error;
  }
};

// Run the script
const main = async () => {
  try {
    await connectDB();
    await testAuth();
  } catch (error) {
    console.error('❌ Script failed:', error);
  } finally {
    mongoose.connection.close();
    console.log('Database connection closed.');
  }
};

// Execute
main();
