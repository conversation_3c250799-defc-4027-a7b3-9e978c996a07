import React, { useState, useEffect, useCallback } from 'react';
import { use<PERSON>ara<PERSON>, Link, useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import { useAuth } from '../context/AuthContext';
import { useModels } from '../context/ModelContext';
import apiService from '../services/api';
import mongoService from '../services/mongoService';
import ModelViewer from '../components/ModelViewer';
import ModelCompare from '../components/ModelCompare';
import ModelAnnotations from '../components/ModelAnnotations';
import ModelAnalytics from '../components/ModelAnalytics';
import ModelExport from '../components/ModelExport';
import ModelCollections from '../components/ModelCollections';
import ReviewsTab from '../components/reviews/ReviewsTab';
import VersionConverter from '../components/VersionConverter';
import AIModelAssistant from '../components/ai/AIModelAssistant';
import DownloadManager from '../components/DownloadManager';
import LoadingIndicator from '../components/ui/LoadingIndicator';
import Modal from '../components/ui/Modal';
import Toast from '../components/ui/Toast';
import {
  FiChevronRight as ChevronRightIcon,
  FiDownload as DownloadIcon,
  FiHeart as HeartIcon,
  FiShare2 as ShareIcon,
  FiTag as TagIcon,
  FiCalendar as CalendarIcon,
  FiBox as CubeIcon,
  FiPackage as ViewIn3DIcon,
  FiInfo as InfoIcon,
  FiMessageSquare as ReviewsIcon,
  FiBarChart2 as AnalyticsIcon,
  FiEdit3 as AnnotateIcon,
  FiLayers as CompareIcon, // Changed from FiGitCompare to FiLayers
  FiFolder,
  FiCpu, // Use FiCpu instead of FiBrain
  FiRefreshCw
} from 'react-icons/fi';

const ModelDetail = () => {
  const { id } = useParams();
  const [model, setModel] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [relatedModels, setRelatedModels] = useState([]);
  const [activeTab, setActiveTab] = useState('info');
  const [isSaved, setIsSaved] = useState(false);
  const [isCompareModalOpen, setIsCompareModalOpen] = useState(false);
  const [isAnnotateModalOpen, setIsAnnotateModalOpen] = useState(false);
  const [isAnalyticsModalOpen, setIsAnalyticsModalOpen] = useState(false);
  const [isExportModalOpen, setIsExportModalOpen] = useState(false);
  const [isCollectionsModalOpen, setIsCollectionsModalOpen] = useState(false);
  const [isAIAssistantOpen, setIsAIAssistantOpen] = useState(false);
  const [isDownloadManagerOpen, setIsDownloadManagerOpen] = useState(false);
  const [isVersionConverterOpen, setIsVersionConverterOpen] = useState(false);
  const [compareModels, setCompareModels] = useState([]);
  const [annotations, setAnnotations] = useState([]);
  const [toastMessage, setToastMessage] = useState('');
  const [toastType, setToastType] = useState('info');
  const [showToast, setShowToast] = useState(false);
  const { currentUser } = useAuth();
  const { getModelById } = useModels();
  const navigate = useNavigate();

  useEffect(() => {
    // Fetch model from real API
    const fetchModel = async () => {
  try {
        setLoading(true);
        // Validate ID
        if (!id || id === 'undefined' || id === 'null' || id.trim() === '' || id.length < 12) {
          setError('Invalid model ID');
          setLoading(false);
          // Redirect to home page immediately for invalid IDs
          navigate('/', { replace: true });
          return;
        }

        // Try to get model from MongoDB first
        try {
          const modelData = await mongoService.getModelById(id);
          if (cachedData && !isExpired(cachedData)) {
  setModel(modelData.data);

            // Fetch related models
            try {
              const relatedData = await mongoService.getRelatedModels(id);
              if (cachedData && !isExpired(cachedData)) {
  setRelatedModels(relatedData.data);
              }
            } catch (relatedErr) {
              }

            setLoading(false);
            return;
          }
        } catch (mongoErr) {
          }

        // If MongoDB fails, try regular API
        try {
          const response = await apiService.models.getById(id);
          if (cachedData && !isExpired(cachedData)) {
  setModel(response.data.data);

            // Fetch related models
            try {
              const relatedResponse = await apiService.models.getRelated(id);
              if (cachedData && !isExpired(cachedData)) {
  setRelatedModels(relatedResponse.data.data);
              }
            } catch (relatedErr) {
              }

            setLoading(false);
            return;
          }
        } catch (apiErr) {
          }

        // If both MongoDB and API fail, try context
        if (cachedData && !isExpired(cachedData)) {
  const contextModel = await getModelById(id);
          if (cachedData && !isExpired(cachedData)) {
  setModel(contextModel);
            setLoading(false);
            return;
          }
        }

        // If all methods fail, show error
        setError('Model not found');
      } catch (err) {
        setError('Failed to load model details');
        } finally {
        setLoading(false);
      }
    };

    fetchModel();
  }, [id]);

  const handleDownload = () => {
    if (!currentUser) {
      // Redirect to login if not authenticated
      navigate('/login', { state: { from: `/model/${id}` } });
      return;
    }

    // Open smart download manager
    setIsDownloadManagerOpen(true);
  };

  // Handle model comparison
  const handleCompare = () => {
    // In a real app, we would fetch similar models to compare
    // For now, we'll use related models
    setCompareModels([parseInt(id), ...relatedModels.slice(0, 1).map(m => m.id)]);
    setIsCompareModalOpen(true);
  };

  // Handle model annotation
  const handleAnnotate = () => {
    if (cachedData && !isExpired(cachedData)) {
  navigate('/login', { state: { from: `/model/${id}` } });
      return;
    }

    setIsAnnotateModalOpen(true);
  };

  // Handle model analytics
  const handleAnalytics = () => {
    if (cachedData && !isExpired(cachedData)) {
  navigate('/login', { state: { from: `/model/${id}` } });
      return;
    }

    setIsAnalyticsModalOpen(true);
  };

  // Handle model export
  const handleExport = () => {
    if (cachedData && !isExpired(cachedData)) {
  navigate('/login', { state: { from: `/model/${id}` } });
      return;
    }

    setIsExportModalOpen(true);
  };

  // Handle collections
  const handleCollections = () => {
    if (cachedData && !isExpired(cachedData)) {
  navigate('/login', { state: { from: `/model/${id}` } });
      return;
    }

    setIsCollectionsModalOpen(true);
  };

  // Handle AI Assistant
  const handleAIAssistant = () => {
    setIsAIAssistantOpen(true);
  };

  // Handle Version Converter
  const handleVersionConverter = () => {
    if (cachedData && !isExpired(cachedData)) {
  navigate('/login', { state: { from: `/model/${id}` } });
      return;
    }

    setIsVersionConverterOpen(true);
  };

  // Handle collection change
  const handleCollectionChange = () => {
    showToastMessage('Collections updated successfully', 'success'); 
  };

  // Handle saving annotations
  const handleSaveAnnotations = (newAnnotations) => {
  setAnnotations(newAnnotations);
    showToastMessage('Annotations saved successfully!', 'success'; 
    setIsAnnotateModalOpen(false);
  };

  // Show toast message
  const showToastMessage = (message, type = 'info') => {
  setToastMessage(message);
    setToastType(type);
    setShowToast(true);

    // Auto-hide toast after 5 seconds
    setTimeout(() => {
  setShowToast(false);
    }, 5000);
  };

  if (cachedData && !isExpired(cachedData)) {
  return (
      <div className="flex justify-center items-center min-h-screen">
        <LoadingIndicator type="spinner" size="lg" text="Loading model details..." />
      </div>
    );
  }

  if (cachedData && !isExpired(cachedData)) {
  return (
      <div className="container mx-auto px-4 py-8">
        <div className="bg-red-100 dark:bg-red-900 border border-red-400 dark:border-red-800 text-red-700 dark:text-red-200 px-4 py-3 rounded-lg shadow-md" role="alert">
          <div className="flex items-center">
            <svg className="w-6 h-6 mr-2 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            <strong className="font-bold mr-1">Error!</strong>
            <span className="block sm:inline"> {error || 'Model not found'}</span>
          </div>
          <Link to="/" className="mt-4 inline-block px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md transition-colors">
            Return to home page
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-gradient-to-br from-gray-50 via-blue-50 to-purple-50 dark:from-gray-900 dark:via-blue-900 dark:to-purple-900 min-h-screen relative overflow-hidden">
      {/* Animated Background */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute top-20 left-20 w-40 h-40 bg-blue-400 rounded-full blur-3xl animate-float"></div>
        <div className="absolute bottom-20 right-20 w-32 h-32 bg-purple-400 rounded-full blur-2xl animate-float" style={{animationDelay: '2s'}}></div>
        <div className="absolute top-1/2 left-1/3 w-24 h-24 bg-pink-400 rounded-full blur-xl animate-float" style={{animationDelay: '4s'}}></div>
      </div>

      {/* Breadcrumb */}
      <div className="glass-card shadow-professional border-b border-white/10 relative z-10">
        <div className="container mx-auto px-4 py-4">
          <nav className="flex flex-wrap items-center justify-between" aria-label="Breadcrumb">
            <ol className="inline-flex items-center space-x-1 md:space-x-3">
              <li className="inline-flex items-center">
                <Link to="/" className="text-gray-700 hover:text-blue-600 dark:text-gray-300 dark:hover:text-white">
                  Home
                </Link>
              </li>
              <li>
                <div className="flex items-center">
                  <ChevronRightIcon className="w-5 h-5 text-gray-400" />
                  <Link to={`/category/${model.category.toLowerCase()}`} className="ml-1 text-gray-700 hover:text-blue-600 md:ml-2 dark:text-gray-300 dark:hover:text-white">
                    {model.category}
                  </Link>
                </div>
              </li>
              {model.subcategory && (
                <li>
                  <div className="flex items-center">
                    <ChevronRightIcon className="w-5 h-5 text-gray-400" />
                    <Link to={`/subcategory/${model.subcategory.toLowerCase()}`} className="ml-1 text-gray-700 hover:text-blue-600 md:ml-2 dark:text-gray-300 dark:hover:text-white">
                      {model.subcategory}
                    </Link>
                  </div>
                </li>
              )}
              <li aria-current="page">
                <div className="flex items-center">
                  <ChevronRightIcon className="w-5 h-5 text-gray-400" />
                  <span className="ml-1 text-gray-500 md:ml-2 dark:text-gray-400 font-medium truncate max-w-[200px]">{model.title}</span>
                </div>
              </li>
            </ol>

            <div className="flex items-center gap-3 mt-2 sm:mt-0">
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="text-sm glass-card hover:bg-white/20 px-4 py-2 rounded-2xl flex items-center border border-white/10 transition-all duration-300 shadow-lg"
                onClick={() => window.history.back()}
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                </svg>
                Back
              </motion.button>

              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="text-sm glass-card hover:bg-white/20 px-4 py-2 rounded-2xl flex items-center border border-white/10 transition-all duration-300 shadow-lg"
                onClick={() => window.print()}
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z" />
                </svg>
                Print
              </motion.button>
            </div>
          </nav>
        </div>
      </div>

      <div className="container mx-auto px-4 py-12 relative z-10">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Model Image */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="lg:col-span-2"
          >
            <div className="glass-card rounded-3xl shadow-professional-lg overflow-hidden border border-white/20">
              {model.modelUrl ? (
                <div id="model-viewer-section" className="mb-4">
                  <ModelViewer
                    modelUrl={model.modelUrl}
                    fallbackImage={model.imageUrl}
                    modelName={model.title}
                  />
                </div>
              ) : (
                <div className="relative group">
                  <img
                    src={model.imageUrl}
                    alt={model.title}
                    className="w-full h-auto object-cover transition-transform duration-700 group-hover:scale-105"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                  <div className="absolute bottom-4 left-4 right-4 text-white opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                    <h3 className="text-xl font-bold mb-2">{model.title}</h3>
                    <p className="text-sm text-white/90">{model.category}</p>
                  </div>
                </div>
              )}
            </div>
          </motion.div>

          {/* Model Info */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="glass-card rounded-3xl shadow-professional-lg p-8 border border-white/20"
          >
            <div className="flex justify-between items-start mb-6">
              <div>
                <h1 className="text-3xl font-black text-gray-900 dark:text-white mb-2 leading-tight">
                  {model.title}
                </h1>
                <div className="flex items-center gap-2">
                  <span className={`inline-flex items-center px-3 py-1 rounded-2xl text-sm font-bold ${
                    model.isPremium
                      ? 'bg-gradient-to-r from-yellow-400 to-orange-500 text-white shadow-lg animate-pulse'
                      : 'bg-gradient-to-r from-green-400 to-blue-500 text-white shadow-lg'
                  }`}>
                    {model.isPremium ? '⭐ Premium' : '🎉 Free'}
                  </span>
                  <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                </div>
              </div>
            </div>

            <div className="space-y-4">
              <div className="flex flex-wrap items-center text-gray-600 dark:text-gray-300">
                <TagIcon className="w-5 h-5 mr-2" />
                <span>Category: </span>
                <Link to={`/category/${model.category.toLowerCase()}`} className="ml-1 text-blue-600 hover:underline dark:text-blue-400">
                  {model.category}
                </Link>
                {model.subcategory && (
                  <>
                    <span className="mx-1">›</span>
                    <Link to={`/subcategory/${model.subcategory.toLowerCase()}`} className="text-blue-600 hover:underline dark:text-blue-400">
                      {model.subcategory}
                    </Link>
                  </>
                )}
              </div>

              <div className="grid grid-cols-2 gap-3">
                <div className="flex items-center text-gray-600 dark:text-gray-300">
                  <CubeIcon className="w-5 h-5 mr-2 flex-shrink-0" />
                  <span>Format: {model.format}</span>
                </div>

                {model.year && (
                  <div className="flex items-center text-gray-600 dark:text-gray-300">
                    <CalendarIcon className="w-5 h-5 mr-2 flex-shrink-0" />
                    <span>Year: {model.year}</span>
                  </div>
                )}

                {model.fileSize && (
                  <div className="flex items-center text-gray-600 dark:text-gray-300">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                    <span>Size: {(model.fileSize / 1024 / 1024).toFixed(2)} MB</span>
                  </div>
                )}

                {model.polygonCount && (
                  <div className="flex items-center text-gray-600 dark:text-gray-300">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 10l-2 1m0 0l-2-1m2 1v2.5M20 7l-2 1m2-1l-2-1m2 1v2.5M14 4l-2-1-2 1M4 7l2-1M4 7l2 1M4 7v2.5M12 21l-2-1m2 1l2-1m-2 1v-2.5M6 18l-2-1v-2.5M18 18l2-1v-2.5" />
                    </svg>
                    <span>Polygons: {model.polygonCount?.toLocaleString() || 'N/A'}</span>
                  </div>
                )}
              </div>

              <div className="flex items-center space-x-2 mt-2">
                {model.textured && (
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                    Textured
                  </span>
                )}
                {model.rigged && (
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200">
                    Rigged
                  </span>
                )}
                {model.animated && (
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200">
                    Animated
                  </span>
                )}
              </div>

              <p className="text-gray-700 dark:text-gray-300 mt-4 line-clamp-3">{model.description}</p>

              <div className="pt-6 border-t border-white/10">
                <motion.button
                  onClick={handleDownload}
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  className="w-full flex items-center justify-center px-6 py-4 bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 hover:from-blue-700 hover:via-purple-700 hover:to-indigo-700 text-white font-bold rounded-2xl shadow-professional-lg transition-all duration-300 group"
                >
                  <DownloadIcon className="w-6 h-6 mr-3 group-hover:scale-110 transition-transform" />
                  <span className="text-lg">Download Model</span>
                  <div className="ml-auto opacity-0 group-hover:opacity-100 transition-opacity">
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                    </svg>
                  </div>
                </motion.button>

                <div className="grid grid-cols-3 gap-3 mt-6">
                  <motion.button
                    onClick={() => {
  if (cachedData && !isExpired(cachedData)) {
  navigate('/login', { state: { from: `/model/${id}` } });
                        return;
                      }
                      setIsSaved(!isSaved);
                      showToastMessage(isSaved ? 'Removed from saved models' : 'Added to saved models', isSaved ? 'info' : 'success'; 
                    }}
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    className={`flex items-center justify-center px-4 py-3 rounded-2xl font-semibold transition-all duration-300 ${
                      isSaved
                        ? 'bg-gradient-to-r from-red-500 to-pink-500 text-white shadow-lg'
                        : 'glass-card border border-white/20 text-gray-700 dark:text-gray-200 hover:bg-white/20'
                    }`}
                  >
                    <HeartIcon className={`w-5 h-5 mr-2 ${isSaved ? 'text-white fill-current' : 'text-red-500'}`} />
                    {isSaved ? 'Saved' : 'Save'}
                  </motion.button>

                  <motion.button
                    onClick={handleCollections}
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    className="glass-card border border-white/20 text-gray-700 dark:text-gray-200 hover:bg-white/20 flex items-center justify-center px-4 py-3 rounded-2xl font-semibold transition-all duration-300"
                  >
                    <FiFolder className="w-5 h-5 mr-2 text-blue-500" />
                    Collections
                  </motion.button>
                  <button
                    onClick={() => {
  if (cachedData && !isExpired(cachedData)) {
  navigator.share({
    title: model.title,
                          text: `Check out this 3D model: ${model.title}`,
                          url: window.location.href
                        }).catch(err => );
                      } else {
                        // Fallback for browsers that don't support navigator.share
                        navigator.clipboard.writeText(window.location.href)
                          .then(() => showToastMessage('Link copied to clipboard!', 'success'))
                          .catch(err => );
                      }
                    }}
                    className="flex-1 flex items-center justify-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-200 dark:border-gray-600 dark:hover:bg-gray-600 transition-colors"
                  >
                    <ShareIcon className="w-5 h-5 mr-2" />
                    Share
                  </button>
                </div>

                <div className="grid grid-cols-2 gap-2 mt-4">
                  <button
                    onClick={handleCompare}
                    className="flex items-center justify-center px-3 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 dark:bg-gray-700 dark:text-gray-200 dark:border-gray-600 dark:hover:bg-gray-600 transition-colors"
                  >
                    <CompareIcon className="w-4 h-4 mr-1" />
                    Compare
                  </button>

                  <button
                    onClick={handleAnnotate}
                    className="flex items-center justify-center px-3 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 dark:bg-gray-700 dark:text-gray-200 dark:border-gray-600 dark:hover:bg-gray-600 transition-colors"
                  >
                    <AnnotateIcon className="w-4 h-4 mr-1" />
                    Annotate
                  </button>

                  <button
                    onClick={handleAnalytics}
                    className="flex items-center justify-center px-3 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 dark:bg-gray-700 dark:text-gray-200 dark:border-gray-600 dark:hover:bg-gray-600 transition-colors"
                  >
                    <AnalyticsIcon className="w-4 h-4 mr-1" />
                    Analytics
                  </button>

                  <button
                    onClick={handleExport}
                    className="flex items-center justify-center px-3 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 dark:bg-gray-700 dark:text-gray-200 dark:border-gray-600 dark:hover:bg-gray-600 transition-colors"
                  >
                    <DownloadIcon className="w-4 h-4 mr-1" />
                    Export
                  </button>
                </div>

                {/* Version Converter Button - Only show for SketchUp models */}
                {model.format && model.format.toLowerCase().includes('sketchup') && (
                  <div className="mt-4">
                    <button
                      onClick={handleVersionConverter}
                      className="w-full flex items-center justify-center px-4 py-3 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-orange-600 hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 dark:bg-orange-700 dark:hover:bg-orange-800 transition-colors"
                    >
                      <FiRefreshCw className="w-5 h-5 mr-2" />
                      Convert Version
                    </button>
                  </div>
                )}

                {/* AI Assistant Button */}
                <div className="mt-4">
                  <button
                    onClick={handleAIAssistant}
                    className="w-full flex items-center justify-center px-4 py-3 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 dark:bg-purple-700 dark:hover:bg-purple-800 transition-colors"
                  >
                    <FiCpu className="w-5 h-5 mr-2" />
                    Trợ Lý AI
                  </button>
                </div>

                {model.modelUrl && (
                  <div className="mt-4">
                    <button
                      onClick={() => document.getElementById('model-viewer-section').scrollIntoView({ behavior: 'smooth' })}
                      className="w-full flex items-center justify-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 dark:bg-green-700 dark:hover:bg-green-800 transition-colors"
                    >
                      <ViewIn3DIcon className="h-5 w-5 mr-2" />
                      View in 3D
                    </button>
                  </div>
                )}
              </div>

              {/* Author info */}
              <div className="mt-6 pt-4 border-t border-gray-200 dark:border-gray-700">
                <div className="flex items-center">
                  <img
                    src={model.createdBy?.profileImage || "https://via.placeholder.com/40"}
                    alt="Author"
                    className="w-10 h-10 rounded-full mr-3"
                  />
                  <div>
                    <h3 className="text-sm font-medium text-gray-900 dark:text-white">
                      {model.createdBy?.name || "Unknown Author"}
                    </h3>
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      Uploaded {new Date(model.createdAt || Date.now()).toLocaleDateString()}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </motion.div>
        </div>

        {/* Tabs */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.3 }}
          className="mt-8"
        >
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg overflow-hidden">
            <div className="border-b border-gray-200 dark:border-gray-700">
              <nav className="flex">
                <button
                  onClick={() => setActiveTab('info')}
                  className={`py-4 px-6 font-medium text-sm border-b-2 focus:outline-none ${
    activeTab === 'info'
                      ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'
                  }`}
                >
                  <div className="flex items-center">
                    <InfoIcon className="h-5 w-5 mr-2" />
                    <span>Description</span>
                  </div>
                </button>

                <button
                  onClick={() => setActiveTab('reviews')}
                  className={`py-4 px-6 font-medium text-sm border-b-2 focus:outline-none ${
    activeTab === 'reviews'
                      ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'
                  }`}
                >
                  <div className="flex items-center">
                    <ReviewsIcon className="h-5 w-5 mr-2" />
                    <span>Reviews</span>
                  </div>
                </button>

                <button
                  onClick={() => setActiveTab('license')}
                  className={`py-4 px-6 font-medium text-sm border-b-2 focus:outline-none ${
    activeTab === 'license'
                      ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'
                  }`}
                >
                  <div className="flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                    </svg>
                    <span>License</span>
                  </div>
                </button>

                <button
                  onClick={() => setActiveTab('comments')}
                  className={`py-4 px-6 font-medium text-sm border-b-2 focus:outline-none ${
    activeTab === 'comments'
                      ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'
                  }`}
                >
                  <div className="flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z" />
                    </svg>
                    <span>Comments</span>
                  </div>
                </button>
              </nav>
            </div>

            <div className="p-6">
              {activeTab === 'info' && (
                <div>
                  <h2 className="text-xl font-bold text-gray-900 dark:text-white mb-4">Description</h2>
                  <div className="prose prose-blue max-w-none dark:prose-dark">
                    <p className="text-gray-700 dark:text-gray-300 mb-6 whitespace-pre-line">{model.description}</p>
                  </div>

                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3 mt-8">Specifications</h3>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                    <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                      <h4 className="font-medium text-gray-900 dark:text-white mb-2">Format</h4>
                      <p className="text-gray-700 dark:text-gray-300">{model.format}</p>
                    </div>

                    <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                      <h4 className="font-medium text-gray-900 dark:text-white mb-2">Category</h4>
                      <p className="text-gray-700 dark:text-gray-300">{model.category} {model.subcategory ? `/ ${model.subcategory}` : '}</p>
                    </div>

                    {model.year && (
                      <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                        <h4 className="font-medium text-gray-900 dark:text-white mb-2">Year</h4>
                        <p className="text-gray-700 dark:text-gray-300">{model.year}</p>
                      </div>
                    )}

                    {model.fileSize && (
                      <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                        <h4 className="font-medium text-gray-900 dark:text-white mb-2">File Size</h4>
                        <p className="text-gray-700 dark:text-gray-300">{(model.fileSize / 1024 / 1024).toFixed(2)} MB</p>
                      </div>
                    )}

                    {model.polygonCount && (
                      <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                        <h4 className="font-medium text-gray-900 dark:text-white mb-2">Polygons</h4>
                        <p className="text-gray-700 dark:text-gray-300">{model.polygonCount?.toLocaleString() || 'N/A'}</p>
                      </div>
                    )}

                    {model.dimensions && (
                      <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                        <h4 className="font-medium text-gray-900 dark:text-white mb-2">Dimensions</h4>
                        <p className="text-gray-700 dark:text-gray-300">
                          {model.dimensions.width} × {model.dimensions.height} × {model.dimensions.depth} {model.dimensions.unit}
                        </p>
                      </div>
                    )}
                  </div>

                  {model.tags && model.tags.length > 0 && (
                    <div className="mt-6">
                      <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">Tags</h3>
                      <div className="flex flex-wrap gap-2">
                        {model.tags.map((tag, index) => (
                          <Link
                            key={index}
                            to={`/search?tag=${tag}`}
                            className="px-3 py-1 bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 rounded-full text-sm text-gray-700 dark:text-gray-300 transition-colors"
                          >
                            {tag}
                          </Link>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              )}

              {activeTab === 'reviews' && (
                <ReviewsTab modelId={id} />
              )}

              {activeTab === 'license' && (
                <div>
                  <h2 className="text-xl font-bold text-gray-900 dark:text-white mb-4">License Information</h2>
                  <div className="bg-gray-50 dark:bg-gray-700 p-5 rounded-lg mb-6">
                    <div className="flex items-center mb-4">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-green-500 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                      </svg>
                      <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                        {model.isPremium ? 'Premium License' : 'Standard License'}
                      </h3>
                    </div>

                    <ul className="space-y-2 text-gray-700 dark:text-gray-300">
                      <li className="flex items-start">
                        <svg className="h-5 w-5 text-green-500 mr-2 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                        <span>Personal use</span>
                      </li>
                      <li className="flex items-start">
                        <svg className="h-5 w-5 text-green-500 mr-2 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                        <span>Include in personal projects</span>
                      </li>
                      {model.isPremium ? (
                        <>
                          <li className="flex items-start">
                            <svg className="h-5 w-5 text-green-500 mr-2 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            <span>Commercial use</span>
                          </li>
                          <li className="flex items-start">
                            <svg className="h-5 w-5 text-green-500 mr-2 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            <span>Unlimited projects</span>
                          </li>
                        </>
                      ) : (
                        <>
                          <li className="flex items-start">
                            <svg className="h-5 w-5 text-red-500 mr-2 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                            <span className="text-gray-500 dark:text-gray-400">Commercial use not allowed</span>
                          </li>
                        </>
                      )}
                    </ul>

                    <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-600 text-sm text-gray-500 dark:text-gray-400">
                      For full license details, please refer to our <a href="/terms" className="text-blue-600 hover:underline dark:text-blue-400">Terms of Use</a>.
                    </div>
                  </div>
                </div>
              )}

              {activeTab === 'comments' && (
                <div>
                  <h2 className="text-xl font-bold text-gray-900 dark:text-white mb-4">Comments</h2>
                  <div className="bg-gray-50 dark:bg-gray-700 p-5 rounded-lg mb-6">
                    <p className="text-center text-gray-500 dark:text-gray-400 py-8">Comments feature coming soon!</p>
                  </div>
                </div>
              )}
            </div>
          </div>
        </motion.div>

        {/* Related Models */}
        {relatedModels.length > 0 && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.4 }}
            className="mt-12"
          >
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-2xl font-bold text-gray-900 dark:text-white">Related Models</h2>
              <Link
                to={`/category/${model.category.toLowerCase()}`}
                className="text-sm text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 flex items-center"
              >
                View all
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                </svg>
              </Link>
            </div>

            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
              {relatedModels.map(relatedModel => (
                <Link
                  key={relatedModel.id}
                  to={`/model/${relatedModel.id}`}
                  className="group bg-white dark:bg-gray-800 rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300"
                >
                  <div className="relative overflow-hidden">
                    <img
                      src={relatedModel.imageUrl}
                      alt={relatedModel.title}
                      className="w-full h-48 object-cover transform group-hover:scale-105 transition-transform duration-300"
                    />
                    {relatedModel.isPremium && (
                      <span className="absolute top-2 right-2 bg-blue-600 text-white text-xs px-2 py-1 rounded-full">
                        Premium
                      </span>
                    )}
                  </div>
                  <div className="p-4">
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white truncate">{relatedModel.title}</h3>
                    <div className="flex items-center justify-between mt-2">
                      <p className="text-sm text-gray-600 dark:text-gray-400">{relatedModel.category}</p>
                      {relatedModel.rating && (
                        <div className="flex items-center">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-yellow-500" viewBox="0 0 20 20" fill="currentColor">
                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                          </svg>
                          <span className="text-sm text-gray-700 dark:text-gray-300 ml-1">{relatedModel.rating}</span>
                        </div>
                      )}
                    </div>
                  </div>
                </Link>
              ))}
            </div>

            <div className="mt-8 text-center">
              <Link
                to="/models"
                className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
              >
                Explore All Models
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 ml-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
                </svg>
              </Link>
            </div>
          </motion.div>
        )}
      </div>

      {/* Modals */}
      <Modal
        isOpen={isCompareModalOpen}
        onClose={() => setIsCompareModalOpen(false)}
        title="Compare Models"
        size="xl"
      >
        <ModelCompare
          modelIds={compareModels}
          onClose={() => setIsCompareModalOpen(false)}
        />
      </Modal>

      <Modal
        isOpen={isAnnotateModalOpen}
        onClose={() => setIsAnnotateModalOpen(false)}
        title="Annotate Model"
        size="xl"
      >
        <div className="h-[70vh]">
          <ModelAnnotations
            modelUrl={model?.modelUrl}
            initialAnnotations={annotations}
            onSave={handleSaveAnnotations}
            readOnly={!currentUser}
          />
        </div>
      </Modal>

      <Modal
        isOpen={isAnalyticsModalOpen}
        onClose={() => setIsAnalyticsModalOpen(false)}
        title="Model Analytics"
        size="lg"
      >
        <ModelAnalytics
          modelId={parseInt(id)}
          period="month"
        />
      </Modal>

      <Modal
        isOpen={isExportModalOpen}
        onClose={() => setIsExportModalOpen(false)}
        title="Export Model"
        size="lg"
      >
        <ModelExport
          modelUrl={model?.modelUrl}
          modelName={model?.title || `model-${id}`}
          onClose={() => setIsExportModalOpen(false)}
          isPremiumModel={model?.isPremium}
        />
      </Modal>

      <Modal
        isOpen={isCollectionsModalOpen}
        onClose={() => setIsCollectionsModalOpen(false)}
        title="Collections"
        size="lg"
      >
        <ModelCollections
          modelId={id}
          onClose={() => setIsCollectionsModalOpen(false)}
          onCollectionChange={handleCollectionChange}
        />
      </Modal>

      {/* AI Assistant Modal */}
      {isAIAssistantOpen && (
        <AIModelAssistant
          modelId={id}
          modelData={model}
          onClose={() => setIsAIAssistantOpen(false)}
        />
      )}

      {/* Download Manager */}
      {isDownloadManagerOpen && (
        <DownloadManager
          model={model}
          onClose={() => setIsDownloadManagerOpen(false)}
        />
      )}

      {/* Version Converter Modal */}
      <Modal
        isOpen={isVersionConverterOpen}
        onClose={() => setIsVersionConverterOpen(false)}
        title=""
        size="xl"
        showHeader={false}
      >
        <VersionConverter
          modelId={id}
          modelData={model}
          onClose={() => setIsVersionConverterOpen(false)}
        />
      </Modal>

      {/* Toast notifications */}
      {showToast && (
        <Toast
          type={toastType}
          message={toastMessage}
          visible={showToast}
          onClose={() => setShowToast(false)}
          duration={5000}
          position="bottom-right"
        />
      )}
    </div>
  );
};

export default ModelDetail;
