const fs = require('fs');
const path = require('path');

// Critical syntax fixes for remaining errors
const criticalFixes = [
  // Fix missing closing parentheses
  { 
    file: 'src/components/Chatbot.jsx',
    pattern: /await chat<PERSON><PERSON>\.get\('\/rate-limit-status';/g,
    replacement: "await chatApi.get('/rate-limit-status');"
  },
  
  // Fix useState syntax
  { 
    file: 'src/components/Newsletter.jsx',
    pattern: /useState\('idle';/g,
    replacement: "useState('idle');"
  },
  
  // Fix destructuring syntax in Button.jsx
  { 
    file: 'src/components/ui/Button.jsx',
    pattern: /const \{\s*\/\/ Fixed content\s*\}\s*variant = 'primary',/g,
    replacement: "const { variant = 'primary',"
  },
  
  // Fix destructuring syntax in Input.jsx
  { 
    file: 'src/components/ui/Input.jsx',
    pattern: /const \{\s*\/\/ Fixed content\s*\}\s*type = 'text',/g,
    replacement: "const { type = 'text',"
  },
  
  // Fix ternary operator in AdvancedSearchFilters.jsx
  { 
    file: 'src/components/AdvancedSearchFilters.jsx',
    pattern: /e\.target\.value === ' \? null : e\.target\.value === 'true'\)/g,
    replacement: "e.target.value === '' ? null : e.target.value === 'true')"
  },
  
  // Fix other critical syntax errors
  { 
    file: 'src/components/WarehouseSearch.jsx',
    pattern: /toast\.error\(error\.response\?\.data\?\.error \|\| 'Download failed';/g,
    replacement: "toast.error(error.response?.data?.error || 'Download failed');"
  },
  
  // Fix unterminated strings
  { 
    file: 'src/components/ai/AIModelAssistant.jsx',
    pattern: /analyzing \? 'animate-spin' : '`\} \/>/g,
    replacement: "analyzing ? 'animate-spin' : ''`} />"
  },
  
  { 
    file: 'src/components/auth/TwoFactorAuth.jsx',
    pattern: /setValidationError\(';/g,
    replacement: "setValidationError('');"
  },
  
  { 
    file: 'src/components/collections/SmartCollection.jsx',
    pattern: /from-black to-transparent text-white' : '`\}/g,
    replacement: "from-black to-transparent text-white' : ''`}"
  },
  
  { 
    file: 'src/components/showroom/VirtualShowroom.jsx',
    pattern: /canvasRef\.current\?\.querySelector\('canvas';/g,
    replacement: "canvasRef.current?.querySelector('canvas');"
  },
  
  { 
    file: 'src/components/ui/ImageWithFallback.jsx',
    pattern: /useState\(lowResSrc \|\| '\/images\/placeholder-tiny\.jpg';/g,
    replacement: "useState(lowResSrc || '/images/placeholder-tiny.jpg');"
  },
  
  // Fix pages errors
  { 
    file: 'src/pages/AdminAddModel.jsx',
    pattern: /Would you like to create another model\?';/g,
    replacement: "Would you like to create another model?');"
  },
  
  { 
    file: 'src/pages/AdminAddModelSimple.jsx',
    pattern: /toast\.error\(error\.message \|\| 'Failed to create model';/g,
    replacement: "toast.error(error.message || 'Failed to create model');"
  },
  
  { 
    file: 'src/pages/CategoryPage.jsx',
    pattern: /\(cat === 'models\/objects';/g,
    replacement: "(cat === 'models/objects')"
  },
  
  { 
    file: 'src/pages/Home.jsx',
    pattern: /useState\('grid';/g,
    replacement: "useState('grid');"
  },
  
  { 
    file: 'src/pages/ModelDetail.jsx',
    pattern: /showToastMessage\('Collections updated successfully', 'success';/g,
    replacement: "showToastMessage('Collections updated successfully', 'success');"
  },
  
  { 
    file: 'src/pages/NewHome.jsx',
    pattern: /chatbot\.querySelector\('button';/g,
    replacement: "chatbot.querySelector('button');"
  },
  
  { 
    file: 'src/pages/Plugins.jsx',
    pattern: /axios\.post\('\/api\/extensions\/create-samples';/g,
    replacement: "axios.post('/api/extensions/create-samples');"
  },
  
  { 
    file: 'src/pages/Profile.jsx',
    pattern: /Failed to update profile\. Please try again\.';/g,
    replacement: "Failed to update profile. Please try again.');"
  },
  
  { 
    file: 'src/pages/SavedModels.jsx',
    pattern: /useState\('grid';/g,
    replacement: "useState('grid');"
  },
  
  { 
    file: 'src/pages/SearchPage.jsx',
    pattern: /searchParams\.get\('q'\) \|\| '';/g,
    replacement: "searchParams.get('q') || '');"
  },
  
  { 
    file: 'src/pages/SmartCollections.jsx',
    pattern: /aiScore: 0\.92,/g,
    replacement: "aiScore: 0.92"
  },
  
  { 
    file: 'src/pages/Subscription.jsx',
    pattern: /plan\.id === 'premium' \? 'transform md:scale-105 z-10' : '`\}/g,
    replacement: "plan.id === 'premium' ? 'transform md:scale-105 z-10' : ''`}"
  },
  
  { 
    file: 'src/pages/UploadModel.jsx',
    pattern: /setPreviewModelUrl\(';/g,
    replacement: "setPreviewModelUrl('');"
  },
  
  // Fix admin pages
  { 
    file: 'src/pages/admin/AddModel.jsx',
    pattern: /setTagInput\(';/g,
    replacement: "setTagInput('');"
  },
  
  { 
    file: 'src/pages/admin/AdminDashboard.jsx',
    pattern: /isSidebarOpen \? 'rotate-180' : '`\} \/>/g,
    replacement: "isSidebarOpen ? 'rotate-180' : ''`} />"
  },
  
  { 
    file: 'src/pages/admin/Analytics.jsx',
    pattern: /useState\('7d';/g,
    replacement: "useState('7d');"
  },
  
  { 
    file: 'src/pages/admin/ModelManagement.jsx',
    pattern: /setSortDirection\('desc';/g,
    replacement: "setSortDirection('desc');"
  },
  
  { 
    file: 'src/pages/admin/Settings.jsx',
    pattern: /loading \? 'animate-spin' : '`\} \/>/g,
    replacement: "loading ? 'animate-spin' : ''`} />"
  },
  
  { 
    file: 'src/pages/admin/UserManagement.jsx',
    pattern: /setSortDirection\('desc';/g,
    replacement: "setSortDirection('desc');"
  },
  
  // Fix utils
  { 
    file: 'src/utils/api.js',
    pattern: /api\.get\('\/admin\/system';/g,
    replacement: "api.get('/admin/system');"
  }
];

function applyCriticalFix(fix) {
  const filePath = fix.file;
  
  try {
    if (!fs.existsSync(filePath)) {
      console.log(`⚠️ File not found: ${filePath}`);
      return 0;
    }
    
    let content = fs.readFileSync(filePath, 'utf8');
    let fixCount = 0;
    
    const matches = content.match(fix.pattern);
    if (matches) {
      content = content.replace(fix.pattern, fix.replacement);
      fixCount = matches.length;
      
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`✅ Fixed ${fixCount} critical errors in ${filePath}`);
    }
    
    return fixCount;
  } catch (error) {
    console.error(`❌ Error processing ${filePath}:`, error.message);
    return 0;
  }
}

// Main execution
console.log('🚨 Starting Critical Syntax Fix...\n');

const startTime = Date.now();
let totalFixes = 0;
let filesFixed = 0;

criticalFixes.forEach(fix => {
  const fixes = applyCriticalFix(fix);
  if (fixes > 0) {
    totalFixes += fixes;
    filesFixed++;
  }
});

const endTime = Date.now();

console.log('\n📊 CRITICAL SYNTAX FIX RESULTS:');
console.log(`✅ Files fixed: ${filesFixed}`);
console.log(`🔧 Total critical errors fixed: ${totalFixes}`);
console.log(`⏱️ Time taken: ${(endTime - startTime) / 1000}s`);

if (totalFixes > 0) {
  console.log('\n🎉 Critical syntax fixes completed successfully!');
  console.log('🚀 You can now run "npm run dev" to start the development server.');
} else {
  console.log('\n✨ No critical syntax errors found!');
}
