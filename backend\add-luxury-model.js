import mongoose from 'mongoose';
import Model from './src/models/Model.js';
import User from './src/models/User.js';

// Connect to MongoDB
mongoose.connect('mongodb://localhost:27017/3dsketchup', {
  useNewUrlParser: true,
  useUnifiedTopology: true,
});

const addLuxuryModel = async () => {
  try {
    console.log('Adding luxury bedroom model...');

    // Get admin user
    const adminUser = await User.findOne({ role: 'admin' });
    if (!adminUser) {
      console.error('Admin user not found');
      process.exit(1);
    }

    // Check if luxury model already exists
    const existingModel = await Model.findOne({ title: /luxury.*bedroom/i });
    if (existingModel) {
      console.log('Luxury bedroom model already exists:', existingModel.title);
      process.exit(0);
    }

    // Create luxury bedroom model
    const luxuryModel = new Model({
      title: 'Luxury Bedroom Interior - Enscape Model',
      description: 'Stunning luxury bedroom interior design with premium furniture, elegant lighting, and sophisticated decor. Perfect for high-end residential projects and architectural visualization. Features detailed textures, realistic materials, and optimized for Enscape rendering. Includes king-size bed, premium bedding, designer furniture, ambient lighting, and luxury accessories.',
      category: 'Residential',
      subcategory: 'Interior',
      format: 'Sketchup 2023',
      year: '2024',
      imageUrl: 'https://3dsketchup.net/wp-content/uploads/2024/01/luxury-bedroom-interior-skp_model-enscape-0401080323-1.jpg',
      fileUrl: 'https://3dsketchup.net/free-download/luxury-bedroom-interior-skp_model-enscape-0401080323/',
      modelUrl: '/uploads/previews/luxury_bedroom_preview.glb',
      fileSize: 45 * 1024 * 1024, // 45 MB
      fileFormat: 'skp',
      polygonCount: 180000,
      textured: true,
      rigged: false,
      animated: false,
      dimensions: {
        width: 6.5,
        height: 3.2,
        depth: 5.8,
        unit: 'm'
      },
      tags: ['luxury', 'bedroom', 'interior', 'residential', 'enscape', 'premium', 'furniture', 'lighting', 'modern', 'elegant'],
      downloads: 1247,
      views: 3892,
      rating: 4.8,
      isPremium: true,
      createdBy: adminUser._id
    });

    const savedModel = await luxuryModel.save();
    console.log('Luxury bedroom model added successfully!');
    console.log('Model ID:', savedModel._id);
    console.log('Model Title:', savedModel.title);

    process.exit(0);
  } catch (error) {
    console.error('Error adding luxury model:', error);
    process.exit(1);
  }
};

addLuxuryModel();
