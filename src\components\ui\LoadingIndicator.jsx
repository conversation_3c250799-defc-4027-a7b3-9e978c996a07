import React from 'react';
import { motion } from 'framer-motion';

/**
 * Enhanced loading indicator with multiple styles
 *
 * @param {Object} props - Component props
 * @param {string} props.type - Type of loader: 'spinner', 'dots', 'progress', 'skeleton'
 * @param {string} props.size - Size of loader: 'sm', 'md', 'lg', 'xl'
 * @param {string} props.color - Color of loader: 'primary', 'secondary', 'accent', 'success', 'error'
 * @param {number} props.progress - Progress value (0-100) for 'progress' type
 * @param {string} props.text - Optional text to display
 * @param {string} props.className - Additional CSS classes
 */
const LoadingIndicator = ({
  type = 'spinner',
  size = 'md',
  color = 'primary',
  progress = 0,
  text,
  className = '',
  ...props
}) => {
  // Size mappings
  const sizeMap = {
    sm: {
      spinner: 'w-4 h-4',
      dots: 'space-x-1',
      dotSize: 'w-1.5 h-1.5',
      progress: 'h-1',
      text: 'text-xs',
    },
    md: {
      spinner: 'w-8 h-8',
      dots: 'space-x-2',
      dotSize: 'w-2.5 h-2.5',
      progress: 'h-2',
      text: 'text-sm',
    },
    lg: {
      spinner: 'w-12 h-12',
      dots: 'space-x-3',
      dotSize: 'w-3.5 h-3.5',
      progress: 'h-3',
      text: 'text-base',
    },
    xl: {
      spinner: 'w-16 h-16',
      dots: 'space-x-4',
      dotSize: 'w-4 h-4',
      progress: 'h-4',
      text: 'text-lg',
    },
  };

  // Color mappings
  const colorMap = {
    primary: 'text-blue-600 dark:text-blue-400',
    secondary: 'text-gray-600 dark:text-gray-400',
    accent: 'text-purple-600 dark:text-purple-400',
    success: 'text-green-600 dark:text-green-400',
    error: 'text-red-600 dark:text-red-400',
  };

  const progressColorMap = {
    primary: 'bg-blue-600 dark:bg-blue-400',
    secondary: 'bg-gray-600 dark:bg-gray-400',
    accent: 'bg-purple-600 dark:bg-purple-400',
    success: 'bg-green-600 dark:bg-green-400',
    error: 'bg-red-600 dark:bg-red-400',
  };

  // Spinner animation
  const spinnerVariants = {
    animate: {
      rotate: 360,
      transition: {
        repeat: Infinity,
        duration: 1,
        ease: 'linear',
      },
    },
  };

  // Dots animation
  const dotsVariants = {
    animate: {
      y: ['0%', '-50%', '0%'],
      transition: {
        repeat: Infinity,
        duration: 1.5,
        ease: 'easeInOut',
      },
    },
  };

  // Render different loader types
  const renderLoader = () => {
    switch (type) {
      case 'spinner':
        return (
          <motion.div
            className={`border-2 border-t-transparent rounded-full ${sizeMap[size].spinner} ${colorMap[color]} border-current`}
            variants={spinnerVariants}
            animate="animate"
            {...props}
          />
        );

      case 'dots':
        return (
          <div className={`flex ${sizeMap[size].dots}`}>
            {[0, 1, 2].map((i) => (
              <motion.div
                key={i}
                className={`rounded-full ${sizeMap[size].dotSize} ${colorMap[color]}`}
                variants={dotsVariants}
                animate="animate"
                custom={i}
                transition={{
                  delay: i * 0.2,
                }}
                {...props}
              />
            ))}
          </div>
        );

      case 'progress':
        return (
          <div className={`w-full ${sizeMap[size].progress} bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden`}>
            <motion.div
              className={`h-full ${progressColorMap[color]}`}
              initial={{ width: '0%' }}
              animate={{ width: `${progress}%` }}
              transition={{ duration: 0.5 }}
              {...props}
            />
          </div>
        );

      case 'skeleton':
        return (
          <div className="w-full space-y-3">
            <div className="h-2.5 bg-gray-200 dark:bg-gray-700 rounded-full w-full animate-pulse"></div>
            <div className="h-2.5 bg-gray-200 dark:bg-gray-700 rounded-full w-3/4 animate-pulse"></div>
            <div className="h-2.5 bg-gray-200 dark:bg-gray-700 rounded-full w-1/2 animate-pulse"></div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className={`flex flex-col items-center justify-center ${className}`}>
      {renderLoader()}
      {text && <p className={`mt-2 ${sizeMap[size].text} ${colorMap[color]}`}>{text}</p>}
    </div>
  );
};

export default LoadingIndicator;
