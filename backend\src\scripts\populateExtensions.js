import mongoose from 'mongoose';
import dotenv from 'dotenv';
import Extension from '../models/Extension.js';

// Load environment variables
dotenv.config();

// Sample extensions data
const sampleExtensions = [
  {
    name: 'SketchUp Viewer Pro',
    description: 'Advanced 3D model viewer with enhanced rendering capabilities, lighting controls, and export options for professional presentations.',
    developer: 'SketchUp Team',
    version: '2.1.0',
    category: 'Visualization',
    price: 'Free',
    downloadUrl: 'https://extensions.sketchup.com/viewer-pro',
    tags: ['viewer', '3d', 'rendering', 'presentation', 'export'],
    features: ['3D viewing', 'advanced rendering', 'lighting controls', 'export options', 'presentation mode'],
    downloads: 15420,
    rating: 4.5,
    status: 'active',
    source: 'sketchup-extensions',
    createdBy: new mongoose.Types.ObjectId(),
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    name: 'Material Editor Plus',
    description: 'Professional material editing and management tool with texture mapping, color adjustment, and material library organization.',
    developer: 'Material Studio',
    version: '1.5.2',
    category: 'Rendering',
    price: '$29.99',
    downloadUrl: 'https://extensions.sketchup.com/material-editor-plus',
    tags: ['materials', 'textures', 'editor', 'mapping', 'library'],
    features: ['material editing', 'texture mapping', 'color adjustment', 'library management', 'preview'],
    downloads: 8930,
    rating: 4.2,
    status: 'active',
    source: 'sketchup-extensions',
    createdBy: new mongoose.Types.ObjectId(),
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    name: 'Architectural Toolkit',
    description: 'Complete set of architectural design tools including walls, doors, windows, stairs, and structural elements for building design.',
    developer: 'Arch Design Co',
    version: '3.0.1',
    category: 'Architecture',
    price: '$49.99',
    downloadUrl: 'https://extensions.sketchup.com/architectural-toolkit',
    tags: ['architecture', 'design', 'tools', 'building', 'structural'],
    features: ['wall tools', 'door placement', 'window creation', 'stair builder', 'structural elements'],
    downloads: 12650,
    rating: 4.7,
    status: 'active',
    source: 'sketchup-extensions',
    createdBy: new mongoose.Types.ObjectId(),
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    name: 'Lighting Studio Pro',
    description: 'Professional lighting design and simulation tool with realistic shadows, light placement, and rendering optimization.',
    developer: 'Light Design Inc',
    version: '2.3.0',
    category: 'Rendering',
    price: '$39.99',
    downloadUrl: 'https://extensions.sketchup.com/lighting-studio-pro',
    tags: ['lighting', 'simulation', 'design', 'shadows', 'rendering'],
    features: ['light placement', 'shadow simulation', 'realistic rendering', 'optimization', 'presets'],
    downloads: 6780,
    rating: 4.3,
    status: 'active',
    source: 'sketchup-extensions',
    createdBy: new mongoose.Types.ObjectId(),
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    name: 'Export Manager Pro',
    description: 'Advanced export options for multiple formats including CAD, 3D printing, and web-ready models with optimization settings.',
    developer: 'Export Solutions',
    version: '1.8.5',
    category: 'Import/Export',
    price: '$24.99',
    downloadUrl: 'https://extensions.sketchup.com/export-manager-pro',
    tags: ['export', 'formats', 'conversion', 'optimization', 'cad'],
    features: ['multiple formats', 'batch export', 'optimization', 'CAD support', '3D printing'],
    downloads: 9840,
    rating: 4.1,
    status: 'active',
    source: 'sketchup-extensions',
    createdBy: new mongoose.Types.ObjectId(),
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    name: 'Furniture Designer',
    description: 'Specialized tool for creating and customizing furniture models with parametric design and material options.',
    developer: 'Furniture Pro',
    version: '2.2.1',
    category: 'Interior Design',
    price: '$34.99',
    downloadUrl: 'https://extensions.sketchup.com/furniture-designer',
    tags: ['furniture', 'design', 'parametric', 'customization', 'modeling'],
    features: ['parametric design', 'furniture templates', 'material options', 'customization', 'library'],
    downloads: 7250,
    rating: 4.4,
    status: 'active',
    source: 'sketchup-extensions',
    createdBy: new mongoose.Types.ObjectId(),
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    name: 'Landscape Tools',
    description: 'Comprehensive landscape design tools with terrain modeling, plant placement, and outdoor environment creation.',
    developer: 'Landscape Design',
    version: '1.9.3',
    category: 'Landscape',
    price: 'Free',
    downloadUrl: 'https://extensions.sketchup.com/landscape-tools',
    tags: ['landscape', 'terrain', 'plants', 'outdoor', 'environment'],
    features: ['terrain modeling', 'plant library', 'outdoor elements', 'environment tools', 'natural textures'],
    downloads: 5680,
    rating: 4.0,
    status: 'active',
    source: 'sketchup-extensions',
    createdBy: new mongoose.Types.ObjectId(),
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    name: 'Animation Studio',
    description: 'Create smooth animations and walkthroughs with camera paths, object animation, and timeline controls.',
    developer: 'Animation Pro',
    version: '3.1.0',
    category: 'Animation',
    price: '$59.99',
    downloadUrl: 'https://extensions.sketchup.com/animation-studio',
    tags: ['animation', 'walkthrough', 'camera', 'timeline', 'motion'],
    features: ['camera paths', 'object animation', 'timeline editor', 'smooth transitions', 'export video'],
    downloads: 4320,
    rating: 4.6,
    status: 'active',
    source: 'sketchup-extensions',
    createdBy: new mongoose.Types.ObjectId(),
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    name: 'Measurement Tools Pro',
    description: 'Advanced measurement and dimensioning tools with area calculation, volume measurement, and detailed reports.',
    developer: 'Measure Tech',
    version: '1.4.2',
    category: 'Utilities',
    price: '$19.99',
    downloadUrl: 'https://extensions.sketchup.com/measurement-tools-pro',
    tags: ['measurement', 'dimensions', 'area', 'volume', 'reports'],
    features: ['precise measurement', 'area calculation', 'volume analysis', 'dimension lines', 'reports'],
    downloads: 8760,
    rating: 4.3,
    status: 'active',
    source: 'sketchup-extensions',
    createdBy: new mongoose.Types.ObjectId(),
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    name: 'Interior Design Suite',
    description: 'Complete interior design solution with room layouts, furniture placement, color schemes, and decoration tools.',
    developer: 'Interior Pro',
    version: '2.5.0',
    category: 'Interior Design',
    price: '$44.99',
    downloadUrl: 'https://extensions.sketchup.com/interior-design-suite',
    tags: ['interior', 'design', 'room', 'furniture', 'decoration'],
    features: ['room layouts', 'furniture placement', 'color schemes', 'decoration tools', 'style presets'],
    downloads: 11200,
    rating: 4.5,
    status: 'active',
    source: 'sketchup-extensions',
    createdBy: new mongoose.Types.ObjectId(),
    createdAt: new Date(),
    updatedAt: new Date()
  }
];

async function populateExtensions() {
  try {
    console.log('🔄 Connecting to MongoDB...');
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('✅ Connected to MongoDB');

    // Clear existing sample data
    console.log('🧹 Clearing existing sample extensions...');
    await Extension.deleteMany({ source: 'sketchup-extensions' });

    // Insert new sample data
    console.log('📦 Inserting sample extensions...');
    const result = await Extension.insertMany(sampleExtensions);

    console.log(`✅ Successfully inserted ${result.length} sample extensions`);

    // Display summary
    console.log('\n📊 Sample Extensions Summary:');
    sampleExtensions.forEach((ext, index) => {
      console.log(`${index + 1}. ${ext.name} (${ext.category}) - ${ext.downloads} downloads`);
    });

    console.log('\n🎉 Sample data population completed!');

  } catch (error) {
    console.error('❌ Error populating extensions:', error);
  } finally {
    await mongoose.disconnect();
    console.log('🔌 Disconnected from MongoDB');
    process.exit(0);
  }
}

// Run the script
populateExtensions();
