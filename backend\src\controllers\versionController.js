import Model from '../models/Model.js';
import versionConverter from '../services/versionConverter.js';
import linkChecker from '../utils/linkChecker.js';
import path from 'path';
import fs from 'fs';

// @desc    Get available versions for a model
// @route   GET /api/version/:modelId/available
// @access  Public
export const getAvailableVersions = async (req, res) => {
  try {
    const { modelId } = req.params;
    
    const model = await Model.findById(modelId);
    if (!model) {
      return res.status(404).json({
        success: false,
        error: 'Model not found'
      });
    }

    // Get current version from model
    const currentVersion = model.sketchupVersion || 
      (model.format && model.format.includes('Sketchup') ? model.format.replace('Sketchup ', '') : null);

    if (!currentVersion) {
      return res.status(400).json({
        success: false,
        error: 'Model version information not available'
      });
    }

    // Get available target versions
    const availableTargetVersions = versionConverter.getAvailableTargetVersions(currentVersion);
    
    // Get already converted versions
    const convertedVersions = model.getAvailableVersions();

    res.status(200).json({
      success: true,
      data: {
        currentVersion,
        availableTargetVersions,
        convertedVersions,
        canConvert: availableTargetVersions.length > 0
      }
    });

  } catch (error) {
    console.error('Get available versions failed:', error);
    res.status(500).json({
      success: false,
      error: error.message || 'Failed to get available versions'
    });
  }
};

// @desc    Request version conversion
// @route   POST /api/version/:modelId/convert
// @access  Private
export const requestConversion = async (req, res) => {
  try {
    const { modelId } = req.params;
    const { targetVersion } = req.body;
    const userId = req.user.id;

    if (!targetVersion) {
      return res.status(400).json({
        success: false,
        error: 'Target version is required'
      });
    }

    const model = await Model.findById(modelId);
    if (!model) {
      return res.status(404).json({
        success: false,
        error: 'Model not found'
      });
    }

    // Get current version
    const currentVersion = model.sketchupVersion || 
      (model.format && model.format.includes('Sketchup') ? model.format.replace('Sketchup ', '') : null);

    if (!currentVersion) {
      return res.status(400).json({
        success: false,
        error: 'Model version information not available'
      });
    }

    // Check if conversion is possible
    if (!versionConverter.canConvert(currentVersion, targetVersion)) {
      return res.status(400).json({
        success: false,
        error: `Cannot convert from SketchUp ${currentVersion} to ${targetVersion}. Only downgrade conversions are supported.`
      });
    }

    // Check if conversion already exists
    if (model.hasVersionConversion(targetVersion)) {
      const existingVersion = model.availableVersions.find(v => v.version === targetVersion);
      return res.status(200).json({
        success: true,
        data: {
          message: 'Conversion already available',
          conversionId: existingVersion._id,
          fileUrl: existingVersion.fileUrl,
          fileSize: existingVersion.fileSize,
          status: 'completed'
        }
      });
    }

    // Add conversion request
    await model.addConversionRequest(currentVersion, targetVersion, userId);
    
    // Get the latest conversion request
    const conversionRequest = model.conversionHistory[model.conversionHistory.length - 1];

    // Start conversion process asynchronously
    processConversion(modelId, conversionRequest._id, currentVersion, targetVersion);

    res.status(202).json({
      success: true,
      data: {
        message: 'Conversion request submitted',
        conversionId: conversionRequest._id,
        status: 'pending',
        estimatedTime: '2-5 minutes'
      }
    });

  } catch (error) {
    console.error('Request conversion failed:', error);
    res.status(500).json({
      success: false,
      error: error.message || 'Failed to request conversion'
    });
  }
};

// @desc    Get conversion status
// @route   GET /api/version/:modelId/conversion/:conversionId
// @access  Public
export const getConversionStatus = async (req, res) => {
  try {
    const { modelId, conversionId } = req.params;

    const model = await Model.findById(modelId);
    if (!model) {
      return res.status(404).json({
        success: false,
        error: 'Model not found'
      });
    }

    const conversion = model.conversionHistory.id(conversionId);
    if (!conversion) {
      return res.status(404).json({
        success: false,
        error: 'Conversion not found'
      });
    }

    res.status(200).json({
      success: true,
      data: {
        conversionId: conversion._id,
        status: conversion.status,
        fromVersion: conversion.fromVersion,
        toVersion: conversion.toVersion,
        fileUrl: conversion.fileUrl,
        fileSize: conversion.fileSize,
        errorMessage: conversion.errorMessage,
        createdAt: conversion.createdAt,
        completedAt: conversion.completedAt
      }
    });

  } catch (error) {
    console.error('Get conversion status failed:', error);
    res.status(500).json({
      success: false,
      error: error.message || 'Failed to get conversion status'
    });
  }
};

// @desc    Download converted version
// @route   GET /api/version/:modelId/download/:version
// @access  Private
export const downloadConvertedVersion = async (req, res) => {
  try {
    const { modelId, version } = req.params;
    const userId = req.user.id;

    const model = await Model.findById(modelId);
    if (!model) {
      return res.status(404).json({
        success: false,
        error: 'Model not found'
      });
    }

    // Check if converted version exists
    const convertedVersion = model.availableVersions.find(v => v.version === version);
    if (!convertedVersion || convertedVersion.conversionStatus !== 'completed') {
      return res.status(404).json({
        success: false,
        error: 'Converted version not found or not ready'
      });
    }

    // Update download count
    await Model.findByIdAndUpdate(modelId, {
      $inc: { downloads: 1 }
    });

    // Return download information for smart download
    res.status(200).json({
      success: true,
      data: {
        message: `SketchUp ${version} version ready for download`,
        fileUrl: convertedVersion.fileUrl,
        fileSize: convertedVersion.fileSize,
        filename: `${model.title.replace(/[^a-zA-Z0-9]/g, '_')}_v${version}.skp`,
        version: version,
        originalVersion: model.sketchupVersion
      }
    });

  } catch (error) {
    console.error('Download converted version failed:', error);
    res.status(500).json({
      success: false,
      error: error.message || 'Failed to download converted version'
    });
  }
};

// @desc    Get conversion statistics
// @route   GET /api/version/stats
// @access  Private (Admin)
export const getConversionStats = async (req, res) => {
  try {
    // Get system stats
    const systemStats = versionConverter.getConversionStats();
    
    // Get database stats
    const totalConversions = await Model.aggregate([
      { $unwind: '$conversionHistory' },
      { $group: { _id: '$conversionHistory.status', count: { $sum: 1 } } }
    ]);

    const popularVersions = await Model.aggregate([
      { $unwind: '$conversionHistory' },
      { $group: { _id: '$conversionHistory.toVersion', count: { $sum: 1 } } },
      { $sort: { count: -1 } },
      { $limit: 5 }
    ]);

    res.status(200).json({
      success: true,
      data: {
        system: systemStats,
        conversions: totalConversions,
        popularVersions
      }
    });

  } catch (error) {
    console.error('Get conversion stats failed:', error);
    res.status(500).json({
      success: false,
      error: error.message || 'Failed to get conversion statistics'
    });
  }
};

// Async function to process conversion
async function processConversion(modelId, conversionId, fromVersion, toVersion) {
  try {
    console.log(`🔄 Processing conversion: ${modelId} from ${fromVersion} to ${toVersion}`);
    
    // Update status to processing
    const model = await Model.findById(modelId);
    await model.updateConversionStatus(conversionId, 'processing');

    // Download original file if needed
    const originalFileUrl = model.fileUrl;
    const filename = `temp_${modelId}_${Date.now()}.skp`;
    
    // Download original file
    const downloadResult = await linkChecker.smartDownload([originalFileUrl], filename);
    const sourceFilePath = downloadResult.localPath;

    // Generate output filename
    const outputFileName = `${model.title.replace(/[^a-zA-Z0-9]/g, '_')}_v${toVersion}_${Date.now()}.skp`;

    // Perform conversion
    const conversionResult = await versionConverter.convertFile(sourceFilePath, toVersion, outputFileName);

    // Generate file URL (assuming files are served from /api/download/file/)
    const fileUrl = `/api/download/file/${outputFileName}`;

    // Update conversion status to completed
    await model.updateConversionStatus(
      conversionId, 
      'completed', 
      fileUrl, 
      conversionResult.fileSize
    );

    console.log(`✅ Conversion completed: ${outputFileName}`);

    // Clean up temporary source file
    if (fs.existsSync(sourceFilePath)) {
      fs.unlinkSync(sourceFilePath);
    }

  } catch (error) {
    console.error(`❌ Conversion failed for ${modelId}:`, error);
    
    // Update conversion status to failed
    const model = await Model.findById(modelId);
    await model.updateConversionStatus(conversionId, 'failed', null, null, error.message);
  }
}
