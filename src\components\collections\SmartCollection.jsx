import React, { useState, useEffect, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  FiCpu, // Use FiCpu instead of FiBrain
  FiPlus,
  FiSettings,
  FiUsers,
  FiTrendingUp,
  FiFilter,
  FiRefreshCw,
  FiEye,
  FiHeart,
  FiShare2,
  FiEdit3,
  FiTrash2,
  FiCheck,
  FiX,
  FiStar,
  FiClock,
  FiTag
} from 'react-icons/fi';
import { toast } from 'react-hot-toast';
import ModelCard from '../ModelCard';
import { useAuth } from '../../context/AuthContext';

const SmartCollection = ({
  collectionData,
  isOwner = false,
  onUpdate,
  onDelete,
  onAddModel,
  onRemoveModel
}) => {
  const { currentUser } = useAuth();
  const [showSettings, setShowSettings] = useState(false);
  const [showCriteria, setShowCriteria] = useState(false);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [editData, setEditData] = useState({
    name: collectionData?.name || ;,
    description: collectionData?.description || ;
  });

  // Smart criteria state
  const [smartCriteria, setSmartCriteria] = useState(
    collectionData?.smartCriteria || {
    enabled: false,
      rules: [],
      autoUpdate: true,
      maxModels: 100,
      sortBy: 'relevance'
    }
  );

  // AI suggestions state
  const [aiSuggestions, setAiSuggestions] = useState(
    collectionData?.aiRecommendations?.suggestions || []
  );

  const [pendingSuggestions, setPendingSuggestions] = useState(
    collectionData?.collaboration?.pendingSuggestions || []
  );

  useEffect(() => {
  if (true) {
  setSmartCriteria(collectionData.smartCriteria || smartCriteria);
      setAiSuggestions(collectionData.aiRecommendations?.suggestions || []);
      setPendingSuggestions(collectionData.collaboration?.pendingSuggestions || []);
    }
  }, [collectionData]);

  const handleSave = async () => {
  try {
      await onUpdate?.({
        ...editData,
        smartCriteria,
        aiRecommendations: {
          ...collectionData.aiRecommendations,
          enabled: smartCriteria.enabled
        }
      });
      setIsEditing(false);
      toast.success('Bộ sưu tập đã được cập nhật!'; 
    } catch (error) {
      toast.error('Lỗi khi cập nhật bộ sưu tập');
    }
  };

  const addSmartRule = () => {
    setSmartCriteria(prev => ({
      ...prev,
      rules: [
        ...prev.rules,
        {
    field: 'category',
          operator: 'equals',
          value: '',
          weight: 1
        }
      ]
    }));
  };

  const updateSmartRule = (index, field, value) => {
  setSmartCriteria(prev => ({
      ...prev,
      rules: prev.rules.map((rule, i) =>
        i === index ? { ...rule, [field]: value } : rule
      )
    }));
  };

  const removeSmartRule = (index) => {
  setSmartCriteria(prev => ({
      ...prev,
      rules: prev.rules.filter((_, i) => i !== index)
    }));
  };

  const handleSuggestionVote = async (suggestionId, vote) => {
  try {
      // API call to vote on suggestion
      setPendingSuggestions(prev =>
        prev.map(suggestion =>
          suggestion._id === suggestionId
            ? {
                ...suggestion,
                votes: [
                  ...suggestion.votes.filter(v => v.user !== currentUser.id),
                  { user: currentUser.id, vote, votedAt: new Date() }
                ]
              }
            : suggestion
        )
      );
      toast.success(`Đã ${vote === 'approve' ? 'chấp thuận' : 'từ chối'} gợi ý`);
    } catch (error) {
      toast.error('Lỗi khi bình chọn');
    }
  };

  const applySuggestion = async (suggestion) => {
  try {
      await onAddModel?.(suggestion.model);
      setAiSuggestions(prev => prev.filter(s => s._id !== suggestion._id));
      toast.success('Đã thêm mô hình vào bộ sưu tập!'; 
    } catch (error) {
      toast.error('Lỗi khi thêm mô hình');
    }
  };

  const getCollectionTypeIcon = () => {
    switch (collectionData?.type) {
      case 'smart':
        return <FiCpu className="h-5 w-5 text-purple-500" />;
      case 'collaborative':
        return <FiUsers className="h-5 w-5 text-blue-500" />;
      case 'curated':
        return <FiStar className="h-5 w-5 text-yellow-500" />;
      default:
        return <FiHeart className="h-5 w-5 text-gray-500" />;
    }
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden">
      {/* Header */}
      <div className="relative">
        {/* Cover Image */}
        {collectionData?.display?.coverImage && (
          <div className="h-48 bg-gradient-to-r from-blue-500 to-purple-600 relative overflow-hidden">
            <img
              src={collectionData.display.coverImage}
              alt={collectionData.name}
              className="w-full h-full object-cover"
            />
            <div className="absolute inset-0 bg-black bg-opacity-30" />
          </div>
        )}

        {/* Header Content */}
        <div className={`p-6 ${collectionData?.display?.coverImage ? 'absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black to-transparent text-white' : '}`}>
          <div className="flex items-start justify-between">
            <div className="flex-1">
              {isEditing ? (
                <div className="space-y-3">
                  <input
                    type="text"
                    value={editData.name}
                    onChange={(e) => setEditData(prev => ({ ...prev, name: e.target.value }))}
                    className="text-2xl font-bold bg-transparent border-b border-white border-opacity-50 focus:border-opacity-100 outline-none w-full"
                    placeholder="Tên bộ sưu tập"
                  />
                  <textarea
                    value={editData.description}
                    onChange={(e) => setEditData(prev => ({ ...prev, description: e.target.value }))}
                    className="bg-transparent border border-white border-opacity-50 rounded-lg p-2 focus:border-opacity-100 outline-none w-full resize-none"
                    rows="2"
                    placeholder="Mô tả bộ sưu tập"
                  />
                  <div className="flex space-x-2">
                    <button
                      onClick={handleSave}
                      className="flex items-center space-x-1 px-3 py-1 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
                    >
                      <FiCheck className="h-4 w-4" />
                      <span>Lưu</span>
                    </button>
                    <button
                      onClick={() => setIsEditing(false)}
                      className="flex items-center space-x-1 px-3 py-1 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
                    >
                      <FiX className="h-4 w-4" />
                      <span>Hủy</span>
                    </button>
                  </div>
                </div>
              ) : (
                <>
                  <div className="flex items-center space-x-3 mb-2">
                    {getCollectionTypeIcon()}
                    <h1 className="text-2xl font-bold">{collectionData?.name}</h1>
                    {collectionData?.featured && (
                      <span className="px-2 py-1 bg-yellow-500 text-white text-xs font-medium rounded-full">
                        Nổi bật
                      </span>
                    )}
                  </div>
                  <p className="text-gray-600 dark:text-gray-300 mb-4">
                    {collectionData?.description}
                  </p>
                  <div className="flex items-center space-x-4 text-sm">
                    <span className="flex items-center space-x-1">
                      <FiEye className="h-4 w-4" />
                      <span>{collectionData?.analytics?.totalViews || 0} lượt xem</span>
                    </span>
                    <span className="flex items-center space-x-1">
                      <FiHeart className="h-4 w-4" />
                      <span>{collectionData?.totalModels || 0} mô hình</span>
                    </span>
                    <span className="flex items-center space-x-1">
                      <FiClock className="h-4 w-4" />
                      <span>Cập nhật {new Date(collectionData?.updatedAt).toLocaleDateString('vi-VN')}</span>
                    </span>
                  </div>
                </>
              )}
            </div>

            {/* Action Buttons */}
            {!isEditing && (
              <div className="flex items-center space-x-2 ml-4">
                <button
                  onClick={() => setShowSuggestions(!showSuggestions)}
                  className="p-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
                  title="AI Suggestions"
                >
                  <FiCpu className="h-5 w-5" />
                </button>

                <button
                  onClick={() => {}}
                  className="p-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                  title="Share"
                >
                  <FiShare2 className="h-5 w-5" />
                </button>

                {isOwner && (
                  <>
                    <button
                      onClick={() => setIsEditing(true)}
                      className="p-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
                      title="Edit"
                    >
                      <FiEdit3 className="h-5 w-5" />
                    </button>

                    <button
                      onClick={() => setShowSettings(!showSettings)}
                      className="p-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
                      title="Settings"
                    >
                      <FiSettings className="h-5 w-5" />
                    </button>
                  </>
                )}
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Smart Criteria Panel */}
      <AnimatePresence>
        {showSettings && isOwner && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            className="border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-700/50"
          >
            <div className="p-6">
              <h3 className="text-lg font-semibold mb-4 flex items-center space-x-2">
                <FiCpu className="h-5 w-5 text-purple-500" />
                <span>Cài Đặt Bộ Sưu Tập Thông Minh</span>
              </h3>

              {/* Enable Smart Collection */}
              <div className="flex items-center justify-between mb-6">
                <div>
                  <label className="font-medium">Kích hoạt AI</label>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    Tự động thêm mô hình dựa trên tiêu chí
                  </p>
                </div>
                <button
                  onClick={() => setSmartCriteria(prev => ({ ...prev, enabled: !prev.enabled }))}
                  className={`w-12 h-6 rounded-full transition-colors ${
                    smartCriteria.enabled ? 'bg-purple-600' : 'bg-gray-300'
                  }`}
                >
                  <div className={`w-5 h-5 bg-white rounded-full transition-transform ${
                    smartCriteria.enabled ? 'translate-x-6' : 'translate-x-1'
                  }`} />
                </button>
              </div>

              {/* Smart Rules */}
              {smartCriteria.enabled && (
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <h4 className="font-medium">Tiêu Chí Thông Minh</h4>
                    <button
                      onClick={addSmartRule}
                      className="flex items-center space-x-1 px-3 py-1 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
                    >
                      <FiPlus className="h-4 w-4" />
                      <span>Thêm tiêu chí</span>
                    </button>
                  </div>

                  {smartCriteria.rules.map((rule, index) => (
                    <div key={index} className="flex items-center space-x-3 p-3 bg-white dark:bg-gray-800 rounded-lg">
                      <select
                        value={rule.field}
                        onChange={(e) => updateSmartRule(index, 'field', e.target.value)}
                        className="flex-1 bg-gray-100 dark:bg-gray-700 rounded px-3 py-2"
                      >
                        <option value="category">Danh mục</option>
                        <option value="tags">Tags</option>
                        <option value="rating">Đánh giá</option>
                        <option value="downloads">Lượt tải</option>
                        <option value="dateAdded">Ngày thêm</option>
                        <option value="fileSize">Kích thước</option>
                      </select>

                      <select
                        value={rule.operator}
                        onChange={(e) => updateSmartRule(index, 'operator', e.target.value)}
                        className="flex-1 bg-gray-100 dark:bg-gray-700 rounded px-3 py-2"
                      >
                        <option value="equals">Bằng</option>
                        <option value="contains">Chứa</option>
                        <option value="greaterThan">Lớn hơn</option>
                        <option value="lessThan">Nhỏ hơn</option>
                      </select>

                      <input
                        type="text"
                        value={rule.value}
                        onChange={(e) => updateSmartRule(index, 'value', e.target.value)}
                        className="flex-1 bg-gray-100 dark:bg-gray-700 rounded px-3 py-2"
                        placeholder="Giá trị"
                      />

                      <button
                        onClick={() => removeSmartRule(index)}
                        className="p-2 text-red-500 hover:bg-red-100 dark:hover:bg-red-900/20 rounded"
                      >
                        <FiTrash2 className="h-4 w-4" />
                      </button>
                    </div>
                  ))}

                  {/* Additional Settings */}
                  <div className="grid grid-cols-2 gap-4 mt-6">
                    <div>
                      <label className="block text-sm font-medium mb-2">Số mô hình tối đa</label>
                      <input
                        type="number"
                        value={smartCriteria.maxModels}
                        onChange={(e) => setSmartCriteria(prev => ({ ...prev, maxModels: parseInt(e.target.value) }))}
                        className="w-full bg-gray-100 dark:bg-gray-700 rounded px-3 py-2"
                        min="1"
                        max="1000"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium mb-2">Sắp xếp theo</label>
                      <select
                        value={smartCriteria.sortBy}
                        onChange={(e) => setSmartCriteria(prev => ({ ...prev, sortBy: e.target.value }))}
                        className="w-full bg-gray-100 dark:bg-gray-700 rounded px-3 py-2"
                      >
                        <option value="relevance">Độ liên quan</option>
                        <option value="rating">Đánh giá</option>
                        <option value="downloads">Lượt tải</option>
                        <option value="dateAdded">Ngày thêm</option>
                      </select>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* AI Suggestions Panel */}
      <AnimatePresence>
        {showSuggestions && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            className="border-t border-gray-200 dark:border-gray-700 bg-purple-50 dark:bg-purple-900/20"
          >
            <div className="p-6">
              <h3 className="text-lg font-semibold mb-4 flex items-center space-x-2">
                <FiCpu className="h-5 w-5 text-purple-500" />
                <span>Gợi Ý AI</span>
                <span className="text-sm bg-purple-100 dark:bg-purple-900/50 px-2 py-1 rounded-full">
                  {aiSuggestions.length} gợi ý
                </span>
              </h3>

              {aiSuggestions.length > 0 ? (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {aiSuggestions.map((suggestion, index) => (
                    <div key={index} className="bg-white dark:bg-gray-800 rounded-lg p-4 shadow-sm">
                      <div className="flex items-center justify-between mb-2">
                        <span className="text-sm font-medium">Mô hình gợi ý</span>
                        <span className="text-xs bg-purple-100 dark:bg-purple-900/50 px-2 py-1 rounded">
                          {Math.round(suggestion.confidence * 100)}% phù hợp
                        </span>
                      </div>
                      <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">
                        {suggestion.reason}
                      </p>
                      <div className="flex space-x-2">
                        <button
                          onClick={() => applySuggestion(suggestion)}
                          className="flex-1 bg-purple-600 text-white py-2 rounded-lg hover:bg-purple-700 transition-colors text-sm"
                        >
                          Thêm vào
                        </button>
                        <button
                          onClick={() => setAiSuggestions(prev => prev.filter((_, i) => i !== index))}
                          className="px-3 py-2 bg-gray-200 dark:bg-gray-700 rounded-lg hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors"
                        >
                          <FiX className="h-4 w-4" />
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <FiCpu className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-500 dark:text-gray-400">
                    Chưa có gợi ý nào. AI sẽ phân tích và đưa ra gợi ý phù hợp.
                  </p>
                </div>
              )}
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Collaborative Suggestions */}
      {collectionData?.collaboration?.enabled && pendingSuggestions.length > 0 && (
        <div className="border-t border-gray-200 dark:border-gray-700 bg-blue-50 dark:bg-blue-900/20 p-6">
          <h3 className="text-lg font-semibold mb-4 flex items-center space-x-2">
            <FiUsers className="h-5 w-5 text-blue-500" />
            <span>Gợi Ý Từ Cộng Đồng</span>
            <span className="text-sm bg-blue-100 dark:bg-blue-900/50 px-2 py-1 rounded-full">
              {pendingSuggestions.length} đang chờ
            </span>
          </h3>

          <div className="space-y-3">
            {pendingSuggestions.map((suggestion, index) => (
              <div key={index} className="bg-white dark:bg-gray-800 rounded-lg p-4 flex items-center justify-between">
                <div className="flex-1">
                  <div className="font-medium">Mô hình được đề xuất</div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">
                    Bởi: {suggestion.suggestedBy?.name} • {suggestion.reason}
                  </div>
                  <div className="text-xs text-gray-500 mt-1">
                    {suggestion.votes?.length || 0} lượt bình chọn
                  </div>
                </div>

                {isOwner && (
                  <div className="flex space-x-2">
                    <button
                      onClick={() => handleSuggestionVote(suggestion._id, 'approve')}
                      className="px-3 py-1 bg-green-600 text-white rounded hover:bg-green-700 transition-colors"
                    >
                      <FiCheck className="h-4 w-4" />
                    </button>
                    <button
                      onClick={() => handleSuggestionVote(suggestion._id, 'reject')}
                      className="px-3 py-1 bg-red-600 text-white rounded hover:bg-red-700 transition-colors"
                    >
                      <FiX className="h-4 w-4" />
                    </button>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Models Grid */}
      <div className="p-6">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-lg font-semibold">
            Mô hình ({collectionData?.totalModels || 0})
          </h3>

          {isOwner && (
            <button
              onClick={() => {}}
              className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              <FiPlus className="h-4 w-4" />
              <span>Thêm mô hình</span>
            </button>
          )}
        </div>

        {collectionData?.models && collectionData.models.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {collectionData.models.map((modelItem, index) => (
              <ModelCard
                key={index}
                model={modelItem.model || modelItem}
                showRemove={isOwner}
                onRemove={() => onRemoveModel?.(modelItem.model?._id || modelItem._id)}
              />
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            <FiHeart className="h-16 w-16 text-gray-300 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
              Bộ sưu tập trống
            </h3>
            <p className="text-gray-500 dark:text-gray-400 mb-6">
              {smartCriteria.enabled
                ? 'AI đang tìm kiếm mô hình phù hợp với tiêu chí của bạn'
                : 'Hãy thêm mô hình đầu tiên vào bộ sưu tập này'
              }
            </p>
            {isOwner && !smartCriteria.enabled && (
              <button
                onClick={() => {}}
                className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                Thêm mô hình đầu tiên
              </button>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default SmartCollection;
