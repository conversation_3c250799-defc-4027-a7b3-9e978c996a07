import fetch from 'node-fetch';

const createAdminViaAPI = async () => {
  try {
    console.log('Creating admin users via API...');

    // Create primary admin
    console.log('1. Creating primary admin...');
    const admin1Response = await fetch('http://localhost:5002/api/auth/register', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        name: 'Admin User',
        email: '<EMAIL>',
        password: 'admin123456'
      })
    });

    let admin1Data = null;
    if (admin1Response.ok) {
      admin1Data = await admin1Response.json();
      console.log('✅ Primary admin created:', admin1Data.user.email);
    } else {
      const error = await admin1Response.text();
      if (error.includes('already exists')) {
        console.log('Primary admin already exists, testing login...');
        
        const loginResponse = await fetch('http://localhost:5002/api/auth/login', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            email: '<EMAIL>',
            password: 'admin123456'
          })
        });

        if (loginResponse.ok) {
          admin1Data = await loginResponse.json();
          console.log('✅ Primary admin login successful:', admin1Data.user.email);
        } else {
          console.log('❌ Primary admin login failed');
        }
      } else {
        console.log('❌ Primary admin creation failed:', error);
      }
    }

    // Create backup admin
    console.log('2. Creating backup admin...');
    const admin2Response = await fetch('http://localhost:5002/api/auth/register', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        name: 'Super Admin',
        email: '<EMAIL>',
        password: 'superadmin123'
      })
    });

    let admin2Data = null;
    if (admin2Response.ok) {
      admin2Data = await admin2Response.json();
      console.log('✅ Backup admin created:', admin2Data.user.email);
    } else {
      const error = await admin2Response.text();
      if (error.includes('already exists')) {
        console.log('Backup admin already exists, testing login...');
        
        const loginResponse = await fetch('http://localhost:5002/api/auth/login', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            email: '<EMAIL>',
            password: 'superadmin123'
          })
        });

        if (loginResponse.ok) {
          admin2Data = await loginResponse.json();
          console.log('✅ Backup admin login successful:', admin2Data.user.email);
        } else {
          console.log('❌ Backup admin login failed');
        }
      } else {
        console.log('❌ Backup admin creation failed:', error);
      }
    }

    // Create Vietnamese admin
    console.log('3. Creating Vietnamese admin...');
    const admin3Response = await fetch('http://localhost:5002/api/auth/register', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        name: 'Quản Trị Viên',
        email: '<EMAIL>',
        password: 'admin2024'
      })
    });

    let admin3Data = null;
    if (admin3Response.ok) {
      admin3Data = await admin3Response.json();
      console.log('✅ Vietnamese admin created:', admin3Data.user.email);
    } else {
      const error = await admin3Response.text();
      if (error.includes('already exists')) {
        console.log('Vietnamese admin already exists, testing login...');
        
        const loginResponse = await fetch('http://localhost:5002/api/auth/login', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            email: '<EMAIL>',
            password: 'admin2024'
          })
        });

        if (loginResponse.ok) {
          admin3Data = await loginResponse.json();
          console.log('✅ Vietnamese admin login successful:', admin3Data.user.email);
        } else {
          console.log('❌ Vietnamese admin login failed');
        }
      } else {
        console.log('❌ Vietnamese admin creation failed:', error);
      }
    }

    console.log('\n🎯 ADMIN ACCOUNTS CREATED/VERIFIED:');
    console.log('');
    
    if (admin1Data) {
      console.log('1. PRIMARY ADMIN:');
      console.log('   Email: <EMAIL>');
      console.log('   Password: admin123456');
      console.log('   Name: Admin User');
      console.log('   User ID:', admin1Data.user.id);
      console.log('   Current Role:', admin1Data.user.role);
      console.log('');
    }

    if (admin2Data) {
      console.log('2. BACKUP ADMIN:');
      console.log('   Email: <EMAIL>');
      console.log('   Password: superadmin123');
      console.log('   Name: Super Admin');
      console.log('   User ID:', admin2Data.user.id);
      console.log('   Current Role:', admin2Data.user.role);
      console.log('');
    }

    if (admin3Data) {
      console.log('3. VIETNAMESE ADMIN:');
      console.log('   Email: <EMAIL>');
      console.log('   Password: admin2024');
      console.log('   Name: Quản Trị Viên');
      console.log('   User ID:', admin3Data.user.id);
      console.log('   Current Role:', admin3Data.user.role);
      console.log('');
    }

    console.log('4. EXISTING TEST ADMIN:');
    console.log('   Email: <EMAIL>');
    console.log('   Password: password123');
    console.log('   Name: Test Admin');
    console.log('   Current Role: user (can be upgraded)');
    console.log('');

    console.log('📝 NOTE: All accounts are created with "user" role by default.');
    console.log('To upgrade to admin role, you need to:');
    console.log('1. Login to the website with any of these accounts');
    console.log('2. Use admin panel to change roles');
    console.log('3. Or manually update database');
    console.log('');
    console.log('🌐 LOGIN URL: http://localhost:5173/login');

  } catch (error) {
    console.error('Error creating admin users:', error);
  }
};

createAdminViaAPI();
