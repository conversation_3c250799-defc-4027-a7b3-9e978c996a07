import fs from 'fs';
import path from 'path';

// Test configuration
const API_BASE = 'http://localhost:5002/api';
const TEST_IMAGES_DIR = './test-images';

// Create test images directory if it doesn't exist
if (!fs.existsSync(TEST_IMAGES_DIR)) {
  fs.mkdirSync(TEST_IMAGES_DIR, { recursive: true });
}

// Test functions
async function testChatbotImageAnalysis() {
  console.log('\n🔍 Testing Chatbot Image Analysis...');

  try {
    // Create a simple test image buffer (1x1 pixel PNG)
    const testImageBuffer = Buffer.from([
      0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A, 0x00, 0x00, 0x00, 0x0D,
      0x49, 0x48, 0x44, 0x52, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x01,
      0x08, 0x02, 0x00, 0x00, 0x00, 0x90, 0x77, 0x53, 0xDE, 0x00, 0x00, 0x00,
      0x0C, 0x49, 0x44, 0x41, 0x54, 0x08, 0xD7, 0x63, 0xF8, 0x00, 0x00, 0x00,
      0x01, 0x00, 0x01, 0x5C, 0xC2, 0x5D, 0xB4, 0x00, 0x00, 0x00, 0x00, 0x49,
      0x45, 0x4E, 0x44, 0xAE, 0x42, 0x60, 0x82
    ]);

    // Simple test - just check if the endpoint is reachable
    const response = await fetch(`${API_BASE}/chat/upload-image`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        test: true
      })
    });

    const result = await response.json();

    if (response.ok && result.success) {
      console.log('✅ Chatbot image analysis: SUCCESS');
      console.log(`   - Analysis completed: ${!!result.data.analysis}`);
      console.log(`   - Related models found: ${result.data.relatedModels?.length || 0}`);
      console.log(`   - Response generated: ${!!result.data.response}`);
      return true;
    } else {
      console.log('❌ Chatbot image analysis: FAILED');
      console.log(`   - Error: ${result.error || 'Unknown error'}`);
      return false;
    }
  } catch (error) {
    console.log('❌ Chatbot image analysis: ERROR');
    console.log(`   - Error: ${error.message}`);
    return false;
  }
}

async function testExtensionVisualSearch() {
  console.log('\n🔍 Testing Extension Visual Search...');

  try {
    // Create a simple test image buffer (1x1 pixel PNG)
    const testImageBuffer = Buffer.from([
      0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A, 0x00, 0x00, 0x00, 0x0D,
      0x49, 0x48, 0x44, 0x52, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x01,
      0x08, 0x02, 0x00, 0x00, 0x00, 0x90, 0x77, 0x53, 0xDE, 0x00, 0x00, 0x00,
      0x0C, 0x49, 0x44, 0x41, 0x54, 0x08, 0xD7, 0x63, 0xF8, 0x00, 0x00, 0x00,
      0x01, 0x00, 0x01, 0x5C, 0xC2, 0x5D, 0xB4, 0x00, 0x00, 0x00, 0x00, 0x49,
      0x45, 0x4E, 0x44, 0xAE, 0x42, 0x60, 0x82
    ]);

    const formData = new FormData();
    formData.append('image', testImageBuffer, {
      filename: 'test-extension-image.png',
      contentType: 'image/png'
    });
    formData.append('category', '');

    const response = await fetch(`${API_BASE}/extensions/visual-search`, {
      method: 'POST',
      body: formData
    });

    const result = await response.json();

    if (response.ok && result.success) {
      console.log('✅ Extension visual search: SUCCESS');
      console.log(`   - Analysis completed: ${!!result.data.analysis}`);
      console.log(`   - Extensions found: ${result.data.extensions?.length || 0}`);
      console.log(`   - Search type: ${result.data.searchType || 'unknown'}`);
      return true;
    } else {
      console.log('❌ Extension visual search: FAILED');
      console.log(`   - Error: ${result.error || 'Unknown error'}`);
      return false;
    }
  } catch (error) {
    console.log('❌ Extension visual search: ERROR');
    console.log(`   - Error: ${error.message}`);
    return false;
  }
}

async function testDatabaseConnections() {
  console.log('\n🔍 Testing Database Connections...');

  try {
    // Test models endpoint
    const modelsResponse = await fetch(`${API_BASE}/mongodb/models`);
    const modelsResult = await modelsResponse.json();

    // Test extensions endpoint (if exists)
    let extensionsCount = 0;
    try {
      const extensionsResponse = await fetch(`${API_BASE}/extensions`);
      if (extensionsResponse.ok) {
        const extensionsResult = await extensionsResponse.json();
        extensionsCount = extensionsResult.data?.length || 0;
      }
    } catch (e) {
      // Extensions endpoint might not exist, that's ok
    }

    if (modelsResponse.ok && modelsResult.success) {
      console.log('✅ Database connections: SUCCESS');
      console.log(`   - Models in database: ${modelsResult.data?.length || 0}`);
      console.log(`   - Extensions in database: ${extensionsCount}`);
      return true;
    } else {
      console.log('❌ Database connections: FAILED');
      console.log(`   - Error: ${modelsResult.error || 'Unknown error'}`);
      return false;
    }
  } catch (error) {
    console.log('❌ Database connections: ERROR');
    console.log(`   - Error: ${error.message}`);
    return false;
  }
}

async function testGeminiAPI() {
  console.log('\n🔍 Testing Gemini API Configuration...');

  try {
    // Test by making a simple image analysis request
    const testImageBuffer = Buffer.from([
      0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A, 0x00, 0x00, 0x00, 0x0D,
      0x49, 0x48, 0x44, 0x52, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x01,
      0x08, 0x02, 0x00, 0x00, 0x00, 0x90, 0x77, 0x53, 0xDE, 0x00, 0x00, 0x00,
      0x0C, 0x49, 0x44, 0x41, 0x54, 0x08, 0xD7, 0x63, 0xF8, 0x00, 0x00, 0x00,
      0x01, 0x00, 0x01, 0x5C, 0xC2, 0x5D, 0xB4, 0x00, 0x00, 0x00, 0x00, 0x49,
      0x45, 0x4E, 0x44, 0xAE, 0x42, 0x60, 0x82
    ]);

    const formData = new FormData();
    formData.append('image', testImageBuffer, {
      filename: 'gemini-test.png',
      contentType: 'image/png'
    });
    formData.append('language', 'en');

    const response = await fetch(`${API_BASE}/chat/upload-image`, {
      method: 'POST',
      body: formData
    });

    const result = await response.json();

    if (response.ok && result.success && result.data.analysis) {
      console.log('✅ Gemini API: SUCCESS');
      console.log(`   - API key valid: Yes`);
      console.log(`   - Vision analysis working: Yes`);
      console.log(`   - Response generated: Yes`);
      return true;
    } else {
      console.log('❌ Gemini API: FAILED');
      console.log(`   - Error: ${result.error || 'API key invalid or analysis failed'}`);
      return false;
    }
  } catch (error) {
    console.log('❌ Gemini API: ERROR');
    console.log(`   - Error: ${error.message}`);
    return false;
  }
}

async function runAllTests() {
  console.log('🚀 Starting Image Search System Tests...');
  console.log('=' .repeat(50));

  const results = {
    database: await testDatabaseConnections(),
    gemini: await testGeminiAPI(),
    chatbot: await testChatbotImageAnalysis(),
    extensions: await testExtensionVisualSearch()
  };

  console.log('\n' + '=' .repeat(50));
  console.log('📊 Test Results Summary:');
  console.log('=' .repeat(50));

  const passed = Object.values(results).filter(Boolean).length;
  const total = Object.keys(results).length;

  Object.entries(results).forEach(([test, passed]) => {
    console.log(`${passed ? '✅' : '❌'} ${test.charAt(0).toUpperCase() + test.slice(1)}: ${passed ? 'PASSED' : 'FAILED'}`);
  });

  console.log('\n' + '=' .repeat(50));
  console.log(`🎯 Overall Result: ${passed}/${total} tests passed`);

  if (passed === total) {
    console.log('🎉 All tests passed! Image search system is 100% functional!');
  } else {
    console.log('⚠️  Some tests failed. Please check the errors above.');
  }

  console.log('=' .repeat(50));

  return passed === total;
}

// Run tests if this script is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  runAllTests()
    .then(success => {
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      console.error('❌ Test runner error:', error);
      process.exit(1);
    });
}

export { runAllTests, testChatbotImageAnalysis, testExtensionVisualSearch, testDatabaseConnections, testGeminiAPI };
