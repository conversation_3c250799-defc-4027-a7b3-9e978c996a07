import mongoose from 'mongoose';
import dotenv from 'dotenv';
import Model from '../models/Model.js';

// Load environment variables
dotenv.config();

const enhancedModels = [
  // Furniture - Chairs
  {
    title: "Modern Ergonomic Office Chair",
    description: "Contemporary office chair with ergonomic design, adjustable height, lumbar support, and breathable mesh back. Perfect for modern workspaces.",
    category: "Furniture",
    tags: ["chair", "office", "modern", "ergonomic", "mesh", "adjustable", "black", "metal", "fabric"],
    fileUrl: "https://example.com/models/office-chair.skp",
    imageUrl: "https://example.com/images/office-chair.jpg",
    fileSize: 2.8,
    format: "Sketchup 2023",
    fileFormat: "skp",
    downloads: 1250,
    rating: 4.6,
    createdBy: new mongoose.Types.ObjectId(),
    views: 3200
  },
  {
    title: "Mid-Century Modern Dining Chair",
    description: "Classic mid-century dining chair with walnut wood frame and upholstered seat. Timeless design for dining rooms.",
    category: "Furniture",
    tags: ["chair", "dining", "mid-century", "walnut", "wood", "upholstered", "brown", "vintage"],
    fileUrl: "https://example.com/models/dining-chair.skp",
    imageUrl: "https://example.com/images/dining-chair.jpg",
    fileSize: 1.9,
    format: "Sketchup 2023",
    fileFormat: "skp",
    downloads: 890,
    rating: 4.4,
    createdBy: new mongoose.Types.ObjectId(),
    views: 2100
  },

  // Furniture - Tables
  {
    title: "Industrial Coffee Table with Steel Frame",
    description: "Industrial style coffee table featuring reclaimed wood top and black steel frame. Perfect for modern living rooms.",
    category: "Furniture",
    tags: ["table", "coffee table", "industrial", "steel", "wood", "reclaimed", "black", "metal", "living room"],
    downloadUrl: "https://example.com/models/industrial-coffee-table.skp",
    fileSize: 3.2,
    format: "SketchUp",
    downloads: 1580,
    rating: 4.7,
    createdBy: new mongoose.Types.ObjectId(),
    views: 4200
  },
  {
    title: "Scandinavian Oak Dining Table",
    description: "Minimalist Scandinavian dining table made from solid oak wood. Clean lines and natural finish for modern homes.",
    category: "Furniture",
    tags: ["table", "dining table", "scandinavian", "oak", "wood", "minimalist", "natural", "dining room"],
    downloadUrl: "https://example.com/models/oak-dining-table.skp",
    fileSize: 4.1,
    format: "SketchUp",
    downloads: 2100,
    rating: 4.8,
    createdBy: new mongoose.Types.ObjectId(),
    views: 5600
  },

  // Residential Architecture
  {
    title: "Contemporary Single Family House",
    description: "Modern single-family house with clean lines, large windows, flat roof, and open floor plan. Features concrete and glass materials.",
    category: "Residential",
    tags: ["house", "contemporary", "modern", "single family", "concrete", "glass", "flat roof", "windows", "residential"],
    downloadUrl: "https://example.com/models/contemporary-house.skp",
    fileSize: 15.6,
    format: "SketchUp",
    downloads: 3200,
    rating: 4.9,
    createdBy: new mongoose.Types.ObjectId(),
    views: 8900
  },
  {
    title: "Traditional Victorian House",
    description: "Classic Victorian house with ornate details, bay windows, decorative trim, and traditional proportions. Brick and wood construction.",
    category: "Residential",
    tags: ["house", "victorian", "traditional", "bay windows", "brick", "wood", "ornate", "decorative", "residential"],
    downloadUrl: "https://example.com/models/victorian-house.skp",
    fileSize: 18.3,
    format: "SketchUp",
    downloads: 2800,
    rating: 4.6,
    createdBy: new mongoose.Types.ObjectId(),
    views: 7200
  },
  {
    title: "Minimalist Apartment Interior",
    description: "Clean and minimalist apartment interior with white walls, concrete floors, and modern furniture. Open concept living space.",
    category: "Residential",
    tags: ["apartment", "interior", "minimalist", "white", "concrete", "modern", "open concept", "living space"],
    downloadUrl: "https://example.com/models/minimalist-apartment.skp",
    fileSize: 12.4,
    format: "SketchUp",
    downloads: 1950,
    rating: 4.5,
    createdBy: new mongoose.Types.ObjectId(),
    views: 5100
  },

  // Commercial Buildings
  {
    title: "Modern Office Building",
    description: "Contemporary office building with glass curtain wall, steel structure, and sustainable design features. Multi-story commercial architecture.",
    category: "Commercial",
    tags: ["office building", "commercial", "modern", "glass", "steel", "curtain wall", "sustainable", "multi-story"],
    downloadUrl: "https://example.com/models/office-building.skp",
    fileSize: 25.7,
    format: "SketchUp",
    downloads: 1600,
    rating: 4.4,
    createdBy: new mongoose.Types.ObjectId(),
    views: 4300
  },
  {
    title: "Boutique Retail Store",
    description: "Small boutique retail store with modern storefront, large display windows, and contemporary interior design.",
    category: "Commercial",
    tags: ["retail", "store", "boutique", "storefront", "display windows", "modern", "commercial", "interior"],
    downloadUrl: "https://example.com/models/boutique-store.skp",
    fileSize: 8.9,
    format: "SketchUp",
    downloads: 1200,
    rating: 4.3,
    createdBy: new mongoose.Types.ObjectId(),
    views: 3100
  },

  // Landscape/Garden
  {
    title: "Modern Garden Pavilion",
    description: "Contemporary garden pavilion with wooden structure, green roof, and integration with landscape. Perfect for outdoor spaces.",
    category: "Landscape/Garden",
    tags: ["pavilion", "garden", "modern", "wood", "green roof", "landscape", "outdoor", "structure"],
    downloadUrl: "https://example.com/models/garden-pavilion.skp",
    fileSize: 6.8,
    format: "SketchUp",
    downloads: 980,
    rating: 4.5,
    createdBy: new mongoose.Types.ObjectId(),
    views: 2600
  },
  {
    title: "Japanese Zen Garden",
    description: "Traditional Japanese zen garden with stone arrangements, gravel patterns, bamboo elements, and minimalist design.",
    category: "Landscape/Garden",
    tags: ["zen garden", "japanese", "traditional", "stone", "gravel", "bamboo", "minimalist", "landscape"],
    downloadUrl: "https://example.com/models/zen-garden.skp",
    fileSize: 5.2,
    format: "SketchUp",
    downloads: 1400,
    rating: 4.7,
    createdBy: new mongoose.Types.ObjectId(),
    views: 3800
  },

  // Exterior Elements
  {
    title: "Modern Entrance Door with Sidelight",
    description: "Contemporary entrance door with glass sidelight, steel frame, and minimalist design. Perfect for modern homes.",
    category: "Exterior",
    tags: ["door", "entrance", "modern", "glass", "sidelight", "steel", "minimalist", "exterior"],
    downloadUrl: "https://example.com/models/modern-door.skp",
    fileSize: 2.1,
    format: "SketchUp",
    downloads: 1800,
    rating: 4.6,
    createdBy: new mongoose.Types.ObjectId(),
    views: 4700
  },
  {
    title: "Large Picture Window",
    description: "Floor-to-ceiling picture window with aluminum frame and energy-efficient glass. Maximizes natural light and views.",
    category: "Exterior",
    tags: ["window", "picture window", "floor-to-ceiling", "aluminum", "glass", "energy efficient", "natural light"],
    downloadUrl: "https://example.com/models/picture-window.skp",
    fileSize: 1.7,
    format: "SketchUp",
    downloads: 2200,
    rating: 4.8,
    createdBy: new mongoose.Types.ObjectId(),
    views: 5900
  },

  // Decorative Items
  {
    title: "Modern Abstract Sculpture",
    description: "Contemporary abstract sculpture in brushed steel finish. Geometric forms create visual interest for modern interiors.",
    category: "Furniture",
    tags: ["sculpture", "abstract", "modern", "steel", "brushed", "geometric", "decorative", "art"],
    downloadUrl: "https://example.com/models/abstract-sculpture.skp",
    fileSize: 1.3,
    format: "SketchUp",
    downloads: 750,
    rating: 4.2,
    createdBy: new mongoose.Types.ObjectId(),
    views: 1900
  },
  {
    title: "Indoor Plant Collection",
    description: "Collection of popular indoor plants including fiddle leaf fig, monstera, and snake plant. Perfect for adding life to interiors.",
    category: "Furniture",
    tags: ["plants", "indoor", "fiddle leaf fig", "monstera", "snake plant", "decorative", "green", "nature"],
    downloadUrl: "https://example.com/models/indoor-plants.skp",
    fileSize: 3.4,
    format: "SketchUp",
    downloads: 1650,
    rating: 4.7,
    createdBy: new mongoose.Types.ObjectId(),
    views: 4400
  }
];

async function populateEnhancedModels() {
  try {
    console.log('🚀 Connecting to MongoDB...');
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('✅ Connected to MongoDB');

    console.log('🔄 Clearing existing enhanced models...');
    await Model.deleteMany({
      title: { $in: enhancedModels.map(model => model.title) }
    });

    console.log('📦 Adding enhanced 3D models...');
    const insertedModels = await Model.insertMany(enhancedModels);

    console.log(`✅ Successfully added ${insertedModels.length} enhanced models`);

    // Display summary
    const categoryCounts = {};
    insertedModels.forEach(model => {
      categoryCounts[model.category] = (categoryCounts[model.category] || 0) + 1;
    });

    console.log('\n📊 Models by category:');
    Object.entries(categoryCounts).forEach(([category, count]) => {
      console.log(`   ${category}: ${count} models`);
    });

    console.log('\n🎯 Enhanced features added:');
    console.log('   ✅ Comprehensive object vocabulary');
    console.log('   ✅ Detailed material specifications');
    console.log('   ✅ Enhanced color descriptions');
    console.log('   ✅ Architectural style variations');
    console.log('   ✅ Room and building type diversity');
    console.log('   ✅ Improved similarity matching data');

  } catch (error) {
    console.error('❌ Error populating enhanced models:', error);
  } finally {
    await mongoose.disconnect();
    console.log('🔌 Disconnected from MongoDB');
    process.exit(0);
  }
}

// Run the population
populateEnhancedModels();
