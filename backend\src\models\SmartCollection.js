import mongoose from 'mongoose';

const SmartCollectionSchema = new mongoose.Schema({
  name: {
    type: String,
    required: [true, 'Please add a collection name'],
    trim: true,
    maxlength: [100, 'Name cannot be more than 100 characters']
  },
  description: {
    type: String,
    maxlength: [500, 'Description cannot be more than 500 characters']
  },
  owner: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  type: {
    type: String,
    enum: ['manual', 'smart', 'collaborative', 'curated'],
    default: 'manual'
  },
  isPublic: {
    type: Boolean,
    default: false
  },
  // Smart collection criteria
  smartCriteria: {
    enabled: { type: Boolean, default: false },
    rules: [{
      field: {
        type: String,
        enum: ['category', 'subcategory', 'format', 'tags', 'rating', 'downloads', 'dateAdded', 'fileSize', 'isPremium']
      },
      operator: {
        type: String,
        enum: ['equals', 'contains', 'startsWith', 'endsWith', 'greaterThan', 'lessThan', 'between', 'in', 'notIn']
      },
      value: mongoose.Schema.Types.Mixed,
      weight: { type: Number, default: 1 }
    }],
    autoUpdate: { type: Boolean, default: true },
    maxModels: { type: Number, default: 100 },
    sortBy: {
      type: String,
      enum: ['relevance', 'rating', 'downloads', 'dateAdded', 'alphabetical'],
      default: 'relevance'
    },
    lastUpdated: Date
  },
  // AI recommendations
  aiRecommendations: {
    enabled: { type: Boolean, default: false },
    basedOn: {
      type: String,
      enum: ['userBehavior', 'similarCollections', 'trendingModels', 'semanticSimilarity'],
      default: 'userBehavior'
    },
    confidence: { type: Number, min: 0, max: 1 },
    lastAnalyzed: Date,
    suggestions: [{
      model: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Model'
      },
      reason: String,
      confidence: { type: Number, min: 0, max: 1 },
      addedAt: { type: Date, default: Date.now }
    }]
  },
  // Manual models (for manual and hybrid collections)
  models: [{
    model: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Model'
    },
    addedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    addedAt: { type: Date, default: Date.now },
    order: { type: Number, default: 0 },
    note: String
  }],
  // Collaborative features
  collaboration: {
    enabled: { type: Boolean, default: false },
    contributors: [{
      user: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User'
      },
      role: {
        type: String,
        enum: ['viewer', 'contributor', 'moderator', 'admin'],
        default: 'contributor'
      },
      addedAt: { type: Date, default: Date.now }
    }],
    permissions: {
      allowSuggestions: { type: Boolean, default: true },
      requireApproval: { type: Boolean, default: true },
      allowVoting: { type: Boolean, default: true }
    },
    pendingSuggestions: [{
      model: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Model'
      },
      suggestedBy: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User'
      },
      reason: String,
      votes: [{
        user: {
          type: mongoose.Schema.Types.ObjectId,
          ref: 'User'
        },
        vote: {
          type: String,
          enum: ['approve', 'reject']
        },
        votedAt: { type: Date, default: Date.now }
      }],
      status: {
        type: String,
        enum: ['pending', 'approved', 'rejected'],
        default: 'pending'
      },
      suggestedAt: { type: Date, default: Date.now }
    }]
  },
  // Analytics and stats
  analytics: {
    totalViews: { type: Number, default: 0 },
    uniqueViewers: { type: Number, default: 0 },
    totalDownloads: { type: Number, default: 0 },
    averageRating: { type: Number, min: 0, max: 5 },
    mostPopularModel: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Model'
    },
    growthRate: { type: Number, default: 0 } // models added per week
  },
  // Display settings
  display: {
    coverImage: String,
    layout: {
      type: String,
      enum: ['grid', 'list', 'masonry', 'carousel'],
      default: 'grid'
    },
    sortOrder: {
      type: String,
      enum: ['manual', 'alphabetical', 'dateAdded', 'rating', 'downloads'],
      default: 'manual'
    },
    showStats: { type: Boolean, default: true },
    showContributors: { type: Boolean, default: true }
  },
  tags: [String],
  featured: { type: Boolean, default: false },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

// Indexes for performance
SmartCollectionSchema.index({ owner: 1 });
SmartCollectionSchema.index({ type: 1, isPublic: 1 });
SmartCollectionSchema.index({ 'smartCriteria.enabled': 1 });
SmartCollectionSchema.index({ featured: 1 });
SmartCollectionSchema.index({ tags: 1 });
SmartCollectionSchema.index({ createdAt: -1 });

// Update timestamps pre-save
SmartCollectionSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  next();
});

// Virtual for total model count
SmartCollectionSchema.virtual('totalModels').get(function() {
  return this.models.length + (this.aiRecommendations.suggestions?.length || 0);
});

// Static method to update smart collections
SmartCollectionSchema.statics.updateSmartCollections = async function() {
  const smartCollections = await this.find({
    'smartCriteria.enabled': true,
    'smartCriteria.autoUpdate': true
  });

  for (const collection of smartCollections) {
    // This would be implemented to update based on criteria
    // For now, just update the lastUpdated timestamp
    collection.smartCriteria.lastUpdated = new Date();
    await collection.save();
  }
};

export default mongoose.model('SmartCollection', SmartCollectionSchema);
