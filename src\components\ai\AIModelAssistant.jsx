import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  FiCpu, // Use FiCpu instead of FiBrain
  FiCheckCircle,
  FiAlertTriangle,
  FiXCircle,
  FiInfo,
  FiRefreshCw,
  FiTrendingUp,
  FiSettings,
  FiEye,
  FiZap
} from 'react-icons/fi';
import { toast } from 'react-hot-toast';
import apiService from '../../services/api';

const AIModelAssistant = ({ modelId, modelData, onClose }) => {
  const [activeTab, setActiveTab] = useState('overview');
  const [analysisData, setAnalysisData] = useState(null);
  const [recommendations, setRecommendations] = useState(null);
  const [healthScore, setHealthScore] = useState(null);
  const [loading, setLoading] = useState(false);
  const [analyzing, setAnalyzing] = useState(false);

  useEffect(() => {
    if (modelId) {
      loadInitialData();
    }
  }, [modelId]);

  const loadInitialData = async () => {
    setLoading(true);
    try {
      // Load health score
      const healthResponse = await apiService.ai.getModelHealth(modelId);
      if (healthResponse.data.success) {
        setHealthScore(healthResponse.data.data);
      }

      // Load recommendations
      const recResponse = await apiService.ai.getRecommendations(modelId);
      if (recResponse.data.success) {
        setRecommendations(recResponse.data.data);
      }
    } catch (error) {
      console.error('Error loading AI data:', error);
    } finally {
      setLoading(false);
    }
  };

  const runAnalysis = async (analysisType = 'quality') => {
    setAnalyzing(true);
    try {
      const response = await apiService.ai.analyzeModel(modelId, { analysisType });
      if (response.data.success) {
        setAnalysisData(response.data.data);
        toast.success('Phân tích hoàn thành!');
        // Reload other data
        loadInitialData();
      }
    } catch (error) {
      console.error('Analysis error:', error);
      toast.error('Lỗi khi phân tích mô hình');
    } finally {
      setAnalyzing(false);
    }
  };

  const getHealthColor = (score) => {
    if (score >= 90) return 'text-green-500';
    if (score >= 75) return 'text-blue-500';
    if (score >= 60) return 'text-yellow-500';
    if (score >= 40) return 'text-orange-500';
    return 'text-red-500';
  };

  const getHealthBgColor = (score) => {
    if (score >= 90) return 'bg-green-100 dark:bg-green-900/20';
    if (score >= 75) return 'bg-blue-100 dark:bg-blue-900/20';
    if (score >= 60) return 'bg-yellow-100 dark:bg-yellow-900/20';
    if (score >= 40) return 'bg-orange-100 dark:bg-orange-900/20';
    return 'bg-red-100 dark:bg-red-900/20';
  };

  const getSeverityIcon = (severity) => {
    switch (severity) {
      case 'critical':
        return <FiXCircle className="text-red-500" />;
      case 'high':
        return <FiAlertTriangle className="text-orange-500" />;
      case 'medium':
        return <FiInfo className="text-yellow-500" />;
      case 'low':
        return <FiCheckCircle className="text-green-500" />;
      default:
        return <FiInfo className="text-gray-500" />;
    }
  };

  const tabs = [
    { id: 'overview', label: 'Tổng Quan', icon: FiEye },
    { id: 'analysis', label: 'Phân Tích', icon: FiCpu },
    { id: 'recommendations', label: 'Gợi Ý', icon: FiTrendingUp },
    { id: 'optimization', label: 'Tối Ưu', icon: FiZap }
  ];

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.95 }}
      animate={{ opacity: 1, scale: 1 }}
      exit={{ opacity: 0, scale: 0.95 }}
      className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-50"
    >
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-blue-100 dark:bg-blue-900/20 rounded-lg">
              <FiCpu className="h-6 w-6 text-blue-600 dark:text-blue-400" />
            </div>
            <div>
              <h2 className="text-xl font-bold text-gray-900 dark:text-white">
                Trợ Lý AI Mô Hình
              </h2>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                {modelData?.title || 'Đang phân tích mô hình...'}
              </p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
          >
            <FiXCircle className="h-5 w-5 text-gray-500" />
          </button>
        </div>

        {/* Health Score Banner */}
        {healthScore && (
          <div className={`p-4 ${getHealthBgColor(healthScore.healthScore)}`}>
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className={`text-2xl font-bold ${getHealthColor(healthScore.healthScore)}`}>
                  {healthScore.healthScore}/100
                </div>
                <div>
                  <div className="font-medium text-gray-900 dark:text-white">
                    Điểm Sức Khỏe Mô Hình
                  </div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">
                    Trạng thái: {healthScore.status === 'excellent' ? 'Xuất sắc' :
                                healthScore.status === 'good' ? 'Tốt' :
                                healthScore.status === 'fair' ? 'Khá' :
                                healthScore.status === 'poor' ? 'Kém' : 'Nghiêm trọng'}
                  </div>
                </div>
              </div>
              <button
                onClick={() => runAnalysis('quality')}
                disabled={analyzing}
                className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 transition-colors"
              >
                <FiRefreshCw className={`h-4 w-4 ${analyzing ? 'animate-spin' : ''}`} />
                <span>{analyzing ? 'Đang phân tích...' : 'Phân tích lại'}</span>
              </button>
            </div>
          </div>
        )}

        {/* Tabs */}
        <div className="flex border-b border-gray-200 dark:border-gray-700">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`flex items-center space-x-2 px-6 py-3 font-medium transition-colors ${
                activeTab === tab.id
                  ? 'text-blue-600 dark:text-blue-400 border-b-2 border-blue-600 dark:border-blue-400'
                  : 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'
              }`}
            >
              <tab.icon className="h-4 w-4" />
              <span>{tab.label}</span>
            </button>
          ))}
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-96">
          <AnimatePresence mode="wait">
            {activeTab === 'overview' && (
              <OverviewTab
                modelData={modelData}
                healthScore={healthScore}
                recommendations={recommendations}
              />
            )}
            {activeTab === 'analysis' && (
              <AnalysisTab
                analysisData={analysisData}
                onRunAnalysis={runAnalysis}
                analyzing={analyzing}
              />
            )}
            {activeTab === 'recommendations' && (
              <RecommendationsTab recommendations={recommendations} />
            )}
            {activeTab === 'optimization' && (
              <OptimizationTab
                modelData={modelData}
                recommendations={recommendations}
              />
            )}
          </AnimatePresence>
        </div>
      </div>
    </motion.div>
  );
};

// Overview Tab Component
const OverviewTab = ({ modelData, healthScore, recommendations }) => (
  <motion.div
    initial={{ opacity: 0, y: 20 }}
    animate={{ opacity: 1, y: 0 }}
    exit={{ opacity: 0, y: -20 }}
    className="space-y-6"
  >
    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
      {/* Model Info */}
      <div className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4">
        <h3 className="font-semibold text-gray-900 dark:text-white mb-3">
          Thông Tin Mô Hình
        </h3>
        <div className="space-y-2 text-sm">
          <div className="flex justify-between">
            <span className="text-gray-600 dark:text-gray-400">Định dạng:</span>
            <span className="font-medium">{modelData?.format}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-600 dark:text-gray-400">Kích thước:</span>
            <span className="font-medium">{modelData?.fileSize ? `${(modelData.fileSize / 1024 / 1024).toFixed(2)} MB` : 'N/A'}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-600 dark:text-gray-400">Polygon:</span>
            <span className="font-medium">{modelData?.polygonCount || 'N/A'}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-600 dark:text-gray-400">Texture:</span>
            <span className="font-medium">{modelData?.textured ? 'Có' : 'Không'}</span>
          </div>
        </div>
      </div>

      {/* Quick Stats */}
      <div className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4">
        <h3 className="font-semibold text-gray-900 dark:text-white mb-3">
          Thống Kê Nhanh
        </h3>
        <div className="space-y-2 text-sm">
          <div className="flex justify-between">
            <span className="text-gray-600 dark:text-gray-400">Lượt tải:</span>
            <span className="font-medium">{modelData?.downloads || 0}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-600 dark:text-gray-400">Đánh giá:</span>
            <span className="font-medium">{modelData?.rating || 'Chưa có'}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-600 dark:text-gray-400">Lượt xem:</span>
            <span className="font-medium">{modelData?.views || 0}</span>
          </div>
        </div>
      </div>
    </div>

    {/* Quick Recommendations */}
    {recommendations && recommendations.recommendations && (
      <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4">
        <h3 className="font-semibold text-gray-900 dark:text-white mb-3">
          Gợi Ý Nhanh
        </h3>
        <div className="space-y-2">
          {recommendations.recommendations.slice(0, 3).map((rec, index) => (
            <div key={index} className="flex items-start space-x-2">
              <FiTrendingUp className="h-4 w-4 text-blue-600 dark:text-blue-400 mt-0.5 flex-shrink-0" />
              <span className="text-sm text-gray-700 dark:text-gray-300">
                {rec.suggestion}
              </span>
            </div>
          ))}
        </div>
      </div>
    )}
  </motion.div>
);

// Analysis Tab Component
const AnalysisTab = ({ analysisData, onRunAnalysis, analyzing }) => (
  <motion.div
    initial={{ opacity: 0, y: 20 }}
    animate={{ opacity: 1, y: 0 }}
    exit={{ opacity: 0, y: -20 }}
    className="space-y-6"
  >
    {/* Analysis Controls */}
    <div className="flex flex-wrap gap-3">
      {['quality', 'geometry', 'optimization', 'compatibility'].map((type) => (
        <button
          key={type}
          onClick={() => onRunAnalysis(type)}
          disabled={analyzing}
          className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 transition-colors"
        >
          Phân tích {type === 'quality' ? 'Chất lượng' :
                    type === 'geometry' ? 'Hình học' :
                    type === 'optimization' ? 'Tối ưu' : 'Tương thích'}
        </button>
      ))}
    </div>

    {/* Analysis Results */}
    {analysisData && (
      <div className="space-y-4">
        {/* Score */}
        <div className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <h3 className="font-semibold text-gray-900 dark:text-white">
              Điểm số: {analysisData.results?.score || 'N/A'}/100
            </h3>
            <span className="text-sm text-gray-500">
              Loại: {analysisData.analysisType}
            </span>
          </div>
        </div>

        {/* Issues */}
        {analysisData.results?.issues && analysisData.results.issues.length > 0 && (
          <div className="bg-red-50 dark:bg-red-900/20 rounded-lg p-4">
            <h3 className="font-semibold text-gray-900 dark:text-white mb-3">
              Vấn đề phát hiện
            </h3>
            <div className="space-y-2">
              {analysisData.results.issues.map((issue, index) => (
                <div key={index} className="flex items-start space-x-2">
                  {getSeverityIcon(issue.severity)}
                  <div className="flex-1">
                    <div className="font-medium text-gray-900 dark:text-white">
                      {issue.description}
                    </div>
                    {issue.suggestion && (
                      <div className="text-sm text-gray-600 dark:text-gray-400">
                        Gợi ý: {issue.suggestion}
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    )}

    {!analysisData && !analyzing && (
      <div className="text-center py-8">
        <FiCpu className="h-12 w-12 text-gray-400 mx-auto mb-4" />
        <p className="text-gray-500 dark:text-gray-400">
          Chọn loại phân tích để bắt đầu
        </p>
      </div>
    )}
  </motion.div>
);

// Recommendations Tab Component
const RecommendationsTab = ({ recommendations }) => (
  <motion.div
    initial={{ opacity: 0, y: 20 }}
    animate={{ opacity: 1, y: 0 }}
    exit={{ opacity: 0, y: -20 }}
    className="space-y-6"
  >
    {recommendations ? (
      <>
        {/* Recommendations */}
        {recommendations.recommendations && recommendations.recommendations.length > 0 && (
          <div className="space-y-4">
            <h3 className="font-semibold text-gray-900 dark:text-white">
              Gợi ý cải thiện
            </h3>
            {recommendations.recommendations.map((rec, index) => (
              <div key={index} className="bg-green-50 dark:bg-green-900/20 rounded-lg p-4">
                <div className="flex items-start space-x-3">
                  <FiTrendingUp className="h-5 w-5 text-green-600 dark:text-green-400 mt-0.5" />
                  <div className="flex-1">
                    <div className="font-medium text-gray-900 dark:text-white">
                      {rec.suggestion}
                    </div>
                    <div className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                      {rec.reason}
                    </div>
                    <div className="flex items-center space-x-2 mt-2">
                      <span className={`px-2 py-1 rounded text-xs font-medium ${
                        rec.priority === 'high' ? 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400' :
                        rec.priority === 'medium' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400' :
                        'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400'
                      }`}>
                        {rec.priority === 'high' ? 'Cao' : rec.priority === 'medium' ? 'Trung bình' : 'Thấp'}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Critical Issues */}
        {recommendations.criticalIssues && recommendations.criticalIssues.length > 0 && (
          <div className="space-y-4">
            <h3 className="font-semibold text-gray-900 dark:text-white text-red-600 dark:text-red-400">
              Vấn đề nghiêm trọng
            </h3>
            {recommendations.criticalIssues.map((issue, index) => (
              <div key={index} className="bg-red-50 dark:bg-red-900/20 rounded-lg p-4">
                <div className="flex items-start space-x-3">
                  <FiAlertTriangle className="h-5 w-5 text-red-600 dark:text-red-400 mt-0.5" />
                  <div className="flex-1">
                    <div className="font-medium text-gray-900 dark:text-white">
                      {issue.description}
                    </div>
                    {issue.suggestion && (
                      <div className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                        Giải pháp: {issue.suggestion}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </>
    ) : (
      <div className="text-center py-8">
        <FiTrendingUp className="h-12 w-12 text-gray-400 mx-auto mb-4" />
        <p className="text-gray-500 dark:text-gray-400">
          Chưa có gợi ý nào. Hãy chạy phân tích trước.
        </p>
      </div>
    )}
  </motion.div>
);

// Optimization Tab Component
const OptimizationTab = ({ modelData, recommendations }) => (
  <motion.div
    initial={{ opacity: 0, y: 20 }}
    animate={{ opacity: 1, y: 0 }}
    exit={{ opacity: 0, y: -20 }}
    className="space-y-6"
  >
    <div className="text-center py-8">
      <FiZap className="h-12 w-12 text-gray-400 mx-auto mb-4" />
      <p className="text-gray-500 dark:text-gray-400">
        Tính năng tối ưu hóa đang được phát triển
      </p>
    </div>
  </motion.div>
);

export default AIModelAssistant;
