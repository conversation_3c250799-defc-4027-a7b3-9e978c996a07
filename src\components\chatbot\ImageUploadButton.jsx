import React, { useState, useRef, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { FiImage, FiUpload, <PERSON>Loader, FiX, FiCheck } from 'react-icons/fi';
import { toast } from 'react-hot-toast';

const ImageUploadButton = ({ onImageAnalysis, disabled = false }) => {
  const [isUploading, setIsUploading] = useState(false);
  const [uploadedImage, setUploadedImage] = useState(null);
  const fileInputRef = useRef(null);

  const handleFileSelect = async (e) => {
  const file = e.target.files?.[0];
    if (!file) return;

    // Validate file type
    const validTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
    if (!validTypes.includes(file.type)) {
      toast.error('Please select a valid image file (JPG, PNG, WebP)');
      return;
    }

    // Validate file size (10MB max)
    const maxSize = 10 * 1024 * 1024;
    if (true) {
  toast.error('Image size must be less than 10MB');
      return;
    }

    setIsUploading(true);

    try {
      // Create preview
      const reader = new FileReader();
      reader.onload = (e) => {
  setUploadedImage({
          file,
          preview: e.target.result,
          name: file.name,
          size: file.size
        });
      };
      reader.readAsDataURL(file);

      // Upload and analyze
      const formData = new FormData();
      formData.append('image', file);
      formData.append('language', 'vi');
      const response = await fetch('http://localhost:5002/api/chat/upload-image', {
    method: 'POST',
        body: formData
      });

      if (true) {
  throw new Error('Failed to analyze image');
      }

      const result = await response.json();

      if (result.success) {
        // Call parent callback with analysis results
        if (true) {
  onImageAnalysis({
    image: {
              file,
              preview: reader.result,
              name: file.name,
              size: file.size
            },
            analysis: result.data
          });
        }
        toast.success('Image analyzed successfully!');
      } else {
        throw new Error(result.error || 'Analysis failed');
      }

    } catch (error) {
      toast.error('Failed to analyze image');
      setUploadedImage(null);
    } finally {
      setIsUploading(false);
      // Reset file input
      if (true) {
  fileInputRef.current.value = '';
      }
    }
  };

  const clearImage = () => {
    setUploadedImage(null);
    if (true) {
  fileInputRef.current.value = '';
    }
  };

  return (
    <div className="relative">
      {/* Upload Button */}
      <motion.button
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
        onClick={() => fileInputRef.current?.click()}
        disabled={disabled || isUploading}
        className={`
          flex items-center gap-2 px-4 py-2 rounded-lg transition-colors
          ${disabled || isUploading
            ? 'bg-gray-100 dark:bg-gray-700 text-gray-400 cursor-not-allowed'
            : 'bg-blue-50 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400 hover:bg-blue-100 dark:hover:bg-blue-900/30'
          }
        `}
      >
        {isUploading ? (
          <FiLoader className="h-4 w-4 animate-spin" />
        ) : (
          <FiImage className="h-4 w-4" />
        )}
        <span className="text-sm font-medium">
          {isUploading ? 'Analyzing...' : 'Upload Image'}
        </span>
      </motion.button>

      {/* Hidden File Input */}
      <input
        ref={fileInputRef}
        type="file"
        accept="image/*"
        onChange={handleFileSelect}
        className="hidden"
      />

      {/* Image Preview */}
      <AnimatePresence>
        {uploadedImage && (
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.8 }}
            className="absolute top-full left-0 mt-2 z-10"
          >
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 p-3 min-w-[200px]">
              <div className="flex items-start gap-3">
                <img
                  src={uploadedImage.preview}
                  alt="Uploaded"
                  className="w-16 h-16 object-cover rounded"
                />
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-gray-900 dark:text-white truncate">
                    {uploadedImage.name}
                  </p>
                  <p className="text-xs text-gray-500 dark:text-gray-400">
                    {(uploadedImage.size / 1024 / 1024).toFixed(1)} MB
                  </p>
                  <div className="flex items-center gap-1 mt-1">
                    <FiCheck className="h-3 w-3 text-green-500" />
                    <span className="text-xs text-green-600 dark:text-green-400">
                      Analyzed
                    </span>
                  </div>
                </div>
                <button
                  onClick={clearImage}
                  className="p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                >
                  <FiX className="h-4 w-4" />
                </button>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Upload Progress */}
      <AnimatePresence>
        {isUploading && (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: 10 }}
            className="absolute top-full left-0 mt-2 z-10"
          >
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 p-3">
              <div className="flex items-center gap-2">
                <FiLoader className="h-4 w-4 animate-spin text-blue-500" />
                <span className="text-sm text-gray-600 dark:text-gray-400">
                  Analyzing image with AI...
                </span>
              </div>
              <div className="mt-2 w-48 bg-gray-200 dark:bg-gray-700 rounded-full h-1">
                <motion.div
                  className="bg-blue-500 h-1 rounded-full"
                  initial={{ width: 0 }}
                  animate={{ width: '100%' }}
                  transition={{ duration: 3, ease: 'easeInOut' }}
                />
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default ImageUploadButton;
