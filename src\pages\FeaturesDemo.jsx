import React, { useState } from 'react';
import { motion } from 'framer-motion';
import {
  FiCpu, // Use FiCpu instead of FiBrain
  FiGrid,
  FiLayers,
  FiZap,
  FiEye,
  FiSettings,
  FiPlay,
  FiPause,
  FiRotateCw,
  FiMaximize2,
  FiStar,
  FiHeart,
  FiShare2,
  FiDownload
} from 'react-icons/fi';
import AIModelAssistant from '../components/ai/AIModelAssistant';
import VirtualShowroom from '../components/showroom/VirtualShowroom';
import SmartCollection from '../components/collections/SmartCollection';
import SEO from '../components/SEO';
import { useModels } from '../context/ModelContext';

const FeaturesDemo = () => {
  const [activeDemo, setActiveDemo] = useState('ai-assistant'; 
  const [showAIAssistant, setShowAIAssistant] = useState(false);

  // Use real model data from context
  const { models } = useModels();
  const demoModel = models && models.length > 0 ? models[0] : {
    _id: 'demo-model-1',
    title: 'Demo Model',
    description: 'A sample 3D model for demonstration purposes.',
    category: 'Demo',
    format: 'GLTF',
    fileSize: 15728640,
    polygonCount: 45000,
    textured: true,
    rigged: false,
    animated: false,
    rating: 4.8,
    downloads: 1250,
    views: 5680,
    imageUrl: 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=800&h=600&fit=crop',
    modelUrl: '/models/demo/living-room.gltf'
  };

  const mockShowroom = {
    _id: 'demo-showroom-1',
    name: 'Modern Interior Showcase',
    description: 'A curated collection of modern interior designs',
    type: 'exhibition',
    theme: 'modern',
    owner: {
    _id: 'demo-user-1',
      name: 'Design Studio',
      profileImage: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face'
    },
    models: [
      {
    model: mockModel,
        position: { x: 0, y: 0, z: 0 },
        rotation: { x: 0, y: 0, z: 0 },
        scale: { x: 1, y: 1, z: 1 },
        label: { text: 'Modern Living Room', visible: true }
      },
      {
    model: { ...mockModel, _id: 'demo-model-2', title: 'Kitchen Design' },
        position: { x: 5, y: 0, z: 0 },
        rotation: { x: 0, y: Math.PI / 4, z: 0 },
        scale: { x: 1, y: 1, z: 1 },
        label: { text: 'Kitchen Design', visible: true }
      }
    ],
    environment: {
    lighting: 'natural',
      background: 'skybox',
      floor: { enabled: true, material: '#f0f0f0', height: 0 }
    },
    settings: {
    allowVR: true,
      allowAR: true,
      enableShadows: true,
      enableReflections: true
    },
    analytics: {
    totalVisits: 342,
      uniqueVisitors: 256
    },
    modelCount: 2,
    updatedAt: new Date().toISOString()
  };

  const mockCollection = {
    _id: 'demo-collection-1',
    name: 'AI-Curated Modern Interiors',
    description: 'A smart collection of modern interior designs automatically curated by AI',
    type: 'smart',
    owner: {
    _id: 'demo-user-1',
      name: 'Design Studio',
      profileImage: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face'
    },
    smartCriteria: {
    enabled: true,
      rules: [
        { field: 'category', operator: 'equals', value: 'Interior', weight: 1 },
        { field: 'rating', operator: 'greaterThan', value: 4.0, weight: 0.8 }
      ],
      autoUpdate: true,
      maxModels: 50,
      sortBy: 'relevance'
    },
    aiRecommendations: {
    enabled: true,
      suggestions: [
        {
    _id: 'suggestion-1',
          model: { ...mockModel, title: 'Minimalist Bedroom' },
          reason: 'Matches your modern interior style preferences',
          confidence: 0.92
        },
        {
    _id: 'suggestion-2',
          model: { ...mockModel, title: 'Contemporary Office' },
          reason: 'High rating and similar design aesthetic',
          confidence: 0.87
        }
      ]
    },
    models: [
      { model: mockModel, addedAt: new Date(), order: 0 },
      { model: { ...mockModel, _id: 'demo-model-3', title: 'Dining Room' }, addedAt: new Date(), order: 1 }
    ],
    analytics: {
    totalViews: 1580,
      uniqueViewers: 892,
      totalDownloads: 234
    },
    totalModels: 2,
    featured: true,
    updatedAt: new Date().toISOString()
  };

  const features = [
    {
    id: 'ai-assistant',
      title: 'Trợ Lý AI Mô Hình',
      description: 'Phân tích thông minh và gợi ý cải thiện mô hình 3D',
      icon: <FiCpu className="h-8 w-8" />,
      color: 'purple',
      demo: () => setShowAIAssistant(true)
    },
    {
    id: 'virtual-showroom',
      title: 'Phòng Trưng Bày Ảo',
      description: 'Trải nghiệm 3D nhập vai với hỗ trợ VR/AR',
      icon: <FiGrid className="h-8 w-8" />,
      color: 'blue',
      demo: () => setActiveDemo('virtual-showroom')
    },
    {
    id: 'smart-collection',
      title: 'Bộ Sưu Tập Thông Minh',
      description: 'AI tự động tuyển chọn và gợi ý mô hình phù hợp',
      icon: <FiLayers className="h-8 w-8" />,
      color: 'green',
      demo: () => setActiveDemo('smart-collection')
    },
    {
    id: 'design-studio',
      title: 'Studio Thiết Kế Tương Tác',
      description: 'Không gian làm việc 3D collaborative',
      icon: <FiZap className="h-8 w-8" />,
      color: 'orange',
      demo: () => setActiveDemo('design-studio')
    }
  ];

  const getColorClasses = (color) => {
  const colors = {
    purple: 'bg-purple-500 hover:bg-purple-600 text-white',
      blue: 'bg-blue-500 hover:bg-blue-600 text-white',
      green: 'bg-green-500 hover:bg-green-600 text-white',
      orange: 'bg-orange-500 hover:bg-orange-600 text-white'
    };
    return colors[color] || colors.blue;
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <SEO
        title="Demo Tính Năng Mới - 3DSKETCHUP.NET"
        description="Trải nghiệm các tính năng AI tiên tiến: Trợ lý AI, Phòng trưng bày ảo, Bộ sưu tập thông minh"
        keywords="AI assistant, virtual showroom, smart collection, 3D design studio"
      />

      {/* Header */}
      <div className="bg-white dark:bg-gray-800 shadow-sm">
        <div className="container mx-auto px-4 py-12">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="text-center"
          >
            <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">
              🚀 Tính Năng Mới Đột Phá
            </h1>
            <p className="text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">
              Khám phá những công nghệ AI tiên tiến và trải nghiệm 3D nhập vai
              được tích hợp vào nền tảng 3DSKETCHUP.NET
            </p>
          </motion.div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-12">
        {/* Feature Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
          {features.map((feature, index) => (
            <motion.div
              key={feature.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
              className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 hover:shadow-xl transition-all duration-300"
            >
              <div className={`w-16 h-16 rounded-lg ${getColorClasses(feature.color)} flex items-center justify-center mb-4`}>
                {feature.icon}
              </div>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                {feature.title}
              </h3>
              <p className="text-gray-600 dark:text-gray-400 text-sm mb-4">
                {feature.description}
              </p>
              <button
                onClick={feature.demo}
                className={`w-full py-2 px-4 rounded-lg ${getColorClasses(feature.color)} transition-colors text-sm font-medium`}
              >
                Xem Demo
              </button>
            </motion.div>
          ))}
        </div>

        {/* Demo Area */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden"
        >
          {/* Demo Header */}
          <div className="bg-gray-50 dark:bg-gray-700 px-6 py-4 border-b border-gray-200 dark:border-gray-600">
            <div className="flex items-center justify-between">
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
                {features.find(f => f.id === activeDemo)?.title || 'Demo Tính Năng'}
              </h2>
              <div className="flex items-center space-x-2">
                <span className="px-3 py-1 bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300 text-sm rounded-full">
                  ● Live Demo
                </span>
              </div>
            </div>
          </div>

          {/* Demo Content */}
          <div className="p-6">
            {activeDemo === 'virtual-showroom' && (
              <div className="h-96 bg-gray-100 dark:bg-gray-700 rounded-lg overflow-hidden">
                <VirtualShowroom
                  showroomData={mockShowroom}
                  isOwner={false}
                  enableVR={true}
                  enableAR={true}
                />
              </div>
            )}

            {activeDemo === 'smart-collection' && (
              <SmartCollection
                collectionData={mockCollection}
                isOwner={false}
              />
            )}

            {activeDemo === 'ai-assistant' && (
              <div className="text-center py-12">
                <FiCpu className="h-16 w-16 text-purple-500 mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                  Trợ Lý AI Mô Hình
                </h3>
                <p className="text-gray-600 dark:text-gray-400 mb-6">
                  Nhấn nút bên dưới để mở trợ lý AI và trải nghiệm phân tích mô hình thông minh
                </p>
                <button
                  onClick={() => setShowAIAssistant(true)}
                  className="px-6 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
                >
                  Mở Trợ Lý AI
                </button>
              </div>
            )}

            {activeDemo === 'design-studio' && (
              <div className="text-center py-12">
                <FiZap className="h-16 w-16 text-orange-500 mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                  Studio Thiết Kế Tương Tác
                </h3>
                <p className="text-gray-600 dark:text-gray-400 mb-6">
                  Tính năng đang được phát triển. Sẽ có không gian làm việc 3D collaborative
                  với khả năng chỉnh sửa real-time và xuất file đa định dạng.
                </p>
                <div className="inline-flex items-center px-4 py-2 bg-orange-100 dark:bg-orange-900/30 text-orange-800 dark:text-orange-300 rounded-lg">
                  <FiSettings className="h-4 w-4 mr-2" />
                  Đang phát triển
                </div>
              </div>
            )}
          </div>
        </motion.div>

        {/* Features List */}
        <div className="mt-12 grid grid-cols-1 lg:grid-cols-2 gap-8">
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6">
            <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
              🧠 Tính Năng AI Tiên Tiến
            </h3>
            <ul className="space-y-3">
              <li className="flex items-start">
                <FiCpu className="h-5 w-5 text-purple-500 mt-0.5 mr-3 flex-shrink-0" />
                <span className="text-gray-700 dark:text-gray-300">
                  Phân tích chất lượng mô hình tự động với AI
                </span>
              </li>
              <li className="flex items-start">
                <FiZap className="h-5 w-5 text-purple-500 mt-0.5 mr-3 flex-shrink-0" />
                <span className="text-gray-700 dark:text-gray-300">
                  Gợi ý tối ưu hóa và cải thiện hiệu suất
                </span>
              </li>
              <li className="flex items-start">
                <FiLayers className="h-5 w-5 text-purple-500 mt-0.5 mr-3 flex-shrink-0" />
                <span className="text-gray-700 dark:text-gray-300">
                  Bộ sưu tập thông minh tự động cập nhật
                </span>
              </li>
              <li className="flex items-start">
                <FiStar className="h-5 w-5 text-purple-500 mt-0.5 mr-3 flex-shrink-0" />
                <span className="text-gray-700 dark:text-gray-300">
                  Đánh giá và phân loại nội dung thông minh
                </span>
              </li>
            </ul>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6">
            <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
              🌍 Trải Nghiệm Nhập Vai
            </h3>
            <ul className="space-y-3">
              <li className="flex items-start">
                <FiGrid className="h-5 w-5 text-blue-500 mt-0.5 mr-3 flex-shrink-0" />
                <span className="text-gray-700 dark:text-gray-300">
                  Phòng trưng bày 3D với hỗ trợ VR/AR
                </span>
              </li>
              <li className="flex items-start">
                <FiEye className="h-5 w-5 text-blue-500 mt-0.5 mr-3 flex-shrink-0" />
                <span className="text-gray-700 dark:text-gray-300">
                  Xem mô hình trong môi trường thực tế
                </span>
              </li>
              <li className="flex items-start">
                <FiShare2 className="h-5 w-5 text-blue-500 mt-0.5 mr-3 flex-shrink-0" />
                <span className="text-gray-700 dark:text-gray-300">
                  Chia sẻ và cộng tác real-time
                </span>
              </li>
              <li className="flex items-start">
                <FiDownload className="h-5 w-5 text-blue-500 mt-0.5 mr-3 flex-shrink-0" />
                <span className="text-gray-700 dark:text-gray-300">
                  Xuất file đa định dạng chuyên nghiệp
                </span>
              </li>
            </ul>
          </div>
        </div>
      </div>

      {/* AI Assistant Modal */}
      {showAIAssistant && (
        <AIModelAssistant
          modelId={demoModel._id}
          modelData={demoModel}
          onClose={() => setShowAIAssistant(false)}
        />
      )}
    </div>
  );
};

export default FeaturesDemo;
