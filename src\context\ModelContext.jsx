// Import React directly for ModelContext to avoid circular dependency issues
import React, { useState, useContext, useEffect } from 'react';

import { useAuth } from './AuthContext';
import mongoService from '../services/mongoService';
import apiService from '../services/api';
import realDataService from '../services/realDataService';
import useOptimizedData from '../hooks/useOptimizedData';

// Create the model context
const ModelContext = React.createContext();

// Custom hooks to use the model context
export const useModels = () => useContext(ModelContext);
export const useModel = () => useContext(ModelContext); // Added for compatibility with existing code

// Backend API URL
const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:5002/api';

export const ModelProvider = ({ children }) => {
  const { currentUser } = useAuth();
  const [models, setModels] = useState([]);
  const [featuredModels, setFeaturedModels] = useState([]);
  const [popularModels, setPopularModels] = useState([]);
  const [newestModels, setNewestModels] = useState([]);
  const [categories, setCategories] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Use optimized data hook for caching and performance
  const {
    fetchData,
    clearCache,
    cache
  } = useOptimizedData({
    enableCaching: true,
    cacheDuration: 5 * 60 * 1000, // 5 minutes
    enablePrefetching: true
  });

  // Fetch models on component mount
  useEffect(() => {
    fetchModels();
    fetchCategories();
  }, []);

  // Fetch all models using realDataService
  const fetchModels = async () => {
    try {
      setLoading(true);
      setError(null);

      console.log('🔄 Fetching models using realDataService...');

      // Use realDataService to get all data
      const [allModels, featuredModels, popularModels, newestModels] = await Promise.all([
        realDataService.getAllModels(),
        realDataService.getFeaturedModels(),
        realDataService.getPopularModels(),
        realDataService.getRecentModels()
      ]);

      console.log('✅ Data fetched successfully:', {
        allModels: allModels.length,
        featuredModels: featuredModels.length,
        popularModels: popularModels.length,
        newestModels: newestModels.length
      });

      // Set the data
      setModels(allModels);
      setFeaturedModels(featuredModels);
      setPopularModels(popularModels);
      setNewestModels(newestModels);

      setLoading(false);
    } catch (err) {
      console.error('❌ Error fetching models with realDataService:', err);
      setError(err.message || 'Failed to fetch models');
      setLoading(false);
    }
  };

  // Fetch categories using realDataService
  const fetchCategories = async () => {
    try {
      console.log('🔄 Fetching categories using realDataService...');

      const categoriesData = await realDataService.getCategories();

      console.log('✅ Categories fetched successfully:', categoriesData.length);
      setCategories(categoriesData);
    } catch (err) {
      console.error('❌ Error fetching categories with realDataService:', err);
    }
  };

  // Get model by ID
  const getModelById = async (id) => {
    // Validate ID first
    if (!id || id === 'undefined' || id === 'null') {
      console.warn('getModelById: Invalid ID provided:', id);
      return null;
    }

    try {
      setLoading(true);
      setError(null);

      // Check if data is in cache
      const cacheKey = `model-${id}`;
      const cachedData = cache[cacheKey];

      if (cachedData) {
        setLoading(false);
        return cachedData.data;
      }

      // Try to fetch from MongoDB first
      try {
        const model = await mongoService.getModelById(id);

        if (model) {
          // Cache the data
          fetchData(cacheKey, {
            method: 'GET',
            useCache: true,
            data: model
          });

          setLoading(false);
          return model;
        }
      } catch (mongoErr) {
        console.warn(`Failed to fetch model ${id} from MongoDB, falling back to API:`, mongoErr);
      }

      // Fall back to API if MongoDB fails
      try {
        const response = await apiService.models.getById(id);
        const model = response.data;

        if (model) {
          // Cache the data
          fetchData(cacheKey, {
            method: 'GET',
            useCache: true,
            data: model
          });

          setLoading(false);
          return model;
        }
      } catch (apiErr) {
        console.error(`API fallback for model ${id} also failed:`, apiErr);

        // If all else fails, try to find in local models array
        const localModel = models.find(model =>
          (model._id && model._id.toString() === id.toString()) ||
          (model.id && model.id.toString() === id.toString())
        );

        if (localModel) {
          setLoading(false);
          return localModel;
        }

        throw apiErr;
      }

      return null;
    } catch (err) {
      setError(err.message || 'Failed to fetch model');
      console.error('Error fetching model:', err);
      return null;
    } finally {
      setLoading(false);
    }
  };

  // Search models
  const searchModels = async (query, filters = {}) => {
    try {
      setLoading(true);
      setError(null);

      // Generate a cache key based on search parameters
      const filterString = Object.entries(filters)
        .filter(([_, value]) => value)
        .map(([key, value]) => `${key}=${value}`)
        .join('&');

      const cacheKey = `search-${query}-${filterString}`;
      const cachedData = cache[cacheKey];

      // Return cached results if available
      if (cachedData) {
        setLoading(false);
        return cachedData.data;
      }

      // Try to search in MongoDB first
      try {
        const searchParams = { query, ...filters };
        const searchResults = await mongoService.searchModels(searchParams);

        if (searchResults && searchResults.length > 0) {
          // Cache the results
          fetchData(cacheKey, {
            method: 'GET',
            useCache: true,
            data: searchResults
          });

          setLoading(false);
          return searchResults;
        }
      } catch (mongoErr) {
        console.warn('Failed to search models in MongoDB, falling back to API:', mongoErr);
      }

      // Fall back to API if MongoDB fails
      try {
        const response = await apiService.search.advanced({ query, ...filters });
        const apiResults = response.data || [];

        // Cache the results
        fetchData(cacheKey, {
          method: 'GET',
          useCache: true,
          data: apiResults
        });

        setLoading(false);
        return apiResults;
      } catch (apiErr) {
        console.error('API search fallback also failed:', apiErr);

        // If all else fails, search in local models array
        let filteredModels = [...models];

        // Search by query
        if (query) {
          const searchTerms = query.toLowerCase().split(' ');
          filteredModels = filteredModels.filter(model => {
            const searchableText = `${model.title || ''} ${model.description || ''} ${model.category || ''} ${model.subcategory || ''} ${(model.tags || []).join(' ')}`.toLowerCase();
            return searchTerms.every(term => searchableText.includes(term));
          });
        }

        // Apply category filter
        if (filters.category) {
          filteredModels = filteredModels.filter(model => model.category === filters.category);
        }

        // Apply subcategory filter
        if (filters.subcategory) {
          filteredModels = filteredModels.filter(model => model.subcategory === filters.subcategory);
        }

        // Apply format filter
        if (filters.format) {
          filteredModels = filteredModels.filter(model =>
            model.format && model.format.toLowerCase().includes(filters.format.toLowerCase())
          );
        }

        // Apply year filter
        if (filters.year) {
          filteredModels = filteredModels.filter(model =>
            model.year === filters.year ||
            (model.dateAdded && new Date(model.dateAdded).getFullYear() === parseInt(filters.year)) ||
            (model.createdAt && new Date(model.createdAt).getFullYear() === parseInt(filters.year))
          );
        }

        // Apply price filter
        if (filters.isPremium !== undefined) {
          filteredModels = filteredModels.filter(model =>
            model.isPremium === (filters.isPremium === 'true' || filters.isPremium === true)
          );
        }

        return filteredModels;
      }
    } catch (err) {
      setError(err.message || 'Search failed');
      console.error('Error searching models:', err);
      return [];
    } finally {
      setLoading(false);
    }
  };

  // Upload model
  const uploadModel = async (modelData, files) => {
    try {
      setLoading(true);
      setError(null);

      // Create form data
      const formData = new FormData();

      // Add model data
      Object.entries(modelData).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          formData.append(key, value);
        }
      });

      // Add files
      Object.entries(files).forEach(([key, file]) => {
        if (file) {
          formData.append(key, file);
        }
      });

      // Add user ID if available
      if (currentUser?.id) {
        formData.append('userId', currentUser.id);
      }

      // Add timestamp
      formData.append('createdAt', new Date().toISOString());

      // Try to upload to MongoDB first
      try {
        const uploadedModel = await mongoService.createModel(formData);

        if (uploadedModel) {
          // Add to models list
          setModels([uploadedModel, ...models]);

          // Clear cache for models list
          clearCache('all-models');

          setLoading(false);
          return uploadedModel;
        }
      } catch (mongoErr) {
        console.warn('Failed to upload model to MongoDB, falling back to API:', mongoErr);
      }

      // Fall back to API if MongoDB fails
      try {
        // Get signed URL for file upload if needed
        if (files.modelFile) {
          const signedUrlResponse = await apiService.upload.getSignedUrl(
            files.modelFile.name,
            files.modelFile.type
          );

          if (signedUrlResponse.data?.signedUrl) {
            // Upload file to signed URL
            await fetch(signedUrlResponse.data.signedUrl, {
              method: 'PUT',
              body: files.modelFile,
              headers: {
                'Content-Type': files.modelFile.type
              }
            });

            // Add file URL to form data
            formData.append('modelUrl', signedUrlResponse.data.fileUrl);
          }
        }

        // Create model via API
        const response = await apiService.models.create(formData);
        const newModel = response.data;

        if (newModel) {
          // Add to models list
          setModels([newModel, ...models]);

          // Clear cache for models list
          clearCache('all-models');

          setLoading(false);
          return newModel;
        }
      } catch (apiErr) {
        console.error('API upload fallback also failed:', apiErr);
        throw apiErr;
      }

      throw new Error('Failed to upload model');
    } catch (err) {
      setError(err.message || 'Upload failed');
      console.error('Error uploading model:', err);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Download model
  const downloadModel = async (id) => {
    try {
      setLoading(true);
      setError(null);

      // Get model first to check if it exists
      const model = await getModelById(id);

      if (!model) {
        throw new Error('Model not found');
      }

      // Try to get download URL from MongoDB first
      try {
        // Record download in MongoDB
        await mongoService.updateModel(id, {
          downloads: (model.downloads || 0) + 1
        });

        // If model has direct download URL, use it
        if (model.downloadUrl) {
          window.open(model.downloadUrl, '_blank');
          setLoading(false);
          return { success: true, downloadUrl: model.downloadUrl };
        }

        // If model has modelUrl but no downloadUrl, create a download link
        if (model.modelUrl) {
          // Create a temporary anchor element
          const link = document.createElement('a');
          link.href = model.modelUrl;
          link.download = model.title ? `${model.title.replace(/\s+/g, '_')}.${model.format?.toLowerCase() || 'model'}` : `model_${id}.${model.format?.toLowerCase() || 'model'}`;
          link.target = '_blank';
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);

          setLoading(false);
          return { success: true, downloadUrl: model.modelUrl };
        }
      } catch (mongoErr) {
        console.warn('Failed to process download with MongoDB, falling back to API:', mongoErr);
      }

      // Fall back to API if MongoDB fails
      try {
        // Get download URL from API
        const response = await apiService.models.getDownloadUrl(id);

        if (response.data?.downloadUrl) {
          // Open download URL in new tab
          window.open(response.data.downloadUrl, '_blank');

          // Record download
          await apiService.models.download(id);

          setLoading(false);
          return { success: true, downloadUrl: response.data.downloadUrl };
        }
      } catch (apiErr) {
        console.error('API download fallback also failed:', apiErr);
        throw apiErr;
      }

      throw new Error('Failed to download model');
    } catch (err) {
      setError(err.message || 'Download failed');
      console.error('Error downloading model:', err);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Get statistics
  const getStatistics = async () => {
    try {
      // Check if data is in cache
      const cacheKey = 'statistics';
      const cachedData = cache[cacheKey];

      if (cachedData) {
        return cachedData.data;
      }

      // Try to fetch from MongoDB first
      try {
        const stats = await mongoService.getStatistics();

        if (stats) {
          // Cache the data
          fetchData(cacheKey, {
            method: 'GET',
            useCache: true,
            data: stats
          });

          return stats;
        }
      } catch (mongoErr) {
        console.warn('Failed to fetch statistics from MongoDB, falling back to API:', mongoErr);
      }

      // Fall back to API if MongoDB fails
      try {
        const response = await apiService.statistics.getSiteStats();
        const apiStats = response.data || {
          modelsCount: models.length,
          usersCount: 0,
          downloadsCount: models.reduce((sum, model) => sum + (model.downloads || 0), 0),
          categoriesCount: categories.length
        };

        // Cache the data
        fetchData(cacheKey, {
          method: 'GET',
          useCache: true,
          data: apiStats
        });

        return apiStats;
      } catch (apiErr) {
        console.error('API statistics fallback also failed:', apiErr);

        // If all else fails, generate statistics from local data
        return {
          modelsCount: models.length,
          usersCount: 0,
          downloadsCount: models.reduce((sum, model) => sum + (model.downloads || 0), 0),
          categoriesCount: categories.length
        };
      }
    } catch (err) {
      console.error('Error fetching statistics:', err);
      return {
        modelsCount: models.length,
        usersCount: 0,
        downloadsCount: models.reduce((sum, model) => sum + (model.downloads || 0), 0),
        categoriesCount: categories.length
      };
    }
  };

  // Get related models
  const getRelatedModels = async (modelId, limit = 4) => {
    try {
      // Check if data is in cache
      const cacheKey = `related-${modelId}-${limit}`;
      const cachedData = cache[cacheKey];

      if (cachedData) {
        return cachedData.data;
      }

      // Get the model first
      const model = await getModelById(modelId);

      if (!model) {
        throw new Error('Model not found');
      }

      // Try to fetch from API first
      try {
        const response = await apiService.models.getRelated(modelId);
        const relatedModels = response.data || [];

        if (relatedModels.length > 0) {
          // Cache the data
          fetchData(cacheKey, {
            method: 'GET',
            useCache: true,
            data: relatedModels.slice(0, limit)
          });

          return relatedModels.slice(0, limit);
        }
      } catch (apiErr) {
        console.warn('Failed to fetch related models from API, using local data:', apiErr);
      }

      // If API fails, find related models locally
      const category = model.category;
      const tags = model.tags || [];

      // Find models with same category or tags
      let relatedModels = models.filter(m => {
        const mId = m._id || m.id;
        return mId && mId.toString() !== modelId.toString() && (
          m.category === category ||
          (m.tags && m.tags.some(tag => tags.includes(tag)))
        );
      });

      // If not enough related models, add some random ones
      if (relatedModels.length < limit) {
        const randomModels = models
          .filter(m => {
            const mId = m._id || m.id;
            return mId && mId.toString() !== modelId.toString() &&
            !relatedModels.some(rm => {
              const rmId = rm._id || rm.id;
              return rmId && rmId.toString() === mId.toString();
            });
          })
          .sort(() => 0.5 - Math.random())
          .slice(0, limit - relatedModels.length);

        relatedModels = [...relatedModels, ...randomModels];
      }

      // Cache the data
      fetchData(cacheKey, {
        method: 'GET',
        useCache: true,
        data: relatedModels.slice(0, limit)
      });

      return relatedModels.slice(0, limit);
    } catch (err) {
      console.error('Error fetching related models:', err);
      return [];
    }
  };

  const value = {
    models,
    featuredModels,
    popularModels,
    newestModels,
    categories,
    loading,
    error,
    fetchModels,
    getModelById,
    searchModels,
    uploadModel,
    downloadModel,
    getStatistics,
    getRelatedModels,
    clearCache
  };

  return <ModelContext.Provider value={value}>{children}</ModelContext.Provider>;
};

export default ModelContext;
