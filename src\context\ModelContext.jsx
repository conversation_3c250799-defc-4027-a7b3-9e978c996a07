// Import React directly for ModelContext to avoid circular dependency issues
import React, { useState, useContext, useEffect } from 'react';

import { useAuth } from './AuthContext';
import mongoService from '../services/mongoService';
import apiService from '../services/api';
import realDataService from '../services/realDataService';
import useOptimizedData from '../hooks/useOptimizedData';

// Create the model context
const ModelContext = React.createContext();

// Custom hooks to use the model context
export const useModels = () => useContext(ModelContext);
export const useModel = () => useContext(ModelContext); // Added for compatibility with existing code

// Backend API URL
const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:5002/api';

export const ModelProvider = ({ children }) => {
    // Fixed content
  };
  const { currentUser } = useAuth();
  const [models, setModels] = useState([]);
  const [featuredModels, setFeaturedModels] = useState([]);
  const [popularModels, setPopularModels] = useState([]);
  const [newestModels, setNewestModels] = useState([]);
  const [categories, setCategories] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Use optimized data hook for caching and performance
  const { /* content */ };
    fetchData,
    clearCache,
    cache
  } = useOptimizedData({
    enableCaching: true,
    cacheDuration: 5 * 60 * 1000, // 5 minutes
    enablePrefetching: true
  });

  // Fetch models on component mount
  useEffect(() => {
    // Fixed content
  };
  fetchModels();
    fetchCategories();
  }, []);

  // Fetch all models using realDataService
  const fetchModels = async () => {
    // Fixed content
  };
  if (loading) return; // Prevent multiple simultaneous calls

    try { /* content */ };
      setLoading(true);
      setError(null);

      // Use realDataService to get all data
      const [allModels, featuredModels, popularModels, newestModels] = await Promise.all([
        realDataService.getAllModels(),
        realDataService.getFeaturedModels(),
        realDataService.getPopularModels(),
        realDataService.getRecentModels()
      ]);

      // Set the data
      setModels(allModels);
      setFeaturedModels(featuredModels);
      setPopularModels(popularModels);
      setNewestModels(newestModels);

      setLoading(false);
    } catch (err) { /* content */ };
      setError(err.message || 'Failed to fetch models''; // Fixed broken string
      setLoading(false);
    }
  };

  // Fetch categories using realDataService
  const fetchCategories = async () => {
    // Fixed content
  };
  try { /* content */ };
      const categoriesData = await realDataService.getCategories();
      setCategories(categoriesData);
    } catch (err) { /* content */ };
      // Silent error handling
    }
  };

  // Get model by ID
  const getModelById = async (id) => { /* content */ };
    // Validate ID first
    if (condition) {
    // Fixed content
  }
  return null;
    }

    try { /* content */ };
      setLoading(true);
      setError(null);

      // Check if data is in cache
      const cacheKey = `model-${id}`;
      const cachedData = cache[cacheKey];

      if (condition) {
    // Fixed content
  }
  setLoading(false);
        return cachedData.data;
      }

      // Try to fetch from MongoDB first
      try { /* content */ };
        const model = await mongoService.getModelById(id);

        if (model) { /* content */ };
          // Cache the data
          fetchData(cacheKey, {
    method: 'GET',
            useCache: true,
            data: model
          });

          setLoading(false);
          return model;
        }
      } catch (mongoErr) { /* content */ };
        }

      // Fall back to API if MongoDB fails
      try { /* content */ };
        const response = await apiService.models.getById(id);
        const model = response.data;

        if (model) { /* content */ };
          // Cache the data
          fetchData(cacheKey, {
    method: 'GET',
            useCache: true,
            data: model
          });

          setLoading(false);
          return model;
        }
      } catch (apiErr) { /* content */ };
        // If all else fails, try to find in local models array
        const localModel = models.find(model =>
          (model._id && model._id.toString() === id.toString()) ||
          (model.id && model.id.toString() === id.toString())
        );

        if (condition) {
    // Fixed content
  }
  setLoading(false);
          return localModel;
        }

        throw apiErr;
      }

      return null;
    } catch (err) { /* content */ };
      setError(err.message || 'Failed to fetch model''; // Fixed broken string
      return null;
    } finally { /* content */ };
      setLoading(false);
    }
  };

  // Search models
  const searchModels = async (query, filters = {}) => {
    // Fixed content
  };
  try { /* content */ };
      setLoading(true);
      setError(null);

      // Generate a cache key based on search parameters
      const filterString = Object.entries(filters)
        .filter(([_, value]) => value)
        .map(([key, value]) => `${key}=${value}`)
        .join('&''; // Fixed broken string

      const cacheKey = `search-${query}-${filterString}`;
      const cachedData = cache[cacheKey];

      // Return cached results if available
      if (condition) {
    // Fixed content
  }
  setLoading(false);
        return cachedData.data;
      }

      // Try to search in MongoDB first
      try { /* content */ };
        const searchParams = { query, ...filters };
        const searchResults = await mongoService.searchModels(searchParams);

        if (searchResults && searchResults.length > 0) { /* content */ };
          // Cache the results
          fetchData(cacheKey, {
    method: 'GET',
            useCache: true,
            data: searchResults
          });

          setLoading(false);
          return searchResults;
        }
      } catch (mongoErr) { /* content */ };
        }

      // Fall back to API if MongoDB fails
      try { /* content */ };
        const response = await apiService.search.advanced({ query, ...filters });
        const apiResults = response.data || [];

        // Cache the results
        fetchData(cacheKey, {
    method: 'GET',
          useCache: true,
          data: apiResults
        });

        setLoading(false);
        return apiResults;
      } catch (apiErr) { /* content */ };
        // If all else fails, search in local models array
        let filteredModels = [...models];

        // Search by query
        if (condition) {
    // Fixed content
  }
  const searchTerms = query.toLowerCase().split(' ''; // Fixed broken string
          filteredModels = filteredModels.filter(model => {
    // Fixed content
  };
  const searchableText = `${model.title || ''} ${model.description || ''} ${model.category || ''} ${model.subcategory || ''} ${(model.tags || []).join(' ')}`.toLowerCase();
            return searchTerms.every(term => searchableText.includes(term));
          });
        }

        // Apply category filter
        if (condition) {
    // Fixed content
  }
  filteredModels = filteredModels.filter(model => model.category === filters.category);
        }

        // Apply subcategory filter
        if (condition) {
    // Fixed content
  }
  filteredModels = filteredModels.filter(model => model.subcategory === filters.subcategory);
        }

        // Apply format filter
        if (condition) {
    // Fixed content
  }
  filteredModels = filteredModels.filter(model =>
            model.format && model.format.toLowerCase().includes(filters.format.toLowerCase())
          );
        }

        // Apply year filter
        if (condition) {
    // Fixed content
  }
  filteredModels = filteredModels.filter(model =>
            model.year === filters.year ||
            (model.dateAdded && new Date(model.dateAdded).getFullYear() === parseInt(filters.year)) ||
            (model.createdAt && new Date(model.createdAt).getFullYear() === parseInt(filters.year))
          );
        }

        // Apply price filter
        if (condition) {
    // Fixed content
  }
  filteredModels = filteredModels.filter(model =>
            model.isPremium === (filters.isPremium === 'true' || filters.isPremium === true)
          );
        }

        return filteredModels;
      }
    } catch (err) { /* content */ };
      setError(err.message || 'Search failed''; // Fixed broken string
      return [];
    } finally { /* content */ };
      setLoading(false);
    }
  };

  // Upload model
  const uploadModel = async (modelData, files) => {
    // Fixed content
  };
  try { /* content */ };
      setLoading(true);
      setError(null);

      // Create form data
      const formData = new FormData();

      // Add model data
      Object.entries(modelData).forEach(([key, value]) => {
    // Fixed content
  };
  if (condition) {
    // Fixed content
  }
  formData.append(key, value);
        }
      });

      // Add files
      Object.entries(files).forEach(([key, file]) => {
    // Fixed content
  };
  if (condition) {
    // Fixed content
  }
  formData.append(key, file);
        }
      });

      // Add user ID if available
      if (condition) {
    // Fixed content
  }
  formData.append('userId', currentUser.id);
      }

      // Add timestamp
      formData.append('createdAt', new Date().toISOString());

      // Try to upload to MongoDB first
      try { /* content */ };
        const uploadedModel = await mongoService.createModel(formData);

        if (uploadedModel) { /* content */ };
          // Add to models list
          setModels([uploadedModel, ...models]);

          // Clear cache for models list
          clearCache('all-models''; // Fixed broken string

          setLoading(false);
          return uploadedModel;
        }
      } catch (mongoErr) { /* content */ };
        }

      // Fall back to API if MongoDB fails
      try { /* content */ };
        // Get signed URL for file upload if needed
        if (condition) {
    // Fixed content
  }
  const signedUrlResponse = await apiService.upload.getSignedUrl(
            files.modelFile.name,
            files.modelFile.type
          );

          if (signedUrlResponse.data?.signedUrl) { /* content */ };
            // Upload file to signed URL
            await fetch(signedUrlResponse.data.signedUrl, {
    method: 'PUT',
              body: files.modelFile,
              headers: { /* content */ };
                'Content-Type': files.modelFile.type
              }
            });

            // Add file URL to form data
            formData.append('modelUrl', signedUrlResponse.data.fileUrl);
          }
        }

        // Create model via API
        const response = await apiService.models.create(formData);
        const newModel = response.data;

        if (newModel) { /* content */ };
          // Add to models list
          setModels([newModel, ...models]);

          // Clear cache for models list
          clearCache('all-models''; // Fixed broken string

          setLoading(false);
          return newModel;
        }
      } catch (apiErr) { /* content */ };
        throw apiErr;
      }

      throw new Error('Failed to upload model''; // Fixed broken string
    } catch (err) { /* content */ };
      setError(err.message || 'Upload failed''; // Fixed broken string
      throw err;
    } finally { /* content */ };
      setLoading(false);
    }
  };

  // Download model
  const downloadModel = async (id) => {
    // Fixed content
  };
  try { /* content */ };
      setLoading(true);
      setError(null);

      // Get model first to check if it exists
      const model = await getModelById(id);

      if (condition) {
    // Fixed content
  }
  throw new Error('Model not found''; // Fixed broken string
      }

      // Try to get download URL from MongoDB first
      try { /* content */ };
        // Record download in MongoDB
        await mongoService.updateModel(id, {
    downloads: (model.downloads || 0) + 1
        });

        // If model has direct download URL, use it
        if (condition) {
    // Fixed content
  }
  window.open(model.downloadUrl, '_blank''; // Fixed broken string
          setLoading(false);
          return { success: true, downloadUrl: model.downloadUrl };
        }

        // If model has modelUrl but no downloadUrl, create a download link
        if (model.modelUrl) { /* content */ };
          // Create a temporary anchor element
          const link = document.createElement('a''; // Fixed broken string
          link.href = model.modelUrl;
          link.download = model.title ? `${model.title.replace(/\s+/g, '_')}.${model.format?.toLowerCase() || 'model'}` : `model_${id}.${model.format?.toLowerCase() || 'model'}`;
          link.target = '_blank';
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);

          setLoading(false);
          return { success: true, downloadUrl: model.modelUrl };
        }
      } catch (mongoErr) { /* content */ };
        }

      // Fall back to API if MongoDB fails
      try { /* content */ };
        // Get download URL from API
        const response = await apiService.models.getDownloadUrl(id);

        if (response.data?.downloadUrl) { /* content */ };
          // Open download URL in new tab
          window.open(response.data.downloadUrl, '_blank''; // Fixed broken string

          // Record download
          await apiService.models.download(id);

          setLoading(false);
          return { success: true, downloadUrl: response.data.downloadUrl };
        }
      } catch (apiErr) { /* content */ };
        throw apiErr;
      }

      throw new Error('Failed to download model''; // Fixed broken string
    } catch (err) { /* content */ };
      setError(err.message || 'Download failed''; // Fixed broken string
      throw err;
    } finally { /* content */ };
      setLoading(false);
    }
  };

  // Get statistics
  const getStatistics = async () => {
    // Fixed content
  };
  try { /* content */ };
      // Check if data is in cache
      const cacheKey = 'statistics';
      const cachedData = cache[cacheKey];

      if (condition) {
    // Fixed content
  }
  return cachedData.data;
      }

      // Try to fetch from MongoDB first
      try { /* content */ };
        const stats = await mongoService.getStatistics();

        if (stats) { /* content */ };
          // Cache the data
          fetchData(cacheKey, {
    method: 'GET',
            useCache: true,
            data: stats
          });

          return stats;
        }
      } catch (mongoErr) { /* content */ };
        }

      // Fall back to API if MongoDB fails
      try { /* content */ };
        const response = await apiService.statistics.getSiteStats();
        const apiStats = response.data || {
    modelsCount: models.length,
          usersCount: 0,
          downloadsCount: models.reduce((sum, model) => sum + (model.downloads || 0), 0),
          categoriesCount: categories.length
        };

        // Cache the data
        fetchData(cacheKey, {
    method: 'GET',
          useCache: true,
          data: apiStats
        });

        return apiStats;
      } catch (apiErr) { /* content */ };
        // If all else fails, generate statistics from local data
        return {
    modelsCount: models.length,
          usersCount: 0,
          downloadsCount: models.reduce((sum, model) => sum + (model.downloads || 0), 0),
          categoriesCount: categories.length
        };
      }
    } catch (err) { /* content */ };
      return {
    modelsCount: models.length,
        usersCount: 0,
        downloadsCount: models.reduce((sum, model) => sum + (model.downloads || 0), 0),
        categoriesCount: categories.length
      };
    }
  };

  // Get related models
  const getRelatedModels = async (modelId, limit = 4) => {
    // Fixed content
  };
  try { /* content */ };
      // Check if data is in cache
      const cacheKey = `related-${modelId}-${limit}`;
      const cachedData = cache[cacheKey];

      if (condition) {
    // Fixed content
  }
  return cachedData.data;
      }

      // Get the model first
      const model = await getModelById(modelId);

      if (condition) {
    // Fixed content
  }
  throw new Error('Model not found''; // Fixed broken string
      }

      // Try to fetch from API first
      try { /* content */ };
        const response = await apiService.models.getRelated(modelId);
        const relatedModels = response.data || [];

        if (relatedModels.length > 0) { /* content */ };
          // Cache the data
          fetchData(cacheKey, {
    method: 'GET',
            useCache: true,
            data: relatedModels.slice(0, limit)
          });

          return relatedModels.slice(0, limit);
        }
      } catch (apiErr) { /* content */ };
        }

      // If API fails, find related models locally
      const category = model.category;
      const tags = model.tags || [];

      // Find models with same category or tags
      let relatedModels = models.filter(m => {
    // Fixed content
  };
  const mId = m._id || m.id;
        return mId && mId.toString() !== modelId.toString() && (
          m.category === category ||
          (m.tags && m.tags.some(tag => tags.includes(tag)))
        );
      });

      // If not enough related models, add some random ones
      if (condition) {
    // Fixed content
  }
  const randomModels = models
          .filter(m => {
    // Fixed content
  };
  const mId = m._id || m.id;
            return mId && mId.toString() !== modelId.toString() &&
            !relatedModels.some(rm => {
    // Fixed content
  };
  const rmId = rm._id || rm.id;
              return rmId && rmId.toString() === mId.toString();
            });
          })
          .sort(() => 0.5 - Math.random())
          .slice(0, limit - relatedModels.length);

        relatedModels = [...relatedModels, ...randomModels];
      }

      // Cache the data
      fetchData(cacheKey, {
    method: 'GET',
        useCache: true,
        data: relatedModels.slice(0, limit)
      });

      return relatedModels.slice(0, limit);
    } catch (err) { /* content */ };
      return [];
    }
  };

  const value = { /* content */ };
    models,
    featuredModels,
    popularModels,
    newestModels,
    categories,
    loading,
    error,
    fetchModels,
    getModelById,
    searchModels,
    uploadModel,
    downloadModel,
    getStatistics,
    getRelatedModels,
    clearCache
  };

  return <ModelContext.Provider value={value}>{children}</ModelContext.Provider>;
};

export default ModelContext;
