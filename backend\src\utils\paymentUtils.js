import Payment from '../models/Payment.js';
import Subscription from '../models/Subscription.js';
import User from '../models/User.js';

// Subscription plans
export const SUBSCRIPTION_PLANS = {
  basic: {
    name: 'Basic Plan',
    description: 'Access to basic features',
    monthlyPrice: 9.99,
    yearlyPrice: 99.99,
    features: {
      downloadLimit: 20,
      accessToPremiumModels: false,
      prioritySupport: false,
      commercialUse: false
    }
  },
  premium: {
    name: 'Premium Plan',
    description: 'Access to premium features',
    monthlyPrice: 19.99,
    yearlyPrice: 199.99,
    features: {
      downloadLimit: 50,
      accessToPremiumModels: true,
      prioritySupport: false,
      commercialUse: false
    }
  },
  professional: {
    name: 'Professional Plan',
    description: 'Access to all features with commercial license',
    monthlyPrice: 29.99,
    yearlyPrice: 299.99,
    features: {
      downloadLimit: 100,
      accessToPremiumModels: true,
      prioritySupport: true,
      commercialUse: true
    }
  }
};

/**
 * Create a new subscription
 * @param {Object} data - Subscription data
 * @returns {Promise<Object>} - Created subscription
 */
export const createSubscription = async (data) => {
  try {
    const { userId, planId, interval } = data;
    
    // Get plan details
    const plan = SUBSCRIPTION_PLANS[planId];
    if (!plan) {
      throw new Error('Invalid subscription plan');
    }
    
    // Calculate end date
    const startDate = new Date();
    const endDate = new Date();
    if (interval === 'yearly') {
      endDate.setFullYear(endDate.getFullYear() + 1);
    } else {
      endDate.setMonth(endDate.getMonth() + 1);
    }
    
    // Create subscription
    const subscription = await Subscription.create({
      user: userId,
      plan: planId,
      status: 'active',
      startDate,
      endDate,
      renewalDate: endDate,
      autoRenew: true,
      price: interval === 'yearly' ? plan.yearlyPrice : plan.monthlyPrice,
      currency: 'USD',
      interval,
      subscriptionId: `sub_${Date.now()}`,
      customerId: `cus_${userId}`,
      features: plan.features
    });
    
    // Create payment record
    await Payment.create({
      user: userId,
      amount: interval === 'yearly' ? plan.yearlyPrice : plan.monthlyPrice,
      currency: 'USD',
      paymentMethod: 'credit_card',
      status: 'completed',
      type: 'subscription',
      description: `Subscription to ${plan.name} (${interval})`,
      paymentId: `pay_${Date.now()}`,
      customerId: `cus_${userId}`,
      subscriptionPlan: planId
    });
    
    // Update user subscription
    await User.findByIdAndUpdate(userId, {
      subscription: {
        type: planId,
        status: 'active',
        startDate,
        endDate
      },
      downloadCredits: plan.features.downloadLimit
    });
    
    return subscription;
  } catch (error) {
    console.error('Error creating subscription:', error);
    throw error;
  }
};

/**
 * Get user's current subscription
 * @param {string} userId - User ID
 * @returns {Promise<Object>} - User's current subscription
 */
export const getCurrentSubscription = async (userId) => {
  try {
    const subscription = await Subscription.findOne({
      user: userId,
      status: 'active'
    }).sort({ createdAt: -1 });
    
    return subscription;
  } catch (error) {
    console.error('Error getting current subscription:', error);
    throw error;
  }
};

/**
 * Get user's payment methods
 * @param {string} userId - User ID
 * @returns {Promise<Array>} - User's payment methods
 */
export const getPaymentMethods = async (userId) => {
  try {
    // In a real app, this would fetch from a payment gateway
    // For now, return mock data
    if (process.env.PAYMENT_MOCK_DATA === 'true') {
      return [
        {
          id: 'pm_1',
          type: 'card',
          brand: 'visa',
          last4: '4242',
          expMonth: 12,
          expYear: 2025,
          isDefault: true
        }
      ];
    }
    
    return [];
  } catch (error) {
    console.error('Error getting payment methods:', error);
    throw error;
  }
};

/**
 * Get user's invoices
 * @param {string} userId - User ID
 * @returns {Promise<Array>} - User's invoices
 */
export const getInvoices = async (userId) => {
  try {
    const payments = await Payment.find({
      user: userId,
      status: 'completed'
    }).sort({ createdAt: -1 });
    
    return payments.map(payment => ({
      id: payment._id,
      amount: payment.amount,
      currency: payment.currency,
      status: payment.status,
      date: payment.createdAt,
      description: payment.description,
      invoiceNumber: `INV-${payment._id.toString().slice(-6).toUpperCase()}-${new Date(payment.createdAt).getFullYear()}`,
      downloadUrl: '#'
    }));
  } catch (error) {
    console.error('Error getting invoices:', error);
    throw error;
  }
};

/**
 * Get user's payment history
 * @param {string} userId - User ID
 * @returns {Promise<Array>} - User's payment history
 */
export const getPaymentHistory = async (userId) => {
  try {
    const payments = await Payment.find({
      user: userId
    }).sort({ createdAt: -1 });
    
    return payments.map(payment => ({
      id: payment._id,
      amount: payment.amount,
      currency: payment.currency,
      status: payment.status,
      date: payment.createdAt,
      description: payment.description,
      paymentMethod: payment.paymentMethod === 'credit_card' ? 
        `${payment.paymentDetails?.cardBrand || 'Card'} •••• ${payment.paymentDetails?.last4 || '****'}` : 
        payment.paymentMethod
    }));
  } catch (error) {
    console.error('Error getting payment history:', error);
    throw error;
  }
};

export default {
  SUBSCRIPTION_PLANS,
  createSubscription,
  getCurrentSubscription,
  getPaymentMethods,
  getInvoices,
  getPaymentHistory
};
