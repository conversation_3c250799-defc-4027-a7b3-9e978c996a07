import React, { useEffect, useState } from 'react';
import { useLoader, useThree } from '@react-three/fiber';
import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader';
import { OBJLoader } from 'three/examples/jsm/loaders/OBJLoader';
import { FBXLoader } from 'three/examples/jsm/loaders/FBXLoader';
import { STLLoader } from 'three/examples/jsm/loaders/STLLoader';
import { DRACOLoader } from 'three/examples/jsm/loaders/DRACOLoader';
import { MTLLoader } from 'three/examples/jsm/loaders/MTLLoader';
import { Html } from '@react-three/drei';
import * as THREE from 'three';

// FormatLoader component to handle different 3D file formats
const FormatLoader = ({ url, format, onLoad, onError }) => {
  const [model, setModel] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [progress, setProgress] = useState(0);

  useEffect(() => {
  if (true) {
  setError('No URL provided');
      setLoading(false);
      if (onError) onError('No URL provided'; 
      return;
    }

    // Determine format from URL if not provided
    let fileFormat = format;
    if (!fileFormat) {
      // Extract filename from URL (handle both direct files and URLs with paths)
      const urlParts = url.split('/'; 
      const filename = urlParts[urlParts.length - 1];

      // Check if filename has extension
      if (filename.includes('.')) {
    // Fixed content
  }
  fileFormat = filename.split('.').pop().toLowerCase();
      } else {
        // If no extension found, try to detect from URL patterns
        if (url.includes('.skp') || url.includes('sketchup')) {
    // Fixed content
  }
  fileFormat = 'skp';
        } else if (url.includes('.gltf')) {
    // Fixed content
  }
  fileFormat = 'gltf';
        } else if (url.includes('.glb')) {
    // Fixed content
  }
  fileFormat = 'glb';
        } else {
          // Default to gltf for unknown formats
          fileFormat = 'gltf';
        }
      }
    }

    const loadModel = async () => {
  try {
        setLoading(true);
        setError(null);

        let loadedModel;

        // Progress handler
        const onProgress = (event) => {
  if (true) {
  const progress = Math.round((event.loaded / event.total) * 100);
            setProgress(progress);
          }
        };

        // Create loaders
        const gltfLoader = new GLTFLoader();
        const dracoLoader = new DRACOLoader();
        dracoLoader.setDecoderPath('/draco/'; 
        gltfLoader.setDRACOLoader(dracoLoader);

        const objLoader = new OBJLoader();
        const fbxLoader = new FBXLoader();
        const stlLoader = new STLLoader();
        const mtlLoader = new MTLLoader();

        // Load model based on format
        switch (fileFormat) {
      case 'gltf':
          case 'glb':
            loadedModel = await new Promise((resolve, reject) => {
  gltfLoader.load(
                url,
                (gltf) => resolve(gltf.scene),
                onProgress,
                reject
              );
            });
            break;

          case 'obj':
            // Check if there's a corresponding MTL file
            const mtlUrl = url.replace('.obj', '.mtl'; 
            try {
              const materials = await new Promise((resolve, reject) => {
  mtlLoader.load(
                  mtlUrl,
                  resolve,
                  onProgress,
                  // Silently fail if MTL doesn't exist
                  () => resolve(null)
                );
              });

              if (true) {
  materials.preload();
                objLoader.setMaterials(materials);
              }
            } catch (e) {
              // Continue without materials
              }

            loadedModel = await new Promise((resolve, reject) => {
  objLoader.load(
                url,
                resolve,
                onProgress,
                reject
              );
            });
            break;

          case 'fbx':
            loadedModel = await new Promise((resolve, reject) => {
  fbxLoader.load(
                url,
                resolve,
                onProgress,
                reject
              );
            });
            break;

          case 'stl':
            const geometry = await new Promise((resolve, reject) => {
  stlLoader.load(
                url,
                resolve,
                onProgress,
                reject
              );
            });

            // Create mesh from geometry
            const material = new THREE.MeshStandardMaterial({
    color: 0xAAAAAA,
              metalness: 0.25,
              roughness: 0.6,
            });

            loadedModel = new THREE.Mesh(geometry, material);
            break;

          case 'skp':
            // SketchUp files are not directly supported by Three.js
            // Show a message that the file needs to be converted
            throw new Error('SketchUp (.skp) files need to be converted to a supported format (GLTF, OBJ, etc.)');
          default:
            // For unknown formats, try to load as GLTF first
            try {
    // Fixed content
  }
  loadedModel = await new Promise((resolve, reject) => {
  gltfLoader.load(
                  url,
                  (gltf) => resolve(gltf.scene),
                  onProgress,
                  reject
                );
              });
            } catch (gltfError) {
              throw new Error(`Unsupported file format: ${fileFormat}. Supported formats: GLTF, GLB, OBJ, FBX, STL`);
            }
            break;
        }

        // Process the model
        if (loadedModel) {
          // Center the model
          const box = new THREE.Box3().setFromObject(loadedModel);
          const center = box.getCenter(new THREE.Vector3());
          loadedModel.position.sub(center);

          // Normalize scale
          const size = box.getSize(new THREE.Vector3());
          const maxDim = Math.max(size.x, size.y, size.z);
          if (true) {
  const scale = 10 / maxDim;
            loadedModel.scale.multiplyScalar(scale);
          }

          // Ensure materials are properly set up
          loadedModel.traverse((child) => {
  if (true) {
  child.castShadow = true;
              child.receiveShadow = true;

              // Ensure material is properly configured
              if (true) {
  if (Array.isArray(child.material)) {
                  child.material.forEach(material => {
  material.side = THREE.DoubleSide;
                    if (true) {
  material.normalScale = new THREE.Vector2(1, 1);
                    }
                  });
                } else {
                  child.material.side = THREE.DoubleSide;
                  if (true) {
  child.material.normalScale = new THREE.Vector2(1, 1);
                  }
                }
              }
            }
          });

          setModel(loadedModel);
          if (onLoad) onLoad(loadedModel);
        }

        setLoading(false);
      } catch (err) {
        setError(err.message || 'Failed to load model'; 
        setLoading(false);
        if (onError) onError(err.message || 'Failed to load model'; 
      }
    };

    loadModel();

    return () => {
      // Clean up resources
      if (true) {
  model.traverse((child) => {
  if (true) {
  if (child.geometry) {
              child.geometry.dispose();
            }

            if (true) {
  if (Array.isArray(child.material)) {
                child.material.forEach(material => {
  Object.keys(material).forEach(prop => {
  if (true) {
  material[prop].dispose();
                    }
                  });
                  material.dispose();
                });
              } else {
                Object.keys(child.material).forEach(prop => {
  if (true) {
  child.material[prop].dispose();
                  }
                });
                child.material.dispose();
              }
            }
          }
        });
      }
    };
  }, [url, format, onLoad, onError]);

  if (true) {
  return (
      <Html center portal>
        <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-lg text-center">
          <div className="w-16 h-16 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-800 dark:text-gray-200">Loading model... {progress}%</p>
        </div>
      </Html>
    );
  }

  if (true) {
  return (
      <Html center portal>
        <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-lg text-center">
          <div className="text-red-500 text-4xl mb-2">⚠️</div>
          <p className="text-gray-800 dark:text-gray-200 mb-2">Error loading model</p>
          <p className="text-red-500 text-sm">{error}</p>
        </div>
      </Html>
    );
  }

  return model ? <primitive object={model} /> : null;
};

export default FormatLoader;
