import React from 'react';
import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import { FiDownload, FiEye, FiStar } from 'react-icons/fi';
import ImageWithFallback from './ui/ImageWithFallback';

const ModelCard = ({ model }) => {
  // Get the correct ID (MongoDB uses _id, others use id)
  const modelId = model._id || model.id;

  // Don't render if no valid ID or ID is too short (MongoDB ObjectId should be 24 chars)
  if (!modelId || modelId.length < 12) {
    return null;
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className="group glass-card rounded-3xl shadow-professional overflow-hidden transition-all duration-500 hover:shadow-professional-lg hover:scale-105 border border-white/20"
    >
      <Link to={`/model/${modelId}`} className="block relative pb-[75%] overflow-hidden rounded-t-3xl">
        <ImageWithFallback
          src={model.imageUrl}
          alt={model.title}
          className="absolute inset-0 w-full h-full object-cover transition-all duration-700 group-hover:scale-110"
          lowResSrc="/images/placeholder-tiny.jpg"
        />

        {/* Overlay gradient */}
        <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

        {/* Premium badge */}
        {model.isPremium && (
          <div className="absolute top-4 right-4 bg-gradient-to-r from-yellow-400 to-orange-500 text-white text-xs font-bold px-3 py-2 rounded-xl shadow-lg animate-pulse">
            ⭐ Premium
          </div>
        )}

        {/* Quick action overlay */}
        <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
          <div className="bg-white/20 backdrop-blur-sm border border-white/30 rounded-2xl p-4 transform scale-90 group-hover:scale-100 transition-transform duration-300">
            <FiEye className="w-8 h-8 text-white" />
          </div>
        </div>

        {/* Category badge */}
        <div className="absolute top-4 left-4 bg-white/20 backdrop-blur-sm border border-white/30 rounded-xl px-3 py-1">
          <span className="text-white text-xs font-medium">{model.category}</span>
        </div>
      </Link>

      <div className="p-6">
        <Link to={`/model/${modelId}`} className="group">
          <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-2 line-clamp-2 group-hover:text-transparent group-hover:bg-gradient-to-r group-hover:from-blue-600 group-hover:to-purple-600 group-hover:bg-clip-text transition-all duration-300">
            {model.title}
          </h3>
        </Link>

        {/* Subcategory tags */}
        <div className="flex items-center gap-2 mb-3">
          {model.subcategory && (
            <span className="px-3 py-1 bg-gradient-to-r from-blue-100 to-purple-100 dark:from-blue-900/30 dark:to-purple-900/30 text-blue-700 dark:text-blue-300 rounded-full text-xs font-medium">
              {model.subcategory}
            </span>
          )}
          <span className="px-3 py-1 bg-gradient-to-r from-green-100 to-teal-100 dark:from-green-900/30 dark:to-teal-900/30 text-green-700 dark:text-green-300 rounded-full text-xs font-medium">
            {model.format || 'SKP'}
          </span>
        </div>

        {/* Description */}
        <div className="mb-4">
          {model.description && (
            <p className="text-sm text-gray-600 dark:text-gray-400 line-clamp-2 leading-relaxed">
              {model.description}
            </p>
          )}
        </div>

        {/* Stats */}
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-1">
              <div className="flex">
                {[...Array(5)].map((_, i) => (
                  <FiStar
                    key={i}
                    className={`w-4 h-4 ${i < Math.floor(model.rating || 4.5) ? 'text-yellow-400 fill-current' : 'text-gray-300'}`}
                  />
                ))}
              </div>
              <span className="text-sm font-medium text-gray-700 dark:text-gray-300 ml-1">
                {model.rating || '4.5'}
              </span>
            </div>
          </div>

          <div className="flex items-center gap-3 text-xs text-gray-500 dark:text-gray-400">
            <span className="flex items-center gap-1">
              <FiDownload className="w-3.5 h-3.5" />
              {model.downloads || '234'}
            </span>
            <span className="flex items-center gap-1">
              <FiEye className="w-3.5 h-3.5" />
              {model.views || '1.2k'}
            </span>
          </div>
        </div>

        {/* Action button */}
        <Link
          to={`/model/${modelId}`}
          className="group relative w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white py-3 px-4 rounded-2xl font-semibold text-center transition-all duration-300 hover:scale-105 shadow-lg hover:shadow-xl flex items-center justify-center gap-2 overflow-hidden"
        >
          <span className="relative z-10">Xem Chi Tiết</span>
          <FiEye className="w-4 h-4 relative z-10 group-hover:scale-110 transition-transform" />
          <div className="absolute inset-0 bg-gradient-to-r from-purple-600 to-pink-600 opacity-0 group-hover:opacity-100 transition-opacity"></div>
        </Link>
      </div>
    </motion.div>
  );
};

export default ModelCard;
