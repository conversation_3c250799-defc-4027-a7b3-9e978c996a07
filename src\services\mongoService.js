import axios from 'axios';

// MongoDB API endpoint
const API_URL = import.meta.env.VITE_MONGODB_API_URL || 'http://localhost:5002/api/mongodb';

// MongoDB service
const mongoService = {
  /**
   * Get statistics from MongoDB
   * @returns {Promise<Object>} Statistics object
   */
  getStatistics: async () => {
    try {
      const response = await axios.get(`${API_URL}/statistics`);
      return response.data;
    } catch (error) {
      console.error('Error fetching statistics:', error);
      throw error;
    }
  },

  /**
   * Get models from MongoDB
   * @param {Object} params - Query parameters
   * @returns {Promise<Array>} Array of models
   */
  getModels: async (params = {}) => {
    try {
      const response = await axios.get(`${API_URL}/models`, { params });
      // API returns {success: true, data: [...]} format
      return response.data.data || response.data;
    } catch (error) {
      console.error('Error fetching models:', error);
      throw error;
    }
  },

  /**
   * Get model by ID from MongoDB
   * @param {string} id - Model ID
   * @returns {Promise<Object>} Model object
   */
  getModelById: async (id) => {
    try {
      const response = await axios.get(`${API_URL}/models/${id}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching model ${id}:`, error);
      throw error;
    }
  },

  /**
   * Create model in MongoDB
   * @param {Object} model - Model data
   * @returns {Promise<Object>} Created model
   */
  createModel: async (model) => {
    try {
      const response = await axios.post(`${API_URL}/models`, model);
      return response.data;
    } catch (error) {
      console.error('Error creating model:', error);
      throw error;
    }
  },

  /**
   * Update model in MongoDB
   * @param {string} id - Model ID
   * @param {Object} model - Model data
   * @returns {Promise<Object>} Updated model
   */
  updateModel: async (id, model) => {
    try {
      const response = await axios.put(`${API_URL}/models/${id}`, model);
      return response.data;
    } catch (error) {
      console.error(`Error updating model ${id}:`, error);
      throw error;
    }
  },

  /**
   * Delete model from MongoDB
   * @param {string} id - Model ID
   * @returns {Promise<Object>} Deletion result
   */
  deleteModel: async (id) => {
    try {
      const response = await axios.delete(`${API_URL}/models/${id}`);
      return response.data;
    } catch (error) {
      console.error(`Error deleting model ${id}:`, error);
      throw error;
    }
  },

  /**
   * Get categories from MongoDB
   * @returns {Promise<Array>} Array of categories
   */
  getCategories: async () => {
    try {
      const response = await axios.get(`${API_URL}/categories`);
      // API returns {success: true, data: [...]} format
      return response.data.data || response.data;
    } catch (error) {
      console.error('Error fetching categories:', error);
      throw error;
    }
  },

  /**
   * Get users from MongoDB
   * @param {Object} params - Query parameters
   * @returns {Promise<Array>} Array of users
   */
  getUsers: async (params = {}) => {
    try {
      const response = await axios.get(`${API_URL}/users`, { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching users:', error);
      throw error;
    }
  },

  /**
   * Get user by ID from MongoDB
   * @param {string} id - User ID
   * @returns {Promise<Object>} User object
   */
  getUserById: async (id) => {
    try {
      const response = await axios.get(`${API_URL}/users/${id}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching user ${id}:`, error);
      throw error;
    }
  },

  /**
   * Create user in MongoDB
   * @param {Object} user - User data
   * @returns {Promise<Object>} Created user
   */
  createUser: async (user) => {
    try {
      const response = await axios.post(`${API_URL}/users`, user);
      return response.data;
    } catch (error) {
      console.error('Error creating user:', error);
      throw error;
    }
  },

  /**
   * Update user in MongoDB
   * @param {string} id - User ID
   * @param {Object} user - User data
   * @returns {Promise<Object>} Updated user
   */
  updateUser: async (id, user) => {
    try {
      const response = await axios.put(`${API_URL}/users/${id}`, user);
      return response.data;
    } catch (error) {
      console.error(`Error updating user ${id}:`, error);
      throw error;
    }
  },

  /**
   * Delete user from MongoDB
   * @param {string} id - User ID
   * @returns {Promise<Object>} Deletion result
   */
  deleteUser: async (id) => {
    try {
      const response = await axios.delete(`${API_URL}/users/${id}`);
      return response.data;
    } catch (error) {
      console.error(`Error deleting user ${id}:`, error);
      throw error;
    }
  },

  /**
   * Get collections from MongoDB
   * @param {Object} params - Query parameters
   * @returns {Promise<Array>} Array of collections
   */
  getCollections: async (params = {}) => {
    try {
      const response = await axios.get(`${API_URL}/collections`, { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching collections:', error);
      throw error;
    }
  },

  /**
   * Get collection by ID from MongoDB
   * @param {string} id - Collection ID
   * @returns {Promise<Object>} Collection object
   */
  getCollectionById: async (id) => {
    try {
      const response = await axios.get(`${API_URL}/collections/${id}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching collection ${id}:`, error);
      throw error;
    }
  },

  /**
   * Create collection in MongoDB
   * @param {Object} collection - Collection data
   * @returns {Promise<Object>} Created collection
   */
  createCollection: async (collection) => {
    try {
      const response = await axios.post(`${API_URL}/collections`, collection);
      return response.data;
    } catch (error) {
      console.error('Error creating collection:', error);
      throw error;
    }
  },

  /**
   * Update collection in MongoDB
   * @param {string} id - Collection ID
   * @param {Object} collection - Collection data
   * @returns {Promise<Object>} Updated collection
   */
  updateCollection: async (id, collection) => {
    try {
      const response = await axios.put(`${API_URL}/collections/${id}`, collection);
      return response.data;
    } catch (error) {
      console.error(`Error updating collection ${id}:`, error);
      throw error;
    }
  },

  /**
   * Delete collection from MongoDB
   * @param {string} id - Collection ID
   * @returns {Promise<Object>} Deletion result
   */
  deleteCollection: async (id) => {
    try {
      const response = await axios.delete(`${API_URL}/collections/${id}`);
      return response.data;
    } catch (error) {
      console.error(`Error deleting collection ${id}:`, error);
      throw error;
    }
  },

  /**
   * Get reviews from MongoDB
   * @param {Object} params - Query parameters
   * @returns {Promise<Array>} Array of reviews
   */
  getReviews: async (params = {}) => {
    try {
      const response = await axios.get(`${API_URL}/reviews`, { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching reviews:', error);
      throw error;
    }
  },

  /**
   * Get reviews for a model from MongoDB
   * @param {string} modelId - Model ID
   * @param {Object} params - Query parameters
   * @returns {Promise<Array>} Array of reviews
   */
  getReviewsByModelId: async (modelId, params = {}) => {
    try {
      const response = await axios.get(`${API_URL}/reviews/model/${modelId}`, { params });
      return response.data;
    } catch (error) {
      console.error(`Error fetching reviews for model ${modelId}:`, error);
      throw error;
    }
  },

  /**
   * Create review in MongoDB
   * @param {Object} review - Review data
   * @returns {Promise<Object>} Created review
   */
  createReview: async (review) => {
    try {
      const response = await axios.post(`${API_URL}/reviews`, review);
      return response.data;
    } catch (error) {
      console.error('Error creating review:', error);
      throw error;
    }
  },

  /**
   * Update review in MongoDB
   * @param {string} id - Review ID
   * @param {Object} review - Review data
   * @returns {Promise<Object>} Updated review
   */
  updateReview: async (id, review) => {
    try {
      const response = await axios.put(`${API_URL}/reviews/${id}`, review);
      return response.data;
    } catch (error) {
      console.error(`Error updating review ${id}:`, error);
      throw error;
    }
  },

  /**
   * Delete review from MongoDB
   * @param {string} id - Review ID
   * @returns {Promise<Object>} Deletion result
   */
  deleteReview: async (id) => {
    try {
      const response = await axios.delete(`${API_URL}/reviews/${id}`);
      return response.data;
    } catch (error) {
      console.error(`Error deleting review ${id}:`, error);
      throw error;
    }
  },

  /**
   * Search models in MongoDB
   * @param {Object} params - Search parameters
   * @returns {Promise<Array>} Array of models
   */
  searchModels: async (params = {}) => {
    try {
      const response = await axios.get(`${API_URL}/search/models`, { params });
      return response.data;
    } catch (error) {
      console.error('Error searching models:', error);
      throw error;
    }
  },

  /**
   * Get dashboard statistics from MongoDB
   * @returns {Promise<Object>} Dashboard statistics
   */
  getDashboardStats: async () => {
    try {
      const response = await axios.get(`${API_URL}/dashboard/stats`);
      return response.data;
    } catch (error) {
      console.error('Error fetching dashboard statistics:', error);
      throw error;
    }
  },

  /**
   * Get related models from MongoDB
   * @param {string} modelId - Model ID
   * @param {number} limit - Number of related models to return
   * @returns {Promise<Array>} Array of related models
   */
  getRelatedModels: async (modelId, limit = 6) => {
    try {
      const response = await axios.get(`${API_URL}/models/${modelId}/related`, {
        params: { limit }
      });
      return response.data.data || response.data;
    } catch (error) {
      console.error(`Error fetching related models for ${modelId}:`, error);
      // Return empty array if related models endpoint fails
      return [];
    }
  }
};

export default mongoService;
