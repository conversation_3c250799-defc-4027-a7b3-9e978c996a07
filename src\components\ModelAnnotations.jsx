import React, { useState, useRef, useEffect } from 'react';
import { Canvas, useThree, useFrame } from '@react-three/fiber';
import { OrbitControls, Html, useGLTF, Environment, PerspectiveCamera } from '@react-three/drei';
import { motion } from 'framer-motion';
import { FiPlus, FiEdit2, FiTrash2, FiSave, FiX, FiEye, FiEyeOff } from 'react-icons/fi';
import * as THREE from 'three';
import Button from './ui/Button';
import LoadingIndicator from './ui/LoadingIndicator';

/**
 * Annotation Marker Component
 */
const AnnotationMarker = ({ position, content, id, isEditing, onEdit, onDelete, isSelected, onClick }) => {
  const [hovered, setHovered] = useState(false);

  return (
    <group position={position}>
      {/* Clickable sphere */}
      <mesh
        onPointerOver={() => setHovered(true)}
        onPointerOut={() => setHovered(false)}
        onClick={(e) => {
          e.stopPropagation();
          onClick(id);
        }}
        scale={hovered || isSelected ? 1.2 : 1}
      >
        <sphereGeometry args={[0.05, 16, 16]} />
        <meshStandardMaterial
          color={isSelected ? '#3b82f6' : (hovered ? '#60a5fa' : '#f59e0b')}
          emissive={isSelected ? '#3b82f6' : (hovered ? '#60a5fa' : '#f59e0b')}
          emissiveIntensity={0.5}
        />
      </mesh>

      {/* HTML annotation content */}
      <Html
        position={[0, 0.1, 0]}
        distanceFactor={10}
        occlude
        transform
        sprite
      >
        <div
          className={`bg-white dark:bg-gray-800 p-2 rounded shadow-lg max-w-xs transform -translate-x-1/2 ${
            isSelected ? 'border-2 border-blue-500' : ''
          }`}
          style={{
            opacity: hovered || isSelected ? 1 : 0.8,
            transition: 'all 0.2s ease',
            pointerEvents: 'all'
          }}
        >
          <div className="text-sm text-gray-800 dark:text-gray-200">{content}</div>

          {isEditing && isSelected && (
            <div className="flex justify-end mt-2 space-x-1">
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  onEdit(id);
                }}
                className="p-1 text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
                aria-label="Edit annotation"
              >
                <FiEdit2 className="w-3 h-3" />
              </button>
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  onDelete(id);
                }}
                className="p-1 text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300"
                aria-label="Delete annotation"
              >
                <FiTrash2 className="w-3 h-3" />
              </button>
            </div>
          )}
        </div>
      </Html>
    </group>
  );
};

/**
 * Model with Annotations Component
 */
const ModelWithAnnotations = ({
  modelUrl,
  annotations,
  isEditing,
  onAddAnnotation,
  onEditAnnotation,
  onDeleteAnnotation,
  selectedAnnotation,
  onSelectAnnotation
}) => {
  const { scene, nodes } = useGLTF(modelUrl);
  const { camera, raycaster, mouse, gl } = useThree();

  // Handle click on model to add annotation
  const handleModelClick = (event) => {
    if (!isEditing) return;

    // Get intersection point
    const intersects = raycaster.intersectObject(scene, true);

    if (intersects.length > 0) {
      const point = intersects[0].point;
      onAddAnnotation(point);
    }
  };

  return (
    <group onClick={handleModelClick}>
      {/* Model */}
      <primitive object={scene} />

      {/* Annotations */}
      {annotations.map((annotation) => (
        <AnnotationMarker
          key={annotation.id}
          id={annotation.id}
          position={annotation.position}
          content={annotation.content}
          isEditing={isEditing}
          onEdit={onEditAnnotation}
          onDelete={onDeleteAnnotation}
          isSelected={selectedAnnotation === annotation.id}
          onClick={onSelectAnnotation}
        />
      ))}
    </group>
  );
};

/**
 * Model Annotations Component
 * Allows adding, editing, and viewing annotations on 3D models
 */
const ModelAnnotations = ({ modelUrl, initialAnnotations = [], onSave, readOnly = false }) => {
  const [annotations, setAnnotations] = useState(initialAnnotations);
  const [isEditing, setIsEditing] = useState(false);
  const [selectedAnnotation, setSelectedAnnotation] = useState(null);
  const [newAnnotationContent, setNewAnnotationContent] = useState('');
  const [newAnnotationPosition, setNewAnnotationPosition] = useState(null);
  const [isAddingAnnotation, setIsAddingAnnotation] = useState(false);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [showAnnotations, setShowAnnotations] = useState(true);

  // Handle model load
  const handleModelLoad = () => {
    setLoading(false);
  };

  // Handle model load error
  const handleModelError = (error) => {
    console.error('Error loading model:', error);
    setError('Failed to load 3D model');
    setLoading(false);
  };

  // Add new annotation
  const handleAddAnnotation = (position) => {
    setNewAnnotationPosition(position);
    setIsAddingAnnotation(true);
  };

  // Save new annotation
  const saveNewAnnotation = () => {
    if (newAnnotationContent.trim() === '') return;

    const newAnnotation = {
      id: Date.now().toString(),
      position: newAnnotationPosition,
      content: newAnnotationContent,
    };

    setAnnotations([...annotations, newAnnotation]);
    setNewAnnotationContent('');
    setNewAnnotationPosition(null);
    setIsAddingAnnotation(false);
  };

  // Cancel adding annotation
  const cancelAddAnnotation = () => {
    setNewAnnotationContent('');
    setNewAnnotationPosition(null);
    setIsAddingAnnotation(false);
  };

  // Edit annotation
  const handleEditAnnotation = (id) => {
    const annotation = annotations.find(a => a.id === id);
    if (annotation) {
      setNewAnnotationContent(annotation.content);
      setNewAnnotationPosition(annotation.position);
      setIsAddingAnnotation(true);
      setSelectedAnnotation(id);
    }
  };

  // Save edited annotation
  const saveEditedAnnotation = () => {
    if (newAnnotationContent.trim() === '') return;

    setAnnotations(annotations.map(a =>
      a.id === selectedAnnotation
        ? { ...a, content: newAnnotationContent }
        : a
    ));

    setNewAnnotationContent('');
    setNewAnnotationPosition(null);
    setIsAddingAnnotation(false);
    setSelectedAnnotation(null);
  };

  // Delete annotation
  const handleDeleteAnnotation = (id) => {
    setAnnotations(annotations.filter(a => a.id !== id));
    if (selectedAnnotation === id) {
      setSelectedAnnotation(null);
    }
  };

  // Save all annotations
  const handleSaveAnnotations = () => {
    if (onSave) {
      onSave(annotations);
    }
    setIsEditing(false);
  };

  // Toggle editing mode
  const toggleEditingMode = () => {
    if (isEditing && onSave) {
      handleSaveAnnotations();
    } else {
      setIsEditing(!isEditing);
    }
  };

  // Toggle annotations visibility
  const toggleAnnotationsVisibility = () => {
    setShowAnnotations(!showAnnotations);
  };

  return (
    <div className="relative w-full h-full">
      {/* Loading overlay */}
      {loading && (
        <div className="absolute inset-0 flex items-center justify-center bg-gray-100 dark:bg-gray-900 bg-opacity-80 dark:bg-opacity-80 z-10">
          <LoadingIndicator type="spinner" size="lg" text="Loading 3D model..." />
        </div>
      )}

      {/* Error message */}
      {error && (
        <div className="absolute inset-0 flex items-center justify-center bg-gray-100 dark:bg-gray-900 bg-opacity-80 dark:bg-opacity-80 z-10">
          <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-lg max-w-md">
            <h3 className="text-lg font-medium text-red-600 dark:text-red-400 mb-2">Error</h3>
            <p className="text-gray-600 dark:text-gray-400">{error}</p>
          </div>
        </div>
      )}

      {/* Controls */}
      <div className="absolute top-4 right-4 z-10 flex space-x-2">
        {!readOnly && (
          <Button
            variant={isEditing ? 'primary' : 'secondary'}
            size="sm"
            onClick={toggleEditingMode}
            leftIcon={isEditing ? <FiSave /> : <FiEdit2 />}
          >
            {isEditing ? 'Save' : 'Edit'}
          </Button>
        )}

        <Button
          variant="secondary"
          size="sm"
          onClick={toggleAnnotationsVisibility}
          leftIcon={showAnnotations ? <FiEyeOff /> : <FiEye />}
        >
          {showAnnotations ? 'Hide' : 'Show'} Annotations
        </Button>
      </div>

      {/* 3D Canvas */}
      <Canvas shadows camera={{ position: [0, 0, 5], fov: 50 }} onCreated={handleModelLoad}>
        <ambientLight intensity={0.5} />
        <spotLight position={[10, 10, 10]} angle={0.15} penumbra={1} intensity={1} castShadow />
        <PerspectiveCamera makeDefault position={[0, 0, 5]} fov={50} />
        <OrbitControls enablePan={true} enableZoom={true} enableRotate={true} />
        <Environment preset="city" />

        {modelUrl && (
          <Suspense fallback={null}>
            {showAnnotations ? (
              <ModelWithAnnotations
                modelUrl={modelUrl}
                annotations={annotations}
                isEditing={isEditing}
                onAddAnnotation={handleAddAnnotation}
                onEditAnnotation={handleEditAnnotation}
                onDeleteAnnotation={handleDeleteAnnotation}
                selectedAnnotation={selectedAnnotation}
                onSelectAnnotation={setSelectedAnnotation}
              />
            ) : (
              <primitive object={useGLTF(modelUrl).scene} />
            )}
          </Suspense>
        )}
      </Canvas>

      {/* Annotation form */}
      {isAddingAnnotation && (
        <motion.div
          className="absolute bottom-4 left-4 right-4 bg-white dark:bg-gray-800 p-4 rounded-lg shadow-lg z-10"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: 20 }}
        >
          <div className="flex items-start">
            <textarea
              className="flex-1 p-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="Enter annotation text..."
              value={newAnnotationContent}
              onChange={(e) => setNewAnnotationContent(e.target.value)}
              rows={3}
              autoFocus
            />
            <div className="ml-2 flex flex-col space-y-2">
              <Button
                variant="primary"
                size="sm"
                onClick={selectedAnnotation ? saveEditedAnnotation : saveNewAnnotation}
                leftIcon={<FiSave />}
              >
                Save
              </Button>
              <Button
                variant="secondary"
                size="sm"
                onClick={cancelAddAnnotation}
                leftIcon={<FiX />}
              >
                Cancel
              </Button>
            </div>
          </div>
        </motion.div>
      )}
    </div>
  );
};

export default ModelAnnotations;
