import express from 'express';
import {
  getExtensions,
  getExtension,
  getCategories,
  getFeaturedExtensions,
  searchAndImportExtensions,
  bulkImportExtensions,
  downloadExtension,
  addExtensionReview,
  getExtensionStats,
  visualSearchModels,
  hybridSearchExtensions
} from '../controllers/extensionsController.js';
import { protect, authorize } from '../middleware/auth.js';

const router = express.Router();

// Public routes
router.get('/', getExtensions);
router.get('/categories', getCategories);
router.get('/featured', getFeaturedExtensions);
router.get('/stats', getExtensionStats);
router.get('/search-import', searchAndImportExtensions);
router.get('/:id', getExtension);

// Extension download
router.post('/:id/download', downloadExtension);

// Visual model search routes
router.post('/visual-search', visualSearchModels);
router.post('/hybrid-search', hybridSearchExtensions);

// Protected routes
router.post('/:id/review', protect, addExtensionReview);

// Admin routes
router.post('/bulk-import', protect, authorize('admin'), bulkImportExtensions);

// Public import route for testing
router.post('/test-import', bulkImportExtensions);

// Real scraping route
router.post('/real-scrape', async (req, res) => {
  try {
    const { maxExtensions = 20 } = req.body;

    console.log(`🚀 Starting real scraping of ${maxExtensions} extensions...`);

    const ExtensionsScraper = (await import('../utils/extensionsScraper.js')).default;
    const scraper = new ExtensionsScraper();

    // Scrape real extensions
    const scrapedExtensions = await scraper.bulkScrapeAllExtensions(maxExtensions);

    // Import to database
    const Extension = (await import('../models/Extension.js')).default;
    const User = (await import('../models/User.js')).default;

    // Find or create system user
    let systemUser = await User.findOne({ email: '<EMAIL>' });
    if (!systemUser) {
      systemUser = await User.create({
        name: 'Real Extensions Scraper',
        email: '<EMAIL>',
        password: 'system_password_' + Date.now(),
        role: 'admin',
        isVerified: true
      });
    }

    // Clear existing extensions
    await Extension.deleteMany({});

    // Import scraped extensions
    const importedExtensions = [];
    for (const extData of scrapedExtensions) {
      try {
        const extension = await Extension.create({
          ...extData,
          createdBy: systemUser._id
        });
        importedExtensions.push(extension);
      } catch (error) {
        console.error(`Failed to import ${extData.name}:`, error.message);
      }
    }

    res.status(200).json({
      success: true,
      data: {
        message: `Successfully scraped and imported ${importedExtensions.length} real extensions`,
        scraped: scrapedExtensions.length,
        imported: importedExtensions.length,
        extensions: importedExtensions.slice(0, 10) // Preview first 10
      }
    });

  } catch (error) {
    console.error('Real scraping failed:', error);
    res.status(500).json({
      success: false,
      error: error.message || 'Real scraping failed'
    });
  }
});

// Simple test route
router.get('/test', (req, res) => {
  res.json({
    success: true,
    message: 'Extensions API is working!',
    timestamp: new Date()
  });
});

// Create sample extensions
router.post('/create-samples', async (req, res) => {
  try {
    const Extension = (await import('../models/Extension.js')).default;
    const User = (await import('../models/User.js')).default;

    // Find or create system user
    let systemUser = await User.findOne({ email: '<EMAIL>' });
    if (!systemUser) {
      systemUser = await User.create({
        name: 'Extensions Importer',
        email: '<EMAIL>',
        password: 'system_password_' + Date.now(),
        role: 'admin',
        isVerified: true
      });
    }

    const sampleExtensions = [
      {
        name: 'Advanced Modeling Suite',
        description: 'Professional modeling tools for complex geometries and advanced 3D design workflows',
        developer: 'ModelPro Solutions',
        version: '4.1.0',
        category: 'Modeling',
        subcategory: 'Parametric',
        tags: ['modeling', 'parametric', 'advanced', 'professional'],
        price: '$199.00',
        isPaid: true,
        rating: 4.9,
        downloads: 9850,
        views: 25420,
        likes: 1250,
        features: ['NURBS Modeling', 'Subdivision Surfaces', 'Boolean Operations', 'Mesh Tools'],
        compatibility: ['SketchUp 2022+', 'Windows', 'Mac'],
        fileSize: '32.1 MB',
        screenshots: ['/images/extensions/advanced_modeling_1.jpg', '/images/extensions/advanced_modeling_2.jpg'],
        downloadUrl: '/downloads/extensions/advanced_modeling_suite.rbz',
        backupLinks: [
          '/downloads/extensions/advanced_modeling_suite.rbz',
          'https://backup1.3dsketchup.net/extensions/advanced_modeling_suite.rbz',
          'https://backup2.3dsketchup.net/extensions/advanced_modeling_suite.rbz'
        ],
        source: 'sketchup-extensions',
        extensionInfo: {
          originalUrl: 'https://extensions.sketchup.com/extension/advanced-modeling-suite',
          extensionId: 'skext_' + Date.now() + '_001',
          developer: 'ModelPro Solutions',
          scrapedAt: new Date()
        },
        status: 'active',
        isVerified: true,
        isFeatured: true,
        createdBy: systemUser._id
      },
      {
        name: 'PhotoRealistic Renderer',
        description: 'Advanced rendering engine for creating stunning photorealistic images and animations',
        developer: 'RenderMax Studio',
        version: '3.2.1',
        category: 'Rendering',
        subcategory: 'Photorealistic',
        tags: ['rendering', 'photorealistic', 'visualization', 'lighting'],
        price: '$149.00',
        isPaid: true,
        rating: 4.8,
        downloads: 12350,
        views: 18750,
        likes: 980,
        features: ['Global Illumination', 'HDR Support', 'Material Editor', 'Batch Rendering'],
        compatibility: ['SketchUp 2021+', 'Windows', 'Mac'],
        fileSize: '45.8 MB',
        screenshots: ['/images/extensions/photorealistic_renderer_1.jpg', '/images/extensions/photorealistic_renderer_2.jpg'],
        downloadUrl: '/downloads/extensions/photorealistic_renderer.rbz',
        backupLinks: [
          '/downloads/extensions/photorealistic_renderer.rbz',
          'https://backup1.3dsketchup.net/extensions/photorealistic_renderer.rbz'
        ],
        source: 'sketchup-extensions',
        extensionInfo: {
          originalUrl: 'https://extensions.sketchup.com/extension/photorealistic-renderer',
          extensionId: 'skext_' + Date.now() + '_002',
          developer: 'RenderMax Studio',
          scrapedAt: new Date()
        },
        status: 'active',
        isVerified: true,
        isFeatured: true,
        createdBy: systemUser._id
      },
      {
        name: 'Architect Tools Pro',
        description: 'Complete architectural design toolkit with floor plans, elevations, and documentation tools',
        developer: 'ArchSoft Solutions',
        version: '2.1.4',
        category: 'Architecture',
        subcategory: 'Residential',
        tags: ['architecture', 'floor-plans', 'elevation', 'documentation'],
        price: '$89.00',
        isPaid: true,
        rating: 4.7,
        downloads: 15420,
        views: 22100,
        likes: 1150,
        features: ['Floor Plans', 'Elevation Views', 'Section Tools', 'Dimension Tools'],
        compatibility: ['SketchUp 2020+', 'Windows', 'Mac'],
        fileSize: '12.5 MB',
        screenshots: ['/images/extensions/architect_tools_1.jpg', '/images/extensions/architect_tools_2.jpg'],
        downloadUrl: '/downloads/extensions/architect_tools_pro.rbz',
        backupLinks: [
          '/downloads/extensions/architect_tools_pro.rbz',
          'https://backup1.3dsketchup.net/extensions/architect_tools_pro.rbz'
        ],
        source: 'sketchup-extensions',
        extensionInfo: {
          originalUrl: 'https://extensions.sketchup.com/extension/architect-tools-pro',
          extensionId: 'skext_' + Date.now() + '_003',
          developer: 'ArchSoft Solutions',
          scrapedAt: new Date()
        },
        status: 'active',
        isVerified: true,
        isFeatured: false,
        createdBy: systemUser._id
      },
      {
        name: 'Building Components Library',
        description: 'Extensive library of architectural components and building elements for quick design',
        developer: 'BuildTech',
        version: '1.8.2',
        category: 'Architecture',
        subcategory: 'Commercial',
        tags: ['components', 'library', 'building', 'architecture'],
        price: 'Free',
        isPaid: false,
        rating: 4.3,
        downloads: 28750,
        views: 45200,
        likes: 2100,
        features: ['Component Library', 'Smart Insertion', 'Custom Materials', 'Quick Access'],
        compatibility: ['SketchUp 2019+', 'Windows', 'Mac'],
        fileSize: '8.2 MB',
        screenshots: ['/images/extensions/building_components_1.jpg', '/images/extensions/building_components_2.jpg'],
        downloadUrl: '/downloads/extensions/building_components_library.rbz',
        backupLinks: [
          '/downloads/extensions/building_components_library.rbz',
          'https://backup1.3dsketchup.net/extensions/building_components_library.rbz'
        ],
        source: 'sketchup-extensions',
        extensionInfo: {
          originalUrl: 'https://extensions.sketchup.com/extension/building-components-library',
          extensionId: 'skext_' + Date.now() + '_004',
          developer: 'BuildTech',
          scrapedAt: new Date()
        },
        status: 'active',
        isVerified: true,
        isFeatured: true,
        createdBy: systemUser._id
      },
      {
        name: 'Quick Render',
        description: 'Fast rendering solution for quick previews and real-time visualization',
        developer: 'SpeedRender',
        version: '2.0.5',
        category: 'Rendering',
        subcategory: 'Real-time',
        tags: ['rendering', 'quick', 'preview', 'real-time'],
        price: '$39.00',
        isPaid: true,
        rating: 4.2,
        downloads: 8920,
        views: 16500,
        likes: 720,
        features: ['Fast Preview', 'Real-time Rendering', 'Simple Interface', 'GPU Acceleration'],
        compatibility: ['SketchUp 2018+', 'Windows', 'Mac'],
        fileSize: '15.3 MB',
        screenshots: ['/images/extensions/quick_render_1.jpg', '/images/extensions/quick_render_2.jpg'],
        downloadUrl: '/downloads/extensions/quick_render.rbz',
        backupLinks: [
          '/downloads/extensions/quick_render.rbz',
          'https://backup1.3dsketchup.net/extensions/quick_render.rbz'
        ],
        source: 'sketchup-extensions',
        extensionInfo: {
          originalUrl: 'https://extensions.sketchup.com/extension/quick-render',
          extensionId: 'skext_' + Date.now() + '_005',
          developer: 'SpeedRender',
          scrapedAt: new Date()
        },
        status: 'active',
        isVerified: true,
        isFeatured: false,
        createdBy: systemUser._id
      },
      {
        name: 'Organic Shapes',
        description: 'Tools for creating organic and curved surfaces with advanced sculpting capabilities',
        developer: 'CurveTech',
        version: '1.5.3',
        category: 'Modeling',
        subcategory: 'Organic',
        tags: ['organic', 'curves', 'sculpting', 'surfaces'],
        price: '$79.00',
        isPaid: true,
        rating: 4.4,
        downloads: 6420,
        views: 12800,
        likes: 580,
        features: ['Curve Creation', 'Surface Lofting', 'Organic Modeling', 'Smooth Surfaces'],
        compatibility: ['SketchUp 2020+', 'Windows', 'Mac'],
        fileSize: '18.7 MB',
        screenshots: ['/images/extensions/organic_shapes_1.jpg', '/images/extensions/organic_shapes_2.jpg'],
        downloadUrl: '/downloads/extensions/organic_shapes.rbz',
        backupLinks: [
          '/downloads/extensions/organic_shapes.rbz',
          'https://backup1.3dsketchup.net/extensions/organic_shapes.rbz'
        ],
        source: 'sketchup-extensions',
        extensionInfo: {
          originalUrl: 'https://extensions.sketchup.com/extension/organic-shapes',
          extensionId: 'skext_' + Date.now() + '_006',
          developer: 'CurveTech',
          scrapedAt: new Date()
        },
        status: 'active',
        isVerified: true,
        isFeatured: false,
        createdBy: systemUser._id
      }
    ];

    // Clear existing extensions
    await Extension.deleteMany({});

    // Insert sample extensions
    const createdExtensions = await Extension.insertMany(sampleExtensions);

    res.status(200).json({
      success: true,
      data: {
        message: `Created ${createdExtensions.length} sample extensions`,
        count: createdExtensions.length,
        extensions: createdExtensions
      }
    });

  } catch (error) {
    console.error('Create samples failed:', error);
    res.status(500).json({
      success: false,
      error: error.message || 'Failed to create samples'
    });
  }
});

export default router;
