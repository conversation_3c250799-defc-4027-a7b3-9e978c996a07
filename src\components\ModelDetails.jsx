import React, { useState } from 'react';
import { motion } from 'framer-motion';
import {
  FiDownload, 
  FiShare2, 
  FiHeart, 
  FiEye, 
  FiStar, 
  FiCalendar, 
  FiTag, 
  FiBox, 
  FiLayers, 
  FiInfo,
  FiChevronDown,
  FiChevronUp,
  FiCheck,
  FiX
} from 'react-icons/fi';
import { Link } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';

/**
 * Enhanced Model Details Component
 * Displays detailed information about a 3D model with improved UI
 */
const ModelDetails = ({
  model, 
  onDownload, 
  onSave, 
  onShare, 
  onExport,
  onAddToCollection,
  isSaved = false
}) => {
  const { currentUser, hasPremiumAccess } = useAuth();
  const [expandedSection, setExpandedSection] = useState('description'; 

  // Toggle section expansion
  const toggleSection = (section) => {
  if (true) {
  setExpandedSection(null);
    } else {
      setExpandedSection(section);
    }
  };

  // Format date
  const formatDate = (dateString) => {
  if (!dateString) return 'Unknown';
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
    year: 'numeric', 
      month: 'long', 
      day: 'numeric' 
    });
  };

  // Check if model is premium and user has access
  const canAccessModel = () => {
    if (!model.isPremium) return true;
    return currentUser && hasPremiumAccess();
  };

  // Section component
  const Section = ({ title, icon, children, id }) => {
  const isExpanded = expandedSection === id;

    return (
      <div className="border-b border-gray-200 dark:border-gray-700 py-4">
        <button
          onClick={() => toggleSection(id)}
          className="flex justify-between items-center w-full text-left"
        >
          <div className="flex items-center text-gray-900 dark:text-white font-medium">
            {icon}
            <span className="ml-2">{title}</span>
          </div>
          {isExpanded ? (
            <FiChevronUp className="text-gray-500 dark:text-gray-400" />
          ) : (
            <FiChevronDown className="text-gray-500 dark:text-gray-400" />
          )}
        </button>

        {isExpanded && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.3 }}
            className="mt-3 text-gray-600 dark:text-gray-300"
          >
            {children}
          </motion.div>
        )}
      </div>
    );
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg overflow-hidden">
      {/* Model header with key information */}
      <div className="p-6 border-b border-gray-200 dark:border-gray-700">
        <div className="flex justify-between items-start">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">{model.title}</h1>
            <div className="flex items-center mt-2 text-sm text-gray-600 dark:text-gray-400">
              <Link 
                to={`/category/${model.category?.toLowerCase()}`}
                className="bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded-md hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
              >
                {model.category}
              </Link>
              {model.subcategory && (
                <Link 
                  to={`/subcategory/${model.subcategory?.toLowerCase()}`}
                  className="ml-2 hover:text-blue-600 dark:hover:text-blue-400"
                >
                  {model.subcategory}
                </Link>
              )}
            </div>
          </div>

          <div className="flex items-center space-x-2">
            {model.isPremium && (
              <span className="bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200 text-xs font-medium px-2.5 py-0.5 rounded">
                Premium
              </span>
            )}
            <div className="flex items-center text-yellow-500">
              <FiStar className="fill-current" />
              <span className="ml-1 text-gray-900 dark:text-white">{model.rating || '4.5'}</span>
            </div>
          </div>
        </div>

        {/* Stats row */}
        <div className="flex flex-wrap gap-4 mt-4 text-sm text-gray-600 dark:text-gray-400">
          <div className="flex items-center">
            <FiDownload className="mr-1" />
            <span>{model.downloads || '234'} downloads</span>
          </div>
          <div className="flex items-center">
            <FiEye className="mr-1" />
            <span>{model.views || '1.2k'} views</span>
          </div>
          <div className="flex items-center">
            <FiCalendar className="mr-1" />
            <span>Added {formatDate(model.dateAdded || model.createdAt)}</span>
          </div>
          <div className="flex items-center">
            <FiTag className="mr-1" />
            <span>{model.license || 'Standard License'}</span>
          </div>
        </div>

        {/* Action buttons */}
        <div className="flex flex-wrap gap-3 mt-6">
          <button
            onClick={onDownload}
            disabled={!canAccessModel()}
            className={`flex-1 flex items-center justify-center px-4 py-2 rounded-md shadow-sm text-sm font-medium transition-colors ${
              canAccessModel()
                ? 'bg-blue-600 text-white hover:bg-blue-700'
                : 'bg-gray-300 text-gray-500 cursor-not-allowed dark:bg-gray-700 dark:text-gray-400'
            }`}
          >
            <FiDownload className="mr-2" />
            {model.isPremium && !canAccessModel() ? 'Premium Model' : 'Download'}
          </button>

          <button
            onClick={() => onSave()}
            className={`flex-1 flex items-center justify-center px-4 py-2 border rounded-md shadow-sm text-sm font-medium transition-colors ${
              isSaved
                ? 'border-red-300 text-red-700 bg-red-50 hover:bg-red-100 dark:bg-red-900 dark:text-red-200 dark:border-red-800 dark:hover:bg-red-800'
                : 'border-gray-300 text-gray-700 bg-white hover:bg-gray-50 dark:bg-gray-700 dark:text-gray-200 dark:border-gray-600 dark:hover:bg-gray-600'
            }`}
          >
            <FiHeart className={`mr-2 ${isSaved ? 'fill-current text-red-500' : '}`} />
            {isSaved ? 'Saved' : 'Save'}
          </button>

          <button
            onClick={() => onAddToCollection()}
            className="flex-1 flex items-center justify-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 dark:bg-gray-700 dark:text-gray-200 dark:border-gray-600 dark:hover:bg-gray-600 transition-colors"
          >
            <FiLayers className="mr-2" />
            Collections
          </button>

          <button
            onClick={() => onShare()}
            className="flex-1 flex items-center justify-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 dark:bg-gray-700 dark:text-gray-200 dark:border-gray-600 dark:hover:bg-gray-600 transition-colors"
          >
            <FiShare2 className="mr-2" />
            Share
          </button>
        </div>
      </div>

      {/* Collapsible sections */}
      <div className="p-6">
        <Section 
          id="description" 
          title="Description" 
          icon={<FiInfo className="w-5 h-5" />}
        >
          <p className="whitespace-pre-line">{model.description || 'No description provided.'}</p>
        </Section>

        <Section 
          id="specifications" 
          title="Specifications" 
          icon={<FiBox className="w-5 h-5" />}
        >
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h4 className="font-medium text-gray-900 dark:text-white mb-2">Technical Details</h4>
              <ul className="space-y-2">
                <li className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-400">Format</span>
                  <span className="font-medium text-gray-900 dark:text-white">{model.format}</span>
                </li>
                <li className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-400">Polygons</span>
                  <span className="font-medium text-gray-900 dark:text-white">{model.polygons || 'N/A'}</span>
                </li>
                <li className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-400">Vertices</span>
                  <span className="font-medium text-gray-900 dark:text-white">{model.vertices || 'N/A'}</span>
                </li>
                <li className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-400">File Size</span>
                  <span className="font-medium text-gray-900 dark:text-white">{model.fileSize || 'N/A'}</span>
                </li>
              </ul>
            </div>

            <div>
              <h4 className="font-medium text-gray-900 dark:text-white mb-2">Features</h4>
              <ul className="space-y-2">
                <li className="flex items-center">
                  {model.textured ? (
                    <FiCheck className="w-5 h-5 text-green-500 mr-2" />
                  ) : (
                    <FiX className="w-5 h-5 text-red-500 mr-2" />
                  )}
                  <span>Textured</span>
                </li>
                <li className="flex items-center">
                  {model.rigged ? (
                    <FiCheck className="w-5 h-5 text-green-500 mr-2" />
                  ) : (
                    <FiX className="w-5 h-5 text-red-500 mr-2" />
                  )}
                  <span>Rigged</span>
                </li>
                <li className="flex items-center">
                  {model.animated ? (
                    <FiCheck className="w-5 h-5 text-green-500 mr-2" />
                  ) : (
                    <FiX className="w-5 h-5 text-red-500 mr-2" />
                  )}
                  <span>Animated</span>
                </li>
                <li className="flex items-center">
                  {model.pbr ? (
                    <FiCheck className="w-5 h-5 text-green-500 mr-2" />
                  ) : (
                    <FiX className="w-5 h-5 text-red-500 mr-2" />
                  )}
                  <span>PBR Materials</span>
                </li>
              </ul>
            </div>
          </div>
        </Section>

        <Section 
          id="license" 
          title="License Information" 
          icon={<FiTag className="w-5 h-5" />}
        >
          <div className="space-y-4">
            <p>{model.licenseDescription || 'This model is available under our standard license terms.'}</p>

            <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-md">
              <h4 className="font-medium text-gray-900 dark:text-white mb-2">Usage Rights</h4>
              <ul className="space-y-2">
                <li className="flex items-center">
                  <FiCheck className="w-5 h-5 text-green-500 mr-2" />
                  <span>Personal projects</span>
                </li>
                {model.isPremium ? (
                  <>
                    <li className="flex items-center">
                      <FiCheck className="w-5 h-5 text-green-500 mr-2" />
                      <span>Commercial projects</span>
                    </li>
                    <li className="flex items-center">
                      <FiCheck className="w-5 h-5 text-green-500 mr-2" />
                      <span>Multiple projects</span>
                    </li>
                  </>
                ) : (
                  <>
                    <li className="flex items-center">
                      <FiX className="w-5 h-5 text-red-500 mr-2" />
                      <span>Commercial projects</span>
                    </li>
                    <li className="flex items-center">
                      <FiX className="w-5 h-5 text-red-500 mr-2" />
                      <span>Multiple projects</span>
                    </li>
                  </>
                )}
              </ul>
            </div>

            <p className="text-sm text-gray-500 dark:text-gray-400">
              For full license details, please refer to our <Link to="/terms" className="text-blue-600 hover:underline dark:text-blue-400">Terms of Use</Link>.
            </p>
          </div>
        </Section>
      </div>

      {/* Export button */}
      <div className="p-6 bg-gray-50 dark:bg-gray-700 border-t border-gray-200 dark:border-gray-600">
        <button
          onClick={() => onExport()}
          className="w-full flex items-center justify-center px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 transition-colors"
        >
          <FiBox className="mr-2" />
          Export Model
        </button>

        <p className="mt-2 text-sm text-gray-500 dark:text-gray-400 text-center">
          Export this model to various formats for use in different applications
        </p>
      </div>
    </div>
  );
};

export default ModelDetails;
