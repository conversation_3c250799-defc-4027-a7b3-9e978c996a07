import mongoose from 'mongoose';
import dotenv from 'dotenv';
import User from './src/models/User.js';

// Load environment variables
dotenv.config();

const createUser = async () => {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('MongoDB Connected');

    // Delete existing admin user
    await User.deleteOne({ email: '<EMAIL>' });
    console.log('Deleted existing admin user');

    // Create new admin user
    const adminUser = new User({
      name: 'Admin',
      email: '<EMAIL>',
      password: 'admin123',
      role: 'admin',
      bio: 'System Administrator',
      subscription: {
        type: 'professional',
        status: 'active'
      }
    });

    await adminUser.save();
    console.log('Admin user created successfully');

    // Test password
    const testUser = await User.findOne({ email: '<EMAIL>' }).select('+password');
    const isMatch = await testUser.matchPassword('admin123');
    console.log('Password test result:', isMatch);

    process.exit(0);
  } catch (error) {
    console.error('Error:', error);
    process.exit(1);
  }
};

createUser();
