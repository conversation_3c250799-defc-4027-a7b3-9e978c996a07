import React from 'react';
import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import { FiHome as HomeIcon } from 'react-icons/fi';

const NotFound = () => {
  return (
    <div className="min-h-screen bg-gray-100 dark:bg-gray-900 flex flex-col justify-center items-center px-4 py-12">
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="text-center"
      >
        <h1 className="text-9xl font-bold text-blue-600 dark:text-blue-500">404</h1>
        <h2 className="text-4xl font-bold text-gray-800 dark:text-white mt-4">Page Not Found</h2>
        <p className="text-lg text-gray-600 dark:text-gray-300 mt-4 max-w-md mx-auto">
          The page you are looking for might have been removed, had its name changed, or is temporarily unavailable.
        </p>

        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.3, duration: 0.5 }}
          className="mt-8"
        >
          <Link
            to="/"
            className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
          >
            <HomeIcon className="h-5 w-5 mr-2" />
            Back to Home
          </Link>
        </motion.div>

        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.5, duration: 0.5 }}
          className="mt-12"
        >
          <p className="text-gray-500 dark:text-gray-400">
            Looking for something specific? Try one of these popular categories:
          </p>
          <div className="flex flex-wrap justify-center gap-3 mt-4">
            <Link
              to="/category/interior"
              className="px-4 py-2 bg-white dark:bg-gray-800 rounded-full text-sm text-gray-700 dark:text-gray-300 shadow hover:shadow-md transition-shadow"
            >
              Interior Scenes
            </Link>
            <Link
              to="/category/exterior"
              className="px-4 py-2 bg-white dark:bg-gray-800 rounded-full text-sm text-gray-700 dark:text-gray-300 shadow hover:shadow-md transition-shadow"
            >
              Exterior Scenes
            </Link>
            <Link
              to="/category/landscape"
              className="px-4 py-2 bg-white dark:bg-gray-800 rounded-full text-sm text-gray-700 dark:text-gray-300 shadow hover:shadow-md transition-shadow"
            >
              Landscape/Garden
            </Link>
            <Link
              to="/category/models"
              className="px-4 py-2 bg-white dark:bg-gray-800 rounded-full text-sm text-gray-700 dark:text-gray-300 shadow hover:shadow-md transition-shadow"
            >
              Models/Objects
            </Link>
          </div>
        </motion.div>
      </motion.div>
    </div>
  );
};

export default NotFound;
