import React, { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Mic, MicOff, Volume2, VolumeX, Brain, Sparkles, 
  Camera, FileText, Search, Download, Star, Zap,
  MessageCircle, Image, Code, Palette, Settings
} from 'lucide-react';

const SmartAIAssistant = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [isListening, setIsListening] = useState(false);
  const [isSpeaking, setIsSpeaking] = useState(false);
  const [messages, setMessages] = useState([]);
  const [inputText, setInputText] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const [aiMode, setAiMode] = useState('general'); // general, design, code, search
  const [voiceEnabled, setVoiceEnabled] = useState(true);
  const [language, setLanguage] = useState('vi-VN');
  const [aiPersonality, setAiPersonality] = useState('friendly');
  
  const messagesEndRef = useRef(null);
  const recognitionRef = useRef(null);
  const synthRef = useRef(null);

  // AI Modes với các chức năng đặc biệt
  const aiModes = {
    general: {
      name: 'Trợ lý chung',
      icon: Brain,
      color: 'from-blue-500 to-purple-600',
      description: 'Hỗ trợ tổng quát về website và 3D models'
    },
    design: {
      name: 'Chuyên gia thiết kế',
      icon: Palette,
      color: 'from-pink-500 to-rose-600',
      description: 'Tư vấn thiết kế 3D và kiến trúc'
    },
    code: {
      name: 'Lập trình viên',
      icon: Code,
      color: 'from-green-500 to-emerald-600',
      description: 'Hỗ trợ code và kỹ thuật'
    },
    search: {
      name: 'Tìm kiếm thông minh',
      icon: Search,
      color: 'from-orange-500 to-amber-600',
      description: 'Tìm kiếm models và extensions'
    }
  };

  // AI Personalities
  const personalities = {
    friendly: { name: 'Thân thiện', emoji: '😊' },
    professional: { name: 'Chuyên nghiệp', emoji: '🤝' },
    creative: { name: 'Sáng tạo', emoji: '🎨' },
    technical: { name: 'Kỹ thuật', emoji: '🔧' }
  };

  // Quick Actions
  const quickActions = [
    { 
      id: 'find-models', 
      text: 'Tìm models phù hợp', 
      icon: Search,
      prompt: 'Hãy giúp tôi tìm những models 3D phù hợp với dự án của tôi'
    },
    { 
      id: 'design-tips', 
      text: 'Lời khuyên thiết kế', 
      icon: Sparkles,
      prompt: 'Cho tôi một số lời khuyên thiết kế 3D hiện đại'
    },
    { 
      id: 'technical-help', 
      text: 'Hỗ trợ kỹ thuật', 
      icon: Settings,
      prompt: 'Tôi cần hỗ trợ kỹ thuật về SketchUp'
    },
    { 
      id: 'inspiration', 
      text: 'Ý tưởng sáng tạo', 
      icon: Star,
      prompt: 'Hãy cho tôi một số ý tưởng sáng tạo cho dự án thiết kế'
    }
  ];

  useEffect(() => {
    // Initialize speech recognition
    if ('webkitSpeechRecognition' in window) {
      recognitionRef.current = new window.webkitSpeechRecognition();
      recognitionRef.current.continuous = false;
      recognitionRef.current.interimResults = false;
      recognitionRef.current.lang = language;

      recognitionRef.current.onresult = (event) => {
        const transcript = event.results[0][0].transcript;
        setInputText(transcript);
        handleSendMessage(transcript);
      };

      recognitionRef.current.onend = () => {
        setIsListening(false);
      };
    }

    // Initialize speech synthesis
    synthRef.current = window.speechSynthesis;

    return () => {
      if (recognitionRef.current) {
        recognitionRef.current.stop();
      }
      if (synthRef.current) {
        synthRef.current.cancel();
      }
    };
  }, [language]);

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const startListening = () => {
    if (recognitionRef.current && !isListening) {
      setIsListening(true);
      recognitionRef.current.start();
    }
  };

  const stopListening = () => {
    if (recognitionRef.current && isListening) {
      recognitionRef.current.stop();
      setIsListening(false);
    }
  };

  const speak = (text) => {
    if (!voiceEnabled || !synthRef.current) return;

    synthRef.current.cancel();
    
    // Clean text for speech
    const cleanText = text
      .replace(/[*#`]/g, '')
      .replace(/\n/g, ' ')
      .replace(/\s+/g, ' ')
      .trim();

    const utterance = new SpeechSynthesisUtterance(cleanText);
    utterance.lang = language;
    utterance.rate = 0.9;
    utterance.pitch = 1;
    
    utterance.onstart = () => setIsSpeaking(true);
    utterance.onend = () => setIsSpeaking(false);
    
    synthRef.current.speak(utterance);
  };

  const handleSendMessage = async (text = inputText) => {
    if (!text.trim()) return;

    const userMessage = {
      id: Date.now(),
      type: 'user',
      content: text,
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setInputText('');
    setIsTyping(true);

    try {
      // Simulate AI response with enhanced context
      const aiResponse = await generateAIResponse(text, aiMode, aiPersonality);
      
      const aiMessage = {
        id: Date.now() + 1,
        type: 'ai',
        content: aiResponse,
        timestamp: new Date(),
        mode: aiMode,
        personality: aiPersonality
      };

      setMessages(prev => [...prev, aiMessage]);
      
      if (voiceEnabled) {
        speak(aiResponse);
      }
    } catch (error) {
      console.error('AI Response Error:', error);
    } finally {
      setIsTyping(false);
    }
  };

  const generateAIResponse = async (text, mode, personality) => {
    // Enhanced AI response generation based on mode and personality
    const responses = {
      general: {
        friendly: `Xin chào! Tôi rất vui được hỗ trợ bạn. Về "${text}", tôi có thể giúp bạn tìm hiểu thêm về các models 3D phù hợp và cách sử dụng chúng hiệu quả nhất.`,
        professional: `Cảm ơn bạn đã liên hệ. Dựa trên yêu cầu "${text}", tôi sẽ cung cấp thông tin chuyên nghiệp và chi tiết để hỗ trợ dự án của bạn.`,
        creative: `Thật tuyệt vời! "${text}" mở ra nhiều khả năng sáng tạo. Hãy cùng khám phá những ý tưởng độc đáo và cách thực hiện chúng.`,
        technical: `Phân tích kỹ thuật cho "${text}": Tôi sẽ cung cấp hướng dẫn chi tiết và giải pháp kỹ thuật cụ thể.`
      },
      design: {
        friendly: `Tôi thích ý tưởng thiết kế của bạn! Về "${text}", có nhiều cách tiếp cận sáng tạo mà chúng ta có thể thử.`,
        professional: `Từ góc độ thiết kế chuyên nghiệp, "${text}" có thể được thực hiện với các nguyên tắc thiết kế hiện đại.`,
        creative: `Wow! "${text}" có tiềm năng sáng tạo tuyệt vời. Hãy cùng tạo ra điều gì đó độc đáo!`,
        technical: `Phân tích thiết kế kỹ thuật cho "${text}": Cần xem xét các yếu tố về tỷ lệ, vật liệu và khả năng thực hiện.`
      }
    };

    return responses[mode]?.[personality] || responses.general.friendly;
  };

  const handleQuickAction = (action) => {
    handleSendMessage(action.prompt);
  };

  return (
    <>
      {/* AI Assistant Toggle Button */}
      <motion.button
        onClick={() => setIsOpen(true)}
        className="fixed bottom-6 right-6 z-50 w-16 h-16 bg-gradient-to-r from-purple-600 to-blue-600 rounded-full shadow-2xl flex items-center justify-center text-white hover:scale-110 transition-all duration-300"
        whileHover={{ scale: 1.1 }}
        whileTap={{ scale: 0.95 }}
        animate={{
          boxShadow: [
            '0 0 20px rgba(147, 51, 234, 0.3)',
            '0 0 40px rgba(147, 51, 234, 0.6)',
            '0 0 20px rgba(147, 51, 234, 0.3)'
          ]
        }}
        transition={{ duration: 2, repeat: Infinity }}
      >
        <Brain className="w-8 h-8" />
      </motion.button>

      {/* AI Assistant Modal */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/50 backdrop-blur-sm"
            onClick={() => setIsOpen(false)}
          >
            <motion.div
              initial={{ scale: 0.8, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.8, opacity: 0 }}
              className="w-full max-w-4xl h-[80vh] bg-white dark:bg-gray-900 rounded-3xl shadow-2xl overflow-hidden"
              onClick={(e) => e.stopPropagation()}
            >
              {/* Header */}
              <div className="p-6 border-b border-gray-200 dark:border-gray-700 bg-gradient-to-r from-purple-600 to-blue-600 text-white">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <div className="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center">
                      <Brain className="w-6 h-6" />
                    </div>
                    <div>
                      <h2 className="text-xl font-bold">Smart AI Assistant</h2>
                      <p className="text-purple-100">Trợ lý AI thông minh cho 3DSKETCHUP.NET</p>
                    </div>
                  </div>
                  <button
                    onClick={() => setIsOpen(false)}
                    className="w-8 h-8 bg-white/20 rounded-full flex items-center justify-center hover:bg-white/30 transition-colors"
                  >
                    ×
                  </button>
                </div>

                {/* AI Mode Selector */}
                <div className="mt-4 flex space-x-2 overflow-x-auto">
                  {Object.entries(aiModes).map(([key, mode]) => {
                    const IconComponent = mode.icon;
                    return (
                      <button
                        key={key}
                        onClick={() => setAiMode(key)}
                        className={`flex items-center space-x-2 px-4 py-2 rounded-full whitespace-nowrap transition-all ${
                          aiMode === key 
                            ? 'bg-white text-purple-600' 
                            : 'bg-white/20 text-white hover:bg-white/30'
                        }`}
                      >
                        <IconComponent className="w-4 h-4" />
                        <span className="text-sm">{mode.name}</span>
                      </button>
                    );
                  })}
                </div>
              </div>

              {/* Chat Area */}
              <div className="flex-1 flex flex-col h-full">
                {/* Messages */}
                <div className="flex-1 overflow-y-auto p-6 space-y-4">
                  {messages.length === 0 && (
                    <div className="text-center py-12">
                      <div className="w-20 h-20 bg-gradient-to-r from-purple-600 to-blue-600 rounded-full flex items-center justify-center mx-auto mb-4">
                        <Sparkles className="w-10 h-10 text-white" />
                      </div>
                      <h3 className="text-xl font-bold text-gray-800 dark:text-white mb-2">
                        Xin chào! Tôi là AI Assistant
                      </h3>
                      <p className="text-gray-600 dark:text-gray-400 mb-6">
                        Tôi có thể giúp bạn tìm models, tư vấn thiết kế, và hỗ trợ kỹ thuật
                      </p>
                      
                      {/* Quick Actions */}
                      <div className="grid grid-cols-2 gap-3 max-w-md mx-auto">
                        {quickActions.map((action) => {
                          const IconComponent = action.icon;
                          return (
                            <button
                              key={action.id}
                              onClick={() => handleQuickAction(action)}
                              className="flex items-center space-x-2 p-3 bg-gray-100 dark:bg-gray-800 rounded-xl hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors text-left"
                            >
                              <IconComponent className="w-5 h-5 text-purple-600" />
                              <span className="text-sm font-medium">{action.text}</span>
                            </button>
                          );
                        })}
                      </div>
                    </div>
                  )}

                  {messages.map((message) => (
                    <motion.div
                      key={message.id}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}
                    >
                      <div className={`max-w-[70%] p-4 rounded-2xl ${
                        message.type === 'user'
                          ? 'bg-purple-600 text-white'
                          : 'bg-gray-100 dark:bg-gray-800 text-gray-800 dark:text-white'
                      }`}>
                        <p>{message.content}</p>
                        <div className="text-xs opacity-70 mt-2">
                          {message.timestamp.toLocaleTimeString()}
                        </div>
                      </div>
                    </motion.div>
                  ))}

                  {isTyping && (
                    <motion.div
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      className="flex justify-start"
                    >
                      <div className="bg-gray-100 dark:bg-gray-800 p-4 rounded-2xl">
                        <div className="flex space-x-1">
                          <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                          <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                          <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                        </div>
                      </div>
                    </motion.div>
                  )}
                  
                  <div ref={messagesEndRef} />
                </div>

                {/* Input Area */}
                <div className="p-6 border-t border-gray-200 dark:border-gray-700">
                  <div className="flex items-center space-x-4">
                    <div className="flex-1 relative">
                      <input
                        type="text"
                        value={inputText}
                        onChange={(e) => setInputText(e.target.value)}
                        onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
                        placeholder="Nhập tin nhắn hoặc sử dụng giọng nói..."
                        className="w-full px-4 py-3 pr-12 bg-gray-100 dark:bg-gray-800 rounded-2xl focus:outline-none focus:ring-2 focus:ring-purple-600"
                      />
                      <button
                        onClick={isListening ? stopListening : startListening}
                        className={`absolute right-3 top-1/2 transform -translate-y-1/2 p-2 rounded-full transition-colors ${
                          isListening 
                            ? 'bg-red-500 text-white animate-pulse' 
                            : 'bg-gray-300 dark:bg-gray-600 text-gray-600 dark:text-gray-300 hover:bg-gray-400 dark:hover:bg-gray-500'
                        }`}
                      >
                        {isListening ? <MicOff className="w-4 h-4" /> : <Mic className="w-4 h-4" />}
                      </button>
                    </div>
                    
                    <button
                      onClick={() => handleSendMessage()}
                      disabled={!inputText.trim()}
                      className="px-6 py-3 bg-purple-600 text-white rounded-2xl hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                    >
                      Gửi
                    </button>
                    
                    <button
                      onClick={() => setVoiceEnabled(!voiceEnabled)}
                      className={`p-3 rounded-2xl transition-colors ${
                        voiceEnabled 
                          ? 'bg-green-500 text-white' 
                          : 'bg-gray-300 dark:bg-gray-600 text-gray-600 dark:text-gray-300'
                      }`}
                    >
                      {voiceEnabled ? <Volume2 className="w-5 h-5" /> : <VolumeX className="w-5 h-5" />}
                    </button>
                  </div>

                  {/* Settings */}
                  <div className="mt-4 flex items-center justify-between text-sm">
                    <div className="flex items-center space-x-4">
                      <select
                        value={language}
                        onChange={(e) => setLanguage(e.target.value)}
                        className="px-3 py-1 bg-gray-100 dark:bg-gray-800 rounded-lg"
                      >
                        <option value="vi-VN">Tiếng Việt</option>
                        <option value="en-US">English</option>
                      </select>
                      
                      <select
                        value={aiPersonality}
                        onChange={(e) => setAiPersonality(e.target.value)}
                        className="px-3 py-1 bg-gray-100 dark:bg-gray-800 rounded-lg"
                      >
                        {Object.entries(personalities).map(([key, personality]) => (
                          <option key={key} value={key}>
                            {personality.emoji} {personality.name}
                          </option>
                        ))}
                      </select>
                    </div>
                    
                    <div className="text-gray-500 dark:text-gray-400">
                      Mode: {aiModes[aiMode].name}
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
};

export default SmartAIAssistant;
