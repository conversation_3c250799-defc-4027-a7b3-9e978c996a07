import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { toast } from 'react-hot-toast';
import {
  FiFolder, FiPlus, FiEdit2, FiTrash2, FiSave, FiX,
  FiHome, FiBuilding, FiTree, FiBox, FiMoreHorizontal
} from 'react-icons/fi';
import { adminAPI } from '../../utils/api';

const ModelCategories = () => {
  const [loading, setLoading] = useState(true);
  const [categories, setCategories] = useState([]);
  const [editingCategory, setEditingCategory] = useState(null);
  const [showAddForm, setShowAddForm] = useState(false);
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    subcategories: []
  });

  // Default categories with icons and subcategories
  const defaultCategories = [
    {
      id: 'residential',
      name: 'Residential',
      description: 'Residential buildings and interiors',
      icon: <FiHome className="h-5 w-5" />,
      color: 'blue',
      subcategories: ['Living Room', 'Kitchen', 'Bedroom', 'Bathroom', 'Office', 'Apartment'],
      modelCount: 0
    },
    {
      id: 'commercial',
      name: 'Commercial',
      description: 'Commercial buildings and spaces',
      icon: <FiBuilding className="h-5 w-5" />,
      color: 'green',
      subcategories: ['Restaurant', 'Hotel', 'Office Building', 'Retail', 'Shopping Mall'],
      modelCount: 0
    },
    {
      id: 'exterior',
      name: 'Exterior',
      description: 'Exterior architecture and facades',
      icon: <FiBuilding className="h-5 w-5" />,
      color: 'purple',
      subcategories: ['House', 'Building', 'Facade', 'Architecture'],
      modelCount: 0
    },
    {
      id: 'landscape',
      name: 'Landscape/Garden',
      description: 'Outdoor spaces and landscaping',
      icon: <FiTree className="h-5 w-5" />,
      color: 'green',
      subcategories: ['Garden', 'Park', 'Trees', 'Plants', 'Outdoor Furniture', 'Water Features'],
      modelCount: 0
    },
    {
      id: 'furniture',
      name: 'Furniture',
      description: 'Indoor furniture and fixtures',
      icon: <FiBox className="h-5 w-5" />,
      color: 'amber',
      subcategories: ['Chairs', 'Tables', 'Sofas', 'Beds', 'Storage', 'Lighting'],
      modelCount: 0
    },
    {
      id: 'plants',
      name: 'Flower/Shrub/Bush',
      description: 'Plants, flowers, and vegetation',
      icon: <FiTree className="h-5 w-5" />,
      color: 'emerald',
      subcategories: ['Flowers', 'Shrubs', 'Bushes', 'Plants', 'Trees'],
      modelCount: 0
    },
    {
      id: 'other',
      name: 'Other',
      description: 'Miscellaneous models',
      icon: <FiMoreHorizontal className="h-5 w-5" />,
      color: 'gray',
      subcategories: ['Miscellaneous', 'Custom', 'Special'],
      modelCount: 0
    }
  ];

  useEffect(() => {
    fetchCategories();
  }, []);

  const fetchCategories = async () => {
    try {
      setLoading(true);

      // Get models to count by category
      const response = await adminAPI.getModels({ limit: 1000 });
      const models = response.data.data.data || [];

      // Count models by category
      const categoryCounts = models.reduce((acc, model) => {
        const category = model.category?.toLowerCase() || 'other';
        acc[category] = (acc[category] || 0) + 1;
        return acc;
      }, {});

      // Update categories with model counts
      const categoriesWithCounts = defaultCategories.map(category => ({
        ...category,
        modelCount: categoryCounts[category.id] || 0
      }));

      setCategories(categoriesWithCounts);
    } catch (error) {
      console.error('Error fetching categories:', error);
      toast.error('Failed to load categories');
      setCategories(defaultCategories);
    } finally {
      setLoading(false);
    }
  };

  const handleEditCategory = (category) => {
    setEditingCategory(category.id);
    setFormData({
      name: category.name,
      description: category.description,
      subcategories: [...category.subcategories]
    });
  };

  const handleSaveCategory = async () => {
    try {
      // In a real app, you would save to backend
      toast.success('Category updated successfully!');

      // Update local state
      setCategories(prev => prev.map(category =>
        category.id === editingCategory
          ? { ...category, ...formData }
          : category
      ));

      setEditingCategory(null);
      setFormData({ name: '', description: '', subcategories: [] });
    } catch (error) {
      console.error('Error updating category:', error);
      toast.error('Failed to update category');
    }
  };

  const handleAddCategory = async () => {
    try {
      if (!formData.name.trim()) {
        toast.error('Category name is required');
        return;
      }

      const newCategory = {
        id: formData.name.toLowerCase().replace(/\s+/g, '-'),
        name: formData.name,
        description: formData.description,
        icon: <FiFolder className="h-5 w-5" />,
        color: 'blue',
        subcategories: formData.subcategories,
        modelCount: 0
      };

      setCategories(prev => [...prev, newCategory]);
      setShowAddForm(false);
      setFormData({ name: '', description: '', subcategories: [] });
      toast.success('Category added successfully!');
    } catch (error) {
      console.error('Error adding category:', error);
      toast.error('Failed to add category');
    }
  };

  const handleDeleteCategory = async (categoryId) => {
    if (!window.confirm('Are you sure you want to delete this category?')) {
      return;
    }

    try {
      setCategories(prev => prev.filter(cat => cat.id !== categoryId));
      toast.success('Category deleted successfully!');
    } catch (error) {
      console.error('Error deleting category:', error);
      toast.error('Failed to delete category');
    }
  };

  const handleCancelEdit = () => {
    setEditingCategory(null);
    setShowAddForm(false);
    setFormData({ name: '', description: '', subcategories: [] });
  };

  const addSubcategory = (subcategory) => {
    if (subcategory.trim() && !formData.subcategories.includes(subcategory.trim())) {
      setFormData(prev => ({
        ...prev,
        subcategories: [...prev.subcategories, subcategory.trim()]
      }));
    }
  };

  const removeSubcategory = (index) => {
    setFormData(prev => ({
      ...prev,
      subcategories: prev.subcategories.filter((_, i) => i !== index)
    }));
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-96">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Model Categories</h1>
          <p className="text-gray-600 dark:text-gray-400">Manage model categories and subcategories</p>
        </div>
        <button
          onClick={() => setShowAddForm(true)}
          className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 flex items-center"
        >
          <FiPlus className="h-4 w-4 mr-2" />
          Add Category
        </button>
      </div>

      {/* Add Category Form */}
      {showAddForm && (
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6"
        >
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Add New Category</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Category Name
              </label>
              <input
                type="text"
                value={formData.name}
                onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                placeholder="Enter category name"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Description
              </label>
              <input
                type="text"
                value={formData.description}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                placeholder="Enter description"
              />
            </div>
          </div>
          <div className="flex justify-end space-x-2 mt-4">
            <button
              onClick={handleCancelEdit}
              className="px-3 py-1 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200"
            >
              Cancel
            </button>
            <button
              onClick={handleAddCategory}
              className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 flex items-center space-x-1"
            >
              <FiSave className="h-4 w-4" />
              <span>Add Category</span>
            </button>
          </div>
        </motion.div>
      )}

      {/* Categories Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {categories.map((category) => (
          <motion.div
            key={category.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden"
          >
            {/* Category Header */}
            <div className={`p-4 bg-${category.color}-50 dark:bg-${category.color}-900/20 border-b border-${category.color}-200 dark:border-${category.color}-800`}>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className={`p-2 bg-${category.color}-100 dark:bg-${category.color}-900/40 rounded-lg text-${category.color}-600 dark:text-${category.color}-400`}>
                    {category.icon}
                  </div>
                  <div>
                    <h3 className={`font-semibold text-${category.color}-900 dark:text-${category.color}-100`}>
                      {category.name}
                    </h3>
                    <p className={`text-sm text-${category.color}-600 dark:text-${category.color}-400`}>
                      {category.modelCount} models
                    </p>
                  </div>
                </div>
                <div className="flex space-x-1">
                  <button
                    onClick={() => handleEditCategory(category)}
                    className={`p-2 text-${category.color}-600 dark:text-${category.color}-400 hover:bg-${category.color}-100 dark:hover:bg-${category.color}-900/40 rounded-lg transition-colors`}
                  >
                    <FiEdit2 className="h-4 w-4" />
                  </button>
                  <button
                    onClick={() => handleDeleteCategory(category.id)}
                    className="p-2 text-red-600 dark:text-red-400 hover:bg-red-100 dark:hover:bg-red-900/40 rounded-lg transition-colors"
                  >
                    <FiTrash2 className="h-4 w-4" />
                  </button>
                </div>
              </div>
              <p className={`mt-2 text-sm text-${category.color}-700 dark:text-${category.color}-300`}>
                {category.description}
              </p>
            </div>

            {/* Category Content */}
            <div className="p-4">
              {editingCategory === category.id ? (
                // Edit Mode
                <div className="space-y-3">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Name
                    </label>
                    <input
                      type="text"
                      value={formData.name}
                      onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                      className="w-full px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Description
                    </label>
                    <input
                      type="text"
                      value={formData.description}
                      onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                      className="w-full px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                    />
                  </div>
                  <div className="flex justify-end space-x-2 pt-2 border-t border-gray-200 dark:border-gray-700">
                    <button
                      onClick={handleCancelEdit}
                      className="px-2 py-1 text-sm text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200"
                    >
                      <FiX className="h-4 w-4" />
                    </button>
                    <button
                      onClick={handleSaveCategory}
                      className="px-2 py-1 text-sm bg-blue-600 text-white rounded hover:bg-blue-700 flex items-center space-x-1"
                    >
                      <FiSave className="h-4 w-4" />
                    </button>
                  </div>
                </div>
              ) : (
                // View Mode
                <div>
                  <h4 className="font-medium text-gray-900 dark:text-white mb-2">Subcategories</h4>
                  <div className="space-y-1">
                    {category.subcategories.map((subcategory, index) => (
                      <div key={index} className="text-sm text-gray-600 dark:text-gray-400 bg-gray-50 dark:bg-gray-700 px-2 py-1 rounded">
                        {subcategory}
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </motion.div>
        ))}
      </div>
    </div>
  );
};

export default ModelCategories;
