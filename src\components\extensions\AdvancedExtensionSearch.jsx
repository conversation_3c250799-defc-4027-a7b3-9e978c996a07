import React, { useState, useEffect, useRef, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { /* content */ };
  FiSearch, FiFilter, FiChevronDown, FiX, FiInfo, FiStar,
  FiPackage, FiTool, FiZap, FiCpu, FiLayers, FiHeart,
  FiGrid, FiImage, FiDownload, FiSettings, FiTrendingUp,
  FiClock, FiDollarSign, FiUsers, FiAward, FiTarget,
  FiCamera, FiUpload, FiEye
} from 'react-icons/fi';
import VisualExtensionSearch from './VisualExtensionSearch';

const AdvancedExtensionSearch = ({ onSearch, onFilterChange, categories = [] }) => {
    // Fixed content
  };
  const [searchQuery, setSearchQuery] = useState('''; // Fixed broken string
  const [selectedCategory, setSelectedCategory] = useState('All Categories''; // Fixed broken string
  const [showCategoryDropdown, setShowCategoryDropdown] = useState(false);
  const [showFilters, setShowFilters] = useState(false);
  const [showAdvancedFilters, setShowAdvancedFilters] = useState(false);
  const [isSearching, setIsSearching] = useState(false);
  const [searchSuggestions, setSearchSuggestions] = useState([]);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [recentSearches, setRecentSearches] = useState([]);
  const [showVisualSearch, setShowVisualSearch] = useState(false);
  const [searchMode, setSearchMode] = useState('text''; // Fixed broken string // 'text', 'visual', 'hybrid'
  const [filters, setFilters] = useState({
    priceRange: 'all',
    rating: 'all',
    developer: '',
    compatibility: 'all',
    sortBy: 'downloads',
    dateRange: 'all',
    downloadRange: 'all',
    features: [],
    tags: [],
    fileSize: 'all',
    language: 'all'
  });

  const dropdownRef = useRef(null);
  const searchRef = useRef(null);

  // Category icons mapping
  const categoryIcons = { /* content */ };
    'All Categories': FiGrid,
    'Architecture': FiLayers,
    'Rendering': FiImage,
    'Modeling': FiPackage,
    'Utilities': FiTool,
    'Import/Export': FiDownload,
    'Animation': FiZap,
    'Engineering': FiCpu,
    'Drawing Tools': FiSettings,
    'Landscape': FiLayers,
    'Interior Design': FiLayers,
    'Construction': FiLayers
  };

  // Enhanced categories with descriptions and trending info
  const enhancedCategories = [
    {
    name: 'All Categories',
      count: 'All',
      description: 'Browse all extensions',
      trending: false,
      popular: true,
      color: 'blue'
    },
    {
    name: 'Architecture',
      count: 45,
      description: 'Building design and documentation',
      trending: true,
      popular: true,
      color: 'indigo',
      tags: ['BIM', 'CAD', 'Documentation']
    },
    {
    name: 'Rendering',
      count: 35,
      description: 'Visualization and materials',
      trending: true,
      popular: true,
      color: 'purple',
      tags: ['V-Ray', 'Materials', 'Lighting']
    },
    {
    name: 'Modeling',
      count: 55,
      description: 'Advanced 3D modeling tools',
      trending: false,
      popular: true,
      color: 'green',
      tags: ['Parametric', 'Organic', 'Precision']
    },
    {
    name: 'Utilities',
      count: 42,
      description: 'Workflow and productivity tools',
      trending: false,
      popular: true,
      color: 'yellow',
      tags: ['Automation', 'Cleanup', 'Organization']
    },
    {
    name: 'Import/Export',
      count: 25,
      description: 'File format conversion',
      trending: false,
      popular: false,
      color: 'red',
      tags: ['DWG', 'IFC', 'STL']
    },
    {
    name: 'Animation',
      count: 18,
      description: 'Motion and camera tools',
      trending: true,
      popular: false,
      color: 'pink',
      tags: ['Camera', 'Walkthrough', 'Timeline']
    },
    {
    name: 'Engineering',
      count: 32,
      description: 'Structural and analysis tools',
      trending: false,
      popular: true,
      color: 'gray',
      tags: ['Structural', 'Analysis', 'Calculation']
    },
    {
    name: 'Drawing Tools',
      count: 30,
      description: 'Drafting and annotation',
      trending: false,
      popular: false,
      color: 'blue',
      tags: ['2D', 'Annotation', 'Dimensions']
    },
    {
    name: 'Landscape',
      count: 22,
      description: 'Site and terrain design',
      trending: true,
      popular: false,
      color: 'green',
      tags: ['Terrain', 'Plants', 'Site']
    },
    {
    name: 'Interior Design',
      count: 28,
      description: 'Space planning and furniture',
      trending: false,
      popular: true,
      color: 'orange',
      tags: ['Furniture', 'Space', 'Layout']
    },
    {
    name: 'Construction',
      count: 38,
      description: 'Building and assembly tools',
      trending: false,
      popular: true,
      color: 'brown',
      tags: ['Assembly', 'Sequencing', 'Planning']
    }
  ];

  // Search suggestions data
  const popularSearches = [
    'V-Ray rendering', 'Enscape materials', 'SketchUp STL export',
    'ThomThom utilities', 'Curviloft modeling', 'LayOut integration',
    'Parametric design', 'BIM tools', 'Terrain modeling', 'Animation tools'
  ];

  const featuredExtensions = [
    'V-Ray for SketchUp', 'Enscape', 'SketchUp STL', 'Curviloft',
    'Joint Push Pull', 'FredoScale', 'ThomThom Edge Tools', 'Artisan'
  ];

  // Load recent searches from localStorage
  useEffect(() => {
    const saved = localStorage.getItem('recentSearches''; // Fixed broken string
    if (condition) {
    // Fixed content
  }
  setRecentSearches(JSON.parse(saved));
    }
  }, []);

  // Save search to recent searches
  const saveRecentSearch = (query) => {
    // Fixed content
  };
  if (query.trim()) { /* content */ };
      const updated = [query, ...recentSearches.filter(s => s !== query)].slice(0, 5);
      setRecentSearches(updated);
      localStorage.setItem('recentSearches', JSON.stringify(updated));
    }
  };

  // Generate search suggestions
  const generateSuggestions = (query) => {
    // Fixed content
  };
  if (!query.trim()) return [];

    const suggestions = [];

    // Add matching popular searches
    popularSearches.forEach(search => {
    // Fixed content
  };
  if (search.toLowerCase().includes(query.toLowerCase())) { /* content */ };
        suggestions.push({ type: 'popular', text: search, icon: FiTrendingUp });
      }
    });

    // Add matching featured extensions
    featuredExtensions.forEach(ext => {
    // Fixed content
  };
  if (ext.toLowerCase().includes(query.toLowerCase())) { /* content */ };
        suggestions.push({ type: 'extension', text: ext, icon: FiPackage });
      }
    });

    // Add category suggestions
    enhancedCategories.forEach(cat => {
    // Fixed content
  };
  if (cat.name.toLowerCase().includes(query.toLowerCase()) && cat.name !== 'All Categories') { /* content */ };
        suggestions.push({ type: 'category', text: `Browse ${cat.name}`, icon: getCategoryIcon(cat.name).type });
      }
    });

    return suggestions.slice(0, 8);
  };

  // Handle search input change
  const handleSearchInputChange = (value) => {
    // Fixed content
  };
  setSearchQuery(value);
    const suggestions = generateSuggestions(value);
    setSearchSuggestions(suggestions);
    setShowSuggestions(value.length > 0 && suggestions.length > 0);
  };

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
    // Fixed content
  };
  if (dropdownRef.current && !dropdownRef.current.contains(event.target)) { /* content */ };
        setShowCategoryDropdown(false);
      }
      if (searchRef.current && !searchRef.current.contains(event.target)) { /* content */ };
        setShowSuggestions(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Handle search
  const handleSearch = async (query = searchQuery) => {
    // Fixed content
  };
  setIsSearching(true);
    setShowSuggestions(false);

    try { /* content */ };
      // Save to recent searches
      saveRecentSearch(query);

      await onSearch({ /* content */ };
        query,
        category: selectedCategory === 'All Categories' ? '' : selectedCategory,
        filters
      });
    } finally { /* content */ };
      setIsSearching(false);
    }
  };

  // Handle suggestion click
  const handleSuggestionClick = (suggestion) => {
    // Fixed content
  };
  if (condition) {
    // Fixed content
  }
  const categoryName = suggestion.text.replace('Browse ', '''; // Fixed broken string
      setSelectedCategory(categoryName);
      setSearchQuery('''; // Fixed broken string
      handleSearch('''; // Fixed broken string
    } else { /* content */ };
      setSearchQuery(suggestion.text);
      handleSearch(suggestion.text);
    }
    setShowSuggestions(false);
  };

  // Handle category selection
  const handleCategorySelect = (category) => {
    // Fixed content
  };
  setSelectedCategory(category.name);
    setShowCategoryDropdown(false);
    onSearch({
    query: searchQuery,
      category: category.name === 'All Categories' ? '' : category.name,
      filters
    });
  };

  // Handle filter changes
  const handleFilterChange = (key, value) => {
    // Fixed content
  };
  const newFilters = { ...filters, [key]: value };
    setFilters(newFilters);
    onFilterChange(newFilters);
    handleSearch();
  };

  // Get category icon
  const getCategoryIcon = (categoryName) => {
    // Fixed content
  };
  const IconComponent = categoryIcons[categoryName] || FiPackage;
    return <IconComponent className="h-4 w-4" />;
  };

  // Handle visual search
  const handleVisualSearch = (searchData) => {
    // Fixed content
  };
  setIsSearching(true);
    setShowVisualSearch(false);

    // Pass visual search data to parent component
    onSearch({ /* content */ };
      ...searchData,
      category: selectedCategory === 'All Categories' ? '' : selectedCategory,
      filters
    });

    setIsSearching(false);
  };

  return (
    <div className="bg-gradient-to-br from-blue-600 via-purple-600 to-indigo-700 relative overflow-hidden">
      {/* Animated Background */}
      <div className="absolute inset-0">
        <div className="absolute inset-0 bg-gradient-to-br from-blue-600/20 via-purple-600/20 to-indigo-700/20"></div>
        <div className="absolute inset-0 opacity-30">
          <div className="absolute top-10 left-10 w-72 h-72 bg-white/10 rounded-full blur-3xl animate-pulse"></div>
          <div className="absolute top-40 right-20 w-96 h-96 bg-purple-300/10 rounded-full blur-3xl animate-pulse delay-1000"></div>
          <div className="absolute bottom-20 left-1/3 w-80 h-80 bg-blue-300/10 rounded-full blur-3xl animate-pulse delay-2000"></div>
        </div>
      </div>

      <div className="relative z-10 px-6 py-16">
        {/* Enhanced Header */}
        <div className="text-center mb-12">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
          >
            <div className="flex items-center justify-center mb-6">
              <motion.div
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ duration: 0.6, delay: 0.2 }}
                className="bg-white/20 backdrop-blur-sm rounded-full p-4 mr-4"
              >
                <FiSearch className="h-12 w-12 text-white" />
              </motion.div>
              <motion.div
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ duration: 0.6, delay: 0.4 }}
                className="bg-white/20 backdrop-blur-sm rounded-full p-4"
              >
                <FiTool className="h-12 w-12 text-white" />
              </motion.div>
            </div>
            <h1 className="text-5xl md:text-6xl font-bold text-white mb-4 bg-gradient-to-r from-white to-blue-100 bg-clip-text text-transparent">
              What can we help you find?
            </h1>
            <p className="text-blue-100 text-xl max-w-3xl mx-auto leading-relaxed">
              Discover powerful extensions to enhance your SketchUp workflow with our intelligent search
            </p>

            {/* Stats */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.6 }}
              className="flex items-center justify-center space-x-8 mt-8"
            >
              <div className="text-center">
                <div className="text-2xl font-bold text-white">500+</div>
                <div className="text-blue-200 text-sm">Extensions</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-white">12</div>
                <div className="text-blue-200 text-sm">Categories</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-white">1M+</div>
                <div className="text-blue-200 text-sm">Downloads</div>
              </div>
            </motion.div>
          </motion.div>
        </div>

        {/* Main Search Bar */}
        <div className="max-w-4xl mx-auto mb-6">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.8 }}
            className="relative"
          >
            <div className="flex bg-white rounded-lg shadow-2xl overflow-hidden backdrop-blur-sm border border-white/20">
            {/* Category Dropdown */}
            <div className="relative" ref={dropdownRef}>
              <button
                onClick={() => setShowCategoryDropdown(!showCategoryDropdown)}
                className="flex items-center px-6 py-4 bg-gray-50 hover:bg-gray-100 transition-colors border-r border-gray-200 min-w-[200px]"
              >
                {getCategoryIcon(selectedCategory)}
                <span className="ml-2 font-medium text-gray-700 truncate">
                  {selectedCategory}
                </span>
                <FiChevronDown className={`ml-2 h-4 w-4 text-gray-500 transition-transform ${showCategoryDropdown ? 'rotate-180' : ''}`} />
              </button>

              {/* Category Dropdown */}
              <AnimatePresence>
                {showCategoryDropdown && (
                  <motion.div
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -10 }}
                    className="absolute top-full left-0 bg-white border border-gray-200 rounded-lg shadow-xl z-50 max-h-96 overflow-y-auto min-w-[600px]"
                  >
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2 p-6">
                      {enhancedCategories.map((category, index) => (
                        <motion.button
                          key={category.name}
                          initial={{ opacity: 0, y: 20 }}
                          animate={{ opacity: 1, y: 0 }}
                          transition={{ duration: 0.3, delay: index * 0.05 }}
                          onClick={() => handleCategorySelect(category)}
                          className="relative flex items-start p-4 hover:bg-blue-50 rounded-xl transition-all duration-200 text-left group border border-gray-100 hover:border-blue-200 hover:shadow-md"
                        >
                          {/* Trending Badge */}
                          {category.trending && (
                            <div className="absolute -top-2 -right-2 bg-orange-500 text-white text-xs px-2 py-1 rounded-full font-medium">
                              Trending
                            </div>
                          )}

                          {/* Popular Badge */}
                          {category.popular && !category.trending && (
                            <div className="absolute -top-2 -right-2 bg-green-500 text-white text-xs px-2 py-1 rounded-full font-medium">
                              Popular
                            </div>
                          )}

                          <div className={`flex-shrink-0 mt-1 text-${category.color}-600 group-hover:text-${category.color}-700 transition-colors`}>
                            {getCategoryIcon(category.name)}
                          </div>
                          <div className="ml-3 min-w-0 flex-1">
                            <div className="font-semibold text-gray-900 text-sm mb-1">
                              {category.name}
                            </div>
                            <div className="text-xs text-gray-500 mb-2 line-clamp-2">
                              {category.description}
                            </div>
                            <div className="flex items-center justify-between">
                              <div className={`text-xs text-${category.color}-600 font-medium`}>
                                {category.count === 'All' ? 'All' : `${category.count} extensions`}
                              </div>
                              {category.tags && (
                                <div className="flex space-x-1">
                                  {category.tags.slice(0, 2).map((tag, tagIndex) => (
                                    <span
                                      key={tagIndex}
                                      className={`text-xs px-2 py-1 bg-${category.color}-100 text-${category.color}-700 rounded-full`}
                                    >
                                      {tag}
                                    </span>
                                  ))}
                                </div>
                              )}
                            </div>
                          </div>
                        </motion.button>
                      ))}
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>
            </div>

            {/* Search Input */}
            <div className="flex-1 relative" ref={searchRef}>
              <input
                type="text"
                value={searchQuery}
                onChange={(e) => handleSearchInputChange(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                onFocus={() => {
    // Fixed content
  };
  if (condition) {
    // Fixed content
  }
  setShowSuggestions(true);
                  }
                }}
                placeholder={`Search for an extension in ${selectedCategory}`}
                className="w-full px-6 py-4 text-gray-700 placeholder-gray-400 focus:outline-none text-lg transition-all duration-200"
              />
              {searchQuery && (
                <button
                  onClick={() => {
    // Fixed content
  };
  setSearchQuery('''; // Fixed broken string
                    setShowSuggestions(false);
                    handleSearch('''; // Fixed broken string
                  }}
                  className="absolute right-16 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors"
                >
                  <FiX className="h-5 w-5" />
                </button>
              )}

              {/* Search Suggestions */}
              <AnimatePresence>
                {showSuggestions && (searchSuggestions.length > 0 || recentSearches.length > 0) && (
                  <motion.div
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -10 }}
                    className="absolute top-full left-0 right-0 bg-white border border-gray-200 rounded-lg shadow-xl z-50 mt-1 max-h-96 overflow-y-auto"
                  >
                    {/* Recent Searches */}
                    {recentSearches.length > 0 && searchQuery.length === 0 && (
                      <div className="p-4 border-b border-gray-100">
                        <div className="flex items-center text-gray-500 text-sm mb-2">
                          <FiClock className="h-4 w-4 mr-2" />
                          Recent Searches
                        </div>
                        {recentSearches.map((search, index) => (
                          <button
                            key={index}
                            onClick={() => {
    // Fixed content
  };
  setSearchQuery(search);
                              handleSearch(search);
                            }}
                            className="block w-full text-left px-3 py-2 hover:bg-gray-50 rounded text-gray-700 text-sm"
                          >
                            {search}
                          </button>
                        ))}
                      </div>
                    )}

                    {/* Search Suggestions */}
                    {searchSuggestions.length > 0 && (
                      <div className="p-2">
                        {searchSuggestions.map((suggestion, index) => (
                          <button
                            key={index}
                            onClick={() => handleSuggestionClick(suggestion)}
                            className="flex items-center w-full px-3 py-2 hover:bg-blue-50 rounded text-left group"
                          >
                            <suggestion.icon className="h-4 w-4 text-gray-400 group-hover:text-blue-600 mr-3" />
                            <span className="text-gray-700 group-hover:text-blue-700">
                              {suggestion.text}
                            </span>
                            {suggestion.type === 'popular' && (
                              <FiTrendingUp className="h-3 w-3 text-orange-500 ml-auto" />
                            )}
                          </button>
                        ))}
                      </div>
                    )}

                    {/* Popular Searches when no query */}
                    {searchQuery.length === 0 && recentSearches.length === 0 && (
                      <div className="p-4">
                        <div className="flex items-center text-gray-500 text-sm mb-3">
                          <FiTrendingUp className="h-4 w-4 mr-2" />
                          Popular Searches
                        </div>
                        <div className="grid grid-cols-2 gap-2">
                          {popularSearches.slice(0, 6).map((search, index) => (
                            <button
                              key={index}
                              onClick={() => {
    // Fixed content
  };
  setSearchQuery(search);
                                handleSearch(search);
                              }}
                              className="px-3 py-2 bg-gray-50 hover:bg-blue-50 rounded text-sm text-gray-700 hover:text-blue-700 transition-colors text-left"
                            >
                              {search}
                            </button>
                          ))}
                        </div>
                      </div>
                    )}
                  </motion.div>
                )}
              </AnimatePresence>
            </div>

            {/* Search Button */}
            <button
              onClick={() => handleSearch()}
              disabled={isSearching}
              className="px-8 py-4 bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white font-medium transition-colors flex items-center min-w-[120px]"
            >
              {isSearching ? (
                <>
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                  Searching...
                </>
              ) : (
                <>
                  <FiSearch className="h-5 w-5 mr-2" />
                  Search
                </>
              )}
            </button>
            </div>
          </motion.div>
        </div>

        {/* Enhanced Quick Filters */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 1.0 }}
          className="max-w-4xl mx-auto"
        >
          <div className="flex flex-col lg:flex-row items-center justify-between mb-6 space-y-4 lg:space-y-0">
            <div className="flex flex-wrap items-center gap-3">
              <button
                onClick={() => setShowFilters(!showFilters)}
                className="flex items-center px-4 py-2 bg-white/10 hover:bg-white/20 text-white rounded-lg transition-all duration-200 backdrop-blur-sm border border-white/20"
              >
                <FiFilter className="h-4 w-4 mr-2" />
                Advanced Filters
                <FiChevronDown className={`ml-2 h-4 w-4 transition-transform ${showFilters ? 'rotate-180' : ''}`} />
              </button>

              <div className="flex items-center space-x-2">
                <span className="text-white/80 text-sm font-medium">Quick filters:</span>
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={() => handleFilterChange('priceRange', 'free')}
                  className={`px-4 py-2 rounded-full text-sm transition-all duration-200 ${ /* content */ };
                    filters.priceRange === 'free'
                      ? 'bg-white text-blue-600 shadow-lg'
                      : 'bg-white/10 text-white hover:bg-white/20 border border-white/20'
                  }`}
                >
                  <FiDollarSign className="h-3 w-3 mr-1 inline" />
                  Free
                </motion.button>
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={() => handleFilterChange('rating', '4+')}
                  className={`px-4 py-2 rounded-full text-sm transition-all duration-200 ${ /* content */ };
                    filters.rating === '4+'
                      ? 'bg-white text-blue-600 shadow-lg'
                      : 'bg-white/10 text-white hover:bg-white/20 border border-white/20'
                  }`}
                >
                  <FiStar className="h-3 w-3 mr-1 inline" />
                  4+ Stars
                </motion.button>
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={() => handleFilterChange('sortBy', 'newest')}
                  className={`px-4 py-2 rounded-full text-sm transition-all duration-200 ${ /* content */ };
                    filters.sortBy === 'newest'
                      ? 'bg-white text-blue-600 shadow-lg'
                      : 'bg-white/10 text-white hover:bg-white/20 border border-white/20'
                  }`}
                >
                  <FiClock className="h-3 w-3 mr-1 inline" />
                  Newest
                </motion.button>
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={() => handleFilterChange('sortBy', 'downloads')}
                  className={`px-4 py-2 rounded-full text-sm transition-all duration-200 ${ /* content */ };
                    filters.sortBy === 'downloads'
                      ? 'bg-white text-blue-600 shadow-lg'
                      : 'bg-white/10 text-white hover:bg-white/20 border border-white/20'
                  }`}
                >
                  <FiTrendingUp className="h-3 w-3 mr-1 inline" />
                  Popular
                </motion.button>
              </div>
            </div>

            <div className="flex items-center space-x-4">
              <button
                onClick={() => setShowVisualSearch(true)}
                className="flex items-center px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-all duration-200 shadow-lg"
              >
                <FiImage className="h-4 w-4 mr-2" />
                Visual Search
              </button>
              <div className="flex items-center text-white/80 text-sm">
                <FiInfo className="h-4 w-4 mr-1" />
                <span>Press Enter to search</span>
              </div>
              <div className="flex items-center text-white/80 text-sm">
                <FiTarget className="h-4 w-4 mr-1" />
                <span>Smart suggestions enabled</span>
              </div>
            </div>
          </div>

          {/* Advanced Filters Panel */}
          <AnimatePresence>
            {showFilters && (
              <motion.div
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: 'auto' }}
                exit={{ opacity: 0, height: 0 }}
                className="bg-white/10 backdrop-blur-sm rounded-lg p-6 mb-4"
              >
                <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                  {/* Price Range */}
                  <div>
                    <label className="block text-white font-medium mb-2">Price Range</label>
                    <select
                      value={filters.priceRange}
                      onChange={(e) => handleFilterChange('priceRange', e.target.value)}
                      className="w-full px-3 py-2 bg-white/20 text-white rounded-lg border border-white/30 focus:outline-none focus:border-white"
                    >
                      <option value="all" className="text-gray-900">All Prices</option>
                      <option value="free" className="text-gray-900">Free</option>
                      <option value="under50" className="text-gray-900">Under $50</option>
                      <option value="50to100" className="text-gray-900">$50 - $100</option>
                      <option value="over100" className="text-gray-900">Over $100</option>
                    </select>
                  </div>

                  {/* Rating */}
                  <div>
                    <label className="block text-white font-medium mb-2">Minimum Rating</label>
                    <select
                      value={filters.rating}
                      onChange={(e) => handleFilterChange('rating', e.target.value)}
                      className="w-full px-3 py-2 bg-white/20 text-white rounded-lg border border-white/30 focus:outline-none focus:border-white"
                    >
                      <option value="all" className="text-gray-900">Any Rating</option>
                      <option value="4+" className="text-gray-900">4+ Stars</option>
                      <option value="4.5+" className="text-gray-900">4.5+ Stars</option>
                      <option value="5" className="text-gray-900">5 Stars</option>
                    </select>
                  </div>

                  {/* Compatibility */}
                  <div>
                    <label className="block text-white font-medium mb-2">SketchUp Version</label>
                    <select
                      value={filters.compatibility}
                      onChange={(e) => handleFilterChange('compatibility', e.target.value)}
                      className="w-full px-3 py-2 bg-white/20 text-white rounded-lg border border-white/30 focus:outline-none focus:border-white"
                    >
                      <option value="all" className="text-gray-900">All Versions</option>
                      <option value="2024" className="text-gray-900">SketchUp 2024</option>
                      <option value="2023" className="text-gray-900">SketchUp 2023</option>
                      <option value="2022" className="text-gray-900">SketchUp 2022+</option>
                      <option value="2021" className="text-gray-900">SketchUp 2021+</option>
                    </select>
                  </div>

                  {/* Sort By */}
                  <div>
                    <label className="block text-white font-medium mb-2">Sort By</label>
                    <select
                      value={filters.sortBy}
                      onChange={(e) => handleFilterChange('sortBy', e.target.value)}
                      className="w-full px-3 py-2 bg-white/20 text-white rounded-lg border border-white/30 focus:outline-none focus:border-white"
                    >
                      <option value="downloads" className="text-gray-900">Most Downloaded</option>
                      <option value="rating" className="text-gray-900">Highest Rated</option>
                      <option value="newest" className="text-gray-900">Newest</option>
                      <option value="name" className="text-gray-900">Name A-Z</option>
                      <option value="price" className="text-gray-900">Price Low to High</option>
                    </select>
                  </div>
                </div>

                {/* Clear Filters */}
                <div className="mt-4 flex justify-end">
                  <button
                    onClick={() => {
    // Fixed content
  };
  setFilters({
    priceRange: 'all',
                        rating: 'all',
                        developer: '',
                        compatibility: 'all',
                        sortBy: 'downloads'
                      });
                      handleSearch();
                    }}
                    className="px-4 py-2 bg-white/20 hover:bg-white/30 text-white rounded-lg transition-colors"
                  >
                    Clear All Filters
                  </button>
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </motion.div>
      </div>

      {/* Visual Search Modal */}
      <AnimatePresence>
        {showVisualSearch && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 backdrop-blur-sm z-[60] flex items-center justify-center p-4"
            onClick={() => setShowVisualSearch(false)}
          >
            <motion.div
              initial={{ opacity: 0, scale: 0.9, y: 20 }}
              animate={{ opacity: 1, scale: 1, y: 0 }}
              exit={{ opacity: 0, scale: 0.9, y: 20 }}
              className="bg-white dark:bg-gray-800 rounded-xl shadow-2xl w-full max-w-4xl max-h-[90vh] overflow-hidden"
              onClick={(e) => e.stopPropagation()}
            >
              {/* Header */}
              <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
                <div className="flex items-center space-x-3">
                  <div className="p-2 bg-purple-100 dark:bg-purple-900/30 rounded-lg">
                    <FiEye className="w-6 h-6 text-purple-600 dark:text-purple-400" />
                  </div>
                  <div>
                    <h3 className="text-xl font-semibold text-gray-900 dark:text-white">
                      Visual Extension Search
                    </h3>
                    <p className="text-sm text-gray-600 dark:text-gray-300">
                      Upload an image to find similar SketchUp extensions
                    </p>
                  </div>
                </div>
                <button
                  onClick={() => setShowVisualSearch(false)}
                  className="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                >
                  <FiX className="w-6 h-6" />
                </button>
              </div>

              {/* Content */}
              <div className="p-6 overflow-y-auto max-h-[75vh]">
                <VisualExtensionSearch
                  onSearch={handleVisualSearch}
                  onFilterChange={onFilterChange}
                  categories={categories}
                />
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default AdvancedExtensionSearch;
