import express from 'express';
import {
  getUsers,
  getUser,
  createUser,
  updateUser,
  deleteUser,
  getUserProfile,
  updateUserProfile,
  updatePassword,
  getSavedModels,
  saveModel,
  removeSavedModel,
  getDownloadHistory,
  getUserSubscription,
  updateSubscription,
  cancelSubscription,
  reactivateSubscription,
  getUserSecurity,
  enable2FA,
  verify2FA,
  disable2FA,
  uploadProfileImage
} from '../controllers/userController.js';
import { getUserActivity } from '../controllers/activityController.js';
import { protect, authorize } from '../middleware/auth.js';

const router = express.Router();

// Temporary route to upgrade user to admin (remove in production) - NO AUTH REQUIRED
router.post('/upgrade-to-admin', async (req, res) => {
  try {
    const { email, secretKey } = req.body;

    // Simple secret key check (in production, use proper authentication)
    if (secretKey !== 'upgrade-admin-2024') {
      return res.status(401).json({
        success: false,
        error: 'Invalid secret key'
      });
    }

    const User = (await import('../models/User.js')).default;

    const user = await User.findOne({ email });
    if (!user) {
      return res.status(404).json({
        success: false,
        error: 'User not found'
      });
    }

    // Update user role to admin
    user.role = 'admin';
    user.subscription = {
      type: 'professional',
      status: 'active',
      startDate: new Date(),
      endDate: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000) // 1 year
    };
    user.downloadCredits = 1000;
    user.bio = user.bio || 'Administrator of 3DSKETCHUP.NET';

    await user.save();

    res.status(200).json({
      success: true,
      message: 'User upgraded to admin successfully',
      user: {
        id: user._id,
        name: user.name,
        email: user.email,
        role: user.role,
        subscription: user.subscription
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// All routes below require authentication
router.use(protect);

// Routes for all authenticated users (no admin required)
// Profile routes
router.get('/profile', getUserProfile);
router.put('/profile', updateUserProfile);
router.put('/password', updatePassword);
router.post('/profile/image', uploadProfileImage);

// Activity routes
router.get('/activity', getUserActivity);

// Saved models routes
router.get('/saved-models', getSavedModels);
router.post('/saved-models/:id', saveModel);
router.delete('/saved-models/:id', removeSavedModel);

// Download history routes
router.get('/download-history', getDownloadHistory);

// Subscription routes
router.get('/subscription', getUserSubscription);
router.put('/subscription', updateSubscription);
router.post('/subscription/cancel', cancelSubscription);
router.post('/subscription/reactivate', reactivateSubscription);

// Security routes
router.get('/security', getUserSecurity);
router.post('/security/2fa/enable', enable2FA);
router.post('/security/2fa/verify', verify2FA);
router.post('/security/2fa/disable', disable2FA);

// Admin routes - only accessible to admin users
router.use(authorize('admin'));

router.route('/')
  .get(getUsers)
  .post(createUser);

router.route('/:id')
  .get(getUser)
  .put(updateUser)
  .delete(deleteUser);

export default router;
