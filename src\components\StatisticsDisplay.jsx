import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  FiDownload,
  FiUsers,
  FiBox,
  FiGrid,
  FiRefreshCw,
  FiTrendingUp,
  FiBarChart2,
  FiPieChart
} from 'react-icons/fi';
import { useModels } from '../context/ModelContext';
import realDataService from '../services/realDataService';
import LoadingIndicator from './ui/LoadingIndicator';

/**
 * Statistics Display Component
 * Shows real-time statistics from MongoDB
 */
const StatisticsDisplay = ({
  showRefreshButton = true,
  animateOnMount = true,
  className = ''
}) => {
  const { getStatistics } = useModels();
  const [statistics, setStatistics] = useState({
    modelsCount: 0,
    usersCount: 0,
    downloadsCount: 0,
    categoriesCount: 0
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [lastUpdated, setLastUpdated] = useState(null);

  // Fetch statistics on mount
  useEffect(() => {
    fetchStatistics();
  }, []);

  // Fetch statistics using realDataService
  const fetchStatistics = async () => {
    try {
      setLoading(true);
      setError(null);

      console.log('🔄 Fetching statistics for StatisticsDisplay...');
      const stats = await realDataService.getStats();
      console.log('✅ Statistics fetched:', stats);

      setStatistics({
        modelsCount: stats.models || 0,
        usersCount: stats.users || 0,
        downloadsCount: stats.downloads || 0,
        categoriesCount: stats.categories || 0
      });

      setLastUpdated(new Date());
    } catch (err) {
      console.error('❌ Error fetching statistics:', err);
      setError('Failed to load statistics');
    } finally {
      setLoading(false);
    }
  };

  // Format number with commas
  const formatNumber = (num) => {
    return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  };

  // Format last updated time
  const formatLastUpdated = () => {
    if (!lastUpdated) return '';

    return lastUpdated.toLocaleTimeString([], {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        type: 'spring',
        stiffness: 300,
        damping: 24
      }
    }
  };

  // Render loading state
  if (loading && !statistics.modelsCount) {
    return (
      <div className={`bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 ${className}`}>
        <LoadingIndicator size="md" text="Loading statistics..." />
      </div>
    );
  }

  // Render error state
  if (error && !statistics.modelsCount) {
    return (
      <div className={`bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 text-center ${className}`}>
        <p className="text-red-500 dark:text-red-400 mb-2">{error}</p>
        <button
          onClick={fetchStatistics}
          className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
        >
          <FiRefreshCw className="inline mr-2" />
          Retry
        </button>
      </div>
    );
  }

  return (
    <motion.div
      className={`bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 ${className}`}
      initial={animateOnMount ? 'hidden' : 'visible'}
      animate="visible"
      variants={containerVariants}
    >
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-xl font-bold text-gray-900 dark:text-white flex items-center">
          <FiBarChart2 className="mr-2" />
          Statistics
        </h2>

        {showRefreshButton && (
          <div className="flex items-center">
            {lastUpdated && (
              <span className="text-xs text-gray-500 dark:text-gray-400 mr-2">
                Updated: {formatLastUpdated()}
              </span>
            )}

            <button
              onClick={fetchStatistics}
              disabled={loading}
              className="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
              aria-label="Refresh statistics"
            >
              <FiRefreshCw className={loading ? 'animate-spin' : ''} />
            </button>
          </div>
        )}
      </div>

      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        {/* Models count */}
        <motion.div
          className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4"
          variants={itemVariants}
        >
          <div className="flex items-center justify-between">
            <div className="bg-blue-100 dark:bg-blue-800 p-3 rounded-full">
              <FiBox className="w-6 h-6 text-blue-600 dark:text-blue-300" />
            </div>

            <div className="text-right">
              <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Models</p>
              <h3 className="text-xl font-bold text-gray-900 dark:text-white">
                {formatNumber(statistics.modelsCount)}
              </h3>
            </div>
          </div>
        </motion.div>

        {/* Users count */}
        <motion.div
          className="bg-green-50 dark:bg-green-900/20 rounded-lg p-4"
          variants={itemVariants}
        >
          <div className="flex items-center justify-between">
            <div className="bg-green-100 dark:bg-green-800 p-3 rounded-full">
              <FiUsers className="w-6 h-6 text-green-600 dark:text-green-300" />
            </div>

            <div className="text-right">
              <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Users</p>
              <h3 className="text-xl font-bold text-gray-900 dark:text-white">
                {formatNumber(statistics.usersCount)}
              </h3>
            </div>
          </div>
        </motion.div>

        {/* Downloads count */}
        <motion.div
          className="bg-purple-50 dark:bg-purple-900/20 rounded-lg p-4"
          variants={itemVariants}
        >
          <div className="flex items-center justify-between">
            <div className="bg-purple-100 dark:bg-purple-800 p-3 rounded-full">
              <FiDownload className="w-6 h-6 text-purple-600 dark:text-purple-300" />
            </div>

            <div className="text-right">
              <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Downloads</p>
              <h3 className="text-xl font-bold text-gray-900 dark:text-white">
                {formatNumber(statistics.downloadsCount)}
              </h3>
            </div>
          </div>
        </motion.div>

        {/* Categories count */}
        <motion.div
          className="bg-amber-50 dark:bg-amber-900/20 rounded-lg p-4"
          variants={itemVariants}
        >
          <div className="flex items-center justify-between">
            <div className="bg-amber-100 dark:bg-amber-800 p-3 rounded-full">
              <FiGrid className="w-6 h-6 text-amber-600 dark:text-amber-300" />
            </div>

            <div className="text-right">
              <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Categories</p>
              <h3 className="text-xl font-bold text-gray-900 dark:text-white">
                {formatNumber(statistics.categoriesCount)}
              </h3>
            </div>
          </div>
        </motion.div>
      </div>

      {/* Growth indicators */}
      <motion.div
        className="mt-6 grid grid-cols-2 gap-4"
        variants={itemVariants}
      >
        <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
          <div className="flex items-center">
            <FiTrendingUp className="w-5 h-5 text-green-500 mr-2" />
            <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
              Most Popular Category
            </span>
          </div>
          <p className="mt-2 text-lg font-semibold text-gray-900 dark:text-white">
            {statistics.topCategory || 'Architecture'}
          </p>
        </div>

        <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
          <div className="flex items-center">
            <FiPieChart className="w-5 h-5 text-blue-500 mr-2" />
            <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
              Most Downloaded
            </span>
          </div>
          <p className="mt-2 text-lg font-semibold text-gray-900 dark:text-white">
            {statistics.topFormat || '3D Models'}
          </p>
        </div>
      </motion.div>
    </motion.div>
  );
};

export default StatisticsDisplay;
