import React from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';
import { toast } from 'react-hot-toast';

/**
 * ProtectedRoute component with enhanced role-based access control
 *
 * @param {Object} props - Component props
 * @param {React.ReactNode} props.children - Child components to render if authorized
 * @param {boolean} props.adminOnly - Whether the route requires admin role
 * @param {string|string[]} props.requiredRole - Specific role(s) required to access the route
 * @param {string} props.requiredPermission - Specific permission required to access the route
 * @param {string} props.unauthorizedMessage - Custom message to show when unauthorized
 * @param {string} props.redirectPath - Custom path to redirect to when unauthorized
 * @returns {React.ReactNode} Protected route component
 */
const ProtectedRoute = ({
  children,
  adminOnly = false,
  requiredRole = null,
  requiredPermission = null,
  unauthorizedMessage = "You don't have permission to access this page",
  redirectPath = "/"
}) => {
  const { currentUser, loading, isAdmin, hasRole, hasPermission } = useAuth();
  const location = useLocation();

  if (loading) {
    // Show loading spinner while checking authentication
    return (
      <div className="flex justify-center items-center h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  // If not logged in, redirect to login page with the return url
  if (!currentUser) {
    return <Navigate to="/login" state={{ from: location }} replace />;
  }

  // Check authorization based on provided criteria
  let isAuthorized = true;

  // Check admin requirement
  if (adminOnly && !isAdmin()) {
    isAuthorized = false;
  }

  // Check role requirement
  if (isAuthorized && requiredRole && !hasRole(requiredRole)) {
    isAuthorized = false;
  }

  // Check permission requirement
  if (isAuthorized && requiredPermission && !hasPermission(requiredPermission)) {
    isAuthorized = false;
  }

  // If not authorized, show toast message and redirect
  if (!isAuthorized) {
    toast.error(unauthorizedMessage);
    return <Navigate to={redirectPath} replace />;
  }

  // If authorized, render the protected component
  return children;
};

export default ProtectedRoute;
