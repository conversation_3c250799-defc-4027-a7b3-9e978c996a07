import fetch from 'node-fetch';

const upgradeUsersToAdmin = async () => {
  try {
    console.log('Upgrading users to admin role...');

    const adminEmails = [
      '<EMAIL>',
      '<EMAIL>', 
      '<EMAIL>',
      '<EMAIL>'
    ];

    const secretKey = 'upgrade-admin-2024';

    for (const email of adminEmails) {
      console.log(`\nUpgrading ${email}...`);
      
      try {
        const response = await fetch('http://localhost:5002/api/users/upgrade-to-admin', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            email: email,
            secretKey: secretKey
          })
        });

        if (response.ok) {
          const data = await response.json();
          console.log('✅ Success:', data.message);
          console.log('   User ID:', data.user.id);
          console.log('   Name:', data.user.name);
          console.log('   Email:', data.user.email);
          console.log('   Role:', data.user.role);
          console.log('   Subscription:', data.user.subscription.type);
        } else {
          const error = await response.text();
          console.log('❌ Failed:', error);
        }
      } catch (err) {
        console.log('❌ Error upgrading', email, ':', err.message);
      }
    }

    console.log('\n🎯 ADMIN UPGRADE COMPLETE!');
    console.log('\nTesting admin logins...');

    // Test login for each admin
    const adminCredentials = [
      { email: '<EMAIL>', password: 'admin123456', name: 'Primary Admin' },
      { email: '<EMAIL>', password: 'superadmin123', name: 'Backup Admin' },
      { email: '<EMAIL>', password: 'admin2024', name: 'Vietnamese Admin' },
      { email: '<EMAIL>', password: 'password123', name: 'Test Admin' }
    ];

    for (const cred of adminCredentials) {
      console.log(`\nTesting login for ${cred.name}...`);
      
      try {
        const loginResponse = await fetch('http://localhost:5002/api/auth/login', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            email: cred.email,
            password: cred.password
          })
        });

        if (loginResponse.ok) {
          const loginData = await loginResponse.json();
          console.log('✅ Login successful!');
          console.log('   Name:', loginData.user.name);
          console.log('   Role:', loginData.user.role);
          console.log('   Token:', loginData.token ? 'Generated' : 'Missing');
        } else {
          const loginError = await loginResponse.text();
          console.log('❌ Login failed:', loginError);
        }
      } catch (err) {
        console.log('❌ Login error:', err.message);
      }
    }

    console.log('\n🚀 FINAL ADMIN ACCOUNTS:');
    console.log('');
    console.log('1. PRIMARY ADMIN:');
    console.log('   Email: <EMAIL>');
    console.log('   Password: admin123456');
    console.log('   Role: admin');
    console.log('   Subscription: professional');
    console.log('');
    console.log('2. BACKUP ADMIN:');
    console.log('   Email: <EMAIL>');
    console.log('   Password: superadmin123');
    console.log('   Role: admin');
    console.log('   Subscription: professional');
    console.log('');
    console.log('3. VIETNAMESE ADMIN:');
    console.log('   Email: <EMAIL>');
    console.log('   Password: admin2024');
    console.log('   Role: admin');
    console.log('   Subscription: professional');
    console.log('');
    console.log('4. TEST ADMIN:');
    console.log('   Email: <EMAIL>');
    console.log('   Password: password123');
    console.log('   Role: admin');
    console.log('   Subscription: professional');
    console.log('');
    console.log('🌐 LOGIN URL: http://localhost:5173/login');
    console.log('💼 All accounts have:');
    console.log('   - Full admin privileges');
    console.log('   - Professional subscription');
    console.log('   - 1000 download credits');
    console.log('   - 1 year subscription validity');

  } catch (error) {
    console.error('Error upgrading users to admin:', error);
  }
};

upgradeUsersToAdmin();
