import mongoose from 'mongoose';
import bcrypt from 'bcryptjs';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const fixPassword = async () => {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('MongoDB Connected');

    // Get the users collection directly
    const db = mongoose.connection.db;
    const usersCollection = db.collection('users');

    // Hash the password
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash('admin123', salt);

    // Update admin user password
    const result = await usersCollection.updateOne(
      { email: '<EMAIL>' },
      { $set: { password: hashedPassword } }
    );

    console.log('Update result:', result);

    // Test the password
    const user = await usersCollection.findOne({ email: '<EMAIL>' });
    if (user) {
      const isMatch = await bcrypt.compare('admin123', user.password);
      console.log('Password test result:', isMatch);
    }

    process.exit(0);
  } catch (error) {
    console.error('Error:', error);
    process.exit(1);
  }
};

fixPassword();
