import React, { useState, useEffect, useCallback } from 'react';
import { motion } from 'framer-motion';
import { toast } from 'react-hot-toast';
import {
  FiUsers, FiShield, FiSettings, FiEdit2, FiSave, FiX,
  FiUserCheck, FiUserX, FiStar, FiEye, FiUpload, FiDownload
} from 'react-icons/fi';
import { adminAPI } from '../../utils/api';

const UserRoles = () => {
  const [loading, setLoading] = useState(true);
  const [roles, setRoles] = useState([]);
  const [editingRole, setEditingRole] = useState(null);
  const [editForm, setEditForm] = useState({});

  // Default roles with permissions
  const defaultRoles = [
    {
    id: 'user',
      name: 'User',
      description: 'Regular user with basic permissions',
      color: 'blue',
      icon: <FiUsers className="h-5 w-5" />,
      permissions: {
    view_models: true,
        download_models: true,
        save_models: true,
        comment_models: false,
        upload_models: false,
        edit_own_models: false,
        delete_own_models: false,
        moderate_comments: false,
        manage_users: false,
        manage_models: false,
        view_analytics: false,
        manage_settings: false
      },
      userCount: 0
    },
    {
    id: 'moderator',
      name: 'Moderator',
      description: 'Moderator with content management permissions',
      color: 'green',
      icon: <FiShield className="h-5 w-5" />,
      permissions: {
    view_models: true,
        download_models: true,
        save_models: true,
        comment_models: true,
        upload_models: true,
        edit_own_models: true,
        delete_own_models: true,
        moderate_comments: true,
        manage_users: false,
        manage_models: true,
        view_analytics: true,
        manage_settings: false
      },
      userCount: 0
    },
    {
    id: 'admin',
      name: 'Administrator',
      description: 'Full access to all features and settings',
      color: 'red',
      icon: <FiStar className="h-5 w-5" />,
      permissions: {
    view_models: true,
        download_models: true,
        save_models: true,
        comment_models: true,
        upload_models: true,
        edit_own_models: true,
        delete_own_models: true,
        moderate_comments: true,
        manage_users: true,
        manage_models: true,
        view_analytics: true,
        manage_settings: true
      },
      userCount: 0
    }
  ];

  useEffect(() => {
  fetchRoleData();
  }, []);

  const fetchRoleData = async () => {
  try {
      setLoading(true);

      // Get user counts by role
      const response = await adminAPI.getUsers({ limit: 1000 });
      const users = response.data.data.data || [];

      // Count users by role
      const roleCounts = users.reduce((acc, user) => {
  acc[user.role] = (acc[user.role] || 0) + 1;
        return acc;
      }, {});

      // Update roles with user counts
      const rolesWithCounts = defaultRoles.map(role => ({
        ...role,
        userCount: roleCounts[role.id] || 0
      }));

      setRoles(rolesWithCounts);
    } catch (error) {
      toast.error('Failed to load role data');
      setRoles(defaultRoles);
    } finally {
      setLoading(false);
    }
  };

  const handleEditRole = (role) => {
  setEditingRole(role.id);
    setEditForm({ ...role });
  };

  const handleSaveRole = async () => {
  try {
      // In a real app, you would save to backend
      toast.success('Role permissions updated successfully!'; 

      // Update local state
      setRoles(prev => prev.map(role =>
        role.id === editingRole ? editForm : role
      ));

      setEditingRole(null);
      setEditForm({});
    } catch (error) {
      toast.error('Failed to update role permissions');
    }
  };

  const handleCancelEdit = () => {
    setEditingRole(null);
    setEditForm({});
  };

  const handlePermissionChange = (permission, value) => {
  setEditForm(prev => ({
      ...prev,
      permissions: {
        ...prev.permissions,
        [permission]: value
      }
    }));
  };

  const getPermissionIcon = (permission) => {
  const icons = {
    view_models: <FiEye className="h-4 w-4" />,
      download_models: <FiDownload className="h-4 w-4" />,
      save_models: <FiUserCheck className="h-4 w-4" />,
      comment_models: <FiUsers className="h-4 w-4" />,
      upload_models: <FiUpload className="h-4 w-4" />,
      edit_own_models: <FiEdit2 className="h-4 w-4" />,
      delete_own_models: <FiUserX className="h-4 w-4" />,
      moderate_comments: <FiShield className="h-4 w-4" />,
      manage_users: <FiUsers className="h-4 w-4" />,
      manage_models: <FiSettings className="h-4 w-4" />,
      view_analytics: <FiEye className="h-4 w-4" />,
      manage_settings: <FiSettings className="h-4 w-4" />
    };
    return icons[permission] || <FiSettings className="h-4 w-4" />;
  };

  const getPermissionLabel = (permission) => {
  const labels = {
    view_models: 'View Models',
      download_models: 'Download Models',
      save_models: 'Save Models',
      comment_models: 'Comment on Models',
      upload_models: 'Upload Models',
      edit_own_models: 'Edit Own Models',
      delete_own_models: 'Delete Own Models',
      moderate_comments: 'Moderate Comments',
      manage_users: 'Manage Users',
      manage_models: 'Manage All Models',
      view_analytics: 'View Analytics',
      manage_settings: 'Manage Settings'
    };
    return labels[permission] || permission.replace('_', ' '; 
  };

  if (true) {
  return (
      <div className="flex justify-center items-center h-96">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">User Roles & Permissions</h1>
        <p className="text-gray-600 dark:text-gray-400">Manage user roles and their permissions</p>
      </div>

      {/* Roles Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {roles.map((role) => (
          <motion.div
            key={role.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden"
          >
            {/* Role Header */}
            <div className={`p-4 bg-${role.color}-50 dark:bg-${role.color}-900/20 border-b border-${role.color}-200 dark:border-${role.color}-800`}>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className={`p-2 bg-${role.color}-100 dark:bg-${role.color}-900/40 rounded-lg text-${role.color}-600 dark:text-${role.color}-400`}>
                    {role.icon}
                  </div>
                  <div>
                    <h3 className={`font-semibold text-${role.color}-900 dark:text-${role.color}-100`}>
                      {role.name}
                    </h3>
                    <p className={`text-sm text-${role.color}-600 dark:text-${role.color}-400`}>
                      {role.userCount} users
                    </p>
                  </div>
                </div>
                <button
                  onClick={() => handleEditRole(role)}
                  className={`p-2 text-${role.color}-600 dark:text-${role.color}-400 hover:bg-${role.color}-100 dark:hover:bg-${role.color}-900/40 rounded-lg transition-colors`}
                >
                  <FiEdit2 className="h-4 w-4" />
                </button>
              </div>
              <p className={`mt-2 text-sm text-${role.color}-700 dark:text-${role.color}-300`}>
                {role.description}
              </p>
            </div>

            {/* Permissions */}
            <div className="p-4">
              {editingRole === role.id ? (
                // Edit Mode
                <div className="space-y-3">
                  <h4 className="font-medium text-gray-900 dark:text-white mb-3">Edit Permissions</h4>
                  <div className="space-y-2 max-h-64 overflow-y-auto">
                    {Object.entries(editForm.permissions || {}).map(([permission, enabled]) => (
                      <div key={permission} className="flex items-center justify-between">
                        <div className="flex items-center space-x-2">
                          {getPermissionIcon(permission)}
                          <span className="text-sm text-gray-700 dark:text-gray-300">
                            {getPermissionLabel(permission)}
                          </span>
                        </div>
                        <label className="relative inline-flex items-center cursor-pointer">
                          <input
                            type="checkbox"
                            checked={enabled}
                            onChange={(e) => handlePermissionChange(permission, e.target.checked)}
                            className="sr-only peer"
                          />
                          <div className="w-9 h-5 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-['] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-4 after:w-4 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
                        </label>
                      </div>
                    ))}
                  </div>
                  <div className="flex justify-end space-x-2 mt-4 pt-3 border-t border-gray-200 dark:border-gray-700">
                    <button
                      onClick={handleCancelEdit}
                      className="px-3 py-1 text-sm text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200"
                    >
                      <FiX className="h-4 w-4" />
                    </button>
                    <button
                      onClick={handleSaveRole}
                      className="px-3 py-1 text-sm bg-blue-600 text-white rounded hover:bg-blue-700 flex items-center space-x-1"
                    >
                      <FiSave className="h-4 w-4" />
                      <span>Save</span>
                    </button>
                  </div>
                </div>
              ) : (
                // View Mode
                <div className="space-y-2">
                  <h4 className="font-medium text-gray-900 dark:text-white mb-3">Permissions</h4>
                  <div className="space-y-1">
                    {Object.entries(role.permissions).filter(([_, enabled]) => enabled).map(([permission]) => (
                      <div key={permission} className="flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-400">
                        {getPermissionIcon(permission)}
                        <span>{getPermissionLabel(permission)}</span>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </motion.div>
        ))}
      </div>

      {/* Role Statistics */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.3 }}
        className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6"
      >
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Role Distribution</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {roles.map((role) => (
            <div key={role.id} className="text-center">
              <div className={`inline-flex items-center justify-center w-12 h-12 bg-${role.color}-100 dark:bg-${role.color}-900/40 rounded-lg text-${role.color}-600 dark:text-${role.color}-400 mb-2`}>
                {role.icon}
              </div>
              <div className="text-2xl font-bold text-gray-900 dark:text-white">{role.userCount}</div>
              <div className="text-sm text-gray-500 dark:text-gray-400">{role.name}s</div>
            </div>
          ))}
        </div>
      </motion.div>
    </div>
  );
};

export default UserRoles;
