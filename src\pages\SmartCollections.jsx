import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Link } from 'react-router-dom';
import {
  FiZap, FiStar, FiTrendingUp, FiClock, FiEye, FiHeart,
  FiGrid, FiList, FiFilter, FiSearch, FiPlus, FiBookmark,
  FiTarget, FiLayers, FiUsers, FiAward, FiRefreshCw,
  FiSettings, FiShare2, FiDownload, FiPlay
} from 'react-icons/fi';
import Header from '../components/Header';
import Footer from '../components/Footer';
import PageTransition from '../components/PageTransition';
import { useAuth } from '../context/AuthContext';
import apiService from '../services/api';

const SmartCollections = () => {
  const { currentUser } = useAuth();
  const [collections, setCollections] = useState([]);
  const [loading, setLoading] = useState(true);
  const [viewMode, setViewMode] = useState('grid');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [isGenerating, setIsGenerating] = useState(false);

  // Smart collection categories
  const categories = [
    { id: 'all', name: 'Tất cả', icon: <FiGrid />, count: 0 },
    { id: 'trending', name: 'Xu hướng', icon: <FiTrendingUp />, count: 0 },
    { id: 'curated', name: 'Được tuyển chọn', icon: <FiStar />, count: 0 },
    { id: 'ai-recommended', name: 'AI gợi ý', icon: <FiZap />, count: 0 },
    { id: 'user-favorites', name: 'Yêu thích', icon: <FiHeart />, count: 0 },
    { id: 'recent', name: 'Mới nhất', icon: <FiClock />, count: 0 }
  ];

  // No mock collections - use real data only

  useEffect(() => {
    loadCollections();
  }, [selectedCategory]);

  const loadCollections = async () => {
    setLoading(true);
    try {
      // Use mock data since collections API is not implemented yet
      const mockCollections = [
        {
          id: 1,
          title: 'Modern Living Rooms',
          description: 'Curated collection of contemporary living room designs',
          thumbnail: '/images/placeholder.jpg',
          type: 'curated',
          curator: 'Curated',
          modelCount: 24,
          views: 1250,
          likes: 89,
          tags: ['modern', 'living room', 'furniture'],
          lastUpdated: new Date().toISOString(),
          aiScore: 0.92
        },
        {
          id: 2,
          title: 'AI Recommended for You',
          description: 'Personalized collection based on your preferences',
          thumbnail: '/images/placeholder.jpg',
          type: 'ai-recommended',
          curator: 'AI',
          modelCount: 18,
          views: 890,
          likes: 67,
          tags: ['personalized', 'ai', 'recommended'],
          lastUpdated: new Date().toISOString(),
          aiScore: 0.88
        }
      ];

      // Filter by category if needed
      const filteredCollections = selectedCategory === 'all'
        ? mockCollections
        : mockCollections.filter(c => c.type === selectedCategory);

      setCollections(filteredCollections);
    } catch (error) {
      console.error('Error loading collections:', error);
      setCollections([]);
    } finally {
      setLoading(false);
    }
  };

  const generateSmartCollection = async () => {
    setIsGenerating(true);
    try {
      // Simulate AI generation with mock data
      await new Promise(resolve => setTimeout(resolve, 2000)); // Simulate API delay

      const newCollection = {
        id: Date.now(),
        title: 'AI Generated Collection',
        description: 'A personalized collection created just for you',
        thumbnail: '/images/placeholder.jpg',
        type: 'ai-recommended',
        curator: 'AI',
        modelCount: Math.floor(Math.random() * 20) + 10,
        views: Math.floor(Math.random() * 500) + 100,
        likes: Math.floor(Math.random() * 50) + 10,
        tags: ['ai-generated', 'personalized', 'new'],
        lastUpdated: new Date().toISOString(),
        aiScore: 0.85 + Math.random() * 0.15
      };

      setCollections(prev => [newCollection, ...prev]);
    } catch (error) {
      console.error('Error generating collection:', error);
      alert('Failed to generate smart collection. Please try again later.');
    } finally {
      setIsGenerating(false);
    }
  };

  const CollectionCard = ({ collection }) => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 group"
    >
      {/* Thumbnail */}
      <div className="relative h-48 overflow-hidden">
        <img
          src={collection.thumbnail}
          alt={collection.title}
          className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
        />
        <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent" />

        {/* Type badge */}
        <div className="absolute top-3 left-3">
          <span className={`px-2 py-1 rounded-full text-xs font-medium text-white ${
            collection.type === 'ai-recommended' ? 'bg-purple-500' :
            collection.type === 'trending' ? 'bg-red-500' :
            collection.type === 'curated' ? 'bg-blue-500' :
            'bg-gray-500'
          }`}>
            {collection.type === 'ai-recommended' && <FiZap className="inline w-3 h-3 mr-1" />}
            {collection.type === 'trending' && <FiTrendingUp className="inline w-3 h-3 mr-1" />}
            {collection.type === 'curated' && <FiStar className="inline w-3 h-3 mr-1" />}
            {collection.curator}
          </span>
        </div>

        {/* AI Score */}
        {collection.aiScore && (
          <div className="absolute top-3 right-3">
            <div className="bg-black/50 backdrop-blur-sm rounded-full px-2 py-1 text-white text-xs">
              AI: {(collection.aiScore * 100).toFixed(0)}%
            </div>
          </div>
        )}

        {/* Stats */}
        <div className="absolute bottom-3 left-3 right-3 flex justify-between text-white text-sm">
          <span className="flex items-center">
            <FiLayers className="w-4 h-4 mr-1" />
            {collection.modelCount} models
          </span>
          <div className="flex items-center space-x-3">
            <span className="flex items-center">
              <FiEye className="w-4 h-4 mr-1" />
              {collection.views}
            </span>
            <span className="flex items-center">
              <FiHeart className="w-4 h-4 mr-1" />
              {collection.likes}
            </span>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="p-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
          {collection.title}
        </h3>
        <p className="text-gray-600 dark:text-gray-400 text-sm mb-4 line-clamp-2">
          {collection.description}
        </p>

        {/* Tags */}
        <div className="flex flex-wrap gap-2 mb-4">
          {collection.tags.map((tag, index) => (
            <span
              key={index}
              className="px-2 py-1 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 text-xs rounded-full"
            >
              {tag}
            </span>
          ))}
        </div>

        {/* Footer */}
        <div className="flex items-center justify-between">
          <div className="text-xs text-gray-500 dark:text-gray-400">
            Cập nhật {new Date(collection.lastUpdated).toLocaleDateString('vi-VN')}
          </div>
          <div className="flex items-center space-x-2">
            <button className="p-2 text-gray-400 hover:text-red-500 transition-colors">
              <FiHeart className="w-4 h-4" />
            </button>
            <button className="p-2 text-gray-400 hover:text-blue-500 transition-colors">
              <FiShare2 className="w-4 h-4" />
            </button>
            <Link
              to={`/collections/${collection.id}`}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm font-medium"
            >
              Xem
            </Link>
          </div>
        </div>
      </div>
    </motion.div>
  );

  return (
    <div className="flex flex-col min-h-screen bg-gray-50 dark:bg-gray-900">
      <Header />

      <PageTransition>
        <main className="flex-grow pt-24 pb-16">
          <div className="container mx-auto px-4">
            {/* Page Header */}
            <div className="text-center mb-12">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5 }}
              >
                <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">
                  Smart Collections
                </h1>
                <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
                  Khám phá các bộ sưu tập thông minh được AI tuyển chọn và cộng đồng đóng góp
                </p>
              </motion.div>
            </div>

            {/* AI Generation Section */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
              className="bg-gradient-to-r from-purple-600 to-blue-600 rounded-xl p-8 mb-12 text-white"
            >
              <div className="flex items-center justify-between">
                <div>
                  <h2 className="text-2xl font-bold mb-2">AI-Powered Collections</h2>
                  <p className="text-purple-100">
                    Để AI tạo bộ sưu tập cá nhân hóa dựa trên sở thích của bạn
                  </p>
                </div>
                <button
                  onClick={generateSmartCollection}
                  disabled={isGenerating}
                  className="flex items-center space-x-2 px-6 py-3 bg-white/20 hover:bg-white/30 rounded-lg transition-colors disabled:opacity-50"
                >
                  {isGenerating ? (
                    <FiRefreshCw className="w-5 h-5 animate-spin" />
                  ) : (
                    <FiZap className="w-5 h-5" />
                  )}
                  <span>{isGenerating ? 'Đang tạo...' : 'Tạo bộ sưu tập'}</span>
                </button>
              </div>
            </motion.div>

            {/* Categories */}
            <div className="flex flex-wrap gap-4 mb-8">
              {categories.map((category) => (
                <button
                  key={category.id}
                  onClick={() => setSelectedCategory(category.id)}
                  className={`flex items-center space-x-2 px-4 py-2 rounded-lg transition-colors ${
                    selectedCategory === category.id
                      ? 'bg-blue-600 text-white'
                      : 'bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'
                  }`}
                >
                  {category.icon}
                  <span>{category.name}</span>
                  {category.count > 0 && (
                    <span className="bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300 px-2 py-1 rounded-full text-xs">
                      {category.count}
                    </span>
                  )}
                </button>
              ))}
            </div>

            {/* Collections Grid */}
            {loading ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                {[...Array(6)].map((_, index) => (
                  <div key={index} className="bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden">
                    <div className="h-48 bg-gray-200 dark:bg-gray-700 animate-pulse"></div>
                    <div className="p-6">
                      <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded animate-pulse mb-2"></div>
                      <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded animate-pulse mb-4"></div>
                      <div className="flex space-x-2 mb-4">
                        <div className="h-6 w-16 bg-gray-200 dark:bg-gray-700 rounded-full animate-pulse"></div>
                        <div className="h-6 w-20 bg-gray-200 dark:bg-gray-700 rounded-full animate-pulse"></div>
                      </div>
                      <div className="flex justify-between items-center">
                        <div className="h-3 w-24 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
                        <div className="h-8 w-16 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                {collections.map((collection) => (
                  <CollectionCard key={collection.id} collection={collection} />
                ))}
              </div>
            )}

            {/* Empty State */}
            {!loading && collections.length === 0 && (
              <div className="text-center py-12">
                <FiLayers className="w-16 h-16 text-gray-300 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                  Không tìm thấy bộ sưu tập
                </h3>
                <p className="text-gray-500 dark:text-gray-400 mb-6">
                  Thử chọn danh mục khác hoặc tạo bộ sưu tập mới
                </p>
                <button
                  onClick={generateSmartCollection}
                  className="inline-flex items-center space-x-2 px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                >
                  <FiZap className="w-5 h-5" />
                  <span>Tạo bộ sưu tập AI</span>
                </button>
              </div>
            )}
          </div>
        </main>
      </PageTransition>

      <Footer />
    </div>
  );
};

export default SmartCollections;
