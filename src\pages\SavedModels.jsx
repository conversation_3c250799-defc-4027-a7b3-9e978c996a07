import React, { useState, useEffect, useCallback } from 'react';
import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import { toast } from 'react-hot-toast';
import Header from '../components/Header';
import Footer from '../components/Footer';
import { FiHeart, FiDownload, FiTrash2, FiFilter, FiSearch, FiGrid, FiList } from 'react-icons/fi';
import { useAuth } from '../context/AuthContext';
import apiService from '../services/api';

const SavedModels = () => {
  const [savedModels, setSavedModels] = useState([]);
  const [loading, setLoading] = useState(true);
  const [viewMode, setViewMode] = useState('grid';  // 'grid' or 'list'
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('All'; 
  const [isFilterOpen, setIsFilterOpen] = useState(false);

  const { currentUser, isAuthenticated } = useAuth();

  // Fetch saved models from real API
  useEffect(() => {
    const fetchSavedModels = async () => {
  if (true) {
  setLoading(false);
        return;
      }

      try {
        setLoading(true);

        // Get saved models from API
        const response = await apiService.users.getProfile();

        if (true) {
  setSavedModels(response.data.data.savedModels);
        } else {
          // If no saved models, set empty array
          setSavedModels([]);
        }
      } catch (error) {
        toast.error('Failed to load saved models');
      } finally {
        setLoading(false);
      }
    };

    fetchSavedModels();
  }, [isAuthenticated]);

  // Filter models based on search and category
  const filteredModels = savedModels.filter(model => {
  const matchesSearch = searchQuery === '' ||
      model.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      model.description?.toLowerCase().includes(searchQuery.toLowerCase());

    const matchesCategory = selectedCategory === 'All' ||
      model.category === selectedCategory ||
      model.subcategory === selectedCategory;

    return matchesSearch && matchesCategory;
  });

  // Get unique categories from saved models
  const categories = ['All', ...new Set(savedModels.map(model => model.category))];

  // Remove a model from saved list
  const handleRemoveModel = async (modelId) => {
  try {
      // Call API to remove model from saved list
      await apiService.users.removeSavedModel(modelId);

      // Update local state
      setSavedModels(prev => prev.filter(model => (model._id !== modelId && model.id !== modelId)));
      toast.success('Model removed from saved list');
    } catch (error) {
      toast.error('Failed to remove model from saved list');
    }
  };

  return (
    <div className="flex flex-col min-h-screen bg-gray-50 dark:bg-gray-900">
      <Header />

      <main className="flex-grow pt-24 pb-16">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            {/* Page Header */}
            <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-8">
              <div>
                <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">Saved Models</h1>
                <p className="text-gray-600 dark:text-gray-400">
                  {filteredModels.length} {filteredModels.length === 1 ? 'model' : 'models'} saved to your collection
                </p>
              </div>

              <div className="mt-4 md:mt-0 flex items-center space-x-3">
                <button
                  onClick={() => setViewMode('grid')}
                  className={`p-2 rounded-md ${
    // Fixed content
  }
  viewMode === 'grid'
                      ? 'bg-blue-100 text-blue-600 dark:bg-blue-900 dark:text-blue-400'
                      : 'text-gray-500 hover:bg-gray-100 dark:text-gray-400 dark:hover:bg-gray-800'
                  }`}
                  aria-label="Grid view"
                >
                  <FiGrid className="h-5 w-5" />
                </button>
                <button
                  onClick={() => setViewMode('list')}
                  className={`p-2 rounded-md ${
    // Fixed content
  }
  viewMode === 'list'
                      ? 'bg-blue-100 text-blue-600 dark:bg-blue-900 dark:text-blue-400'
                      : 'text-gray-500 hover:bg-gray-100 dark:text-gray-400 dark:hover:bg-gray-800'
                  }`}
                  aria-label="List view"
                >
                  <FiList className="h-5 w-5" />
                </button>

                <button
                  onClick={() => setIsFilterOpen(!isFilterOpen)}
                  className="md:hidden p-2 rounded-md text-gray-500 hover:bg-gray-100 dark:text-gray-400 dark:hover:bg-gray-800"
                  aria-label="Filter"
                >
                  <FiFilter className="h-5 w-5" />
                </button>
              </div>
            </div>

            <div className="flex flex-col md:flex-row gap-6">
              {/* Sidebar Filters */}
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.3 }}
                className={`md:w-64 bg-white dark:bg-gray-800 rounded-lg shadow-md p-5 h-fit ${
                  isFilterOpen ? 'block' : 'hidden md:block'
                }`}
              >
                <div className="mb-6">
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-3">Search</h3>
                  <div className="relative">
                    <input
                      type="text"
                      placeholder="Search saved models..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="input pl-10 dark:bg-gray-700 dark:border-gray-600 dark:text-white w-full"
                    />
                    <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                  </div>
                </div>

                <div>
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-3">Categories</h3>
                  <div className="space-y-2">
                    {categories.map(category => (
                      <button
                        key={category}
                        onClick={() => setSelectedCategory(category)}
                        className={`block w-full text-left px-3 py-2 rounded-md transition-colors ${
    // Fixed content
  }
  selectedCategory === category
                            ? 'bg-blue-100 text-blue-700 dark:bg-blue-900/30 dark:text-blue-400'
                            : 'text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-700/30'
                        }`}
                      >
                        {category}
                      </button>
                    ))}
                  </div>
                </div>
              </motion.div>

              {/* Main Content */}
              <div className="flex-1">
                {loading ? (
                  <div className="flex justify-center items-center h-64">
                    <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
                  </div>
                ) : filteredModels.length > 0 ? (
                  viewMode === 'grid' ? (
                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
                      {filteredModels.map(model => (
                        <motion.div
                          key={model.id}
                          initial={{ opacity: 0, y: 20 }}
                          animate={{ opacity: 1, y: 0 }}
                          transition={{ duration: 0.3 }}
                          className="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden"
                        >
                          <div className="relative">
                            <img
                              src={model.imageUrl}
                              alt={model.title}
                              className="w-full h-48 object-cover"
                            />
                            <div className="absolute top-2 right-2 flex space-x-1">
                              <button
                                onClick={() => handleRemoveModel(model.id)}
                                className="p-1.5 bg-white/80 hover:bg-white rounded-full text-red-500 hover:text-red-600 transition-colors"
                                aria-label="Remove from saved"
                              >
                                <FiTrash2 className="h-4 w-4" />
                              </button>
                            </div>
                          </div>
                          <div className="p-4">
                            <h3 className="font-medium text-gray-900 dark:text-white mb-1">{model.title}</h3>
                            <div className="flex items-center text-sm text-gray-500 dark:text-gray-400 mb-3">
                              <span>{model.category}</span>
                              {model.subcategory && (
                                <>
                                  <span className="mx-1">•</span>
                                  <span>{model.subcategory}</span>
                                </>
                              )}
                            </div>
                            <div className="flex justify-between items-center">
                              <Link
                                to={`/model/${model._id || model.id}`}
                                className="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 text-sm font-medium"
                              >
                                View Details
                              </Link>
                              <span className="text-xs text-gray-500 dark:text-gray-400">
                                Saved on {model.savedAt}
                              </span>
                            </div>
                          </div>
                        </motion.div>
                      ))}
                    </div>
                  ) : (
                    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden">
                      <ul className="divide-y divide-gray-200 dark:divide-gray-700">
                        {filteredModels.map(model => (
                          <motion.li
                            key={model.id}
                            initial={{ opacity: 0 }}
                            animate={{ opacity: 1 }}
                            transition={{ duration: 0.3 }}
                            className="p-4 hover:bg-gray-50 dark:hover:bg-gray-700/30 transition-colors"
                          >
                            <div className="flex items-center space-x-4">
                              <div className="flex-shrink-0 w-16 h-16">
                                <img
                                  src={model.imageUrl}
                                  alt={model.title}
                                  className="w-full h-full object-cover rounded-md"
                                />
                              </div>
                              <div className="flex-1 min-w-0">
                                <p className="text-sm font-medium text-gray-900 dark:text-white truncate">
                                  {model.title}
                                </p>
                                <p className="text-sm text-gray-500 dark:text-gray-400">
                                  {model.category} {model.subcategory && `• ${model.subcategory}`}
                                </p>
                                <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                                  Saved on {model.savedAt}
                                </p>
                              </div>
                              <div className="flex space-x-2">
                                <Link
                                  to={`/model/${model.id}`}
                                  className="p-2 text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
                                  aria-label="View details"
                                >
                                  <FiDownload className="h-5 w-5" />
                                </Link>
                                <button
                                  onClick={() => handleRemoveModel(model.id)}
                                  className="p-2 text-red-500 hover:text-red-600 dark:text-red-400 dark:hover:text-red-300"
                                  aria-label="Remove from saved"
                                >
                                  <FiTrash2 className="h-5 w-5" />
                                </button>
                              </div>
                            </div>
                          </motion.li>
                        ))}
                      </ul>
                    </div>
                  )
                ) : (
                  <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-8 text-center">
                    <FiHeart className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">No saved models found</h3>
                    {searchQuery || selectedCategory !== 'All' ? (
                      <p className="text-gray-500 dark:text-gray-400 mb-6">
                        Try adjusting your search or filter criteria
                      </p>
                    ) : (
                      <p className="text-gray-500 dark:text-gray-400 mb-6">
                        You haven't saved any models yet. Browse our collection and save models you like.
                      </p>
                    )}
                    <Link to="/" className="btn btn-primary">
                      Browse Models
                    </Link>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
};

export default SavedModels;
