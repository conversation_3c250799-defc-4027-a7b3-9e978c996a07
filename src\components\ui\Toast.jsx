import React, { useState, useEffect, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  FiX,
  FiCheck,
  FiInfo,
  FiAlertTriangle,
  FiAlertOctagon
} from 'react-icons/fi';

/**
 * Enhanced Toast component with animations and auto-dismiss
 *
 * @param {Object} props - Component props
 * @param {string} props.type - Type of toast: 'success', 'error', 'info', 'warning'
 * @param {string} props.message - Toast message
 * @param {string} props.title - Optional toast title
 * @param {number} props.duration - Duration in ms before auto-dismiss (0 for no auto-dismiss)
 * @param {boolean} props.visible - Whether the toast is visible
 * @param {Function} props.onClose - Function to call when toast is closed
 * @param {string} props.position - Position of toast: 'top-right', 'top-left', 'bottom-right', 'bottom-left'
 */
const Toast = ({
  type = 'info',
  message,
  title,
  duration = 5000,
  visible = true,
  onClose,
  position = 'top-right',
  ...props
}) => {
  const [isVisible, setIsVisible] = useState(visible);
  const [progress, setProgress] = useState(100);
  const [intervalId, setIntervalId] = useState(null);

  // Type configurations
  const typeConfig = {
    success: {
      icon: <FiCheck />,
      bgColor: 'bg-green-500',
      textColor: 'text-white',
      progressColor: 'bg-green-300',
    },
    error: {
      icon: <FiAlertOctagon />,
      bgColor: 'bg-red-500',
      textColor: 'text-white',
      progressColor: 'bg-red-300',
    },
    info: {
      icon: <FiInfo />,
      bgColor: 'bg-blue-500',
      textColor: 'text-white',
      progressColor: 'bg-blue-300',
    },
    warning: {
      icon: <FiAlertTriangle />,
      bgColor: 'bg-yellow-500',
      textColor: 'text-white',
      progressColor: 'bg-yellow-300',
    },
  };

  // Position configurations
  const positionConfig = {
    'top-right': 'top-4 right-4',
    'top-left': 'top-4 left-4',
    'bottom-right': 'bottom-4 right-4',
    'bottom-left': 'bottom-4 left-4',
  };

  // Handle auto-dismiss
  useEffect(() => {
    setIsVisible(visible);

    if (visible && duration > 0) {
      // Clear any existing interval
      if (intervalId) {
        clearInterval(intervalId);
      }

      // Reset progress
      setProgress(100);

      // Set up progress tracking
      const startTime = Date.now();
      const id = setInterval(() => {
        const elapsed = Date.now() - startTime;
        const remaining = Math.max(0, 100 - (elapsed / duration) * 100);

        setProgress(remaining);

        if (remaining <= 0) {
          clearInterval(id);
          setIsVisible(false);
          if (onClose) onClose();
        }
      }, 100);

      setIntervalId(id);

      return () => {
        clearInterval(id);
      };
    }
  }, [visible, duration, onClose]);

  // Handle manual close
  const handleClose = () => {
    setIsVisible(false);
    if (intervalId) {
      clearInterval(intervalId);
    }
    if (onClose) onClose();
  };

  // Animation variants
  const toastVariants = {
    hidden: {
      opacity: 0,
      y: position.includes('top') ? -20 : 20,
      scale: 0.95,
    },
    visible: {
      opacity: 1,
      y: 0,
      scale: 1,
      transition: {
        duration: 0.3,
        ease: 'easeOut',
      },
    },
    exit: {
      opacity: 0,
      scale: 0.95,
      transition: {
        duration: 0.2,
        ease: 'easeIn',
      },
    },
  };

  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          className={`fixed ${positionConfig[position]} z-50 max-w-md shadow-lg rounded-lg overflow-hidden`}
          variants={toastVariants}
          initial="hidden"
          animate="visible"
          exit="exit"
          {...props}
        >
          <div className={`${typeConfig[type].bgColor} ${typeConfig[type].textColor} p-4 flex items-start`}>
            <div className="flex-shrink-0 mr-3 mt-0.5">
              {typeConfig[type].icon}
            </div>

            <div className="flex-1 mr-2">
              {title && <h4 className="font-medium">{title}</h4>}
              <p className={title ? 'text-sm opacity-90' : '}>{message}</p>
            </div>

            <button
              className="flex-shrink-0 ml-auto -mr-1 text-white opacity-80 hover:opacity-100 focus:outline-none"
              onClick={handleClose}
              aria-label="Close"
            >
              <FiX />
            </button>
          </div>

          {duration > 0 && (
            <div className="h-1 bg-gray-200">
              <div
                className={`h-full ${typeConfig[type].progressColor}`}
                style={{ width: `${progress}%` }}
              />
            </div>
          )}
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default Toast;
