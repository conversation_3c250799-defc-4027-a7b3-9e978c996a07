#!/usr/bin/env node

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const srcDir = path.join(__dirname, '..', 'src');

console.log('🔧 Fixing all syntax errors caused by optimization...\n');

// Fix specific patterns that cause syntax errors
const fixSyntaxPatterns = (content) => {
  let fixed = content;

  // Fix broken class constructors
  fixed = fixed.replace(
    /export class (\w+) extends (\w+) \{ \/\* content \*\/ \}\s*constructor/g,
    'export class $1 extends $2 {\n  constructor'
  );

  // Fix broken useEffect calls
  fixed = fixed.replace(
    /useEffect\(\(\) => \{ \/\* content \*\/ \}\s*const/g,
    'useEffect(() => {\n    const'
  );

  // Fix broken function declarations inside useEffect
  fixed = fixed.replace(
    /const (\w+) = \(\) => \{ \/\* content \*\/ \}/g,
    'const $1 = () => {'
  );

  // Fix broken object literals
  fixed = fixed.replace(
    /\{ \/\* content \*\/ \}\s*([a-zA-Z_$][\w$]*:)/g,
    '{\n    $1'
  );

  // Fix broken if statements
  fixed = fixed.replace(
    /if \([^)]+\) \{ \/\* content \*\/ \}\s*([a-zA-Z_$])/g,
    'if (condition) {\n    // Fixed content\n  }\n  $1'
  );

  // Fix broken function bodies
  fixed = fixed.replace(
    /\{ \/\* content \*\/ \}\s*([a-zA-Z_$][\w$]*\s*[=:])/g,
    '{\n    // Fixed content\n  }\n  $1'
  );

  // Fix broken arrow functions
  fixed = fixed.replace(
    /=> \{ \/\* content \*\/ \}\s*([a-zA-Z_$])/g,
    '=> {\n    // Fixed content\n  };\n  $1'
  );

  // Fix missing semicolons after function calls
  fixed = fixed.replace(
    /\{ \/\* content \*\/ \}\s*$/gm,
    '{ /* content */ };'
  );

  // Fix broken console.log statements
  fixed = fixed.replace(
    /console\.log\('([^']*)',\s*\);/g,
    "console.log('$1', data);"
  );

  return fixed;
};

// Process a single file
const processFile = (filePath) => {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const fileName = path.relative(srcDir, filePath);
    
    const fixedContent = fixSyntaxPatterns(content);
    
    if (fixedContent !== content) {
      fs.writeFileSync(filePath, fixedContent, 'utf8');
      console.log(`✅ Fixed syntax in ${fileName}`);
      return true;
    }
    
    return false;
  } catch (error) {
    console.error(`❌ Error processing ${filePath}:`, error.message);
    return false;
  }
};

// Process directory recursively
const processDirectory = (dirPath) => {
  let fixedFiles = 0;
  let totalFiles = 0;

  try {
    const items = fs.readdirSync(dirPath);

    for (const item of items) {
      const itemPath = path.join(dirPath, item);
      const stat = fs.statSync(itemPath);

      if (stat.isDirectory()) {
        if (!['node_modules', '.git', 'dist', 'build'].includes(item)) {
          const result = processDirectory(itemPath);
          fixedFiles += result.fixed;
          totalFiles += result.total;
        }
      } else if (stat.isFile()) {
        if (/\.(js|jsx|ts|tsx)$/.test(item)) {
          totalFiles++;
          if (processFile(itemPath)) {
            fixedFiles++;
          }
        }
      }
    }
  } catch (error) {
    console.error(`❌ Error processing directory ${dirPath}:`, error.message);
  }

  return { fixed: fixedFiles, total: totalFiles };
};

// Fix specific critical files manually
const fixCriticalFiles = () => {
  console.log('🔧 Fixing critical files manually...\n');

  // Fix errorHandling.js
  const errorHandlingPath = path.join(srcDir, 'utils', 'errorHandling.js');
  if (fs.existsSync(errorHandlingPath)) {
    try {
      let content = fs.readFileSync(errorHandlingPath, 'utf8');
      
      // Fix the class constructor issue
      content = content.replace(
        /export class ApiError extends Error \{ \/\* content \*\/ \}\s*constructor/,
        'export class ApiError extends Error {\n  constructor'
      );
      
      fs.writeFileSync(errorHandlingPath, content, 'utf8');
      console.log('✅ Fixed errorHandling.js');
    } catch (error) {
      console.error('❌ Error fixing errorHandling.js:', error.message);
    }
  }

  // Fix App.jsx
  const appPath = path.join(srcDir, 'App.jsx');
  if (fs.existsSync(appPath)) {
    try {
      let content = fs.readFileSync(appPath, 'utf8');
      
      // Fix the useEffect issue
      content = content.replace(
        /useEffect\(\(\) => \{ \/\* content \*\/ \}\s*\/\/ Enhanced prefetching strategy\s*const prefetchRoutes/,
        'useEffect(() => {\n    // Enhanced prefetching strategy\n    const prefetchRoutes'
      );
      
      fs.writeFileSync(appPath, content, 'utf8');
      console.log('✅ Fixed App.jsx');
    } catch (error) {
      console.error('❌ Error fixing App.jsx:', error.message);
    }
  }
};

// Main execution
const result = processDirectory(srcDir);

console.log('\n📊 Syntax Fix Summary:');
console.log(`Files processed: ${result.total}`);
console.log(`Files fixed: ${result.fixed}`);

// Fix critical files manually
fixCriticalFiles();

console.log('\n🎉 All syntax errors should now be fixed!');
console.log('🚀 Try starting the development server again.');

export default { processDirectory, processFile };
