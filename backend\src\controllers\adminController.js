import User from '../models/User.js';
import Model from '../models/Model.js';
import Payment from '../models/Payment.js';
import Category from '../models/Category.js';

// @desc    Get admin dashboard statistics
// @route   GET /api/admin/stats
// @access  Private/Admin
export const getAdminStats = async (req, res, next) => {
  try {
    // Get total counts
    const totalUsers = await User.countDocuments();
    const totalModels = await Model.countDocuments();

    // Calculate total downloads
    const models = await Model.find({}, 'downloads');
    const totalDownloads = models.reduce((sum, model) => sum + (model.downloads || 0), 0);

    // Calculate total revenue
    const payments = await Payment.find({ status: 'completed' }, 'amount');
    const totalRevenue = payments.reduce((sum, payment) => sum + (payment.amount || 0), 0);

    // Get recent users (last 5)
    const recentUsers = await User.find()
      .sort({ createdAt: -1 })
      .limit(5)
      .select('name email createdAt role status');

    // Get recent models (last 5)
    const recentModels = await Model.find()
      .sort({ createdAt: -1 })
      .limit(5)
      .populate('createdBy', 'name')
      .select('title category createdAt downloads');

    // Calculate growth percentages (mock data for now)
    const userGrowth = 4.2;
    const modelGrowth = 9.8;
    const downloadGrowth = 1.1;
    const revenueGrowth = 2.7;

    // Get monthly user registrations (last 6 months)
    const sixMonthsAgo = new Date();
    sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 5);

    const usersByMonth = await User.aggregate([
      {
        $match: {
          createdAt: { $gte: sixMonthsAgo }
        }
      },
      {
        $group: {
          _id: {
            year: { $year: '$createdAt' },
            month: { $month: '$createdAt' }
          },
          count: { $sum: 1 }
        }
      },
      {
        $sort: { '_id.year': 1, '_id.month': 1 }
      }
    ]);

    // Get monthly downloads (approximation)
    const downloadsByMonth = await Model.aggregate([
      {
        $match: {
          updatedAt: { $gte: sixMonthsAgo }
        }
      },
      {
        $group: {
          _id: {
            year: { $year: '$updatedAt' },
            month: { $month: '$updatedAt' }
          },
          downloads: { $sum: '$downloads' }
        }
      },
      {
        $sort: { '_id.year': 1, '_id.month': 1 }
      }
    ]);

    res.status(200).json({
      success: true,
      data: {
        totalUsers,
        totalModels,
        totalDownloads,
        totalRevenue,
        userGrowth,
        modelGrowth,
        downloadGrowth,
        revenueGrowth,
        recentUsers,
        recentModels,
        usersByMonth,
        downloadsByMonth
      }
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Get all users (admin)
// @route   GET /api/admin/users
// @access  Private/Admin
export const getUsers = async (req, res, next) => {
  try {
    // Get query parameters
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const search = req.query.search || '';
    const role = req.query.role;
    const status = req.query.status;
    const sortBy = req.query.sortBy || 'createdAt';
    const sortDirection = req.query.sortDirection || 'desc';

    // Build query
    let query = {};

    // Search filter
    if (search) {
      query.$or = [
        { name: { $regex: search, $options: 'i' } },
        { email: { $regex: search, $options: 'i' } }
      ];
    }

    // Role filter
    if (role && role !== 'all') {
      query.role = role;
    }

    // Status filter
    if (status && status !== 'all') {
      query.status = status;
    }

    // Calculate skip
    const skip = (page - 1) * limit;

    // Build sort object
    const sort = {};
    sort[sortBy] = sortDirection === 'asc' ? 1 : -1;

    // Get total count
    const total = await User.countDocuments(query);

    // Get users with pagination
    const users = await User.find(query)
      .sort(sort)
      .skip(skip)
      .limit(limit)
      .select('-password');

    res.status(200).json({
      success: true,
      data: {
        data: users,
        total: total,
        page: page,
        pages: Math.ceil(total / limit),
        limit: limit
      }
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Get single user (admin)
// @route   GET /api/admin/users/:id
// @access  Private/Admin
export const getUser = async (req, res, next) => {
  try {
    const user = await User.findById(req.params.id).select('-password');

    if (!user) {
      return res.status(404).json({
        success: false,
        error: 'User not found'
      });
    }

    res.status(200).json({
      success: true,
      data: user
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Update user (admin)
// @route   PUT /api/admin/users/:id
// @access  Private/Admin
export const updateUser = async (req, res, next) => {
  try {
    const user = await User.findByIdAndUpdate(
      req.params.id,
      req.body,
      {
        new: true,
        runValidators: true
      }
    ).select('-password');

    if (!user) {
      return res.status(404).json({
        success: false,
        error: 'User not found'
      });
    }

    res.status(200).json({
      success: true,
      data: user
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Delete user (admin)
// @route   DELETE /api/admin/users/:id
// @access  Private/Admin
export const deleteUser = async (req, res, next) => {
  try {
    const user = await User.findById(req.params.id);

    if (!user) {
      return res.status(404).json({
        success: false,
        error: 'User not found'
      });
    }

    await user.deleteOne();

    res.status(200).json({
      success: true,
      data: {}
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Bulk delete users (admin)
// @route   POST /api/admin/users/bulk-delete
// @access  Private/Admin
export const bulkDeleteUsers = async (req, res, next) => {
  try {
    const { userIds } = req.body;

    if (!userIds || !Array.isArray(userIds) || userIds.length === 0) {
      return res.status(400).json({
        success: false,
        error: 'Please provide user IDs to delete'
      });
    }

    const result = await User.deleteMany({
      _id: { $in: userIds }
    });

    res.status(200).json({
      success: true,
      data: {
        deletedCount: result.deletedCount
      }
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Get all models (admin)
// @route   GET /api/admin/models
// @access  Private/Admin
export const getModels = async (req, res, next) => {
  try {
    // Get query parameters
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const search = req.query.search || '';
    const category = req.query.category;
    const status = req.query.status;
    const sortBy = req.query.sortBy || 'createdAt';
    const sortDirection = req.query.sortDirection || 'desc';

    // Build query
    let query = {};

    // Search filter
    if (search) {
      query.$or = [
        { title: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } }
      ];
    }

    // Category filter
    if (category && category !== 'all') {
      query.category = category;
    }

    // Status filter
    if (status && status !== 'all') {
      query.status = status;
    }

    // Calculate skip
    const skip = (page - 1) * limit;

    // Build sort object
    const sort = {};
    sort[sortBy] = sortDirection === 'asc' ? 1 : -1;

    // Get total count
    const total = await Model.countDocuments(query);

    // Get models with pagination
    const models = await Model.find(query)
      .sort(sort)
      .skip(skip)
      .limit(limit)
      .populate('createdBy', 'name email');

    res.status(200).json({
      success: true,
      data: {
        data: models,
        total: total,
        page: page,
        pages: Math.ceil(total / limit),
        limit: limit
      }
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Get single model (admin)
// @route   GET /api/admin/models/:id
// @access  Private/Admin
export const getModel = async (req, res, next) => {
  try {
    const model = await Model.findById(req.params.id)
      .populate('createdBy', 'name email');

    if (!model) {
      return res.status(404).json({
        success: false,
        error: 'Model not found'
      });
    }

    res.status(200).json({
      success: true,
      data: model
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Update model (admin)
// @route   PUT /api/admin/models/:id
// @access  Private/Admin
export const updateModel = async (req, res, next) => {
  try {
    const model = await Model.findByIdAndUpdate(
      req.params.id,
      req.body,
      {
        new: true,
        runValidators: true
      }
    ).populate('createdBy', 'name email');

    if (!model) {
      return res.status(404).json({
        success: false,
        error: 'Model not found'
      });
    }

    res.status(200).json({
      success: true,
      data: model
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Delete model (admin)
// @route   DELETE /api/admin/models/:id
// @access  Private/Admin
export const deleteModel = async (req, res, next) => {
  try {
    const model = await Model.findById(req.params.id);

    if (!model) {
      return res.status(404).json({
        success: false,
        error: 'Model not found'
      });
    }

    await model.deleteOne();

    res.status(200).json({
      success: true,
      data: {}
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Bulk delete models (admin)
// @route   POST /api/admin/models/bulk-delete
// @access  Private/Admin
export const bulkDeleteModels = async (req, res, next) => {
  try {
    const { modelIds } = req.body;

    if (!modelIds || !Array.isArray(modelIds) || modelIds.length === 0) {
      return res.status(400).json({
        success: false,
        error: 'Please provide model IDs to delete'
      });
    }

    const result = await Model.deleteMany({
      _id: { $in: modelIds }
    });

    res.status(200).json({
      success: true,
      data: {
        deletedCount: result.deletedCount
      }
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Approve model (admin)
// @route   PUT /api/admin/models/:id/approve
// @access  Private/Admin
export const approveModel = async (req, res, next) => {
  try {
    const model = await Model.findByIdAndUpdate(
      req.params.id,
      { status: 'approved' },
      { new: true, runValidators: true }
    );

    if (!model) {
      return res.status(404).json({
        success: false,
        error: 'Model not found'
      });
    }

    res.status(200).json({
      success: true,
      data: model
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Reject model (admin)
// @route   PUT /api/admin/models/:id/reject
// @access  Private/Admin
export const rejectModel = async (req, res, next) => {
  try {
    const { reason } = req.body;

    const model = await Model.findByIdAndUpdate(
      req.params.id,
      {
        status: 'rejected',
        rejectionReason: reason
      },
      { new: true, runValidators: true }
    );

    if (!model) {
      return res.status(404).json({
        success: false,
        error: 'Model not found'
      });
    }

    res.status(200).json({
      success: true,
      data: model
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Feature model (admin)
// @route   PUT /api/admin/models/:id/feature
// @access  Private/Admin
export const featureModel = async (req, res, next) => {
  try {
    const model = await Model.findByIdAndUpdate(
      req.params.id,
      { featured: true },
      { new: true, runValidators: true }
    );

    if (!model) {
      return res.status(404).json({
        success: false,
        error: 'Model not found'
      });
    }

    res.status(200).json({
      success: true,
      data: model
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Unfeature model (admin)
// @route   PUT /api/admin/models/:id/unfeature
// @access  Private/Admin
export const unfeatureModel = async (req, res, next) => {
  try {
    const model = await Model.findByIdAndUpdate(
      req.params.id,
      { featured: false },
      { new: true, runValidators: true }
    );

    if (!model) {
      return res.status(404).json({
        success: false,
        error: 'Model not found'
      });
    }

    res.status(200).json({
      success: true,
      data: model
    });
  } catch (error) {
    next(error);
  }
};

// Placeholder functions for analytics and settings
export const getAnalytics = async (req, res, next) => {
  res.status(200).json({ success: true, data: { message: 'Analytics endpoint' } });
};

export const getUserAnalytics = async (req, res, next) => {
  res.status(200).json({ success: true, data: { message: 'User analytics endpoint' } });
};

export const getModelAnalytics = async (req, res, next) => {
  res.status(200).json({ success: true, data: { message: 'Model analytics endpoint' } });
};

export const getRevenueAnalytics = async (req, res, next) => {
  res.status(200).json({ success: true, data: { message: 'Revenue analytics endpoint' } });
};

export const getSettings = async (req, res, next) => {
  res.status(200).json({ success: true, data: { message: 'Settings endpoint' } });
};

export const updateSettings = async (req, res, next) => {
  res.status(200).json({ success: true, data: { message: 'Update settings endpoint' } });
};

export const getSystemInfo = async (req, res, next) => {
  res.status(200).json({ success: true, data: { message: 'System info endpoint' } });
};

export const clearCache = async (req, res, next) => {
  res.status(200).json({ success: true, data: { message: 'Clear cache endpoint' } });
};

export const backupDatabase = async (req, res, next) => {
  res.status(200).json({ success: true, data: { message: 'Backup database endpoint' } });
};

// @desc    Create user (admin)
// @route   POST /api/admin/users
// @access  Private/Admin
export const createUser = async (req, res, next) => {
  try {
    const {
      name,
      email,
      password,
      role,
      phone,
      jobTitle,
      company,
      location,
      website,
      bio,
      status
    } = req.body;

    // Check if user already exists
    const existingUser = await User.findOne({ email });
    if (existingUser) {
      return res.status(400).json({
        success: false,
        error: 'User with this email already exists'
      });
    }

    // Create user
    const user = await User.create({
      name,
      email,
      password,
      role: role || 'user',
      phone,
      jobTitle,
      company,
      location,
      website,
      bio,
      status: status || 'active'
    });

    // Remove password from response
    user.password = undefined;

    res.status(201).json({
      success: true,
      data: user
    });
  } catch (error) {
    next(error);
  }
};