#!/usr/bin/env sh
. "$(dirname -- "$0")/_/husky.sh"

# Run ESLint to check for React singleton usage
npm run lint

# Check for duplicate React instances
echo "Checking for duplicate React instances..."
npm ls react | grep -v "deduped" || true
npm ls react-dom | grep -v "deduped" || true

# Remind about React singleton usage
echo "\033[33mReminder: Always import React hooks from './utils/reactSingleton' instead of 'react'\033[0m"
