import VirtualShowroom from '../models/VirtualShowroom.js';
import Model from '../models/Model.js';
import asyncHandler from 'express-async-handler';

// @desc    Create new virtual showroom
// @route   POST /api/showrooms
// @access  Private
export const createShowroom = asyncHandler(async (req, res) => {
  const showroomData = {
    ...req.body,
    owner: req.user.id
  };

  const showroom = await VirtualShowroom.create(showroomData);

  res.status(201).json({
    success: true,
    data: showroom
  });
});

// @desc    Get all showrooms
// @route   GET /api/showrooms
// @access  Public
export const getShowrooms = asyncHandler(async (req, res) => {
  const { 
    page = 1, 
    limit = 12, 
    type, 
    theme, 
    featured,
    search,
    owner 
  } = req.query;

  // Build query
  let query = {};
  
  // Public showrooms only for non-owners
  if (!owner || owner !== req.user?.id) {
    query['access.isPublic'] = true;
  }
  
  if (type) query.type = type;
  if (theme) query.theme = theme;
  if (featured) query.featured = featured === 'true';
  if (owner) query.owner = owner;
  
  if (search) {
    query.$or = [
      { name: { $regex: search, $options: 'i' } },
      { description: { $regex: search, $options: 'i' } },
      { tags: { $in: [new RegExp(search, 'i')] } }
    ];
  }

  const showrooms = await VirtualShowroom.find(query)
    .populate('owner', 'name profileImage')
    .populate('models.model', 'title imageUrl category rating downloads')
    .sort({ featured: -1, 'analytics.totalVisits': -1, createdAt: -1 })
    .limit(limit * 1)
    .skip((page - 1) * limit);

  const total = await VirtualShowroom.countDocuments(query);

  res.status(200).json({
    success: true,
    data: showrooms,
    pagination: {
      page: parseInt(page),
      limit: parseInt(limit),
      total,
      pages: Math.ceil(total / limit)
    }
  });
});

// @desc    Get single showroom
// @route   GET /api/showrooms/:id
// @access  Public
export const getShowroom = asyncHandler(async (req, res) => {
  const showroom = await VirtualShowroom.findById(req.params.id)
    .populate('owner', 'name profileImage bio')
    .populate('models.model', 'title description imageUrl category format fileSize rating downloads isPremium')
    .populate('access.allowedUsers', 'name profileImage');

  if (!showroom) {
    return res.status(404).json({
      success: false,
      error: 'Showroom not found'
    });
  }

  // Check access permissions
  if (!showroom.access.isPublic) {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        error: 'Authentication required'
      });
    }

    const hasAccess = showroom.owner.toString() === req.user.id ||
                     showroom.access.allowedUsers.some(user => user._id.toString() === req.user.id);

    if (!hasAccess) {
      return res.status(403).json({
        success: false,
        error: 'Access denied'
      });
    }
  }

  // Increment view count
  showroom.analytics.totalVisits += 1;
  await showroom.save();

  res.status(200).json({
    success: true,
    data: showroom
  });
});

// @desc    Update showroom
// @route   PUT /api/showrooms/:id
// @access  Private
export const updateShowroom = asyncHandler(async (req, res) => {
  let showroom = await VirtualShowroom.findById(req.params.id);

  if (!showroom) {
    return res.status(404).json({
      success: false,
      error: 'Showroom not found'
    });
  }

  // Check ownership
  if (showroom.owner.toString() !== req.user.id && req.user.role !== 'admin') {
    return res.status(403).json({
      success: false,
      error: 'Not authorized to update this showroom'
    });
  }

  showroom = await VirtualShowroom.findByIdAndUpdate(
    req.params.id,
    req.body,
    { new: true, runValidators: true }
  ).populate('owner', 'name profileImage')
   .populate('models.model', 'title imageUrl category');

  res.status(200).json({
    success: true,
    data: showroom
  });
});

// @desc    Delete showroom
// @route   DELETE /api/showrooms/:id
// @access  Private
export const deleteShowroom = asyncHandler(async (req, res) => {
  const showroom = await VirtualShowroom.findById(req.params.id);

  if (!showroom) {
    return res.status(404).json({
      success: false,
      error: 'Showroom not found'
    });
  }

  // Check ownership
  if (showroom.owner.toString() !== req.user.id && req.user.role !== 'admin') {
    return res.status(403).json({
      success: false,
      error: 'Not authorized to delete this showroom'
    });
  }

  await showroom.deleteOne();

  res.status(200).json({
    success: true,
    data: {}
  });
});

// @desc    Add model to showroom
// @route   POST /api/showrooms/:id/models
// @access  Private
export const addModelToShowroom = asyncHandler(async (req, res) => {
  const { modelId, position, rotation, scale, spotlight, label } = req.body;

  const showroom = await VirtualShowroom.findById(req.params.id);

  if (!showroom) {
    return res.status(404).json({
      success: false,
      error: 'Showroom not found'
    });
  }

  // Check ownership or edit permissions
  if (showroom.owner.toString() !== req.user.id && req.user.role !== 'admin') {
    return res.status(403).json({
      success: false,
      error: 'Not authorized to edit this showroom'
    });
  }

  // Check if model exists
  const model = await Model.findById(modelId);
  if (!model) {
    return res.status(404).json({
      success: false,
      error: 'Model not found'
    });
  }

  // Check if model already exists in showroom
  const existingModel = showroom.models.find(m => m.model.toString() === modelId);
  if (existingModel) {
    return res.status(400).json({
      success: false,
      error: 'Model already exists in this showroom'
    });
  }

  // Add model to showroom
  showroom.models.push({
    model: modelId,
    position: position || { x: 0, y: 0, z: 0 },
    rotation: rotation || { x: 0, y: 0, z: 0 },
    scale: scale || { x: 1, y: 1, z: 1 },
    spotlight: spotlight || { enabled: false },
    label: label || { visible: true },
    order: showroom.models.length
  });

  await showroom.save();

  // Populate the new model data
  await showroom.populate('models.model', 'title imageUrl category');

  res.status(200).json({
    success: true,
    data: showroom
  });
});

// @desc    Remove model from showroom
// @route   DELETE /api/showrooms/:id/models/:modelId
// @access  Private
export const removeModelFromShowroom = asyncHandler(async (req, res) => {
  const showroom = await VirtualShowroom.findById(req.params.id);

  if (!showroom) {
    return res.status(404).json({
      success: false,
      error: 'Showroom not found'
    });
  }

  // Check ownership
  if (showroom.owner.toString() !== req.user.id && req.user.role !== 'admin') {
    return res.status(403).json({
      success: false,
      error: 'Not authorized to edit this showroom'
    });
  }

  // Remove model from showroom
  showroom.models = showroom.models.filter(
    m => m.model.toString() !== req.params.modelId
  );

  await showroom.save();

  res.status(200).json({
    success: true,
    data: showroom
  });
});

// @desc    Update model position in showroom
// @route   PUT /api/showrooms/:id/models/:modelId
// @access  Private
export const updateModelInShowroom = asyncHandler(async (req, res) => {
  const { position, rotation, scale, spotlight, label } = req.body;

  const showroom = await VirtualShowroom.findById(req.params.id);

  if (!showroom) {
    return res.status(404).json({
      success: false,
      error: 'Showroom not found'
    });
  }

  // Check ownership
  if (showroom.owner.toString() !== req.user.id && req.user.role !== 'admin') {
    return res.status(403).json({
      success: false,
      error: 'Not authorized to edit this showroom'
    });
  }

  // Find and update model
  const modelIndex = showroom.models.findIndex(
    m => m.model.toString() === req.params.modelId
  );

  if (modelIndex === -1) {
    return res.status(404).json({
      success: false,
      error: 'Model not found in showroom'
    });
  }

  // Update model properties
  if (position) showroom.models[modelIndex].position = position;
  if (rotation) showroom.models[modelIndex].rotation = rotation;
  if (scale) showroom.models[modelIndex].scale = scale;
  if (spotlight) showroom.models[modelIndex].spotlight = spotlight;
  if (label) showroom.models[modelIndex].label = label;

  await showroom.save();

  res.status(200).json({
    success: true,
    data: showroom.models[modelIndex]
  });
});

// @desc    Get featured showrooms
// @route   GET /api/showrooms/featured
// @access  Public
export const getFeaturedShowrooms = asyncHandler(async (req, res) => {
  const showrooms = await VirtualShowroom.find({
    featured: true,
    'access.isPublic': true
  })
    .populate('owner', 'name profileImage')
    .populate('models.model', 'title imageUrl category')
    .sort({ 'analytics.totalVisits': -1 })
    .limit(6);

  res.status(200).json({
    success: true,
    data: showrooms
  });
});

// @desc    Get showroom analytics
// @route   GET /api/showrooms/:id/analytics
// @access  Private
export const getShowroomAnalytics = asyncHandler(async (req, res) => {
  const showroom = await VirtualShowroom.findById(req.params.id);

  if (!showroom) {
    return res.status(404).json({
      success: false,
      error: 'Showroom not found'
    });
  }

  // Check ownership
  if (showroom.owner.toString() !== req.user.id && req.user.role !== 'admin') {
    return res.status(403).json({
      success: false,
      error: 'Not authorized to view analytics'
    });
  }

  res.status(200).json({
    success: true,
    data: showroom.analytics
  });
});
