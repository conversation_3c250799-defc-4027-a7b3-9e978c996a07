import User from '../models/User.js';
import Payment from '../models/Payment.js';
import Subscription from '../models/Subscription.js';
import Model from '../models/Model.js';
import paymentUtils from '../utils/paymentUtils.js';

// Subscription plans
const SUBSCRIPTION_PLANS = paymentUtils.SUBSCRIPTION_PLANS;

// @desc    Create payment intent
// @route   POST /api/payments/create-payment-intent
// @access  Private
export const createPaymentIntent = async (req, res, next) => {
  try {
    const { amount, currency = 'usd', paymentType, modelId, subscriptionPlan, interval = 'monthly' } = req.body;

    // Validate required fields
    if (!amount || !paymentType) {
      return res.status(400).json({
        success: false,
        error: 'Please provide amount and payment type'
      });
    }

    // If it's a model purchase, check if model exists
    if (paymentType === 'model_purchase' && modelId) {
      const model = await Model.findById(modelId);
      if (!model) {
        return res.status(404).json({
          success: false,
          error: 'Model not found'
        });
      }
    }

    // If it's a subscription, validate plan
    if (paymentType === 'subscription' && subscriptionPlan) {
      if (!SUBSCRIPTION_PLANS[subscriptionPlan]) {
        return res.status(400).json({
          success: false,
          error: 'Invalid subscription plan'
        });
      }
    }

    // Get or create Stripe customer
    let customer;
    const user = await User.findById(req.user.id);

    if (user.subscription && user.subscription.stripeCustomerId) {
      customer = await stripe.customers.retrieve(user.subscription.stripeCustomerId);
    } else {
      customer = await stripe.customers.create({
        email: user.email,
        name: user.name,
        metadata: {
          userId: user._id.toString()
        }
      });

      // Update user with Stripe customer ID
      user.subscription = {
        ...user.subscription,
        stripeCustomerId: customer.id
      };
      await user.save();
    }

    // Create payment intent
    const paymentIntent = await stripe.paymentIntents.create({
      amount: Math.round(amount * 100), // Convert to cents
      currency,
      customer: customer.id,
      metadata: {
        userId: req.user.id,
        paymentType,
        modelId: modelId || '',
        subscriptionPlan: subscriptionPlan || '',
        interval: interval || ''
      }
    });

    res.status(200).json({
      success: true,
      clientSecret: paymentIntent.client_secret
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Confirm payment
// @route   POST /api/payments/confirm-payment
// @access  Private
export const confirmPayment = async (req, res, next) => {
  try {
    const { paymentIntentId } = req.body;

    if (!paymentIntentId) {
      return res.status(400).json({
        success: false,
        error: 'Please provide payment intent ID'
      });
    }

    // Retrieve payment intent
    const paymentIntent = await stripe.paymentIntents.retrieve(paymentIntentId);

    if (paymentIntent.status !== 'succeeded') {
      return res.status(400).json({
        success: false,
        error: 'Payment has not been completed'
      });
    }

    // Extract metadata
    const { userId, paymentType, modelId, subscriptionPlan, interval } = paymentIntent.metadata;

    // Verify user
    if (userId !== req.user.id) {
      return res.status(401).json({
        success: false,
        error: 'Not authorized to confirm this payment'
      });
    }

    // Create payment record
    const payment = await Payment.create({
      user: req.user.id,
      amount: paymentIntent.amount / 100, // Convert from cents
      currency: paymentIntent.currency,
      paymentMethod: 'credit_card',
      status: 'completed',
      type: paymentType,
      description: getPaymentDescription(paymentType, modelId, subscriptionPlan),
      stripePaymentId: paymentIntent.id,
      stripeCustomerId: paymentIntent.customer,
      subscriptionPlan,
      model: modelId || null
    });

    // Handle subscription if applicable
    if (paymentType === 'subscription' && subscriptionPlan) {
      await handleSubscriptionPurchase(req.user.id, subscriptionPlan, interval, payment._id);
    }

    // Handle model purchase if applicable
    if (paymentType === 'model_purchase' && modelId) {
      await handleModelPurchase(req.user.id, modelId);
    }

    res.status(200).json({
      success: true,
      data: payment
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Get payment history
// @route   GET /api/payments/history
// @access  Private
export const getPaymentHistory = async (req, res, next) => {
  try {
    const payments = await Payment.find({ user: req.user.id })
      .sort('-createdAt')
      .populate('model', 'title imageUrl');

    res.status(200).json({
      success: true,
      count: payments.length,
      data: payments
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Get payment details
// @route   GET /api/payments/:id
// @access  Private
export const getPaymentDetails = async (req, res, next) => {
  try {
    const payment = await Payment.findById(req.params.id)
      .populate('model', 'title imageUrl')
      .populate('user', 'name email');

    if (!payment) {
      return res.status(404).json({
        success: false,
        error: 'Payment not found'
      });
    }

    // Check if user is authorized
    if (payment.user._id.toString() !== req.user.id && req.user.role !== 'admin') {
      return res.status(401).json({
        success: false,
        error: 'Not authorized to view this payment'
      });
    }

    res.status(200).json({
      success: true,
      data: payment
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Create subscription
// @route   POST /api/payments/subscriptions
// @access  Private
export const createSubscription = async (req, res, next) => {
  try {
    const { plan, interval = 'monthly' } = req.body;

    if (!plan || !SUBSCRIPTION_PLANS[plan]) {
      return res.status(400).json({
        success: false,
        error: 'Please provide a valid subscription plan'
      });
    }

    const user = await User.findById(req.user.id);

    // Check if user already has an active subscription
    const existingSubscription = await Subscription.findOne({
      user: req.user.id,
      status: 'active'
    });

    if (existingSubscription) {
      return res.status(400).json({
        success: false,
        error: 'You already have an active subscription'
      });
    }

    // Calculate subscription details
    const price = interval === 'yearly'
      ? SUBSCRIPTION_PLANS[plan].yearlyPrice
      : SUBSCRIPTION_PLANS[plan].monthlyPrice;

    const endDate = new Date();
    if (interval === 'yearly') {
      endDate.setFullYear(endDate.getFullYear() + 1);
    } else {
      endDate.setMonth(endDate.getMonth() + 1);
    }

    // Create subscription record
    const subscription = await Subscription.create({
      user: req.user.id,
      plan,
      status: 'active',
      startDate: new Date(),
      endDate,
      renewalDate: endDate,
      price,
      interval,
      features: SUBSCRIPTION_PLANS[plan].features
    });

    // Update user's subscription info
    user.subscription = {
      type: plan,
      startDate: new Date(),
      endDate,
      status: 'active'
    };

    // Reset download credits based on new plan
    switch (plan) {
      case 'basic':
        user.downloadCredits = 20;
        break;
      case 'premium':
        user.downloadCredits = 50;
        break;
      case 'professional':
        user.downloadCredits = 100;
        break;
      default:
        user.downloadCredits = 5;
    }

    await user.save();

    res.status(201).json({
      success: true,
      data: subscription
    });
  } catch (error) {
    next(error);
  }
};

// Helper functions
const getPaymentDescription = (paymentType, modelId, subscriptionPlan) => {
  switch (paymentType) {
    case 'subscription':
      return `Subscription to ${subscriptionPlan} plan`;
    case 'model_purchase':
      return `Purchase of model #${modelId}`;
    case 'one_time_purchase':
      return 'One-time purchase';
    default:
      return 'Payment';
  }
};

const handleSubscriptionPurchase = async (userId, plan, interval, paymentId) => {
  const user = await User.findById(userId);

  // Calculate subscription details
  const price = interval === 'yearly'
    ? SUBSCRIPTION_PLANS[plan].yearlyPrice
    : SUBSCRIPTION_PLANS[plan].monthlyPrice;

  const endDate = new Date();
  if (interval === 'yearly') {
    endDate.setFullYear(endDate.getFullYear() + 1);
  } else {
    endDate.setMonth(endDate.getMonth() + 1);
  }

  // Create subscription record
  const subscription = await Subscription.create({
    user: userId,
    plan,
    status: 'active',
    startDate: new Date(),
    endDate,
    renewalDate: endDate,
    price,
    interval,
    features: SUBSCRIPTION_PLANS[plan].features,
    paymentHistory: [{
      paymentId,
      date: new Date(),
      amount: price,
      status: 'successful'
    }]
  });

  // Update user's subscription info
  user.subscription = {
    type: plan,
    startDate: new Date(),
    endDate,
    status: 'active'
  };

  // Reset download credits based on new plan
  switch (plan) {
    case 'basic':
      user.downloadCredits = 20;
      break;
    case 'premium':
      user.downloadCredits = 50;
      break;
    case 'professional':
      user.downloadCredits = 100;
      break;
    default:
      user.downloadCredits = 5;
  }

  await user.save();

  return subscription;
};

const handleModelPurchase = async (userId, modelId) => {
  // Add model to user's purchased models
  await User.findByIdAndUpdate(userId, {
    $addToSet: { purchasedModels: modelId }
  });

  // Increment model's purchase count
  await Model.findByIdAndUpdate(modelId, {
    $inc: { purchases: 1 }
  });
};

// @desc    Cancel subscription
// @route   DELETE /api/payments/subscriptions/:id
// @access  Private
export const cancelSubscription = async (req, res, next) => {
  try {
    const subscription = await Subscription.findById(req.params.id);

    if (!subscription) {
      return res.status(404).json({
        success: false,
        error: 'Subscription not found'
      });
    }

    // Check if user is authorized
    if (subscription.user.toString() !== req.user.id && req.user.role !== 'admin') {
      return res.status(401).json({
        success: false,
        error: 'Not authorized to cancel this subscription'
      });
    }

    // Update subscription status
    subscription.status = 'cancelled';
    await subscription.save();

    // Update user's subscription info
    const user = await User.findById(req.user.id);
    if (user.subscription) {
      user.subscription.status = 'cancelled';
      await user.save();
    }

    res.status(200).json({
      success: true,
      data: subscription
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Update subscription
// @route   PUT /api/payments/subscriptions/:id
// @access  Private
export const updateSubscription = async (req, res, next) => {
  try {
    const { plan, interval } = req.body;

    const subscription = await Subscription.findById(req.params.id);

    if (!subscription) {
      return res.status(404).json({
        success: false,
        error: 'Subscription not found'
      });
    }

    // Check if user is authorized
    if (subscription.user.toString() !== req.user.id && req.user.role !== 'admin') {
      return res.status(401).json({
        success: false,
        error: 'Not authorized to update this subscription'
      });
    }

    // Update subscription fields if provided
    if (plan && SUBSCRIPTION_PLANS[plan]) {
      subscription.plan = plan;
      subscription.features = SUBSCRIPTION_PLANS[plan].features;

      // Update price based on interval
      if (interval) {
        subscription.interval = interval;
        subscription.price = interval === 'yearly'
          ? SUBSCRIPTION_PLANS[plan].yearlyPrice
          : SUBSCRIPTION_PLANS[plan].monthlyPrice;
      }
    }

    await subscription.save();

    // Update user's subscription info
    const user = await User.findById(req.user.id);
    if (user.subscription && plan) {
      user.subscription.type = plan;

      // Reset download credits based on new plan
      switch (plan) {
        case 'basic':
          user.downloadCredits = 20;
          break;
        case 'premium':
          user.downloadCredits = 50;
          break;
        case 'professional':
          user.downloadCredits = 100;
          break;
        default:
          user.downloadCredits = 5;
      }

      await user.save();
    }

    res.status(200).json({
      success: true,
      data: subscription
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Get subscription details
// @route   GET /api/payments/subscriptions/:id
// @access  Private
export const getSubscriptionDetails = async (req, res, next) => {
  try {
    const subscription = await Subscription.findById(req.params.id);

    if (!subscription) {
      return res.status(404).json({
        success: false,
        error: 'Subscription not found'
      });
    }

    // Check if user is authorized
    if (subscription.user.toString() !== req.user.id && req.user.role !== 'admin') {
      return res.status(401).json({
        success: false,
        error: 'Not authorized to view this subscription'
      });
    }

    res.status(200).json({
      success: true,
      data: subscription
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Get current subscription
// @route   GET /api/payments/subscriptions/user
// @access  Private
export const getCurrentSubscription = async (req, res, next) => {
  try {
    // Always return a default free subscription instead of error
    const defaultSubscription = {
      id: 'sub_free_' + req.user.id,
      plan: 'free',
      status: 'active',
      startDate: new Date().toISOString(),
      endDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
      price: 0,
      interval: 'monthly',
      features: [
        '3 downloads per month',
        'Access to free models only',
        'Community support',
        'Personal use only',
        'Standard resolution downloads'
      ]
    };

    res.status(200).json({
      success: true,
      data: defaultSubscription
    });
  } catch (error) {
    console.error('Error in getCurrentSubscription:', error);
    // Always return default subscription even on error
    const defaultSubscription = {
      id: 'sub_free_' + req.user.id,
      plan: 'free',
      status: 'active',
      startDate: new Date().toISOString(),
      endDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
      price: 0,
      interval: 'monthly',
      features: []
    };

    res.status(200).json({
      success: true,
      data: defaultSubscription
    });
  }
};

// @desc    Get invoices
// @route   GET /api/payments/user-invoices
// @access  Private
export const getInvoices = async (req, res, next) => {
  try {
    // Always return empty array instead of error
    res.status(200).json({
      success: true,
      count: 0,
      data: []
    });
  } catch (error) {
    console.error('Error in getInvoices:', error);
    res.status(200).json({
      success: true,
      count: 0,
      data: []
    });
  }
};

// @desc    Get payment methods
// @route   GET /api/payments/user-methods
// @access  Private
export const getPaymentMethods = async (req, res, next) => {
  try {
    // Always return empty array instead of error
    res.status(200).json({
      success: true,
      count: 0,
      data: []
    });
  } catch (error) {
    console.error('Error in getPaymentMethods:', error);
    res.status(200).json({
      success: true,
      count: 0,
      data: []
    });
  }
};

// @desc    Add payment method
// @route   POST /api/payments/methods
// @access  Private
export const addPaymentMethod = async (req, res, next) => {
  try {
    const { paymentMethodId, isDefault } = req.body;

    if (!paymentMethodId) {
      return res.status(400).json({
        success: false,
        error: 'Please provide payment method ID'
      });
    }

    const user = await User.findById(req.user.id);

    // If user has no Stripe customer ID, create one
    if (!user.subscription || !user.subscription.stripeCustomerId) {
      const customer = await stripe.customers.create({
        email: user.email,
        name: user.name,
        metadata: {
          userId: user._id.toString()
        }
      });

      user.subscription = {
        ...user.subscription,
        stripeCustomerId: customer.id
      };
      await user.save();
    }



    // In production, attach payment method to customer
    await stripe.paymentMethods.attach(paymentMethodId, {
      customer: user.subscription.stripeCustomerId
    });

    // Set as default if requested
    if (isDefault) {
      await stripe.paymentMethods.update(paymentMethodId, {
        metadata: { isDefault: 'true' }
      });
    }

    // Get updated payment method
    const paymentMethod = await stripe.paymentMethods.retrieve(paymentMethodId);

    // Format payment method
    const formattedPaymentMethod = {
      id: paymentMethod.id,
      type: paymentMethod.type,
      brand: paymentMethod.card.brand,
      last4: paymentMethod.card.last4,
      expMonth: paymentMethod.card.exp_month,
      expYear: paymentMethod.card.exp_year,
      isDefault: paymentMethod.metadata.isDefault === 'true'
    };

    res.status(200).json({
      success: true,
      data: formattedPaymentMethod
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Remove payment method
// @route   DELETE /api/payments/methods/:id
// @access  Private
export const removePaymentMethod = async (req, res, next) => {
  try {
    const paymentMethodId = req.params.id;
    const user = await User.findById(req.user.id);

    // If user has no Stripe customer ID, return error
    if (!user.subscription || !user.subscription.stripeCustomerId) {
      return res.status(400).json({
        success: false,
        error: 'No payment methods found'
      });
    }



    // In production, detach payment method from customer
    await stripe.paymentMethods.detach(paymentMethodId);

    res.status(200).json({
      success: true,
      data: {}
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Handle payment webhook
// @route   POST /api/payments/webhook
// @access  Public
export const handleStripeWebhook = async (req, res, next) => {
  try {
    // This is a placeholder for a real payment webhook handler
    // In a real implementation, you would verify the webhook signature
    // and handle different event types

    console.log('Payment webhook received');

    res.status(200).json({ received: true });
  } catch (error) {
    next(error);
  }
};

// @desc    Get payment API status
// @route   GET /api/payments/status
// @access  Public
export const getPaymentApiStatus = async (req, res, next) => {
  try {
    // Check if payment is enabled
    let status = 'online';
    let message = 'Payment system is operational';

    if (process.env.PAYMENT_ENABLED !== 'true') {
      status = 'offline';
      message = 'Payment system is not enabled';
    }

    return res.status(200).json({
      success: status === 'online',
      status,
      message,
      timestamp: new Date().toISOString()
    });
  } catch (err) {
    console.error('Error checking payment API status:', err);
    return res.status(500).json({
      success: false,
      status: 'error',
      message: 'Failed to check payment system status',
      timestamp: new Date().toISOString()
    });
  }
};